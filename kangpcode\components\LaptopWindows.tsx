'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface LaptopWindowsProps {
  isVisible: boolean;
  onClose: () => void;
}

interface GitHubRepo {
  id: number;
  name: string;
  description: string;
  language: string;
  stargazers_count: number;
  forks_count: number;
  html_url: string;
  updated_at: string;
}

interface GitHubUser {
  login: string;
  name: string;
  bio: string;
  public_repos: number;
  followers: number;
  following: number;
  avatar_url: string;
  html_url: string;
}

export default function LaptopWindows({ isVisible, onClose }: LaptopWindowsProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isBooting, setIsBooting] = useState(true);
  const [githubUser, setGithubUser] = useState<GitHubUser | null>(null);
  const [githubRepos, setGithubRepos] = useState<GitHubRepo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState<'profile' | 'repos' | 'stats'>('profile');

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Boot sequence
  useEffect(() => {
    if (isVisible) {
      setIsBooting(true);
      const bootTimer = setTimeout(() => {
        setIsBooting(false);
        fetchGitHubData();
      }, 2000);
      return () => clearTimeout(bootTimer);
    }
  }, [isVisible]);

  const fetchGitHubData = async () => {
    setIsLoading(true);
    try {
      // Fetch user data
      const userResponse = await fetch('https://api.github.com/users/kangpcode');
      if (userResponse.ok) {
        const userData = await userResponse.json();
        setGithubUser(userData);
      }

      // Fetch repositories
      const reposResponse = await fetch('https://api.github.com/users/kangpcode/repos?sort=updated&per_page=10');
      if (reposResponse.ok) {
        const reposData = await reposResponse.json();
        setGithubRepos(reposData);
      }
    } catch (error) {
      console.error('Error fetching GitHub data:', error);
      // Set mock data if API fails
      setGithubUser({
        login: 'kangpcode',
        name: 'Dhafa Nazula Permadi',
        bio: 'Fullstack Developer & Content Creator | Building the future of Indonesian tech',
        public_repos: 25,
        followers: 150,
        following: 80,
        avatar_url: '/assets/images/kangpcode-avatar.jpg',
        html_url: 'https://github.com/kangpcode'
      });
      
      setGithubRepos([
        {
          id: 1,
          name: 'portfolio-3d',
          description: 'Interactive 3D Portfolio Website with Three.js',
          language: 'TypeScript',
          stargazers_count: 45,
          forks_count: 12,
          html_url: 'https://github.com/kangpcode/portfolio-3d',
          updated_at: '2024-01-15T10:30:00Z'
        },
        {
          id: 2,
          name: 'langkah-kode-nusantara',
          description: 'Educational programming book for Indonesian developers',
          language: 'Markdown',
          stargazers_count: 89,
          forks_count: 23,
          html_url: 'https://github.com/kangpcode/langkah-kode-nusantara',
          updated_at: '2024-01-10T14:20:00Z'
        }
      ]);
    }
    setIsLoading(false);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      weekday: 'short',
      month: 'short',
      day: 'numeric'
    });
  };

  const getLanguageColor = (language: string) => {
    const colors: { [key: string]: string } = {
      'TypeScript': '#3178c6',
      'JavaScript': '#f1e05a',
      'Python': '#3572A5',
      'Java': '#b07219',
      'C++': '#f34b7d',
      'HTML': '#e34c26',
      'CSS': '#1572B6',
      'Markdown': '#083fa1',
      'PHP': '#4F5D95'
    };
    return colors[language] || '#6b7280';
  };

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black z-50 overflow-hidden">
      <AnimatePresence>
        {isBooting ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex items-center justify-center h-full bg-blue-600 text-white"
          >
            <div className="text-center space-y-4">
              <div className="text-6xl mb-4">🪟</div>
              <h2 className="text-2xl font-bold">Windows 11</h2>
              <p className="text-lg">Starting GitHub Viewer...</p>
              <div className="flex justify-center space-x-1 mt-4">
                {[0, 1, 2, 3, 4].map((i) => (
                  <motion.div
                    key={i}
                    className="w-2 h-2 bg-white rounded-full"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      delay: i * 0.1,
                    }}
                  />
                ))}
              </div>
            </div>
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="h-full bg-gradient-to-br from-blue-100 to-blue-200 relative"
          >
            {/* Windows Taskbar */}
            <div className="absolute bottom-0 left-0 right-0 h-12 bg-gray-900/95 backdrop-blur-sm flex items-center justify-between px-4 z-10">
              {/* Start Button */}
              <button className="flex items-center space-x-2 px-3 py-2 hover:bg-gray-700/50 rounded transition-colors text-white">
                <span className="text-xl">🪟</span>
                <span className="font-medium">Start</span>
              </button>

              {/* App Icons */}
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-xl text-white">
                  🌐
                </div>
                <div className="w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center text-xl text-white">
                  📁
                </div>
              </div>

              {/* System Tray */}
              <div className="flex items-center space-x-4 text-white text-sm">
                <span>🔊 🔋 📶</span>
                <div className="text-right">
                  <div className="font-mono">{formatTime(currentTime)}</div>
                  <div className="text-xs">{formatDate(currentTime)}</div>
                </div>
                <button
                  onClick={onClose}
                  className="hover:bg-red-600 px-2 py-1 rounded transition-colors"
                >
                  ✕
                </button>
              </div>
            </div>

            {/* Main Content - GitHub Viewer */}
            <div className="h-[calc(100%-3rem)] p-4">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="h-full bg-white rounded-lg shadow-2xl overflow-hidden"
              >
                {/* Browser Header */}
                <div className="h-12 bg-gray-100 border-b flex items-center justify-between px-4">
                  <div className="flex items-center space-x-2">
                    <div className="flex space-x-1">
                      <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                      <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <div className="ml-4 bg-gray-200 rounded px-3 py-1 text-sm text-gray-600">
                      🔒 github.com/kangpcode
                    </div>
                  </div>
                  <div className="text-lg font-bold text-gray-700">GitHub Profile</div>
                </div>

                {/* Tab Navigation */}
                <div className="h-10 bg-gray-50 border-b flex items-center px-4">
                  {[
                    { id: 'profile', name: 'Profile', icon: '👤' },
                    { id: 'repos', name: 'Repositories', icon: '📁' },
                    { id: 'stats', name: 'Statistics', icon: '📊' }
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id as any)}
                      className={`flex items-center space-x-2 px-4 py-2 rounded-t transition-colors ${
                        activeTab === tab.id 
                          ? 'bg-white border-t-2 border-blue-500 text-blue-600' 
                          : 'hover:bg-gray-100 text-gray-600'
                      }`}
                    >
                      <span>{tab.icon}</span>
                      <span className="font-medium">{tab.name}</span>
                    </button>
                  ))}
                </div>

                {/* Content Area */}
                <div className="flex-1 p-6 overflow-y-auto">
                  {isLoading ? (
                    <div className="flex items-center justify-center h-64">
                      <div className="text-center space-y-4">
                        <div className="text-4xl">⏳</div>
                        <p className="text-gray-600">Loading GitHub data...</p>
                      </div>
                    </div>
                  ) : (
                    <AnimatePresence mode="wait">
                      {activeTab === 'profile' && githubUser && (
                        <motion.div
                          key="profile"
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          className="space-y-6"
                        >
                          <div className="flex items-start space-x-6">
                            <div className="w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center text-4xl">
                              👨‍💻
                            </div>
                            <div className="flex-1">
                              <h1 className="text-3xl font-bold text-gray-900 mb-2">{githubUser.name}</h1>
                              <p className="text-xl text-gray-600 mb-4">@{githubUser.login}</p>
                              <p className="text-gray-700 mb-4">{githubUser.bio}</p>
                              <div className="flex space-x-6 text-sm text-gray-600">
                                <span>👥 {githubUser.followers} followers</span>
                                <span>👤 {githubUser.following} following</span>
                                <span>📁 {githubUser.public_repos} repositories</span>
                              </div>
                            </div>
                          </div>
                        </motion.div>
                      )}

                      {activeTab === 'repos' && (
                        <motion.div
                          key="repos"
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          className="space-y-4"
                        >
                          <h2 className="text-2xl font-bold text-gray-900 mb-4">Recent Repositories</h2>
                          {githubRepos.map((repo) => (
                            <div key={repo.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  <h3 className="text-lg font-semibold text-blue-600 mb-2">{repo.name}</h3>
                                  <p className="text-gray-700 mb-3">{repo.description}</p>
                                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                                    <span className="flex items-center space-x-1">
                                      <div 
                                        className="w-3 h-3 rounded-full" 
                                        style={{ backgroundColor: getLanguageColor(repo.language) }}
                                      ></div>
                                      <span>{repo.language}</span>
                                    </span>
                                    <span>⭐ {repo.stargazers_count}</span>
                                    <span>🍴 {repo.forks_count}</span>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </motion.div>
                      )}

                      {activeTab === 'stats' && githubUser && (
                        <motion.div
                          key="stats"
                          initial={{ opacity: 0, x: 20 }}
                          animate={{ opacity: 1, x: 0 }}
                          exit={{ opacity: 0, x: -20 }}
                          className="space-y-6"
                        >
                          <h2 className="text-2xl font-bold text-gray-900 mb-4">GitHub Statistics</h2>
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                            <div className="bg-blue-50 p-4 rounded-lg text-center">
                              <div className="text-2xl font-bold text-blue-600">{githubUser.public_repos}</div>
                              <div className="text-sm text-gray-600">Repositories</div>
                            </div>
                            <div className="bg-green-50 p-4 rounded-lg text-center">
                              <div className="text-2xl font-bold text-green-600">{githubUser.followers}</div>
                              <div className="text-sm text-gray-600">Followers</div>
                            </div>
                            <div className="bg-purple-50 p-4 rounded-lg text-center">
                              <div className="text-2xl font-bold text-purple-600">{githubUser.following}</div>
                              <div className="text-sm text-gray-600">Following</div>
                            </div>
                            <div className="bg-orange-50 p-4 rounded-lg text-center">
                              <div className="text-2xl font-bold text-orange-600">
                                {githubRepos.reduce((sum, repo) => sum + repo.stargazers_count, 0)}
                              </div>
                              <div className="text-sm text-gray-600">Total Stars</div>
                            </div>
                          </div>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  )}
                </div>
              </motion.div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

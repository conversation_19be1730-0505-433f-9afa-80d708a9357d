{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/LoadingScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface LoadingScreenProps {\n  onComplete: () => void;\n}\n\nexport default function LoadingScreen({ onComplete }: LoadingScreenProps) {\n  const [progress, setProgress] = useState(0);\n  const [loadingText, setLoadingText] = useState('Memuat Dunia KangPCode...');\n\n  const loadingSteps = [\n    'Memuat Dunia KangPCode...',\n    'Menyiapkan Kamar Virtual...',\n    'Mengaktifkan Komputer 3D...',\n    'Memuat Poster Ilmuwan...',\n    'Menyiapkan Action Figures...',\n    'Dunia KangPCode Siap!'\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setProgress(prev => {\n        const newProgress = prev + 1;\n        \n        // Update loading text based on progress\n        const stepIndex = Math.floor((newProgress / 100) * loadingSteps.length);\n        if (stepIndex < loadingSteps.length) {\n          setLoadingText(loadingSteps[stepIndex]);\n        }\n\n        if (newProgress >= 100) {\n          clearInterval(interval);\n          setTimeout(() => onComplete(), 500);\n          return 100;\n        }\n        return newProgress;\n      });\n    }, 50); // Complete in ~5 seconds\n\n    return () => clearInterval(interval);\n  }, [onComplete]);\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        className=\"fixed inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center z-50\"\n      >\n        <div className=\"text-center space-y-8\">\n          {/* Logo/Avatar */}\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\n            className=\"w-32 h-32 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center shadow-2xl\"\n          >\n            <span className=\"text-4xl font-bold text-white\">KC</span>\n          </motion.div>\n\n          {/* Title */}\n          <motion.h1\n            initial={{ y: 20, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ delay: 0.4 }}\n            className=\"text-4xl md:text-6xl font-bold text-white mb-4\"\n          >\n            KangPCode\n          </motion.h1>\n\n          <motion.p\n            initial={{ y: 20, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ delay: 0.6 }}\n            className=\"text-xl text-blue-200 mb-8\"\n          >\n            Portfolio 3D Interaktif\n          </motion.p>\n\n          {/* Loading Bar */}\n          <div className=\"w-80 mx-auto\">\n            <motion.div\n              initial={{ width: 0 }}\n              animate={{ width: '100%' }}\n              transition={{ delay: 0.8 }}\n              className=\"h-2 bg-gray-700 rounded-full overflow-hidden\"\n            >\n              <motion.div\n                className=\"h-full bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full\"\n                style={{ width: `${progress}%` }}\n                transition={{ duration: 0.1 }}\n              />\n            </motion.div>\n            \n            <div className=\"flex justify-between mt-2 text-sm text-blue-200\">\n              <span>{progress}%</span>\n              <span>Loading...</span>\n            </div>\n          </div>\n\n          {/* Loading Text */}\n          <motion.p\n            key={loadingText}\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -10 }}\n            className=\"text-lg text-cyan-300 font-medium\"\n          >\n            {loadingText}\n          </motion.p>\n\n          {/* Animated Dots */}\n          <div className=\"flex justify-center space-x-2\">\n            {[0, 1, 2].map((i) => (\n              <motion.div\n                key={i}\n                className=\"w-3 h-3 bg-cyan-400 rounded-full\"\n                animate={{\n                  scale: [1, 1.2, 1],\n                  opacity: [0.5, 1, 0.5],\n                }}\n                transition={{\n                  duration: 1.5,\n                  repeat: Infinity,\n                  delay: i * 0.2,\n                }}\n              />\n            ))}\n          </div>\n        </div>\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AASe,SAAS,cAAc,EAAE,UAAU,EAAsB;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,YAAY,CAAA;gBACV,MAAM,cAAc,OAAO;gBAE3B,wCAAwC;gBACxC,MAAM,YAAY,KAAK,KAAK,CAAC,AAAC,cAAc,MAAO,aAAa,MAAM;gBACtE,IAAI,YAAY,aAAa,MAAM,EAAE;oBACnC,eAAe,YAAY,CAAC,UAAU;gBACxC;gBAEA,IAAI,eAAe,KAAK;oBACtB,cAAc;oBACd,WAAW,IAAM,cAAc;oBAC/B,OAAO;gBACT;gBACA,OAAO;YACT;QACF,GAAG,KAAK,yBAAyB;QAEjC,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAW;IAEf,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;sBAEV,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BAAE,OAAO;4BAAK,MAAM;4BAAU,WAAW;wBAAI;wBACzD,WAAU;kCAEV,cAAA,8OAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;kCAIlD,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCACX;;;;;;kCAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCACX;;;;;;kCAKD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAO;gCACzB,YAAY;oCAAE,OAAO;gCAAI;gCACzB,WAAU;0CAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,SAAS,CAAC,CAAC;oCAAC;oCAC/B,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;0CAIhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAM;4CAAS;;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBAEP,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,WAAU;kCAET;uBANI;;;;;kCAUP,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,SAAS;oCACP,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;oCAClB,SAAS;wCAAC;wCAAK;wCAAG;qCAAI;gCACxB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,OAAO,IAAI;gCACb;+BAVK;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBrB", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/ComputerGUI.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface ComputerGUIProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\ntype AppType = 'terminal' | 'resume' | 'projects' | 'games' | 'vscode' | 'browser' | 'settings' | 'info';\n\nexport default function ComputerGUI({ isVisible, onClose }: ComputerGUIProps) {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [activeApp, setActiveApp] = useState<AppType | null>(null);\n  const [isBooting, setIsBooting] = useState(true);\n\n  // Update time every second\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Boot sequence\n  useEffect(() => {\n    if (isVisible) {\n      setIsBooting(true);\n      const bootTimer = setTimeout(() => {\n        setIsBooting(false);\n      }, 2000);\n      return () => clearTimeout(bootTimer);\n    }\n  }, [isVisible]);\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString('id-ID', { \n      hour: '2-digit', \n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n\n  const formatDate = (date: Date) => {\n    return date.toLocaleDateString('id-ID', { \n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const desktopApps = [\n    { id: 'terminal', name: 'Terminal AI', icon: '🧠', color: 'bg-green-600' },\n    { id: 'resume', name: 'Resume', icon: '📄', color: 'bg-blue-600' },\n    { id: 'projects', name: 'My Projects', icon: '💻', color: 'bg-purple-600' },\n    { id: 'games', name: 'Mini Games', icon: '🎮', color: 'bg-red-600' },\n  ];\n\n  const taskbarApps = [\n    { id: 'terminal', name: 'Terminal AI', icon: '🧠' },\n    { id: 'vscode', name: 'VSCode', icon: '💻' },\n    { id: 'browser', name: 'Browser', icon: '🌐' },\n    { id: 'settings', name: 'Settings', icon: '⚙️' },\n    { id: 'info', name: 'System Info', icon: '🧮' },\n    { id: 'games', name: 'Games', icon: '🎮' },\n  ];\n\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50 overflow-hidden\">\n      <AnimatePresence>\n        {isBooting ? (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"flex items-center justify-center h-full bg-black text-white\"\n          >\n            <div className=\"text-center space-y-4\">\n              <div className=\"text-6xl mb-4\">🐧</div>\n              <h2 className=\"text-2xl font-bold\">KangPCode ArchLinux</h2>\n              <p className=\"text-lg\">Booting KDE Plasma...</p>\n              <div className=\"flex justify-center space-x-1 mt-4\">\n                {[0, 1, 2].map((i) => (\n                  <motion.div\n                    key={i}\n                    className=\"w-3 h-3 bg-blue-500 rounded-full\"\n                    animate={{\n                      scale: [1, 1.2, 1],\n                      opacity: [0.5, 1, 0.5],\n                    }}\n                    transition={{\n                      duration: 1,\n                      repeat: Infinity,\n                      delay: i * 0.2,\n                    }}\n                  />\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        ) : (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"h-full bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 relative\"\n          >\n            {/* Desktop Wallpaper */}\n            <div className=\"absolute inset-0 bg-gradient-to-br from-blue-800/50 to-purple-800/50\" />\n            \n            {/* Top Panel */}\n            <div className=\"absolute top-0 left-0 right-0 h-8 bg-gray-900/90 backdrop-blur-sm border-b border-gray-700 flex items-center justify-between px-4 text-white text-sm z-10\">\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"font-bold\">kangpcode@archlinux</span>\n                <span>KDE Plasma 5.27</span>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <span>{formatDate(currentTime)}</span>\n                <span className=\"font-mono\">{formatTime(currentTime)}</span>\n                <button\n                  onClick={onClose}\n                  className=\"hover:bg-red-600 px-2 py-1 rounded transition-colors\"\n                >\n                  ✕\n                </button>\n              </div>\n            </div>\n\n            {/* Desktop Icons */}\n            <div className=\"absolute top-12 left-4 grid grid-cols-1 gap-4 z-10\">\n              {desktopApps.map((app) => (\n                <motion.button\n                  key={app.id}\n                  onClick={() => setActiveApp(app.id as AppType)}\n                  className=\"flex flex-col items-center space-y-1 p-2 rounded hover:bg-white/10 transition-colors group\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <div className={`w-12 h-12 ${app.color} rounded-lg flex items-center justify-center text-2xl shadow-lg group-hover:shadow-xl transition-shadow`}>\n                    {app.icon}\n                  </div>\n                  <span className=\"text-white text-xs font-medium\">{app.name}</span>\n                </motion.button>\n              ))}\n            </div>\n\n            {/* Bottom Taskbar */}\n            <div className=\"absolute bottom-0 left-0 right-0 h-12 bg-gray-900/90 backdrop-blur-sm border-t border-gray-700 flex items-center justify-between px-4 z-10\">\n              {/* Start Menu */}\n              <button className=\"flex items-center space-x-2 px-3 py-2 hover:bg-gray-700/50 rounded transition-colors text-white\">\n                <span className=\"text-xl\">🐧</span>\n                <span className=\"font-medium\">Menu</span>\n              </button>\n\n              {/* App Icons */}\n              <div className=\"flex items-center space-x-2\">\n                {taskbarApps.map((app) => (\n                  <motion.button\n                    key={app.id}\n                    onClick={() => setActiveApp(app.id as AppType)}\n                    className={`w-10 h-10 rounded-lg flex items-center justify-center text-xl transition-colors ${\n                      activeApp === app.id \n                        ? 'bg-blue-600 text-white' \n                        : 'hover:bg-gray-700/50 text-gray-300'\n                    }`}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    title={app.name}\n                  >\n                    {app.icon}\n                  </motion.button>\n                ))}\n              </div>\n\n              {/* System Tray */}\n              <div className=\"flex items-center space-x-2 text-white\">\n                <span className=\"text-sm\">🔊 🔋 📶</span>\n                <span className=\"text-sm font-mono\">{formatTime(currentTime)}</span>\n              </div>\n            </div>\n\n            {/* Active Application Window */}\n            <AnimatePresence>\n              {activeApp && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  exit={{ opacity: 0, scale: 0.9 }}\n                  className=\"absolute inset-8 bg-gray-800 rounded-lg shadow-2xl border border-gray-600 overflow-hidden z-20\"\n                >\n                  {/* Window Title Bar */}\n                  <div className=\"h-8 bg-gray-700 flex items-center justify-between px-4 border-b border-gray-600\">\n                    <span className=\"text-white font-medium\">\n                      {taskbarApps.find(app => app.id === activeApp)?.name}\n                    </span>\n                    <div className=\"flex space-x-2\">\n                      <button className=\"w-4 h-4 bg-yellow-500 rounded-full hover:bg-yellow-400\"></button>\n                      <button className=\"w-4 h-4 bg-green-500 rounded-full hover:bg-green-400\"></button>\n                      <button \n                        onClick={() => setActiveApp(null)}\n                        className=\"w-4 h-4 bg-red-500 rounded-full hover:bg-red-400\"\n                      ></button>\n                    </div>\n                  </div>\n\n                  {/* Window Content */}\n                  <div className=\"flex-1 p-4 text-white overflow-auto\">\n                    {activeApp === 'terminal' && (\n                      <div className=\"font-mono text-green-400 space-y-2\">\n                        <div>KangPCode Terminal AI v1.0</div>\n                        <div>Type 'help' for available commands</div>\n                        <div className=\"text-white\">$ _</div>\n                      </div>\n                    )}\n                    \n                    {activeApp === 'resume' && (\n                      <div className=\"text-center space-y-4\">\n                        <h3 className=\"text-xl font-bold\">Resume Generator</h3>\n                        <p>Generate PDF resume dari data KangPCode</p>\n                        <button className=\"bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors\">\n                          Generate PDF\n                        </button>\n                      </div>\n                    )}\n                    \n                    {activeApp === 'projects' && (\n                      <div className=\"space-y-4\">\n                        <h3 className=\"text-xl font-bold\">My Projects</h3>\n                        <div className=\"grid grid-cols-2 gap-4\">\n                          <div className=\"bg-gray-700 p-4 rounded\">\n                            <h4 className=\"font-bold\">Portfolio 3D</h4>\n                            <p className=\"text-sm text-gray-300\">Interactive 3D portfolio website</p>\n                          </div>\n                          <div className=\"bg-gray-700 p-4 rounded\">\n                            <h4 className=\"font-bold\">Langkah Kode</h4>\n                            <p className=\"text-sm text-gray-300\">Educational programming book</p>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                    \n                    {activeApp === 'games' && (\n                      <div className=\"text-center space-y-4\">\n                        <h3 className=\"text-xl font-bold\">Mini Games</h3>\n                        <div className=\"grid grid-cols-3 gap-4\">\n                          <button className=\"bg-green-600 hover:bg-green-700 p-4 rounded transition-colors\">\n                            🐍 Snake\n                          </button>\n                          <button className=\"bg-blue-600 hover:bg-blue-700 p-4 rounded transition-colors\">\n                            ⭕ TicTacToe\n                          </button>\n                          <button className=\"bg-purple-600 hover:bg-purple-700 p-4 rounded transition-colors\">\n                            🧠 Trivia\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                    \n                    {(activeApp === 'vscode' || activeApp === 'browser' || activeApp === 'settings' || activeApp === 'info') && (\n                      <div className=\"text-center space-y-4\">\n                        <h3 className=\"text-xl font-bold\">Coming Soon</h3>\n                        <p>Aplikasi ini sedang dalam pengembangan</p>\n                      </div>\n                    )}\n                  </div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAYe,SAAS,YAAY,EAAE,SAAS,EAAE,OAAO,EAAoB;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,YAAY;YACxB,eAAe,IAAI;QACrB,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,aAAa;YACb,MAAM,YAAY,WAAW;gBAC3B,aAAa;YACf,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,cAAc;QAClB;YAAE,IAAI;YAAY,MAAM;YAAe,MAAM;YAAM,OAAO;QAAe;QACzE;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM;YAAM,OAAO;QAAc;QACjE;YAAE,IAAI;YAAY,MAAM;YAAe,MAAM;YAAM,OAAO;QAAgB;QAC1E;YAAE,IAAI;YAAS,MAAM;YAAc,MAAM;YAAM,OAAO;QAAa;KACpE;IAED,MAAM,cAAc;QAClB;YAAE,IAAI;YAAY,MAAM;YAAe,MAAM;QAAK;QAClD;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM;QAAK;QAC3C;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAK;QAC7C;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;QAC/C;YAAE,IAAI;YAAQ,MAAM;YAAe,MAAM;QAAK;QAC9C;YAAE,IAAI;YAAS,MAAM;YAAS,MAAM;QAAK;KAC1C;IAED,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;sBACb,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAE,WAAU;sCAAU;;;;;;sCACvB,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;wCAClB,SAAS;4CAAC;4CAAK;4CAAG;yCAAI;oCACxB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO,IAAI;oCACb;mCAVK;;;;;;;;;;;;;;;;;;;;qCAiBf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAY;;;;;;kDAC5B,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAM,WAAW;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAa,WAAW;;;;;;kDACxC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,oBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,8OAAC;wCAAI,WAAW,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,uGAAuG,CAAC;kDAC5I,IAAI,IAAI;;;;;;kDAEX,8OAAC;wCAAK,WAAU;kDAAkC,IAAI,IAAI;;;;;;;+BATrD,IAAI,EAAE;;;;;;;;;;kCAejB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAIhC,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,oBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS,IAAM,aAAa,IAAI,EAAE;wCAClC,WAAW,CAAC,gFAAgF,EAC1F,cAAc,IAAI,EAAE,GAChB,2BACA,sCACJ;wCACF,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;wCACvB,OAAO,IAAI,IAAI;kDAEd,IAAI,IAAI;uCAXJ,IAAI,EAAE;;;;;;;;;;0CAiBjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCAAK,WAAU;kDAAqB,WAAW;;;;;;;;;;;;;;;;;;kCAKpD,8OAAC,yLAAA,CAAA,kBAAe;kCACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAC/B,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDACb,YAAY,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY;;;;;;sDAElD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;;;;;;8DAClB,8OAAC;oDAAO,WAAU;;;;;;8DAClB,8OAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAU;;;;;;;;;;;;;;;;;;8CAMhB,8OAAC;oCAAI,WAAU;;wCACZ,cAAc,4BACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;8DACL,8OAAC;oDAAI,WAAU;8DAAa;;;;;;;;;;;;wCAI/B,cAAc,0BACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;8DAAE;;;;;;8DACH,8OAAC;oDAAO,WAAU;8DAAoE;;;;;;;;;;;;wCAMzF,cAAc,4BACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAY;;;;;;8EAC1B,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAY;;;;;;8EAC1B,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;wCAM5C,cAAc,yBACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAO,WAAU;sEAAgE;;;;;;sEAGlF,8OAAC;4DAAO,WAAU;sEAA8D;;;;;;sEAGhF,8OAAC;4DAAO,WAAU;sEAAkE;;;;;;;;;;;;;;;;;;wCAOzF,CAAC,cAAc,YAAY,cAAc,aAAa,cAAc,cAAc,cAAc,MAAM,mBACrG,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3B", "debugId": null}}, {"offset": {"line": 1004, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/TerminalAI.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface TerminalAIProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\ninterface TerminalLine {\n  type: 'input' | 'output' | 'error';\n  content: string;\n  timestamp: Date;\n}\n\nexport default function TerminalAI({ isVisible, onClose }: TerminalAIProps) {\n  const [input, setInput] = useState('');\n  const [history, setHistory] = useState<TerminalLine[]>([]);\n  const [isTyping, setIsTyping] = useState(false);\n  const terminalRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // AI Knowledge Base\n  const knowledgeBase = {\n    'siapa kangpcode': 'KangPCode ad<PERSON><PERSON>, developer dan kreator teknologi Nusantara yang berfokus pada pengembangan aplikasi web modern dan edukasi teknologi.',\n    'pendidikan': 'Informatika – Universitas XYZ, dengan fokus pada pengembangan web dan teknologi modern.',\n    'skill': 'Next.js, TailwindCSS, SQLite, Laravel, Bun, React, PWA, Three.js, TypeScript, Python, Docker, dan teknologi web modern lainnya.',\n    'pengalaman': 'Magang di CV Bintang Gumilang, freelance proyek TI lokal, dan berbagai proyek pengembangan web untuk klien Indonesia.',\n    'hobi': 'Ngoding, mempelajari sejarah tokoh teknologi, menonton anime, menulis, dan berbagi pengetahuan teknologi.',\n    'buku': 'Langkah Kode Nusantara - Perjalanan belajar dan berkarya dalam dunia teknologi Indonesia (GitHub: kangpcode/langkah-kode-nusantara)',\n    'proyek': 'Portfolio 3D Interaktif, Langkah Kode Nusantara, berbagai aplikasi web dengan Next.js dan React, serta proyek edukasi teknologi.',\n    'kontak': 'GitHub: github.com/kangpcode | Email: <EMAIL> | Status: Open for Collaboration',\n    'teknologi': 'Spesialisasi dalam JavaScript/TypeScript ecosystem, React/Next.js, modern CSS frameworks, database design, dan 3D web development.',\n    'visi': 'Membangun ekosistem teknologi Indonesia yang kuat melalui edukasi, open source, dan kolaborasi komunitas developer lokal.',\n  };\n\n  const commands = {\n    'help': 'Perintah yang tersedia:\\n- siapa kangpcode\\n- pendidikan\\n- skill\\n- pengalaman\\n- hobi\\n- buku\\n- proyek\\n- kontak\\n- teknologi\\n- visi\\n- clear\\n- help',\n    'clear': 'CLEAR_TERMINAL',\n    'ls': 'projects/\\nbooks/\\nskills/\\ncontacts/\\nexperience/',\n    'pwd': '/home/<USER>/portfolio',\n    'whoami': 'kangpcode (Dhafa Nazula Permadi)',\n    'date': new Date().toLocaleString('id-ID'),\n    'uname': 'KangPCode Terminal AI v1.0 - Interactive Portfolio Assistant',\n  };\n\n  useEffect(() => {\n    if (isVisible) {\n      setHistory([\n        {\n          type: 'output',\n          content: '🧠 KangPCode Terminal AI v1.0\\nSelamat datang! Ketik \"help\" untuk melihat perintah yang tersedia.\\nAtau tanyakan tentang KangPCode dengan bahasa natural.',\n          timestamp: new Date()\n        }\n      ]);\n      setTimeout(() => {\n        inputRef.current?.focus();\n      }, 100);\n    }\n  }, [isVisible]);\n\n  useEffect(() => {\n    if (terminalRef.current) {\n      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;\n    }\n  }, [history]);\n\n  const processCommand = async (cmd: string) => {\n    const command = cmd.toLowerCase().trim();\n    \n    // Add user input to history\n    setHistory(prev => [...prev, {\n      type: 'input',\n      content: `$ ${cmd}`,\n      timestamp: new Date()\n    }]);\n\n    setIsTyping(true);\n\n    // Simulate AI thinking delay\n    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));\n\n    let response = '';\n\n    // Check exact commands first\n    if (commands[command as keyof typeof commands]) {\n      const result = commands[command as keyof typeof commands];\n      if (result === 'CLEAR_TERMINAL') {\n        setHistory([]);\n        setIsTyping(false);\n        return;\n      }\n      response = result;\n    }\n    // Check knowledge base\n    else if (knowledgeBase[command as keyof typeof knowledgeBase]) {\n      response = `🧠 ${knowledgeBase[command as keyof typeof knowledgeBase]}`;\n    }\n    // Natural language processing (simple keyword matching)\n    else {\n      const keywords = Object.keys(knowledgeBase);\n      const matchedKeyword = keywords.find(keyword => \n        command.includes(keyword) || keyword.includes(command.split(' ')[0])\n      );\n      \n      if (matchedKeyword) {\n        response = `🧠 ${knowledgeBase[matchedKeyword as keyof typeof knowledgeBase]}`;\n      } else if (command.includes('halo') || command.includes('hai') || command.includes('hello')) {\n        response = '👋 Halo! Saya AI Assistant KangPCode. Ada yang bisa saya bantu? Ketik \"help\" untuk melihat perintah yang tersedia.';\n      } else if (command.includes('terima kasih') || command.includes('thanks')) {\n        response = '🙏 Sama-sama! Senang bisa membantu. Ada pertanyaan lain tentang KangPCode?';\n      } else if (command.includes('bye') || command.includes('exit') || command.includes('quit')) {\n        response = '👋 Sampai jumpa! Terima kasih telah menggunakan KangPCode Terminal AI.';\n      } else {\n        response = `❓ Maaf, saya tidak mengerti perintah \"${cmd}\". Ketik \"help\" untuk melihat perintah yang tersedia atau tanyakan tentang KangPCode.`;\n      }\n    }\n\n    // Add AI response to history\n    setHistory(prev => [...prev, {\n      type: 'output',\n      content: response,\n      timestamp: new Date()\n    }]);\n\n    setIsTyping(false);\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (input.trim()) {\n      processCommand(input);\n      setInput('');\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className=\"fixed inset-4 bg-black rounded-lg shadow-2xl border border-green-500/30 overflow-hidden z-50\"\n    >\n      {/* Terminal Header */}\n      <div className=\"h-8 bg-gray-900 flex items-center justify-between px-4 border-b border-green-500/30\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n          <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n          <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n          <span className=\"text-green-400 text-sm font-mono ml-4\">🧠 KangPCode Terminal AI</span>\n        </div>\n        <button\n          onClick={onClose}\n          className=\"text-gray-400 hover:text-white transition-colors\"\n        >\n          ✕\n        </button>\n      </div>\n\n      {/* Terminal Content */}\n      <div \n        ref={terminalRef}\n        className=\"flex-1 p-4 font-mono text-sm text-green-400 bg-black overflow-y-auto h-[calc(100%-8rem)]\"\n      >\n        {history.map((line, index) => (\n          <motion.div\n            key={index}\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            className={`mb-2 ${\n              line.type === 'input' \n                ? 'text-cyan-400' \n                : line.type === 'error' \n                ? 'text-red-400' \n                : 'text-green-400'\n            }`}\n          >\n            <pre className=\"whitespace-pre-wrap font-mono\">{line.content}</pre>\n          </motion.div>\n        ))}\n        \n        {isTyping && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"text-yellow-400 flex items-center space-x-2\"\n          >\n            <span>🧠 AI sedang berpikir</span>\n            <div className=\"flex space-x-1\">\n              {[0, 1, 2].map((i) => (\n                <motion.div\n                  key={i}\n                  className=\"w-1 h-1 bg-yellow-400 rounded-full\"\n                  animate={{\n                    scale: [1, 1.5, 1],\n                    opacity: [0.5, 1, 0.5],\n                  }}\n                  transition={{\n                    duration: 1,\n                    repeat: Infinity,\n                    delay: i * 0.2,\n                  }}\n                />\n              ))}\n            </div>\n          </motion.div>\n        )}\n      </div>\n\n      {/* Terminal Input */}\n      <div className=\"h-12 bg-gray-900 border-t border-green-500/30 flex items-center px-4\">\n        <form onSubmit={handleSubmit} className=\"flex-1 flex items-center space-x-2\">\n          <span className=\"text-cyan-400 font-mono\">$</span>\n          <input\n            ref={inputRef}\n            type=\"text\"\n            value={input}\n            onChange={(e) => setInput(e.target.value)}\n            className=\"flex-1 bg-transparent text-green-400 font-mono outline-none placeholder-gray-500\"\n            placeholder=\"Ketik perintah atau tanyakan tentang KangPCode...\"\n            disabled={isTyping}\n          />\n        </form>\n      </div>\n\n      {/* Terminal Footer */}\n      <div className=\"h-6 bg-gray-800 border-t border-green-500/30 flex items-center justify-between px-4 text-xs text-gray-400\">\n        <span>KangPCode Terminal AI - Interactive Portfolio Assistant</span>\n        <span>Press Ctrl+C to exit</span>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAgBe,SAAS,WAAW,EAAE,SAAS,EAAE,OAAO,EAAmB;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,oBAAoB;IACpB,MAAM,gBAAgB;QACpB,mBAAmB;QACnB,cAAc;QACd,SAAS;QACT,cAAc;QACd,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,UAAU;QACV,aAAa;QACb,QAAQ;IACV;IAEA,MAAM,WAAW;QACf,QAAQ;QACR,SAAS;QACT,MAAM;QACN,OAAO;QACP,UAAU;QACV,QAAQ,IAAI,OAAO,cAAc,CAAC;QAClC,SAAS;IACX;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,WAAW;gBACT;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW,IAAI;gBACjB;aACD;YACD,WAAW;gBACT,SAAS,OAAO,EAAE;YACpB,GAAG;QACL;IACF,GAAG;QAAC;KAAU;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,SAAS,GAAG,YAAY,OAAO,CAAC,YAAY;QAClE;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,iBAAiB,OAAO;QAC5B,MAAM,UAAU,IAAI,WAAW,GAAG,IAAI;QAEtC,4BAA4B;QAC5B,WAAW,CAAA,OAAQ;mBAAI;gBAAM;oBAC3B,MAAM;oBACN,SAAS,CAAC,EAAE,EAAE,KAAK;oBACnB,WAAW,IAAI;gBACjB;aAAE;QAEF,YAAY;QAEZ,6BAA6B;QAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,MAAM,KAAK,MAAM,KAAK;QAEvE,IAAI,WAAW;QAEf,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,QAAiC,EAAE;YAC9C,MAAM,SAAS,QAAQ,CAAC,QAAiC;YACzD,IAAI,WAAW,kBAAkB;gBAC/B,WAAW,EAAE;gBACb,YAAY;gBACZ;YACF;YACA,WAAW;QACb,OAEK,IAAI,aAAa,CAAC,QAAsC,EAAE;YAC7D,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,QAAsC,EAAE;QACzE,OAEK;YACH,MAAM,WAAW,OAAO,IAAI,CAAC;YAC7B,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAA,UACnC,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ,KAAK,CAAC,IAAI,CAAC,EAAE;YAGrE,IAAI,gBAAgB;gBAClB,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,eAA6C,EAAE;YAChF,OAAO,IAAI,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,UAAU;gBAC3F,WAAW;YACb,OAAO,IAAI,QAAQ,QAAQ,CAAC,mBAAmB,QAAQ,QAAQ,CAAC,WAAW;gBACzE,WAAW;YACb,OAAO,IAAI,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,SAAS;gBAC1F,WAAW;YACb,OAAO;gBACL,WAAW,CAAC,sCAAsC,EAAE,IAAI,qFAAqF,CAAC;YAChJ;QACF;QAEA,6BAA6B;QAC7B,WAAW,CAAA,OAAQ;mBAAI;gBAAM;oBAC3B,MAAM;oBACN,SAAS;oBACT,WAAW,IAAI;gBACjB;aAAE;QAEF,YAAY;IACd;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,MAAM,IAAI,IAAI;YAChB,eAAe;YACf,SAAS;QACX;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAC/B,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAwC;;;;;;;;;;;;kCAE1D,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAMH,8OAAC;gBACC,KAAK;gBACL,WAAU;;oBAET,QAAQ,GAAG,CAAC,CAAC,MAAM,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAW,CAAC,KAAK,EACf,KAAK,IAAI,KAAK,UACV,kBACA,KAAK,IAAI,KAAK,UACd,iBACA,kBACJ;sCAEF,cAAA,8OAAC;gCAAI,WAAU;0CAAiC,KAAK,OAAO;;;;;;2BAXvD;;;;;oBAeR,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,WAAU;;0CAEV,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,SAAS;4CACP,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;4CAClB,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;wCACxB;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,OAAO,IAAI;wCACb;uCAVK;;;;;;;;;;;;;;;;;;;;;;0BAmBjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BAAK,WAAU;sCAA0B;;;;;;sCAC1C,8OAAC;4BACC,KAAK;4BACL,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,WAAU;4BACV,aAAY;4BACZ,UAAU;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAK;;;;;;kCACN,8OAAC;kCAAK;;;;;;;;;;;;;;;;;;AAId", "debugId": null}}, {"offset": {"line": 1360, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/LaptopWindows.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface LaptopWindowsProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\ninterface GitHubRepo {\n  id: number;\n  name: string;\n  description: string;\n  language: string;\n  stargazers_count: number;\n  forks_count: number;\n  html_url: string;\n  updated_at: string;\n}\n\ninterface GitHubUser {\n  login: string;\n  name: string;\n  bio: string;\n  public_repos: number;\n  followers: number;\n  following: number;\n  avatar_url: string;\n  html_url: string;\n}\n\nexport default function LaptopWindows({ isVisible, onClose }: LaptopWindowsProps) {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [isBooting, setIsBooting] = useState(true);\n  const [githubUser, setGithubUser] = useState<GitHubUser | null>(null);\n  const [githubRepos, setGithubRepos] = useState<GitHubRepo[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState<'profile' | 'repos' | 'stats'>('profile');\n\n  // Update time every second\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Boot sequence\n  useEffect(() => {\n    if (isVisible) {\n      setIsBooting(true);\n      const bootTimer = setTimeout(() => {\n        setIsBooting(false);\n        fetchGitHubData();\n      }, 2000);\n      return () => clearTimeout(bootTimer);\n    }\n  }, [isVisible]);\n\n  const fetchGitHubData = async () => {\n    setIsLoading(true);\n    try {\n      // Fetch user data\n      const userResponse = await fetch('https://api.github.com/users/kangpcode');\n      if (userResponse.ok) {\n        const userData = await userResponse.json();\n        setGithubUser(userData);\n      }\n\n      // Fetch repositories\n      const reposResponse = await fetch('https://api.github.com/users/kangpcode/repos?sort=updated&per_page=10');\n      if (reposResponse.ok) {\n        const reposData = await reposResponse.json();\n        setGithubRepos(reposData);\n      }\n    } catch (error) {\n      console.error('Error fetching GitHub data:', error);\n      // Set mock data if API fails\n      setGithubUser({\n        login: 'kangpcode',\n        name: 'Dhafa Nazula Permadi',\n        bio: 'Fullstack Developer & Content Creator | Building the future of Indonesian tech',\n        public_repos: 25,\n        followers: 150,\n        following: 80,\n        avatar_url: '/assets/images/kangpcode-avatar.jpg',\n        html_url: 'https://github.com/kangpcode'\n      });\n      \n      setGithubRepos([\n        {\n          id: 1,\n          name: 'portfolio-3d',\n          description: 'Interactive 3D Portfolio Website with Three.js',\n          language: 'TypeScript',\n          stargazers_count: 45,\n          forks_count: 12,\n          html_url: 'https://github.com/kangpcode/portfolio-3d',\n          updated_at: '2024-01-15T10:30:00Z'\n        },\n        {\n          id: 2,\n          name: 'langkah-kode-nusantara',\n          description: 'Educational programming book for Indonesian developers',\n          language: 'Markdown',\n          stargazers_count: 89,\n          forks_count: 23,\n          html_url: 'https://github.com/kangpcode/langkah-kode-nusantara',\n          updated_at: '2024-01-10T14:20:00Z'\n        }\n      ]);\n    }\n    setIsLoading(false);\n  };\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString('en-US', { \n      hour: '2-digit', \n      minute: '2-digit',\n      hour12: true\n    });\n  };\n\n  const formatDate = (date: Date) => {\n    return date.toLocaleDateString('en-US', { \n      weekday: 'short',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  const getLanguageColor = (language: string) => {\n    const colors: { [key: string]: string } = {\n      'TypeScript': '#3178c6',\n      'JavaScript': '#f1e05a',\n      'Python': '#3572A5',\n      'Java': '#b07219',\n      'C++': '#f34b7d',\n      'HTML': '#e34c26',\n      'CSS': '#1572B6',\n      'Markdown': '#083fa1',\n      'PHP': '#4F5D95'\n    };\n    return colors[language] || '#6b7280';\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50 overflow-hidden\">\n      <AnimatePresence>\n        {isBooting ? (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"flex items-center justify-center h-full bg-blue-600 text-white\"\n          >\n            <div className=\"text-center space-y-4\">\n              <div className=\"text-6xl mb-4\">🪟</div>\n              <h2 className=\"text-2xl font-bold\">Windows 11</h2>\n              <p className=\"text-lg\">Starting GitHub Viewer...</p>\n              <div className=\"flex justify-center space-x-1 mt-4\">\n                {[0, 1, 2, 3, 4].map((i) => (\n                  <motion.div\n                    key={i}\n                    className=\"w-2 h-2 bg-white rounded-full\"\n                    animate={{\n                      scale: [1, 1.2, 1],\n                      opacity: [0.5, 1, 0.5],\n                    }}\n                    transition={{\n                      duration: 1,\n                      repeat: Infinity,\n                      delay: i * 0.1,\n                    }}\n                  />\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        ) : (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"h-full bg-gradient-to-br from-blue-100 to-blue-200 relative\"\n          >\n            {/* Windows Taskbar */}\n            <div className=\"absolute bottom-0 left-0 right-0 h-12 bg-gray-900/95 backdrop-blur-sm flex items-center justify-between px-4 z-10\">\n              {/* Start Button */}\n              <button className=\"flex items-center space-x-2 px-3 py-2 hover:bg-gray-700/50 rounded transition-colors text-white\">\n                <span className=\"text-xl\">🪟</span>\n                <span className=\"font-medium\">Start</span>\n              </button>\n\n              {/* App Icons */}\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-xl text-white\">\n                  🌐\n                </div>\n                <div className=\"w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center text-xl text-white\">\n                  📁\n                </div>\n              </div>\n\n              {/* System Tray */}\n              <div className=\"flex items-center space-x-4 text-white text-sm\">\n                <span>🔊 🔋 📶</span>\n                <div className=\"text-right\">\n                  <div className=\"font-mono\">{formatTime(currentTime)}</div>\n                  <div className=\"text-xs\">{formatDate(currentTime)}</div>\n                </div>\n                <button\n                  onClick={onClose}\n                  className=\"hover:bg-red-600 px-2 py-1 rounded transition-colors\"\n                >\n                  ✕\n                </button>\n              </div>\n            </div>\n\n            {/* Main Content - GitHub Viewer */}\n            <div className=\"h-[calc(100%-3rem)] p-4\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"h-full bg-white rounded-lg shadow-2xl overflow-hidden\"\n              >\n                {/* Browser Header */}\n                <div className=\"h-12 bg-gray-100 border-b flex items-center justify-between px-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n                      <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n                      <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                    </div>\n                    <div className=\"ml-4 bg-gray-200 rounded px-3 py-1 text-sm text-gray-600\">\n                      🔒 github.com/kangpcode\n                    </div>\n                  </div>\n                  <div className=\"text-lg font-bold text-gray-700\">GitHub Profile</div>\n                </div>\n\n                {/* Tab Navigation */}\n                <div className=\"h-10 bg-gray-50 border-b flex items-center px-4\">\n                  {[\n                    { id: 'profile', name: 'Profile', icon: '👤' },\n                    { id: 'repos', name: 'Repositories', icon: '📁' },\n                    { id: 'stats', name: 'Statistics', icon: '📊' }\n                  ].map((tab) => (\n                    <button\n                      key={tab.id}\n                      onClick={() => setActiveTab(tab.id as any)}\n                      className={`flex items-center space-x-2 px-4 py-2 rounded-t transition-colors ${\n                        activeTab === tab.id \n                          ? 'bg-white border-t-2 border-blue-500 text-blue-600' \n                          : 'hover:bg-gray-100 text-gray-600'\n                      }`}\n                    >\n                      <span>{tab.icon}</span>\n                      <span className=\"font-medium\">{tab.name}</span>\n                    </button>\n                  ))}\n                </div>\n\n                {/* Content Area */}\n                <div className=\"flex-1 p-6 overflow-y-auto\">\n                  {isLoading ? (\n                    <div className=\"flex items-center justify-center h-64\">\n                      <div className=\"text-center space-y-4\">\n                        <div className=\"text-4xl\">⏳</div>\n                        <p className=\"text-gray-600\">Loading GitHub data...</p>\n                      </div>\n                    </div>\n                  ) : (\n                    <AnimatePresence mode=\"wait\">\n                      {activeTab === 'profile' && githubUser && (\n                        <motion.div\n                          key=\"profile\"\n                          initial={{ opacity: 0, x: 20 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          exit={{ opacity: 0, x: -20 }}\n                          className=\"space-y-6\"\n                        >\n                          <div className=\"flex items-start space-x-6\">\n                            <div className=\"w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center text-4xl\">\n                              👨‍💻\n                            </div>\n                            <div className=\"flex-1\">\n                              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">{githubUser.name}</h1>\n                              <p className=\"text-xl text-gray-600 mb-4\">@{githubUser.login}</p>\n                              <p className=\"text-gray-700 mb-4\">{githubUser.bio}</p>\n                              <div className=\"flex space-x-6 text-sm text-gray-600\">\n                                <span>👥 {githubUser.followers} followers</span>\n                                <span>👤 {githubUser.following} following</span>\n                                <span>📁 {githubUser.public_repos} repositories</span>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      )}\n\n                      {activeTab === 'repos' && (\n                        <motion.div\n                          key=\"repos\"\n                          initial={{ opacity: 0, x: 20 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          exit={{ opacity: 0, x: -20 }}\n                          className=\"space-y-4\"\n                        >\n                          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Recent Repositories</h2>\n                          {githubRepos.map((repo) => (\n                            <div key={repo.id} className=\"border rounded-lg p-4 hover:shadow-md transition-shadow\">\n                              <div className=\"flex items-start justify-between\">\n                                <div className=\"flex-1\">\n                                  <h3 className=\"text-lg font-semibold text-blue-600 mb-2\">{repo.name}</h3>\n                                  <p className=\"text-gray-700 mb-3\">{repo.description}</p>\n                                  <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                                    <span className=\"flex items-center space-x-1\">\n                                      <div \n                                        className=\"w-3 h-3 rounded-full\" \n                                        style={{ backgroundColor: getLanguageColor(repo.language) }}\n                                      ></div>\n                                      <span>{repo.language}</span>\n                                    </span>\n                                    <span>⭐ {repo.stargazers_count}</span>\n                                    <span>🍴 {repo.forks_count}</span>\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </motion.div>\n                      )}\n\n                      {activeTab === 'stats' && githubUser && (\n                        <motion.div\n                          key=\"stats\"\n                          initial={{ opacity: 0, x: 20 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          exit={{ opacity: 0, x: -20 }}\n                          className=\"space-y-6\"\n                        >\n                          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">GitHub Statistics</h2>\n                          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                            <div className=\"bg-blue-50 p-4 rounded-lg text-center\">\n                              <div className=\"text-2xl font-bold text-blue-600\">{githubUser.public_repos}</div>\n                              <div className=\"text-sm text-gray-600\">Repositories</div>\n                            </div>\n                            <div className=\"bg-green-50 p-4 rounded-lg text-center\">\n                              <div className=\"text-2xl font-bold text-green-600\">{githubUser.followers}</div>\n                              <div className=\"text-sm text-gray-600\">Followers</div>\n                            </div>\n                            <div className=\"bg-purple-50 p-4 rounded-lg text-center\">\n                              <div className=\"text-2xl font-bold text-purple-600\">{githubUser.following}</div>\n                              <div className=\"text-sm text-gray-600\">Following</div>\n                            </div>\n                            <div className=\"bg-orange-50 p-4 rounded-lg text-center\">\n                              <div className=\"text-2xl font-bold text-orange-600\">\n                                {githubRepos.reduce((sum, repo) => sum + repo.stargazers_count, 0)}\n                              </div>\n                              <div className=\"text-sm text-gray-600\">Total Stars</div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      )}\n                    </AnimatePresence>\n                  )}\n                </div>\n              </motion.div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAgCe,SAAS,cAAc,EAAE,SAAS,EAAE,OAAO,EAAsB;IAC9E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiC;IAE1E,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,YAAY;YACxB,eAAe,IAAI;QACrB,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,aAAa;YACb,MAAM,YAAY,WAAW;gBAC3B,aAAa;gBACb;YACF,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,kBAAkB;YAClB,MAAM,eAAe,MAAM,MAAM;YACjC,IAAI,aAAa,EAAE,EAAE;gBACnB,MAAM,WAAW,MAAM,aAAa,IAAI;gBACxC,cAAc;YAChB;YAEA,qBAAqB;YACrB,MAAM,gBAAgB,MAAM,MAAM;YAClC,IAAI,cAAc,EAAE,EAAE;gBACpB,MAAM,YAAY,MAAM,cAAc,IAAI;gBAC1C,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,6BAA6B;YAC7B,cAAc;gBACZ,OAAO;gBACP,MAAM;gBACN,KAAK;gBACL,cAAc;gBACd,WAAW;gBACX,WAAW;gBACX,YAAY;gBACZ,UAAU;YACZ;YAEA,eAAe;gBACb;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,kBAAkB;oBAClB,aAAa;oBACb,UAAU;oBACV,YAAY;gBACd;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,kBAAkB;oBAClB,aAAa;oBACb,UAAU;oBACV,YAAY;gBACd;aACD;QACH;QACA,aAAa;IACf;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,SAAS;YACT,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAoC;YACxC,cAAc;YACd,cAAc;YACd,UAAU;YACV,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,OAAO;YACP,YAAY;YACZ,OAAO;QACT;QACA,OAAO,MAAM,CAAC,SAAS,IAAI;IAC7B;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;sBACb,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAE,WAAU;sCAAU;;;;;;sCACvB,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;wCAClB,SAAS;4CAAC;4CAAK;4CAAG;yCAAI;oCACxB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO,IAAI;oCACb;mCAVK;;;;;;;;;;;;;;;;;;;;qCAiBf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAIhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAuF;;;;;;kDAGtG,8OAAC;wCAAI,WAAU;kDAAuF;;;;;;;;;;;;0CAMxG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAa,WAAW;;;;;;0DACvC,8OAAC;gDAAI,WAAU;0DAAW,WAAW;;;;;;;;;;;;kDAEvC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;8DAA2D;;;;;;;;;;;;sDAI5E,8OAAC;4CAAI,WAAU;sDAAkC;;;;;;;;;;;;8CAInD,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,IAAI;4CAAW,MAAM;4CAAW,MAAM;wCAAK;wCAC7C;4CAAE,IAAI;4CAAS,MAAM;4CAAgB,MAAM;wCAAK;wCAChD;4CAAE,IAAI;4CAAS,MAAM;4CAAc,MAAM;wCAAK;qCAC/C,CAAC,GAAG,CAAC,CAAC,oBACL,8OAAC;4CAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4CAClC,WAAW,CAAC,kEAAkE,EAC5E,cAAc,IAAI,EAAE,GAChB,sDACA,mCACJ;;8DAEF,8OAAC;8DAAM,IAAI,IAAI;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAe,IAAI,IAAI;;;;;;;2CATlC,IAAI,EAAE;;;;;;;;;;8CAejB,8OAAC;oCAAI,WAAU;8CACZ,0BACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DAAW;;;;;;8DAC1B,8OAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;6DAIjC,8OAAC,yLAAA,CAAA,kBAAe;wCAAC,MAAK;;4CACnB,cAAc,aAAa,4BAC1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,WAAU;0DAEV,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;sEAA+E;;;;;;sEAG9F,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAyC,WAAW,IAAI;;;;;;8EACtE,8OAAC;oEAAE,WAAU;;wEAA6B;wEAAE,WAAW,KAAK;;;;;;;8EAC5D,8OAAC;oEAAE,WAAU;8EAAsB,WAAW,GAAG;;;;;;8EACjD,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;;gFAAK;gFAAI,WAAW,SAAS;gFAAC;;;;;;;sFAC/B,8OAAC;;gFAAK;gFAAI,WAAW,SAAS;gFAAC;;;;;;;sFAC/B,8OAAC;;gFAAK;gFAAI,WAAW,YAAY;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;;+CAjBpC;;;;;4CAwBP,cAAc,yBACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,WAAU;;kEAEV,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;oDACrD,YAAY,GAAG,CAAC,CAAC,qBAChB,8OAAC;4DAAkB,WAAU;sEAC3B,cAAA,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAG,WAAU;sFAA4C,KAAK,IAAI;;;;;;sFACnE,8OAAC;4EAAE,WAAU;sFAAsB,KAAK,WAAW;;;;;;sFACnD,8OAAC;4EAAI,WAAU;;8FACb,8OAAC;oFAAK,WAAU;;sGACd,8OAAC;4FACC,WAAU;4FACV,OAAO;gGAAE,iBAAiB,iBAAiB,KAAK,QAAQ;4FAAE;;;;;;sGAE5D,8OAAC;sGAAM,KAAK,QAAQ;;;;;;;;;;;;8FAEtB,8OAAC;;wFAAK;wFAAG,KAAK,gBAAgB;;;;;;;8FAC9B,8OAAC;;wFAAK;wFAAI,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;2DAdxB,KAAK,EAAE;;;;;;+CARf;;;;;4CA+BP,cAAc,WAAW,4BACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,WAAU;;kEAEV,8OAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAoC,WAAW,YAAY;;;;;;kFAC1E,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAqC,WAAW,SAAS;;;;;;kFACxE,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFAAsC,WAAW,SAAS;;;;;;kFACzE,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACZ,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,gBAAgB,EAAE;;;;;;kFAElE,8OAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;+CAxBvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuC9B", "debugId": null}}, {"offset": {"line": 2267, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/BookKangPCode.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface BookKangPCodeProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\nexport default function BookKangPCode({ isVisible, onClose }: BookKangPCodeProps) {\n  const [currentPage, setCurrentPage] = useState(0);\n\n  const bookData = {\n    title: \"Langkah Kode Nusantara\",\n    subtitle: \"Perjalanan Belajar & Berkarya dalam Dunia Teknologi Indonesia\",\n    author: \"<PERSON><PERSON><PERSON> (KangPCode)\",\n    description: \"Sebuah panduan komprehensif untuk developer Indonesia yang ingin membangun karir di dunia teknologi dengan tetap mengangkat nilai-nilai lokal dan berkontribusi untuk kemajuan bangsa.\",\n    githubUrl: \"https://github.com/kangpcode/langkah-kode-nusantara\",\n    pdfUrl: \"/assets/book/langkah-kode-nusantara-sample.pdf\",\n    coverImage: \"/assets/images/book/cover.jpg\",\n    chapters: [\n      {\n        number: 1,\n        title: \"Pengenalan Dunia Teknologi Indonesia\",\n        description: \"Sejarah perkembangan teknologi di Indonesia dan peluang masa depan\"\n      },\n      {\n        number: 2,\n        title: \"Dasar-Dasar Programming\",\n        description: \"Fundamental programming dengan pendekatan yang mudah dipahami\"\n      },\n      {\n        number: 3,\n        title: \"Web Development Modern\",\n        description: \"Membangun aplikasi web dengan teknologi terkini\"\n      },\n      {\n        number: 4,\n        title: \"Mobile Development\",\n        description: \"Pengembangan aplikasi mobile untuk platform Android dan iOS\"\n      },\n      {\n        number: 5,\n        title: \"DevOps & Deployment\",\n        description: \"Praktik terbaik dalam deployment dan maintenance aplikasi\"\n      },\n      {\n        number: 6,\n        title: \"Membangun Karir Tech di Indonesia\",\n        description: \"Strategi membangun karir teknologi yang berkelanjutan\"\n      }\n    ],\n    skills: [\n      \"JavaScript & TypeScript\",\n      \"React & Next.js\",\n      \"Node.js & Express\",\n      \"Python & Django\",\n      \"Database Design\",\n      \"Docker & Kubernetes\",\n      \"AWS & Cloud Computing\",\n      \"Git & Version Control\",\n      \"Testing & Quality Assurance\",\n      \"UI/UX Design Principles\"\n    ],\n    stats: {\n      pages: 350,\n      chapters: 6,\n      codeExamples: 150,\n      projects: 12\n    }\n  };\n\n  const pages = [\n    // Cover Page\n    {\n      type: 'cover',\n      content: (\n        <div className=\"h-full bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white flex flex-col justify-center items-center p-8 text-center\">\n          <div className=\"text-6xl mb-6\">📘</div>\n          <h1 className=\"text-4xl font-bold mb-4\">{bookData.title}</h1>\n          <h2 className=\"text-xl mb-6 text-blue-200\">{bookData.subtitle}</h2>\n          <p className=\"text-lg mb-8\">oleh {bookData.author}</p>\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\n            <div className=\"bg-white/10 p-3 rounded\">\n              <div className=\"text-2xl font-bold\">{bookData.stats.pages}</div>\n              <div>Halaman</div>\n            </div>\n            <div className=\"bg-white/10 p-3 rounded\">\n              <div className=\"text-2xl font-bold\">{bookData.stats.chapters}</div>\n              <div>Bab</div>\n            </div>\n            <div className=\"bg-white/10 p-3 rounded\">\n              <div className=\"text-2xl font-bold\">{bookData.stats.codeExamples}</div>\n              <div>Contoh Kode</div>\n            </div>\n            <div className=\"bg-white/10 p-3 rounded\">\n              <div className=\"text-2xl font-bold\">{bookData.stats.projects}</div>\n              <div>Proyek</div>\n            </div>\n          </div>\n        </div>\n      )\n    },\n    // About Page\n    {\n      type: 'about',\n      content: (\n        <div className=\"h-full bg-white p-8 overflow-y-auto\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">Tentang Buku</h2>\n          <p className=\"text-lg text-gray-700 mb-6 leading-relaxed\">\n            {bookData.description}\n          </p>\n          \n          <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">Mengapa Buku Ini?</h3>\n          <ul className=\"list-disc list-inside text-gray-700 space-y-2 mb-6\">\n            <li>Pendekatan pembelajaran yang disesuaikan dengan konteks Indonesia</li>\n            <li>Studi kasus dari startup dan perusahaan teknologi lokal</li>\n            <li>Panduan praktis membangun portfolio yang menarik</li>\n            <li>Tips networking dan membangun personal branding</li>\n            <li>Strategi menghadapi tantangan industri teknologi Indonesia</li>\n          </ul>\n\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">Target Pembaca</h3>\n          <ul className=\"list-disc list-inside text-gray-700 space-y-2\">\n            <li>Mahasiswa informatika dan teknik komputer</li>\n            <li>Fresh graduate yang ingin berkarir di bidang teknologi</li>\n            <li>Developer yang ingin meningkatkan skill dan karir</li>\n            <li>Entrepreneur yang ingin membangun startup teknologi</li>\n            <li>Siapa saja yang tertarik dengan dunia teknologi Indonesia</li>\n          </ul>\n        </div>\n      )\n    },\n    // Chapters Page\n    {\n      type: 'chapters',\n      content: (\n        <div className=\"h-full bg-white p-8 overflow-y-auto\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">Daftar Isi</h2>\n          <div className=\"space-y-4\">\n            {bookData.chapters.map((chapter) => (\n              <div key={chapter.number} className=\"border-l-4 border-blue-500 pl-4 py-2\">\n                <h3 className=\"text-xl font-semibold text-gray-900\">\n                  Bab {chapter.number}: {chapter.title}\n                </h3>\n                <p className=\"text-gray-600 mt-1\">{chapter.description}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      )\n    },\n    // Skills Page\n    {\n      type: 'skills',\n      content: (\n        <div className=\"h-full bg-white p-8 overflow-y-auto\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">Teknologi yang Dibahas</h2>\n          <div className=\"grid grid-cols-2 gap-4\">\n            {bookData.skills.map((skill, index) => (\n              <motion.div\n                key={skill}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1 }}\n                className=\"bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200\"\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\n                  <span className=\"font-medium text-gray-900\">{skill}</span>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n          \n          <div className=\"mt-8 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200\">\n            <h3 className=\"text-xl font-bold text-gray-900 mb-3\">💡 Bonus Content</h3>\n            <ul className=\"list-disc list-inside text-gray-700 space-y-1\">\n              <li>Template CV dan Portfolio untuk Developer</li>\n              <li>Cheat Sheet untuk Interview Technical</li>\n              <li>Resource Learning Path yang Terstruktur</li>\n              <li>Komunitas Developer Indonesia</li>\n            </ul>\n          </div>\n        </div>\n      )\n    }\n  ];\n\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className=\"fixed inset-4 bg-white rounded-lg shadow-2xl border overflow-hidden z-50\"\n    >\n      {/* Book Header */}\n      <div className=\"h-16 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between px-6 text-white\">\n        <div className=\"flex items-center space-x-3\">\n          <span className=\"text-2xl\">📘</span>\n          <div>\n            <h2 className=\"font-bold\">{bookData.title}</h2>\n            <p className=\"text-sm text-blue-100\">by {bookData.author}</p>\n          </div>\n        </div>\n        <button\n          onClick={onClose}\n          className=\"hover:bg-white/20 p-2 rounded transition-colors\"\n        >\n          ✕\n        </button>\n      </div>\n\n      {/* Book Content */}\n      <div className=\"flex h-[calc(100%-4rem)]\">\n        {/* Page Content */}\n        <div className=\"flex-1\">\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={currentPage}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -20 }}\n              className=\"h-full\"\n            >\n              {pages[currentPage]?.content}\n            </motion.div>\n          </AnimatePresence>\n        </div>\n      </div>\n\n      {/* Book Navigation */}\n      <div className=\"h-16 bg-gray-100 border-t flex items-center justify-between px-6\">\n        <div className=\"flex space-x-2\">\n          {pages.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => setCurrentPage(index)}\n              className={`w-3 h-3 rounded-full transition-colors ${\n                currentPage === index ? 'bg-blue-600' : 'bg-gray-300 hover:bg-gray-400'\n              }`}\n            />\n          ))}\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}\n            disabled={currentPage === 0}\n            className=\"px-4 py-2 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed rounded transition-colors\"\n          >\n            ← Prev\n          </button>\n          \n          <span className=\"text-sm text-gray-600\">\n            {currentPage + 1} / {pages.length}\n          </span>\n          \n          <button\n            onClick={() => setCurrentPage(Math.min(pages.length - 1, currentPage + 1))}\n            disabled={currentPage === pages.length - 1}\n            className=\"px-4 py-2 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed rounded transition-colors\"\n          >\n            Next →\n          </button>\n        </div>\n\n        <div className=\"flex space-x-2\">\n          <a\n            href={bookData.githubUrl}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"px-4 py-2 bg-gray-900 hover:bg-gray-800 text-white rounded transition-colors\"\n          >\n            📁 GitHub\n          </a>\n          <a\n            href={bookData.pdfUrl}\n            download\n            className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors\"\n          >\n            📄 Download PDF\n          </a>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAUe,SAAS,cAAc,EAAE,SAAS,EAAE,OAAO,EAAsB;IAC9E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,WAAW;QACf,OAAO;QACP,UAAU;QACV,QAAQ;QACR,aAAa;QACb,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,UAAU;YACR;gBACE,QAAQ;gBACR,OAAO;gBACP,aAAa;YACf;YACA;gBACE,QAAQ;gBACR,OAAO;gBACP,aAAa;YACf;YACA;gBACE,QAAQ;gBACR,OAAO;gBACP,aAAa;YACf;YACA;gBACE,QAAQ;gBACR,OAAO;gBACP,aAAa;YACf;YACA;gBACE,QAAQ;gBACR,OAAO;gBACP,aAAa;YACf;YACA;gBACE,QAAQ;gBACR,OAAO;gBACP,aAAa;YACf;SACD;QACD,QAAQ;YACN;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;YACL,OAAO;YACP,UAAU;YACV,cAAc;YACd,UAAU;QACZ;IACF;IAEA,MAAM,QAAQ;QACZ,aAAa;QACb;YACE,MAAM;YACN,uBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,8OAAC;wBAAG,WAAU;kCAA2B,SAAS,KAAK;;;;;;kCACvD,8OAAC;wBAAG,WAAU;kCAA8B,SAAS,QAAQ;;;;;;kCAC7D,8OAAC;wBAAE,WAAU;;4BAAe;4BAAM,SAAS,MAAM;;;;;;;kCACjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAsB,SAAS,KAAK,CAAC,KAAK;;;;;;kDACzD,8OAAC;kDAAI;;;;;;;;;;;;0CAEP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAsB,SAAS,KAAK,CAAC,QAAQ;;;;;;kDAC5D,8OAAC;kDAAI;;;;;;;;;;;;0CAEP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAsB,SAAS,KAAK,CAAC,YAAY;;;;;;kDAChE,8OAAC;kDAAI;;;;;;;;;;;;0CAEP,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAsB,SAAS,KAAK,CAAC,QAAQ;;;;;;kDAC5D,8OAAC;kDAAI;;;;;;;;;;;;;;;;;;;;;;;;QAKf;QACA,aAAa;QACb;YACE,MAAM;YACN,uBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCACV,SAAS,WAAW;;;;;;kCAGvB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;kCAGN,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;0CACJ,8OAAC;0CAAG;;;;;;;;;;;;;;;;;;QAIZ;QACA,gBAAgB;QAChB;YACE,MAAM;YACN,uBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAI,WAAU;kCACZ,SAAS,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACtB,8OAAC;gCAAyB,WAAU;;kDAClC,8OAAC;wCAAG,WAAU;;4CAAsC;4CAC7C,QAAQ,MAAM;4CAAC;4CAAG,QAAQ,KAAK;;;;;;;kDAEtC,8OAAC;wCAAE,WAAU;kDAAsB,QAAQ,WAAW;;;;;;;+BAJ9C,QAAQ,MAAM;;;;;;;;;;;;;;;;QAUlC;QACA,cAAc;QACd;YACE,MAAM;YACN,uBACE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAI,WAAU;kCACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAI;gCACjC,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;+BAR1C;;;;;;;;;;kCAcX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;kDACJ,8OAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;QAKd;KACD;IAED,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAC/B,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAW;;;;;;0CAC3B,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAa,SAAS,KAAK;;;;;;kDACzC,8OAAC;wCAAE,WAAU;;4CAAwB;4CAAI,SAAS,MAAM;;;;;;;;;;;;;;;;;;;kCAG5D,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAMH,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;wBAAC,MAAK;kCACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAET,KAAK,CAAC,YAAY,EAAE;2BANhB;;;;;;;;;;;;;;;;;;;;0BAab,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,GAAG,sBACb,8OAAC;gCAEC,SAAS,IAAM,eAAe;gCAC9B,WAAW,CAAC,uCAAuC,EACjD,gBAAgB,QAAQ,gBAAgB,iCACxC;+BAJG;;;;;;;;;;kCASX,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;gCACxD,UAAU,gBAAgB;gCAC1B,WAAU;0CACX;;;;;;0CAID,8OAAC;gCAAK,WAAU;;oCACb,cAAc;oCAAE;oCAAI,MAAM,MAAM;;;;;;;0CAGnC,8OAAC;gCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,MAAM,MAAM,GAAG,GAAG,cAAc;gCACvE,UAAU,gBAAgB,MAAM,MAAM,GAAG;gCACzC,WAAU;0CACX;;;;;;;;;;;;kCAKH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAM,SAAS,SAAS;gCACxB,QAAO;gCACP,KAAI;gCACJ,WAAU;0CACX;;;;;;0CAGD,8OAAC;gCACC,MAAM,SAAS,MAAM;gCACrB,QAAQ;gCACR,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 3036, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/IDCardLanyard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface IDCardLanyardProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\nexport default function IDCardLanyard({ isVisible, onClose }: IDCardLanyardProps) {\n  const [isFlipped, setIsFlipped] = useState(false);\n\n  const cardData = {\n    name: \"<PERSON><PERSON><PERSON>\",\n    nickname: \"KangPC<PERSON>\",\n    role: \"Fullstack Developer\",\n    specialization: \"Web & Mobile Development\",\n    status: \"Open for Collaboration\",\n    email: \"<EMAIL>\",\n    github: \"github.com/kangpcode\",\n    linkedin: \"linkedin.com/in/kangpcode\",\n    website: \"kangpcode.dev\",\n    photo: \"/assets/images/idcard/photo.jpg\",\n    qrCode: \"/assets/images/idcard/qr-github.png\",\n    skills: [\n      \"JavaScript/TypeScript\",\n      \"React/Next.js\",\n      \"Node.js\",\n      \"Python\",\n      \"Docker\",\n      \"AWS\"\n    ],\n    achievements: [\n      \"🏆 Best Portfolio 2024\",\n      \"📚 Author of 'Langkah <PERSON>'\",\n      \"🎯 100+ Projects Completed\",\n      \"👥 Active in Tech Community\"\n    ],\n    experience: \"3+ Years\",\n    location: \"Indonesia\",\n    languages: [\"Indonesian\", \"English\"],\n    motto: \"Building the future of Indonesian tech, one line of code at a time.\"\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4\"\n    >\n      <div className=\"relative\">\n        {/* Lanyard */}\n        <div className=\"absolute -top-20 left-1/2 transform -translate-x-1/2\">\n          <div className=\"w-6 h-20 bg-gradient-to-b from-blue-600 to-blue-800 rounded-t-full\"></div>\n          <div className=\"w-8 h-4 bg-gray-300 rounded-full -mt-2 -ml-1 border-2 border-gray-400\"></div>\n        </div>\n\n        {/* ID Card Container */}\n        <motion.div\n          className=\"relative w-96 h-64 cursor-pointer\"\n          style={{ perspective: \"1000px\" }}\n          onClick={() => setIsFlipped(!isFlipped)}\n          whileHover={{ y: -5 }}\n        >\n          <motion.div\n            className=\"relative w-full h-full\"\n            style={{ transformStyle: \"preserve-3d\" }}\n            animate={{ rotateY: isFlipped ? 180 : 0 }}\n            transition={{ duration: 0.6 }}\n          >\n            {/* Front Side */}\n            <div\n              className=\"absolute inset-0 w-full h-full bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl shadow-2xl border-2 border-white/20\"\n              style={{ backfaceVisibility: \"hidden\" }}\n            >\n              {/* Card Header */}\n              <div className=\"bg-white/10 backdrop-blur-sm p-3 rounded-t-xl border-b border-white/20\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-white\">\n                    <div className=\"text-xs font-medium opacity-80\">DEVELOPER ID</div>\n                    <div className=\"text-sm font-bold\">KangPCode Portfolio</div>\n                  </div>\n                  <div className=\"text-2xl\">💻</div>\n                </div>\n              </div>\n\n              {/* Card Content */}\n              <div className=\"p-4 text-white\">\n                <div className=\"flex items-start space-x-4\">\n                  {/* Photo */}\n                  <div className=\"w-20 h-20 bg-gray-300 rounded-lg flex items-center justify-center text-3xl border-2 border-white/30\">\n                    👨‍💻\n                  </div>\n\n                  {/* Info */}\n                  <div className=\"flex-1\">\n                    <h2 className=\"text-xl font-bold mb-1\">{cardData.name}</h2>\n                    <p className=\"text-blue-200 text-sm mb-1\">\"{cardData.nickname}\"</p>\n                    <p className=\"text-white/90 text-sm mb-2\">{cardData.role}</p>\n                    <p className=\"text-blue-200 text-xs\">{cardData.specialization}</p>\n                  </div>\n                </div>\n\n                {/* Status */}\n                <div className=\"mt-4 p-2 bg-green-500/20 rounded-lg border border-green-400/30\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n                    <span className=\"text-green-200 text-sm font-medium\">{cardData.status}</span>\n                  </div>\n                </div>\n\n                {/* Quick Stats */}\n                <div className=\"mt-3 grid grid-cols-3 gap-2 text-center\">\n                  <div className=\"bg-white/10 rounded p-1\">\n                    <div className=\"text-xs font-bold\">{cardData.experience}</div>\n                    <div className=\"text-xs opacity-80\">Experience</div>\n                  </div>\n                  <div className=\"bg-white/10 rounded p-1\">\n                    <div className=\"text-xs font-bold\">100+</div>\n                    <div className=\"text-xs opacity-80\">Projects</div>\n                  </div>\n                  <div className=\"bg-white/10 rounded p-1\">\n                    <div className=\"text-xs font-bold\">{cardData.skills.length}</div>\n                    <div className=\"text-xs opacity-80\">Skills</div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Card Footer */}\n              <div className=\"absolute bottom-0 left-0 right-0 bg-white/10 backdrop-blur-sm p-2 rounded-b-xl border-t border-white/20\">\n                <div className=\"text-center text-white text-xs\">\n                  Click to flip • {cardData.location}\n                </div>\n              </div>\n            </div>\n\n            {/* Back Side */}\n            <div\n              className=\"absolute inset-0 w-full h-full bg-gradient-to-br from-gray-800 via-gray-900 to-black rounded-xl shadow-2xl border-2 border-gray-600\"\n              style={{ \n                backfaceVisibility: \"hidden\",\n                transform: \"rotateY(180deg)\"\n              }}\n            >\n              {/* Back Header */}\n              <div className=\"bg-gray-700/50 backdrop-blur-sm p-3 rounded-t-xl border-b border-gray-600\">\n                <div className=\"flex items-center justify-between text-white\">\n                  <div>\n                    <div className=\"text-xs font-medium opacity-80\">CONTACT & SKILLS</div>\n                    <div className=\"text-sm font-bold\">Professional Details</div>\n                  </div>\n                  <div className=\"text-2xl\">🔗</div>\n                </div>\n              </div>\n\n              {/* Back Content */}\n              <div className=\"p-4 text-white space-y-3\">\n                {/* Contact Info */}\n                <div>\n                  <h3 className=\"text-sm font-bold mb-2 text-blue-300\">Contact</h3>\n                  <div className=\"space-y-1 text-xs\">\n                    <div>📧 {cardData.email}</div>\n                    <div>🐙 {cardData.github}</div>\n                    <div>🌐 {cardData.website}</div>\n                  </div>\n                </div>\n\n                {/* Skills */}\n                <div>\n                  <h3 className=\"text-sm font-bold mb-2 text-green-300\">Core Skills</h3>\n                  <div className=\"grid grid-cols-2 gap-1\">\n                    {cardData.skills.map((skill, index) => (\n                      <div key={index} className=\"text-xs bg-gray-700/50 px-2 py-1 rounded\">\n                        {skill}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* QR Code */}\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-sm font-bold mb-1 text-purple-300\">Quick Access</h3>\n                    <div className=\"text-xs opacity-80\">Scan for GitHub</div>\n                  </div>\n                  <div className=\"w-12 h-12 bg-white rounded flex items-center justify-center\">\n                    <div className=\"text-black text-xs font-bold\">QR</div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Back Footer */}\n              <div className=\"absolute bottom-0 left-0 right-0 bg-gray-700/50 backdrop-blur-sm p-2 rounded-b-xl border-t border-gray-600\">\n                <div className=\"text-center text-white text-xs\">\n                  \"{cardData.motto}\"\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n\n        {/* Close Button */}\n        <button\n          onClick={onClose}\n          className=\"absolute -top-8 -right-8 w-8 h-8 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors\"\n        >\n          ✕\n        </button>\n\n        {/* Flip Instruction */}\n        <div className=\"absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-white text-sm text-center\">\n          <div className=\"bg-black/50 px-3 py-1 rounded-full\">\n            🖱️ Click to flip card\n          </div>\n        </div>\n      </div>\n\n      {/* Background Actions */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-4\">\n        <motion.a\n          href={`https://${cardData.github}`}\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          className=\"bg-gray-900 hover:bg-gray-800 text-white px-4 py-2 rounded-lg transition-colors\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          🐙 GitHub\n        </motion.a>\n        <motion.a\n          href={`mailto:${cardData.email}`}\n          className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          📧 Contact\n        </motion.a>\n        <motion.button\n          onClick={() => {\n            navigator.clipboard.writeText(`${cardData.name} - ${cardData.role}\\n${cardData.email}\\n${cardData.github}`);\n            alert('Contact info copied to clipboard!');\n          }}\n          className=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          📋 Copy Info\n        </motion.button>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUe,SAAS,cAAc,EAAE,SAAS,EAAE,OAAO,EAAsB;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,WAAW;QACf,MAAM;QACN,UAAU;QACV,MAAM;QACN,gBAAgB;QAChB,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,QAAQ;YACN;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,UAAU;QACV,WAAW;YAAC;YAAc;SAAU;QACpC,OAAO;IACT;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAC/B,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BAAE,aAAa;wBAAS;wBAC/B,SAAS,IAAM,aAAa,CAAC;wBAC7B,YAAY;4BAAE,GAAG,CAAC;wBAAE;kCAEpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,OAAO;gCAAE,gBAAgB;4BAAc;4BACvC,SAAS;gCAAE,SAAS,YAAY,MAAM;4BAAE;4BACxC,YAAY;gCAAE,UAAU;4BAAI;;8CAG5B,8OAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,oBAAoB;oCAAS;;sDAGtC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EAAiC;;;;;;0EAChD,8OAAC;gEAAI,WAAU;0EAAoB;;;;;;;;;;;;kEAErC,8OAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;;;;;;;sDAK9B,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEAEb,8OAAC;4DAAI,WAAU;sEAAsG;;;;;;sEAKrH,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA0B,SAAS,IAAI;;;;;;8EACrD,8OAAC;oEAAE,WAAU;;wEAA6B;wEAAE,SAAS,QAAQ;wEAAC;;;;;;;8EAC9D,8OAAC;oEAAE,WAAU;8EAA8B,SAAS,IAAI;;;;;;8EACxD,8OAAC;oEAAE,WAAU;8EAAyB,SAAS,cAAc;;;;;;;;;;;;;;;;;;8DAKjE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAsC,SAAS,MAAM;;;;;;;;;;;;;;;;;8DAKzE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAqB,SAAS,UAAU;;;;;;8EACvD,8OAAC;oEAAI,WAAU;8EAAqB;;;;;;;;;;;;sEAEtC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAoB;;;;;;8EACnC,8OAAC;oEAAI,WAAU;8EAAqB;;;;;;;;;;;;sEAEtC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAAqB,SAAS,MAAM,CAAC,MAAM;;;;;;8EAC1D,8OAAC;oEAAI,WAAU;8EAAqB;;;;;;;;;;;;;;;;;;;;;;;;sDAM1C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;oDAAiC;oDAC7B,SAAS,QAAQ;;;;;;;;;;;;;;;;;;8CAMxC,8OAAC;oCACC,WAAU;oCACV,OAAO;wCACL,oBAAoB;wCACpB,WAAW;oCACb;;sDAGA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAI,WAAU;0EAAiC;;;;;;0EAChD,8OAAC;gEAAI,WAAU;0EAAoB;;;;;;;;;;;;kEAErC,8OAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;;;;;;;sDAK9B,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAuC;;;;;;sEACrD,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;;wEAAI;wEAAI,SAAS,KAAK;;;;;;;8EACvB,8OAAC;;wEAAI;wEAAI,SAAS,MAAM;;;;;;;8EACxB,8OAAC;;wEAAI;wEAAI,SAAS,OAAO;;;;;;;;;;;;;;;;;;;8DAK7B,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,8OAAC;4DAAI,WAAU;sEACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC;oEAAgB,WAAU;8EACxB;mEADO;;;;;;;;;;;;;;;;8DAQhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAAyC;;;;;;8EACvD,8OAAC;oEAAI,WAAU;8EAAqB;;;;;;;;;;;;sEAEtC,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;0EAA+B;;;;;;;;;;;;;;;;;;;;;;;sDAMpD,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;oDAAiC;oDAC5C,SAAS,KAAK;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3B,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAKD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAqC;;;;;;;;;;;;;;;;;0BAOxD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,MAAM,CAAC,QAAQ,EAAE,SAAS,MAAM,EAAE;wBAClC,QAAO;wBACP,KAAI;wBACJ,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;kCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,MAAM,CAAC,OAAO,EAAE,SAAS,KAAK,EAAE;wBAChC,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;kCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;4BACP,UAAU,SAAS,CAAC,SAAS,CAAC,GAAG,SAAS,IAAI,CAAC,GAAG,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE,SAAS,KAAK,CAAC,EAAE,EAAE,SAAS,MAAM,EAAE;4BAC1G,MAAM;wBACR;wBACA,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;;;;;;;;;;;;;AAMT", "debugId": null}}, {"offset": {"line": 3746, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/RoomScene3D.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense, useRef, useState } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport {\n  OrbitControls,\n  Environment,\n  Text,\n  Box,\n  Plane,\n  Html,\n  useTexture,\n  PerspectiveCamera\n} from '@react-three/drei';\nimport { motion } from 'framer-motion';\nimport * as THREE from 'three';\nimport ComputerGUI from './ComputerGUI';\nimport TerminalA<PERSON> from './TerminalAI';\nimport LaptopWindows from './LaptopWindows';\nimport BookKangPCode from './BookKangPCode';\nimport IDCardLanyard from './IDCardLanyard';\nimport PosterIlmuwan from './PosterIlmuwan';\nimport ActionFigure from './ActionFigure';\nimport WhiteboardProject from './WhiteboardProject';\n\n// Room Environment Component\nfunction Room() {\n  return (\n    <group>\n      {/* Floor */}\n      <Plane \n        args={[20, 20]} \n        rotation={[-Math.PI / 2, 0, 0]} \n        position={[0, -2, 0]}\n      >\n        <meshStandardMaterial color=\"#8B4513\" />\n      </Plane>\n\n      {/* Walls */}\n      <Plane args={[20, 10]} position={[0, 3, -10]}>\n        <meshStandardMaterial color=\"#F5F5DC\" />\n      </Plane>\n      \n      <Plane args={[20, 10]} position={[-10, 3, 0]} rotation={[0, Math.PI / 2, 0]}>\n        <meshStandardMaterial color=\"#F5F5DC\" />\n      </Plane>\n      \n      <Plane args={[20, 10]} position={[10, 3, 0]} rotation={[0, -Math.PI / 2, 0]}>\n        <meshStandardMaterial color=\"#F5F5DC\" />\n      </Plane>\n\n      {/* Ceiling */}\n      <Plane \n        args={[20, 20]} \n        rotation={[Math.PI / 2, 0, 0]} \n        position={[0, 8, 0]}\n      >\n        <meshStandardMaterial color=\"#FFFFFF\" />\n      </Plane>\n    </group>\n  );\n}\n\n// Computer 3D Component\nfunction Computer3D({ onComputerClick }: { onComputerClick: () => void }) {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const [isHovered, setIsHovered] = useState(false);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;\n    }\n  });\n\n  return (\n    <group position={[-4, -1, -2]}>\n      {/* Monitor */}\n      <Box ref={meshRef} args={[3, 2, 0.2]} position={[0, 1, 0]}>\n        <meshStandardMaterial color=\"#1a1a1a\" />\n      </Box>\n\n      {/* Screen */}\n      <Plane args={[2.8, 1.8]} position={[0, 1, 0.11]}>\n        <meshStandardMaterial\n          color={isHovered ? \"#001080\" : \"#000080\"}\n          emissive={isHovered ? \"#000060\" : \"#000040\"}\n        />\n      </Plane>\n\n      {/* KDE Plasma Logo on Screen */}\n      <Html position={[0, 1, 0.12]} center>\n        <div className=\"text-white text-center pointer-events-none\">\n          <div className=\"text-4xl mb-2\">🐧</div>\n          <div className=\"text-sm font-bold\">KDE Plasma</div>\n        </div>\n      </Html>\n\n      {/* Base */}\n      <Box args={[0.5, 0.8, 0.5]} position={[0, 0, 0]}>\n        <meshStandardMaterial color=\"#333333\" />\n      </Box>\n\n      {/* Keyboard */}\n      <Box args={[2, 0.1, 1]} position={[0, -1.5, 1]}>\n        <meshStandardMaterial color=\"#2a2a2a\" />\n      </Box>\n\n      {/* Click Area */}\n      <Html position={[0, 1, 0.2]} center>\n        <div\n          className=\"w-32 h-20 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onComputerClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Klik untuk membuka KDE Plasma\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              🖱️ Klik untuk membuka\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Laptop 3D Component\nfunction Laptop3D({ onLaptopClick }: { onLaptopClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[4, -1, -1]}>\n      {/* Laptop Base */}\n      <Box args={[2.5, 0.1, 1.8]} position={[0, -1.5, 0]}>\n        <meshStandardMaterial color=\"#C0C0C0\" />\n      </Box>\n\n      {/* Laptop Screen */}\n      <Box args={[2.5, 1.6, 0.1]} position={[0, 0, -0.8]} rotation={[-0.2, 0, 0]}>\n        <meshStandardMaterial color=\"#1a1a1a\" />\n      </Box>\n\n      {/* Screen Display */}\n      <Plane args={[2.3, 1.4]} position={[0, 0, -0.75]} rotation={[-0.2, 0, 0]}>\n        <meshStandardMaterial\n          color={isHovered ? \"#0088D4\" : \"#0078D4\"}\n          emissive={isHovered ? \"#004d8b\" : \"#003d6b\"}\n        />\n      </Plane>\n\n      {/* Windows Logo on Screen */}\n      <Html position={[0, 0, -0.74]} center>\n        <div className=\"text-white text-center pointer-events-none\" style={{ transform: 'rotateX(-11.5deg)' }}>\n          <div className=\"text-3xl mb-1\">🪟</div>\n          <div className=\"text-xs font-bold\">Windows 11</div>\n        </div>\n      </Html>\n\n      {/* Click Area */}\n      <Html position={[0, 0, -0.7]} center>\n        <div\n          className=\"w-24 h-16 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onLaptopClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Klik untuk membuka GitHub Viewer\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              🖱️ GitHub Viewer\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Book 3D Component\nfunction Book3D({ onBookClick }: { onBookClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[0, -1.3, 0]}>\n      <Box args={[1.5, 0.2, 1]} position={[0, 0, 0]}>\n        <meshStandardMaterial color={isHovered ? \"#A0522D\" : \"#8B4513\"} />\n      </Box>\n\n      {/* Book Cover Text */}\n      <Html position={[0, 0.15, 0.51]} center>\n        <div className=\"text-white text-center pointer-events-none text-xs\">\n          <div className=\"font-bold\">📘</div>\n          <div className=\"font-bold\">Langkah</div>\n          <div className=\"font-bold\">Kode</div>\n          <div className=\"font-bold\">Nusantara</div>\n        </div>\n      </Html>\n\n      <Html position={[0, 0.2, 0]} center>\n        <div\n          className=\"w-16 h-12 bg-transparent cursor-pointer hover:bg-yellow-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onBookClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Langkah Kode Nusantara\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              📖 Baca Buku\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// ID Card Component\nfunction IDCard3D({ onIDCardClick }: { onIDCardClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[0, -0.9, 0.3]}>\n      <Box args={[0.8, 0.05, 0.5]} position={[0, 0, 0]}>\n        <meshStandardMaterial color={isHovered ? \"#F0F0F0\" : \"#FFFFFF\"} />\n      </Box>\n\n      {/* ID Card Content */}\n      <Html position={[0, 0.03, 0.26]} center>\n        <div className=\"text-black text-center pointer-events-none text-xs\">\n          <div className=\"font-bold\">🪪 KangPCode</div>\n          <div className=\"text-xs\">Developer ID</div>\n        </div>\n      </Html>\n\n      <Html position={[0, 0.1, 0]} center>\n        <div\n          className=\"w-8 h-6 bg-transparent cursor-pointer hover:bg-green-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onIDCardClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"ID Card KangPCode\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              🪪 Lihat ID\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Poster Ilmuwan 3D Component\nfunction PosterIlmuwan3D({ onPosterClick }: { onPosterClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[-8, 2, -9]}>\n      {/* Poster Frame */}\n      <Box args={[3, 4, 0.1]} position={[0, 0, 0]}>\n        <meshStandardMaterial color={isHovered ? \"#8B4513\" : \"#654321\"} />\n      </Box>\n\n      {/* Poster Content */}\n      <Plane args={[2.8, 3.8]} position={[0, 0, 0.06]}>\n        <meshStandardMaterial color=\"#FFFFFF\" />\n      </Plane>\n\n      {/* Poster Text */}\n      <Html position={[0, 0, 0.07]} center>\n        <div className=\"text-black text-center pointer-events-none text-xs w-32\">\n          <div className=\"font-bold text-lg\">👨‍🔬</div>\n          <div className=\"font-bold\">POSTER</div>\n          <div className=\"font-bold\">ILMUWAN</div>\n          <div className=\"text-xs mt-2\">Tesla, Einstein,</div>\n          <div className=\"text-xs\">Al-Khawarizmi,</div>\n          <div className=\"text-xs\">Onno W. Purbo</div>\n        </div>\n      </Html>\n\n      <Html position={[0, 0, 0.1]} center>\n        <div\n          className=\"w-32 h-40 bg-transparent cursor-pointer hover:bg-purple-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onPosterClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Poster Ilmuwan Teknologi\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              👨‍🔬 Lihat Poster\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Action Figure Shelf 3D Component\nfunction ActionFigureShelf3D({ onShelfClick }: { onShelfClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[8, 0, -8]}>\n      {/* Shelf */}\n      <Box args={[2, 3, 0.5]} position={[0, 0, 0]}>\n        <meshStandardMaterial color={isHovered ? \"#D2B48C\" : \"#DEB887\"} />\n      </Box>\n\n      {/* Shelf Dividers */}\n      <Box args={[2, 0.1, 0.5]} position={[0, 1, 0]}>\n        <meshStandardMaterial color=\"#8B7355\" />\n      </Box>\n      <Box args={[2, 0.1, 0.5]} position={[0, -1, 0]}>\n        <meshStandardMaterial color=\"#8B7355\" />\n      </Box>\n\n      {/* Figures */}\n      <Html position={[0, 0, 0.3]} center>\n        <div className=\"text-center pointer-events-none\">\n          <div className=\"grid grid-cols-3 gap-1 text-lg\">\n            <div>🏴‍☠️</div>\n            <div>⚔️</div>\n            <div>🍥</div>\n            <div>⚡</div>\n            <div>🧪</div>\n            <div>⚗️</div>\n          </div>\n          <div className=\"text-xs font-bold mt-1\">Action Figures</div>\n        </div>\n      </Html>\n\n      <Html position={[0, 0, 0.4]} center>\n        <div\n          className=\"w-20 h-32 bg-transparent cursor-pointer hover:bg-orange-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onShelfClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Action Figure Collection\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              🧸 Lihat Koleksi\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Whiteboard 3D Component\nfunction Whiteboard3D({ onWhiteboardClick }: { onWhiteboardClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[0, 2, -9]}>\n      {/* Whiteboard Frame */}\n      <Box args={[4, 3, 0.1]} position={[0, 0, 0]}>\n        <meshStandardMaterial color=\"#2F4F4F\" />\n      </Box>\n\n      {/* Whiteboard Surface */}\n      <Plane args={[3.8, 2.8]} position={[0, 0, 0.06]}>\n        <meshStandardMaterial color={isHovered ? \"#F8F8FF\" : \"#FFFFFF\"} />\n      </Plane>\n\n      {/* Project Pins */}\n      <Html position={[0, 0, 0.07]} center>\n        <div className=\"text-black text-center pointer-events-none text-xs\">\n          <div className=\"font-bold text-lg\">📌</div>\n          <div className=\"font-bold\">PROJECT</div>\n          <div className=\"font-bold\">WHITEBOARD</div>\n          <div className=\"grid grid-cols-2 gap-2 mt-2 text-xs\">\n            <div className=\"bg-yellow-200 p-1 rounded\">Portfolio 3D</div>\n            <div className=\"bg-blue-200 p-1 rounded\">Langkah Kode</div>\n            <div className=\"bg-green-200 p-1 rounded\">DevTools ID</div>\n            <div className=\"bg-red-200 p-1 rounded\">Smart Campus</div>\n          </div>\n        </div>\n      </Html>\n\n      <Html position={[0, 0, 0.1]} center>\n        <div\n          className=\"w-40 h-32 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onWhiteboardClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Project Whiteboard\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              📌 Lihat Proyek\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Lighting Setup\nfunction Lighting() {\n  return (\n    <>\n      <ambientLight intensity={0.4} />\n      <pointLight position={[0, 6, 0]} intensity={0.8} />\n      <pointLight position={[-5, 4, -5]} intensity={0.6} color=\"#FFE4B5\" />\n      <pointLight position={[5, 4, -5]} intensity={0.6} color=\"#E6E6FA\" />\n      <directionalLight \n        position={[10, 10, 5]} \n        intensity={0.5}\n        castShadow\n        shadow-mapSize-width={2048}\n        shadow-mapSize-height={2048}\n      />\n    </>\n  );\n}\n\n// Main RoomScene3D Component\ninterface RoomScene3DProps {\n  onExitRoom?: () => void;\n}\n\nexport default function RoomScene3D({ onExitRoom }: RoomScene3DProps) {\n  const [isLoading, setIsLoading] = useState(true);\n  const [showComputerGUI, setShowComputerGUI] = useState(false);\n  const [showTerminalAI, setShowTerminalAI] = useState(false);\n  const [showLaptopWindows, setShowLaptopWindows] = useState(false);\n  const [showBook, setShowBook] = useState(false);\n  const [showIDCard, setShowIDCard] = useState(false);\n  const [showPoster, setShowPoster] = useState(false);\n  const [showActionFigure, setShowActionFigure] = useState(false);\n  const [showWhiteboard, setShowWhiteboard] = useState(false);\n\n  const handleComputerClick = () => {\n    setShowComputerGUI(true);\n  };\n\n  const handleLaptopClick = () => {\n    setShowLaptopWindows(true);\n  };\n\n  const handleBookClick = () => {\n    setShowBook(true);\n  };\n\n  const handleIDCardClick = () => {\n    setShowIDCard(true);\n  };\n\n  const handlePosterClick = () => {\n    setShowPoster(true);\n  };\n\n  const handleActionFigureClick = () => {\n    setShowActionFigure(true);\n  };\n\n  const handleWhiteboardClick = () => {\n    setShowWhiteboard(true);\n  };\n\n  const handleCloseComputer = () => {\n    setShowComputerGUI(false);\n    setShowTerminalAI(false);\n  };\n\n  const handleCloseLaptop = () => {\n    setShowLaptopWindows(false);\n  };\n\n  const handleCloseBook = () => {\n    setShowBook(false);\n  };\n\n  const handleCloseIDCard = () => {\n    setShowIDCard(false);\n  };\n\n  const handleClosePoster = () => {\n    setShowPoster(false);\n  };\n\n  const handleCloseActionFigure = () => {\n    setShowActionFigure(false);\n  };\n\n  const handleCloseWhiteboard = () => {\n    setShowWhiteboard(false);\n  };\n\n  return (\n    <div className=\"w-full h-screen relative\">\n      <Canvas\n        shadows\n        camera={{ position: [0, 2, 8], fov: 60 }}\n        onCreated={() => setIsLoading(false)}\n      >\n        <Suspense fallback={null}>\n          <Lighting />\n          <Room />\n          <Computer3D onComputerClick={handleComputerClick} />\n          <Laptop3D onLaptopClick={handleLaptopClick} />\n          <Book3D onBookClick={handleBookClick} />\n          <IDCard3D onIDCardClick={handleIDCardClick} />\n          <PosterIlmuwan3D onPosterClick={handlePosterClick} />\n          <ActionFigureShelf3D onShelfClick={handleActionFigureClick} />\n          <Whiteboard3D onWhiteboardClick={handleWhiteboardClick} />\n          \n          <OrbitControls\n            enablePan={true}\n            enableZoom={true}\n            enableRotate={true}\n            minDistance={3}\n            maxDistance={15}\n            minPolarAngle={0}\n            maxPolarAngle={Math.PI / 2}\n          />\n          \n          <Environment preset=\"apartment\" />\n        </Suspense>\n      </Canvas>\n\n      {/* UI Overlay */}\n      <div className=\"absolute top-4 left-4 z-10\">\n        <motion.div\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          className=\"bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white\"\n        >\n          <h2 className=\"text-xl font-bold mb-2\">Kamar KangPCode</h2>\n          <p className=\"text-sm text-gray-300\">\n            Klik objek untuk berinteraksi\n          </p>\n        </motion.div>\n      </div>\n\n      {/* Exit Button */}\n      <div className=\"absolute top-4 right-4 z-10\">\n        <motion.button\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          onClick={onExitRoom}\n          className=\"bg-red-500/80 hover:bg-red-600/80 backdrop-blur-sm rounded-lg px-4 py-2 text-white font-medium transition-colors\"\n        >\n          Keluar Kamar\n        </motion.button>\n      </div>\n\n      {/* Loading Overlay */}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center z-20\">\n          <div className=\"text-white text-xl\">Memuat Scene 3D...</div>\n        </div>\n      )}\n\n      {/* Computer GUI Overlay */}\n      <ComputerGUI\n        isVisible={showComputerGUI}\n        onClose={handleCloseComputer}\n      />\n\n      {/* Terminal AI Overlay */}\n      <TerminalAI\n        isVisible={showTerminalAI}\n        onClose={() => setShowTerminalAI(false)}\n      />\n\n      {/* Laptop Windows Overlay */}\n      <LaptopWindows\n        isVisible={showLaptopWindows}\n        onClose={handleCloseLaptop}\n      />\n\n      {/* Book Overlay */}\n      <BookKangPCode\n        isVisible={showBook}\n        onClose={handleCloseBook}\n      />\n\n      {/* ID Card Overlay */}\n      <IDCardLanyard\n        isVisible={showIDCard}\n        onClose={handleCloseIDCard}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAUA;AAEA;AACA;AACA;AACA;AACA;AApBA;;;;;;;;;;;AAyBA,6BAA6B;AAC7B,SAAS;IACP,qBACE,8OAAC;;0BAEC,8OAAC,0JAAA,CAAA,QAAK;gBACJ,MAAM;oBAAC;oBAAI;iBAAG;gBACd,UAAU;oBAAC,CAAC,KAAK,EAAE,GAAG;oBAAG;oBAAG;iBAAE;gBAC9B,UAAU;oBAAC;oBAAG,CAAC;oBAAG;iBAAE;0BAEpB,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAG;0BAC1C,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAG9B,8OAAC,0JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAI;iBAAG;gBAAE,UAAU;oBAAC,CAAC;oBAAI;oBAAG;iBAAE;gBAAE,UAAU;oBAAC;oBAAG,KAAK,EAAE,GAAG;oBAAG;iBAAE;0BACzE,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAG9B,8OAAC,0JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAI;oBAAG;iBAAE;gBAAE,UAAU;oBAAC;oBAAG,CAAC,KAAK,EAAE,GAAG;oBAAG;iBAAE;0BACzE,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,QAAK;gBACJ,MAAM;oBAAC;oBAAI;iBAAG;gBACd,UAAU;oBAAC,KAAK,EAAE,GAAG;oBAAG;oBAAG;iBAAE;gBAC7B,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAEnB,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;;;;;;;AAIpC;AAEA,wBAAwB;AACxB,SAAS,WAAW,EAAE,eAAe,EAAmC;IACtE,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QACzE;IACF;IAEA,qBACE,8OAAC;QAAM,UAAU;YAAC,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;;0BAE3B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,KAAK;gBAAS,MAAM;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BACvD,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;0BAC7C,cAAA,8OAAC;oBACC,OAAO,YAAY,YAAY;oBAC/B,UAAU,YAAY,YAAY;;;;;;;;;;;0BAKtC,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;gBAAE,MAAM;0BAClC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAI,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAKvC,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC7C,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAE;0BAC5C,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,MAAM;0BACjC,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,8OAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;AAEA,sBAAsB;AACtB,SAAS,SAAS,EAAE,aAAa,EAAiC;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAM,UAAU;YAAC;YAAG,CAAC;YAAG,CAAC;SAAE;;0BAE1B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAE;0BAChD,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAI;gBAAE,UAAU;oBAAC,CAAC;oBAAK;oBAAG;iBAAE;0BACxE,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAK;gBAAE,UAAU;oBAAC,CAAC;oBAAK;oBAAG;iBAAE;0BACtE,cAAA,8OAAC;oBACC,OAAO,YAAY,YAAY;oBAC/B,UAAU,YAAY,YAAY;;;;;;;;;;;0BAKtC,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAK;gBAAE,MAAM;0BACnC,cAAA,8OAAC;oBAAI,WAAU;oBAA6C,OAAO;wBAAE,WAAW;oBAAoB;;sCAClG,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAI,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAKvC,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAI;gBAAE,MAAM;0BAClC,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,8OAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;AAEA,oBAAoB;AACpB,SAAS,OAAO,EAAE,WAAW,EAA+B;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAM,UAAU;YAAC;YAAG,CAAC;YAAK;SAAE;;0BAC3B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAE;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC3C,cAAA,8OAAC;oBAAqB,OAAO,YAAY,YAAY;;;;;;;;;;;0BAIvD,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAM;iBAAK;gBAAE,MAAM;0BACrC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,8OAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,8OAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,8OAAC;4BAAI,WAAU;sCAAY;;;;;;;;;;;;;;;;;0BAI/B,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAK;iBAAE;gBAAE,MAAM;0BACjC,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,8OAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;AAEA,oBAAoB;AACpB,SAAS,SAAS,EAAE,aAAa,EAAiC;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAM,UAAU;YAAC;YAAG,CAAC;YAAK;SAAI;;0BAC7B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC9C,cAAA,8OAAC;oBAAqB,OAAO,YAAY,YAAY;;;;;;;;;;;0BAIvD,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAM;iBAAK;gBAAE,MAAM;0BACrC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,8OAAC;4BAAI,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAI7B,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAK;iBAAE;gBAAE,MAAM;0BACjC,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,8OAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;AAEA,8BAA8B;AAC9B,SAAS,gBAAgB,EAAE,aAAa,EAAiC;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAM,UAAU;YAAC,CAAC;YAAG;YAAG,CAAC;SAAE;;0BAE1B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BACzC,cAAA,8OAAC;oBAAqB,OAAO,YAAY,YAAY;;;;;;;;;;;0BAIvD,8OAAC,0JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;0BAC7C,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;gBAAE,MAAM;0BAClC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAoB;;;;;;sCACnC,8OAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,8OAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,8OAAC;4BAAI,WAAU;sCAAe;;;;;;sCAC9B,8OAAC;4BAAI,WAAU;sCAAU;;;;;;sCACzB,8OAAC;4BAAI,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAI7B,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,MAAM;0BACjC,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,8OAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;AAEA,mCAAmC;AACnC,SAAS,oBAAoB,EAAE,YAAY,EAAgC;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAM,UAAU;YAAC;YAAG;YAAG,CAAC;SAAE;;0BAEzB,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BACzC,cAAA,8OAAC;oBAAqB,OAAO,YAAY,YAAY;;;;;;;;;;;0BAIvD,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC3C,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAE9B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAG;iBAAE;0BAC5C,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,MAAM;0BACjC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAI;;;;;;8CACL,8OAAC;8CAAI;;;;;;8CACL,8OAAC;8CAAI;;;;;;8CACL,8OAAC;8CAAI;;;;;;8CACL,8OAAC;8CAAI;;;;;;8CACL,8OAAC;8CAAI;;;;;;;;;;;;sCAEP,8OAAC;4BAAI,WAAU;sCAAyB;;;;;;;;;;;;;;;;;0BAI5C,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,MAAM;0BACjC,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,8OAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;AAEA,0BAA0B;AAC1B,SAAS,aAAa,EAAE,iBAAiB,EAAqC;IAC5E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAM,UAAU;YAAC;YAAG;YAAG,CAAC;SAAE;;0BAEzB,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BACzC,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;0BAC7C,cAAA,8OAAC;oBAAqB,OAAO,YAAY,YAAY;;;;;;;;;;;0BAIvD,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;gBAAE,MAAM;0BAClC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAoB;;;;;;sCACnC,8OAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,8OAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAA4B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CAA0B;;;;;;8CACzC,8OAAC;oCAAI,WAAU;8CAA2B;;;;;;8CAC1C,8OAAC;oCAAI,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;;;;;;0BAK9C,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,MAAM;0BACjC,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,8OAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;AAEA,iBAAiB;AACjB,SAAS;IACP,qBACE;;0BACE,8OAAC;gBAAa,WAAW;;;;;;0BACzB,8OAAC;gBAAW,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,WAAW;;;;;;0BAC5C,8OAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAG;oBAAG,CAAC;iBAAE;gBAAE,WAAW;gBAAK,OAAM;;;;;;0BACzD,8OAAC;gBAAW,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAE;gBAAE,WAAW;gBAAK,OAAM;;;;;;0BACxD,8OAAC;gBACC,UAAU;oBAAC;oBAAI;oBAAI;iBAAE;gBACrB,WAAW;gBACX,UAAU;gBACV,wBAAsB;gBACtB,yBAAuB;;;;;;;;AAI/B;AAOe,SAAS,YAAY,EAAE,UAAU,EAAoB;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,sBAAsB;QAC1B,mBAAmB;IACrB;IAEA,MAAM,oBAAoB;QACxB,qBAAqB;IACvB;IAEA,MAAM,kBAAkB;QACtB,YAAY;IACd;IAEA,MAAM,oBAAoB;QACxB,cAAc;IAChB;IAEA,MAAM,oBAAoB;QACxB,cAAc;IAChB;IAEA,MAAM,0BAA0B;QAC9B,oBAAoB;IACtB;IAEA,MAAM,wBAAwB;QAC5B,kBAAkB;IACpB;IAEA,MAAM,sBAAsB;QAC1B,mBAAmB;QACnB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB;QACxB,qBAAqB;IACvB;IAEA,MAAM,kBAAkB;QACtB,YAAY;IACd;IAEA,MAAM,oBAAoB;QACxB,cAAc;IAChB;IAEA,MAAM,oBAAoB;QACxB,cAAc;IAChB;IAEA,MAAM,0BAA0B;QAC9B,oBAAoB;IACtB;IAEA,MAAM,wBAAwB;QAC5B,kBAAkB;IACpB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mMAAA,CAAA,SAAM;gBACL,OAAO;gBACP,QAAQ;oBAAE,UAAU;wBAAC;wBAAG;wBAAG;qBAAE;oBAAE,KAAK;gBAAG;gBACvC,WAAW,IAAM,aAAa;0BAE9B,cAAA,8OAAC,qMAAA,CAAA,WAAQ;oBAAC,UAAU;;sCAClB,8OAAC;;;;;sCACD,8OAAC;;;;;sCACD,8OAAC;4BAAW,iBAAiB;;;;;;sCAC7B,8OAAC;4BAAS,eAAe;;;;;;sCACzB,8OAAC;4BAAO,aAAa;;;;;;sCACrB,8OAAC;4BAAS,eAAe;;;;;;sCACzB,8OAAC;4BAAgB,eAAe;;;;;;sCAChC,8OAAC;4BAAoB,cAAc;;;;;;sCACnC,8OAAC;4BAAa,mBAAmB;;;;;;sCAEjC,8OAAC,iKAAA,CAAA,gBAAa;4BACZ,WAAW;4BACX,YAAY;4BACZ,cAAc;4BACd,aAAa;4BACb,aAAa;4BACb,eAAe;4BACf,eAAe,KAAK,EAAE,GAAG;;;;;;sCAG3B,8OAAC,+JAAA,CAAA,cAAW;4BAAC,QAAO;;;;;;;;;;;;;;;;;0BAKxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAyB;;;;;;sCACvC,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAOzC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;YAMF,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAAqB;;;;;;;;;;;0BAKxC,8OAAC,0HAAA,CAAA,UAAW;gBACV,WAAW;gBACX,SAAS;;;;;;0BAIX,8OAAC,yHAAA,CAAA,UAAU;gBACT,WAAW;gBACX,SAAS,IAAM,kBAAkB;;;;;;0BAInC,8OAAC,4HAAA,CAAA,UAAa;gBACZ,WAAW;gBACX,SAAS;;;;;;0BAIX,8OAAC,4HAAA,CAAA,UAAa;gBACZ,WAAW;gBACX,SAAS;;;;;;0BAIX,8OAAC,4HAAA,CAAA,UAAa;gBACZ,WAAW;gBACX,SAAS;;;;;;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 5395, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport LoadingScreen from '@/components/LoadingScreen';\nimport RoomScene3D from '@/components/RoomScene3D';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ntype SceneType = 'loading' | 'intro' | 'room';\n\nexport default function Home() {\n  const [currentScene, setCurrentScene] = useState<SceneType>('loading');\n\n  // Disable right-click context menu for security\n  useEffect(() => {\n    const disableRightClick = (e: MouseEvent) => e.preventDefault();\n    document.addEventListener('contextmenu', disableRightClick);\n    return () => document.removeEventListener('contextmenu', disableRightClick);\n  }, []);\n\n  const handleLoadingComplete = () => {\n    setCurrentScene('intro');\n  };\n\n  const handleEnterRoom = () => {\n    setCurrentScene('room');\n  };\n\n  const handleExitRoom = () => {\n    setCurrentScene('intro');\n  };\n\n  return (\n    <div className=\"w-full h-screen overflow-hidden\">\n      <AnimatePresence mode=\"wait\">\n        {currentScene === 'loading' && (\n          <LoadingScreen key=\"loading\" onComplete={handleLoadingComplete} />\n        )}\n\n        {currentScene === 'intro' && (\n          <motion.div\n            key=\"intro\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"w-full h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center relative overflow-hidden\"\n          >\n            {/* Background Animation */}\n            <div className=\"absolute inset-0\">\n              {[...Array(50)].map((_, i) => (\n                <motion.div\n                  key={i}\n                  className=\"absolute w-1 h-1 bg-white rounded-full\"\n                  style={{\n                    left: `${Math.random() * 100}%`,\n                    top: `${Math.random() * 100}%`,\n                  }}\n                  animate={{\n                    opacity: [0, 1, 0],\n                    scale: [0, 1, 0],\n                  }}\n                  transition={{\n                    duration: 3,\n                    repeat: Infinity,\n                    delay: Math.random() * 3,\n                  }}\n                />\n              ))}\n            </div>\n\n            {/* Main Content */}\n            <div className=\"text-center z-10 space-y-8\">\n              <motion.h1\n                initial={{ y: -50, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ delay: 0.2 }}\n                className=\"text-6xl md:text-8xl font-bold text-white mb-4\"\n              >\n                KangPCode\n              </motion.h1>\n\n              <motion.p\n                initial={{ y: 50, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ delay: 0.4 }}\n                className=\"text-2xl text-purple-200 mb-8\"\n              >\n                Portfolio 3D Interaktif\n              </motion.p>\n\n              <motion.p\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.6 }}\n                className=\"text-lg text-purple-300 mb-12 max-w-2xl mx-auto\"\n              >\n                Selamat datang di dunia virtual KangPCode! Jelajahi kamar virtual yang penuh dengan teknologi,\n                proyek, dan inspirasi dari seorang developer Indonesia.\n              </motion.p>\n\n              {/* Door to Room */}\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ delay: 0.8, type: \"spring\", stiffness: 200 }}\n                className=\"relative\"\n              >\n                <motion.button\n                  onClick={handleEnterRoom}\n                  className=\"group relative bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-bold py-4 px-8 rounded-lg text-xl shadow-2xl transition-all duration-300 transform hover:scale-105\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <span className=\"relative z-10\">🚪 Masuk ke Kamar KangPCode</span>\n\n                  {/* Glow Effect */}\n                  <motion.div\n                    className=\"absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-lg blur-lg opacity-30\"\n                    animate={{\n                      scale: [1, 1.1, 1],\n                      opacity: [0.3, 0.5, 0.3],\n                    }}\n                    transition={{\n                      duration: 2,\n                      repeat: Infinity,\n                    }}\n                  />\n                </motion.button>\n              </motion.div>\n\n              <motion.p\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 1 }}\n                className=\"text-sm text-purple-400 mt-4\"\n              >\n                Klik pintu untuk memulai petualangan 3D Anda\n              </motion.p>\n            </div>\n          </motion.div>\n        )}\n\n        {currentScene === 'room' && (\n          <motion.div\n            key=\"room\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n          >\n            <RoomScene3D onExitRoom={handleExitRoom} />\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IAE5D,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB,CAAC,IAAkB,EAAE,cAAc;QAC7D,SAAS,gBAAgB,CAAC,eAAe;QACzC,OAAO,IAAM,SAAS,mBAAmB,CAAC,eAAe;IAC3D,GAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,gBAAgB;IAClB;IAEA,MAAM,kBAAkB;QACtB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB;QACrB,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;YAAC,MAAK;;gBACnB,iBAAiB,2BAChB,8OAAC,4HAAA,CAAA,UAAa;oBAAe,YAAY;mBAAtB;;;;;gBAGpB,iBAAiB,yBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,OAAO;wCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAChC;oCACA,SAAS;wCACP,SAAS;4CAAC;4CAAG;4CAAG;yCAAE;wCAClB,OAAO;4CAAC;4CAAG;4CAAG;yCAAE;oCAClB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO,KAAK,MAAM,KAAK;oCACzB;mCAdK;;;;;;;;;;sCAoBX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,GAAG,CAAC;wCAAI,SAAS;oCAAE;oCAC9B,SAAS;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;8CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,SAAS;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;8CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;8CAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,YAAY;wCAAE,OAAO;wCAAK,MAAM;wCAAU,WAAW;oCAAI;oCACzD,WAAU;8CAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAGhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDACP,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;oDAClB,SAAS;wDAAC;wDAAK;wDAAK;qDAAI;gDAC1B;gDACA,YAAY;oDACV,UAAU;oDACV,QAAQ;gDACV;;;;;;;;;;;;;;;;;8CAKN,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,OAAO;oCAAE;oCACvB,WAAU;8CACX;;;;;;;;;;;;;mBA9FC;;;;;gBAqGP,iBAAiB,wBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;8BAEnB,cAAA,8OAAC,0HAAA,CAAA,UAAW;wBAAC,YAAY;;;;;;mBALrB;;;;;;;;;;;;;;;;AAWhB", "debugId": null}}]}
'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

interface MusicToggleProps {
  onVolumeChange?: (volume: number) => void;
}

export default function MusicToggle({ onVolumeChange }: MusicToggleProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(0.3);
  const [isLoaded, setIsLoaded] = useState(false);
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);
  const audioRef = useRef<HTMLAudioElement>(null);

  useEffect(() => {
    // Load saved preferences
    const savedVolume = localStorage.getItem('music-volume');
    const savedPlaying = localStorage.getItem('music-playing');
    
    if (savedVolume) {
      const vol = parseFloat(savedVolume);
      setVolume(vol);
      onVolumeChange?.(vol);
    }
    
    if (savedPlaying === 'true') {
      setIsPlaying(true);
    }
  }, [onVolumeChange]);

  useEffect(() => {
    if (audioRef.current) {
      audioRef.current.volume = volume;
      
      if (isPlaying && isLoaded) {
        audioRef.current.play().catch(console.error);
      } else {
        audioRef.current.pause();
      }
    }
  }, [isPlaying, volume, isLoaded]);

  const toggleMusic = () => {
    if (!isLoaded) return;
    
    const newPlaying = !isPlaying;
    setIsPlaying(newPlaying);
    localStorage.setItem('music-playing', newPlaying.toString());
  };

  const handleVolumeChange = (newVolume: number) => {
    setVolume(newVolume);
    localStorage.setItem('music-volume', newVolume.toString());
    onVolumeChange?.(newVolume);
  };

  const handleAudioLoad = () => {
    setIsLoaded(true);
  };

  const handleAudioError = () => {
    console.warn('Failed to load background music');
    setIsLoaded(false);
  };

  return (
    <div className="relative">
      {/* Hidden Audio Element */}
      <audio
        ref={audioRef}
        loop
        preload="auto"
        onCanPlayThrough={handleAudioLoad}
        onError={handleAudioError}
      >
        <source src="/assets/audio/lofi.mp3" type="audio/mpeg" />
        <source src="/assets/audio/lofi.ogg" type="audio/ogg" />
        Your browser does not support the audio element.
      </audio>

      {/* Music Control Button */}
      <motion.button
        onClick={toggleMusic}
        onMouseEnter={() => setShowVolumeSlider(true)}
        onMouseLeave={() => setShowVolumeSlider(false)}
        className={`relative w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${
          isPlaying 
            ? 'bg-green-500 hover:bg-green-600 shadow-lg shadow-green-500/30' 
            : 'bg-gray-500 hover:bg-gray-600 shadow-lg'
        } ${!isLoaded ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        disabled={!isLoaded}
        aria-label={isPlaying ? 'Pause music' : 'Play music'}
      >
        {/* Music Icon */}
        <motion.div
          animate={{
            scale: isPlaying ? [1, 1.2, 1] : 1,
          }}
          transition={{
            duration: 1,
            repeat: isPlaying ? Infinity : 0,
            ease: "easeInOut"
          }}
          className="text-white text-lg"
        >
          {!isLoaded ? '⏳' : isPlaying ? '🎵' : '🔇'}
        </motion.div>

        {/* Sound Waves Animation */}
        {isPlaying && (
          <div className="absolute -right-1 top-1/2 transform -translate-y-1/2">
            {[0, 1, 2].map((i) => (
              <motion.div
                key={i}
                className="absolute w-1 bg-white rounded-full"
                style={{
                  left: `${i * 3}px`,
                  height: '4px',
                }}
                animate={{
                  height: ['4px', '12px', '4px'],
                  opacity: [0.4, 1, 0.4],
                }}
                transition={{
                  duration: 0.8,
                  repeat: Infinity,
                  delay: i * 0.1,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>
        )}

        {/* Loading Spinner */}
        {!isLoaded && (
          <motion.div
            className="absolute inset-0 border-2 border-white border-t-transparent rounded-full"
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          />
        )}
      </motion.button>

      {/* Volume Slider */}
      <motion.div
        initial={{ opacity: 0, x: -20, scale: 0.8 }}
        animate={{
          opacity: showVolumeSlider && isLoaded ? 1 : 0,
          x: showVolumeSlider && isLoaded ? -60 : -20,
          scale: showVolumeSlider && isLoaded ? 1 : 0.8,
        }}
        transition={{ duration: 0.2 }}
        className="absolute right-full top-1/2 transform -translate-y-1/2 mr-2 pointer-events-none"
        style={{ pointerEvents: showVolumeSlider && isLoaded ? 'auto' : 'none' }}
      >
        <div className="bg-black/80 backdrop-blur-sm rounded-lg p-3 flex items-center space-x-3 border border-white/20">
          <span className="text-white text-xs">🔊</span>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={volume}
            onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
            className="w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer slider"
            style={{
              background: `linear-gradient(to right, #10b981 0%, #10b981 ${volume * 100}%, #4b5563 ${volume * 100}%, #4b5563 100%)`
            }}
          />
          <span className="text-white text-xs font-mono w-8">
            {Math.round(volume * 100)}%
          </span>
        </div>
      </motion.div>

      {/* Music Info Tooltip */}
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{
          opacity: showVolumeSlider && isLoaded ? 1 : 0,
          y: showVolumeSlider && isLoaded ? 0 : 10,
        }}
        transition={{ duration: 0.2, delay: 0.1 }}
        className="absolute top-full mt-2 left-1/2 transform -translate-x-1/2 pointer-events-none"
      >
        <div className="bg-black/80 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/20">
          <div className="text-white text-xs text-center">
            <div className="font-medium">🎧 Lofi Campus Vibes</div>
            <div className="opacity-70">Background Music</div>
          </div>
        </div>
      </motion.div>

      {/* Custom Slider Styles */}
      <style jsx>{`
        .slider::-webkit-slider-thumb {
          appearance: none;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: #10b981;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
        
        .slider::-moz-range-thumb {
          width: 16px;
          height: 16px;
          border-radius: 50%;
          background: #10b981;
          cursor: pointer;
          border: 2px solid #ffffff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }
      `}</style>
    </div>
  );
}

// Hook for controlling music from other components
export function useMusic() {
  const [isPlaying, setIsPlaying] = useState(false);
  const [volume, setVolume] = useState(0.3);

  useEffect(() => {
    const savedPlaying = localStorage.getItem('music-playing');
    const savedVolume = localStorage.getItem('music-volume');
    
    if (savedPlaying) {
      setIsPlaying(savedPlaying === 'true');
    }
    
    if (savedVolume) {
      setVolume(parseFloat(savedVolume));
    }
    
    // Listen for changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'music-playing' && e.newValue) {
        setIsPlaying(e.newValue === 'true');
      }
      if (e.key === 'music-volume' && e.newValue) {
        setVolume(parseFloat(e.newValue));
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const toggleMusic = () => {
    const newPlaying = !isPlaying;
    setIsPlaying(newPlaying);
    localStorage.setItem('music-playing', newPlaying.toString());
  };

  const setMusicVolume = (newVolume: number) => {
    setVolume(newVolume);
    localStorage.setItem('music-volume', newVolume.toString());
  };

  return {
    isPlaying,
    volume,
    toggleMusic,
    setVolume: setMusicVolume,
  };
}

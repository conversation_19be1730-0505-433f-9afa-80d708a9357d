'use client';

import { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';

interface TerminalAIProps {
  isVisible: boolean;
  onClose: () => void;
}

interface TerminalLine {
  type: 'input' | 'output' | 'error';
  content: string;
  timestamp: Date;
}

export default function TerminalAI({ isVisible, onClose }: TerminalAIProps) {
  const [input, setInput] = useState('');
  const [history, setHistory] = useState<TerminalLine[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const terminalRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // AI Knowledge Base
  const knowledgeBase = {
    'siapa kangpcode': 'KangPCode ad<PERSON><PERSON>, developer dan kreator teknologi Nusantara yang berfokus pada pengembangan aplikasi web modern dan edukasi teknologi.',
    'pendidikan': 'Informatika – Universitas XYZ, dengan fokus pada pengembangan web dan teknologi modern.',
    'skill': 'Next.js, TailwindCSS, SQLite, Laravel, Bun, React, PWA, Three.js, TypeScript, Python, Docker, dan teknologi web modern lainnya.',
    'pengalaman': 'Magang di CV Bintang Gumilang, freelance proyek TI lokal, dan berbagai proyek pengembangan web untuk klien Indonesia.',
    'hobi': 'Ngoding, mempelajari sejarah tokoh teknologi, menonton anime, menulis, dan berbagi pengetahuan teknologi.',
    'buku': 'Langkah Kode Nusantara - Perjalanan belajar dan berkarya dalam dunia teknologi Indonesia (GitHub: kangpcode/langkah-kode-nusantara)',
    'proyek': 'Portfolio 3D Interaktif, Langkah Kode Nusantara, berbagai aplikasi web dengan Next.js dan React, serta proyek edukasi teknologi.',
    'kontak': 'GitHub: github.com/kangpcode | Email: <EMAIL> | Status: Open for Collaboration',
    'teknologi': 'Spesialisasi dalam JavaScript/TypeScript ecosystem, React/Next.js, modern CSS frameworks, database design, dan 3D web development.',
    'visi': 'Membangun ekosistem teknologi Indonesia yang kuat melalui edukasi, open source, dan kolaborasi komunitas developer lokal.',
  };

  const commands = {
    'help': 'Perintah yang tersedia:\n- siapa kangpcode\n- pendidikan\n- skill\n- pengalaman\n- hobi\n- buku\n- proyek\n- kontak\n- teknologi\n- visi\n- clear\n- help',
    'clear': 'CLEAR_TERMINAL',
    'ls': 'projects/\nbooks/\nskills/\ncontacts/\nexperience/',
    'pwd': '/home/<USER>/portfolio',
    'whoami': 'kangpcode (Dhafa Nazula Permadi)',
    'date': new Date().toLocaleString('id-ID'),
    'uname': 'KangPCode Terminal AI v1.0 - Interactive Portfolio Assistant',
  };

  useEffect(() => {
    if (isVisible) {
      setHistory([
        {
          type: 'output',
          content: '🧠 KangPCode Terminal AI v1.0\nSelamat datang! Ketik "help" untuk melihat perintah yang tersedia.\nAtau tanyakan tentang KangPCode dengan bahasa natural.',
          timestamp: new Date()
        }
      ]);
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [isVisible]);

  useEffect(() => {
    if (terminalRef.current) {
      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;
    }
  }, [history]);

  const processCommand = async (cmd: string) => {
    const command = cmd.toLowerCase().trim();
    
    // Add user input to history
    setHistory(prev => [...prev, {
      type: 'input',
      content: `$ ${cmd}`,
      timestamp: new Date()
    }]);

    setIsTyping(true);

    // Simulate AI thinking delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

    let response = '';

    // Check exact commands first
    if (commands[command as keyof typeof commands]) {
      const result = commands[command as keyof typeof commands];
      if (result === 'CLEAR_TERMINAL') {
        setHistory([]);
        setIsTyping(false);
        return;
      }
      response = result;
    }
    // Check knowledge base
    else if (knowledgeBase[command as keyof typeof knowledgeBase]) {
      response = `🧠 ${knowledgeBase[command as keyof typeof knowledgeBase]}`;
    }
    // Natural language processing (simple keyword matching)
    else {
      const keywords = Object.keys(knowledgeBase);
      const matchedKeyword = keywords.find(keyword => 
        command.includes(keyword) || keyword.includes(command.split(' ')[0])
      );
      
      if (matchedKeyword) {
        response = `🧠 ${knowledgeBase[matchedKeyword as keyof typeof knowledgeBase]}`;
      } else if (command.includes('halo') || command.includes('hai') || command.includes('hello')) {
        response = '👋 Halo! Saya AI Assistant KangPCode. Ada yang bisa saya bantu? Ketik "help" untuk melihat perintah yang tersedia.';
      } else if (command.includes('terima kasih') || command.includes('thanks')) {
        response = '🙏 Sama-sama! Senang bisa membantu. Ada pertanyaan lain tentang KangPCode?';
      } else if (command.includes('bye') || command.includes('exit') || command.includes('quit')) {
        response = '👋 Sampai jumpa! Terima kasih telah menggunakan KangPCode Terminal AI.';
      } else {
        response = `❓ Maaf, saya tidak mengerti perintah "${cmd}". Ketik "help" untuk melihat perintah yang tersedia atau tanyakan tentang KangPCode.`;
      }
    }

    // Add AI response to history
    setHistory(prev => [...prev, {
      type: 'output',
      content: response,
      timestamp: new Date()
    }]);

    setIsTyping(false);
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (input.trim()) {
      processCommand(input);
      setInput('');
    }
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-4 bg-black rounded-lg shadow-2xl border border-green-500/30 overflow-hidden z-50"
    >
      {/* Terminal Header */}
      <div className="h-8 bg-gray-900 flex items-center justify-between px-4 border-b border-green-500/30">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-red-500 rounded-full"></div>
          <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
          <div className="w-3 h-3 bg-green-500 rounded-full"></div>
          <span className="text-green-400 text-sm font-mono ml-4">🧠 KangPCode Terminal AI</span>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white transition-colors"
        >
          ✕
        </button>
      </div>

      {/* Terminal Content */}
      <div 
        ref={terminalRef}
        className="flex-1 p-4 font-mono text-sm text-green-400 bg-black overflow-y-auto h-[calc(100%-8rem)]"
      >
        {history.map((line, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className={`mb-2 ${
              line.type === 'input' 
                ? 'text-cyan-400' 
                : line.type === 'error' 
                ? 'text-red-400' 
                : 'text-green-400'
            }`}
          >
            <pre className="whitespace-pre-wrap font-mono">{line.content}</pre>
          </motion.div>
        ))}
        
        {isTyping && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="text-yellow-400 flex items-center space-x-2"
          >
            <span>🧠 AI sedang berpikir</span>
            <div className="flex space-x-1">
              {[0, 1, 2].map((i) => (
                <motion.div
                  key={i}
                  className="w-1 h-1 bg-yellow-400 rounded-full"
                  animate={{
                    scale: [1, 1.5, 1],
                    opacity: [0.5, 1, 0.5],
                  }}
                  transition={{
                    duration: 1,
                    repeat: Infinity,
                    delay: i * 0.2,
                  }}
                />
              ))}
            </div>
          </motion.div>
        )}
      </div>

      {/* Terminal Input */}
      <div className="h-12 bg-gray-900 border-t border-green-500/30 flex items-center px-4">
        <form onSubmit={handleSubmit} className="flex-1 flex items-center space-x-2">
          <span className="text-cyan-400 font-mono">$</span>
          <input
            ref={inputRef}
            type="text"
            value={input}
            onChange={(e) => setInput(e.target.value)}
            className="flex-1 bg-transparent text-green-400 font-mono outline-none placeholder-gray-500"
            placeholder="Ketik perintah atau tanyakan tentang KangPCode..."
            disabled={isTyping}
          />
        </form>
      </div>

      {/* Terminal Footer */}
      <div className="h-6 bg-gray-800 border-t border-green-500/30 flex items-center justify-between px-4 text-xs text-gray-400">
        <span>KangPCode Terminal AI - Interactive Portfolio Assistant</span>
        <span>Press Ctrl+C to exit</span>
      </div>
    </motion.div>
  );
}

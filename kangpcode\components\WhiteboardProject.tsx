'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface WhiteboardProjectProps {
  isVisible: boolean;
  onClose: () => void;
}

interface Project {
  id: number;
  name: string;
  description: string;
  stack: string[];
  previewImage: string;
  demoUrl?: string;
  githubUrl?: string;
  status: 'completed' | 'in-progress' | 'planned';
  featured: boolean;
  category: 'web' | 'mobile' | 'desktop' | 'ai' | 'other';
  completionDate?: string;
  highlights: string[];
  challenges: string[];
  learnings: string[];
}

export default function WhiteboardProject({ isVisible, onClose }: WhiteboardProjectProps) {
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [filterStatus, setFilterStatus] = useState<'all' | 'completed' | 'in-progress' | 'planned'>('all');

  const projects: Project[] = [
    {
      id: 1,
      name: "Portfolio 3D Interaktif",
      description: "Website portfolio berbasis 3D dengan Three.js yang menampilkan pengalaman game-like untuk eksplorasi karir dan proyek developer",
      stack: ["Next.js", "Three.js", "TypeScript", "TailwindCSS", "Framer Motion", "SQLite"],
      previewImage: "🌐",
      demoUrl: "https://kangpcode.dev",
      githubUrl: "https://github.com/kangpcode/portfolio-3d",
      status: "in-progress",
      featured: true,
      category: "web",
      highlights: [
        "Interactive 3D room environment",
        "Real-time GitHub integration",
        "AI-powered terminal assistant",
        "PWA with offline capabilities"
      ],
      challenges: [
        "Optimizing 3D performance on mobile",
        "Creating intuitive 3D interactions",
        "Balancing visual appeal with functionality"
      ],
      learnings: [
        "Advanced Three.js techniques",
        "3D UI/UX design principles",
        "Performance optimization for 3D web apps"
      ]
    },
    {
      id: 2,
      name: "Langkah Kode Nusantara",
      description: "Buku digital interaktif untuk developer Indonesia yang ingin membangun karir di dunia teknologi dengan tetap mengangkat nilai-nilai lokal",
      stack: ["Markdown", "GitBook", "React", "PDF.js"],
      previewImage: "📚",
      githubUrl: "https://github.com/kangpcode/langkah-kode-nusantara",
      status: "completed",
      featured: true,
      category: "other",
      completionDate: "2024-01-15",
      highlights: [
        "350+ halaman konten berkualitas",
        "150+ contoh kode praktis",
        "12 proyek hands-on",
        "Fokus pada konteks Indonesia"
      ],
      challenges: [
        "Menyesuaikan konten dengan konteks lokal",
        "Membuat contoh yang relevan",
        "Balancing theory dan practice"
      ],
      learnings: [
        "Technical writing skills",
        "Content structuring",
        "Educational material design"
      ]
    },
    {
      id: 3,
      name: "DevTools Indonesia",
      description: "Platform kolaboratif untuk developer Indonesia dengan fitur code sharing, mentoring, dan job matching berbasis skill assessment",
      stack: ["Next.js", "Node.js", "PostgreSQL", "Redis", "Docker", "AWS"],
      previewImage: "🛠️",
      status: "planned",
      featured: true,
      category: "web",
      highlights: [
        "Real-time code collaboration",
        "AI-powered skill assessment",
        "Mentoring marketplace",
        "Local job opportunities"
      ],
      challenges: [
        "Building scalable real-time features",
        "Creating fair skill assessment",
        "Community building strategies"
      ],
      learnings: [
        "Real-time architecture design",
        "Community platform development",
        "AI integration in web apps"
      ]
    },
    {
      id: 4,
      name: "Smart Campus IoT",
      description: "Sistem IoT untuk monitoring dan otomasi kampus menggunakan sensor dan dashboard real-time untuk efisiensi energi",
      stack: ["Arduino", "Raspberry Pi", "Python", "React", "InfluxDB", "Grafana"],
      previewImage: "🏫",
      status: "completed",
      featured: false,
      category: "other",
      completionDate: "2023-12-10",
      highlights: [
        "50+ sensor nodes deployed",
        "Real-time monitoring dashboard",
        "30% energy efficiency improvement",
        "Automated alert system"
      ],
      challenges: [
        "Sensor network reliability",
        "Data processing at scale",
        "Hardware-software integration"
      ],
      learnings: [
        "IoT architecture design",
        "Time-series data handling",
        "Hardware programming"
      ]
    },
    {
      id: 5,
      name: "EduGame Math",
      description: "Game edukasi matematika untuk anak SD dengan pendekatan gamifikasi dan adaptive learning algorithm",
      stack: ["Unity", "C#", "Firebase", "Analytics"],
      previewImage: "🎮",
      status: "completed",
      featured: false,
      category: "mobile",
      completionDate: "2023-08-20",
      highlights: [
        "Adaptive difficulty system",
        "Progress tracking for parents",
        "Engaging mini-games",
        "1000+ downloads"
      ],
      challenges: [
        "Balancing fun and education",
        "Creating adaptive algorithms",
        "Child-friendly UI design"
      ],
      learnings: [
        "Game development with Unity",
        "Educational game design",
        "Mobile app optimization"
      ]
    },
    {
      id: 6,
      name: "AI Code Reviewer",
      description: "Tool AI untuk review kode otomatis dengan fokus pada best practices, security, dan performance optimization",
      stack: ["Python", "TensorFlow", "FastAPI", "Docker", "GitHub API"],
      previewImage: "🤖",
      status: "in-progress",
      featured: false,
      category: "ai",
      highlights: [
        "Multi-language support",
        "Security vulnerability detection",
        "Performance optimization suggestions",
        "GitHub integration"
      ],
      challenges: [
        "Training accurate models",
        "Handling diverse codebases",
        "Providing actionable feedback"
      ],
      learnings: [
        "Machine learning for code analysis",
        "Natural language processing",
        "API design for AI services"
      ]
    }
  ];

  const statusColors = {
    completed: 'green',
    'in-progress': 'blue',
    planned: 'yellow'
  };

  const categoryIcons = {
    web: '🌐',
    mobile: '📱',
    desktop: '💻',
    ai: '🤖',
    other: '📦'
  };

  const filteredProjects = filterStatus === 'all' 
    ? projects 
    : projects.filter(p => p.status === filterStatus);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-4 bg-white rounded-lg shadow-2xl border overflow-hidden z-50"
    >
      {/* Whiteboard Header */}
      <div className="h-16 bg-gradient-to-r from-gray-100 to-gray-200 flex items-center justify-between px-6 border-b-4 border-gray-800">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">📌</span>
          <div>
            <h2 className="font-bold text-lg text-gray-900">Project Whiteboard</h2>
            <p className="text-sm text-gray-600">Pinned Projects & Ideas</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="hover:bg-gray-300 p-2 rounded transition-colors text-gray-700"
        >
          ✕
        </button>
      </div>

      {/* Filter Tabs */}
      <div className="h-12 bg-gray-50 border-b flex items-center px-6">
        {[
          { id: 'all', name: 'All Projects', count: projects.length },
          { id: 'completed', name: 'Completed', count: projects.filter(p => p.status === 'completed').length },
          { id: 'in-progress', name: 'In Progress', count: projects.filter(p => p.status === 'in-progress').length },
          { id: 'planned', name: 'Planned', count: projects.filter(p => p.status === 'planned').length }
        ].map((filter) => (
          <button
            key={filter.id}
            onClick={() => setFilterStatus(filter.id as any)}
            className={`flex items-center space-x-2 px-4 py-2 rounded transition-colors mr-2 ${
              filterStatus === filter.id 
                ? 'bg-blue-500 text-white' 
                : 'hover:bg-gray-200 text-gray-600'
            }`}
          >
            <span>{filter.name}</span>
            <span className="text-xs bg-white/20 px-2 py-1 rounded-full">{filter.count}</span>
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="flex h-[calc(100%-7rem)]">
        {/* Projects Grid */}
        <div className="w-1/2 p-6 overflow-y-auto border-r bg-gray-50">
          <div className="grid grid-cols-1 gap-4">
            {filteredProjects.map((project) => (
              <motion.div
                key={project.id}
                onClick={() => setSelectedProject(project)}
                className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all bg-white shadow-sm ${
                  selectedProject?.id === project.id
                    ? 'border-blue-500 shadow-md'
                    : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                } ${project.featured ? 'ring-2 ring-yellow-300' : ''}`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                layout
              >
                {/* Featured Badge */}
                {project.featured && (
                  <div className="absolute -top-2 -right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full">
                    ⭐ Featured
                  </div>
                )}

                {/* Project Header */}
                <div className="flex items-start space-x-4 mb-3">
                  <div className="text-3xl">{project.previewImage}</div>
                  <div className="flex-1">
                    <h3 className="font-bold text-lg text-gray-900">{project.name}</h3>
                    <div className="flex items-center space-x-2 mt-1">
                      <span className="text-sm text-gray-500">{categoryIcons[project.category]}</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        project.status === 'completed' ? 'bg-green-100 text-green-800' :
                        project.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                        'bg-yellow-100 text-yellow-800'
                      }`}>
                        {project.status.replace('-', ' ')}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Description */}
                <p className="text-gray-700 text-sm mb-3 line-clamp-2">{project.description}</p>

                {/* Tech Stack */}
                <div className="flex flex-wrap gap-1">
                  {project.stack.slice(0, 4).map((tech, index) => (
                    <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                      {tech}
                    </span>
                  ))}
                  {project.stack.length > 4 && (
                    <span className="text-xs text-gray-500">+{project.stack.length - 4} more</span>
                  )}
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Project Detail */}
        <div className="w-1/2 p-6 overflow-y-auto">
          <AnimatePresence mode="wait">
            {selectedProject ? (
              <motion.div
                key={selectedProject.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-6"
              >
                {/* Project Header */}
                <div className="text-center">
                  <div className="text-6xl mb-4">{selectedProject.previewImage}</div>
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">{selectedProject.name}</h2>
                  <p className="text-gray-600 leading-relaxed mb-4">{selectedProject.description}</p>
                  
                  <div className="flex justify-center space-x-4 mb-4">
                    {selectedProject.demoUrl && (
                      <a
                        href={selectedProject.demoUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
                      >
                        🌐 Live Demo
                      </a>
                    )}
                    {selectedProject.githubUrl && (
                      <a
                        href={selectedProject.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="px-4 py-2 bg-gray-900 hover:bg-gray-800 text-white rounded transition-colors"
                      >
                        📁 GitHub
                      </a>
                    )}
                  </div>
                </div>

                {/* Tech Stack */}
                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">🛠️ Tech Stack</h3>
                  <div className="flex flex-wrap gap-2">
                    {selectedProject.stack.map((tech, index) => (
                      <span key={index} className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Highlights */}
                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">✨ Key Highlights</h3>
                  <ul className="space-y-2">
                    {selectedProject.highlights.map((highlight, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-green-500 mt-1">•</span>
                        <span className="text-gray-700">{highlight}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Challenges */}
                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">🎯 Challenges</h3>
                  <ul className="space-y-2">
                    {selectedProject.challenges.map((challenge, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-orange-500 mt-1">•</span>
                        <span className="text-gray-700">{challenge}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Learnings */}
                <div>
                  <h3 className="text-lg font-bold text-gray-900 mb-3">📚 Key Learnings</h3>
                  <ul className="space-y-2">
                    {selectedProject.learnings.map((learning, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-blue-500 mt-1">•</span>
                        <span className="text-gray-700">{learning}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Completion Date */}
                {selectedProject.completionDate && (
                  <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                    <div className="flex items-center space-x-2">
                      <span className="text-green-600">✅</span>
                      <span className="text-green-800 font-medium">
                        Completed on {new Date(selectedProject.completionDate).toLocaleDateString('id-ID')}
                      </span>
                    </div>
                  </div>
                )}
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="flex items-center justify-center h-full text-center"
              >
                <div className="space-y-4">
                  <div className="text-6xl">📌</div>
                  <h3 className="text-xl font-bold text-gray-900">Select a Project</h3>
                  <p className="text-gray-600">Click on any project card to see detailed information</p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>
    </motion.div>
  );
}

// Security utilities for protecting the portfolio

export class SecurityManager {
  private static instance: SecurityManager;
  private isDevMode: boolean;
  private protectionEnabled: boolean;

  private constructor() {
    this.isDevMode = process.env.NODE_ENV === 'development';
    this.protectionEnabled = !this.isDevMode;
  }

  static getInstance(): SecurityManager {
    if (!SecurityManager.instance) {
      SecurityManager.instance = new SecurityManager();
    }
    return SecurityManager.instance;
  }

  // Initialize all security measures
  initialize() {
    if (!this.protectionEnabled) {
      console.log('🔓 Security protection disabled in development mode');
      return;
    }

    this.disableRightClick();
    this.disableKeyboardShortcuts();
    this.disableTextSelection();
    this.disableImageDragging();
    this.detectDevTools();
    this.obfuscateConsole();
    this.preventFraming();
  }

  // Disable right-click context menu
  private disableRightClick() {
    document.addEventListener('contextmenu', (e) => {
      e.preventDefault();
      this.showSecurityMessage('Right-click disabled for security');
      return false;
    });
  }

  // Disable common keyboard shortcuts for developer tools
  private disableKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Disable F12 (DevTools)
      if (e.key === 'F12') {
        e.preventDefault();
        this.showSecurityMessage('Developer tools access restricted');
        return false;
      }

      // Disable Ctrl+Shift+I (DevTools)
      if (e.ctrlKey && e.shiftKey && e.key === 'I') {
        e.preventDefault();
        this.showSecurityMessage('Developer tools access restricted');
        return false;
      }

      // Disable Ctrl+Shift+J (Console)
      if (e.ctrlKey && e.shiftKey && e.key === 'J') {
        e.preventDefault();
        this.showSecurityMessage('Console access restricted');
        return false;
      }

      // Disable Ctrl+U (View Source)
      if (e.ctrlKey && e.key === 'u') {
        e.preventDefault();
        this.showSecurityMessage('Source code viewing restricted');
        return false;
      }

      // Disable Ctrl+Shift+C (Element Inspector)
      if (e.ctrlKey && e.shiftKey && e.key === 'C') {
        e.preventDefault();
        this.showSecurityMessage('Element inspector access restricted');
        return false;
      }

      // Disable Ctrl+S (Save Page)
      if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        this.showSecurityMessage('Page saving restricted');
        return false;
      }
    });
  }

  // Disable text selection
  private disableTextSelection() {
    document.addEventListener('selectstart', (e) => {
      e.preventDefault();
      return false;
    });

    document.addEventListener('dragstart', (e) => {
      e.preventDefault();
      return false;
    });
  }

  // Disable image dragging
  private disableImageDragging() {
    document.addEventListener('dragstart', (e) => {
      if (e.target instanceof HTMLImageElement) {
        e.preventDefault();
        return false;
      }
    });
  }

  // Detect if developer tools are open
  private detectDevTools() {
    let devtools = { open: false, orientation: null };
    
    setInterval(() => {
      if (window.outerHeight - window.innerHeight > 200 || 
          window.outerWidth - window.innerWidth > 200) {
        if (!devtools.open) {
          devtools.open = true;
          this.handleDevToolsDetected();
        }
      } else {
        devtools.open = false;
      }
    }, 500);

    // Alternative detection method
    let element = new Image();
    Object.defineProperty(element, 'id', {
      get: () => {
        this.handleDevToolsDetected();
        return 'devtools-detector';
      }
    });

    setInterval(() => {
      console.log(element);
      console.clear();
    }, 1000);
  }

  // Handle when developer tools are detected
  private handleDevToolsDetected() {
    console.clear();
    console.log('%c🔒 KangPCode Portfolio Security', 'color: #ff6b6b; font-size: 20px; font-weight: bold;');
    console.log('%cDeveloper tools detected. This portfolio is protected.', 'color: #ffa726; font-size: 14px;');
    console.log('%cIf you\'re interested in the code, check out the GitHub repository!', 'color: #66bb6a; font-size: 12px;');
    console.log('%c🔗 https://github.com/kangpcode/portfolio-3d', 'color: #42a5f5; font-size: 12px;');
    
    // Optionally blur the page content
    document.body.style.filter = 'blur(5px)';
    setTimeout(() => {
      document.body.style.filter = 'none';
    }, 3000);
  }

  // Obfuscate console messages
  private obfuscateConsole() {
    // Override console methods
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;

    console.log = (...args) => {
      if (this.isDevMode) {
        originalLog.apply(console, args);
      } else {
        originalLog('%c🔒 Console access restricted', 'color: #ff6b6b;');
      }
    };

    console.warn = (...args) => {
      if (this.isDevMode) {
        originalWarn.apply(console, args);
      }
    };

    console.error = (...args) => {
      if (this.isDevMode) {
        originalError.apply(console, args);
      }
    };

    // Add welcome message
    setTimeout(() => {
      console.clear();
      console.log('%c👋 Welcome to KangPCode Portfolio!', 'color: #4CAF50; font-size: 24px; font-weight: bold;');
      console.log('%c🚀 Built with Next.js, Three.js, and lots of ☕', 'color: #2196F3; font-size: 16px;');
      console.log('%c📚 Interested in the code? Visit: https://github.com/kangpcode', 'color: #FF9800; font-size: 14px;');
      console.log('%c💼 Looking for a developer? Let\'s connect!', 'color: #9C27B0; font-size: 14px;');
    }, 2000);
  }

  // Prevent the page from being embedded in frames
  private preventFraming() {
    if (window.top !== window.self) {
      window.top!.location = window.self.location;
    }
  }

  // Show security message to user
  private showSecurityMessage(message: string) {
    // Create a temporary notification
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 12px 20px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.3);
      z-index: 10000;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      font-size: 14px;
      font-weight: 500;
      animation: slideIn 0.3s ease-out;
    `;

    // Add animation keyframes
    if (!document.getElementById('security-styles')) {
      const style = document.createElement('style');
      style.id = 'security-styles';
      style.textContent = `
        @keyframes slideIn {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
          from { transform: translateX(0); opacity: 1; }
          to { transform: translateX(100%); opacity: 0; }
        }
      `;
      document.head.appendChild(style);
    }

    notification.innerHTML = `
      <div style="display: flex; align-items: center; gap: 8px;">
        <span>🔒</span>
        <span>${message}</span>
      </div>
    `;

    document.body.appendChild(notification);

    // Remove notification after 3 seconds
    setTimeout(() => {
      notification.style.animation = 'slideOut 0.3s ease-in';
      setTimeout(() => {
        if (notification.parentNode) {
          notification.parentNode.removeChild(notification);
        }
      }, 300);
    }, 3000);
  }

  // Disable protection (for development)
  disable() {
    this.protectionEnabled = false;
    console.log('🔓 Security protection disabled');
  }

  // Enable protection
  enable() {
    this.protectionEnabled = true;
    this.initialize();
    console.log('🔒 Security protection enabled');
  }
}

// Export singleton instance
export const security = SecurityManager.getInstance();

// React hook for security
export function useSecurity() {
  const initializeSecurity = () => {
    security.initialize();
  };

  const disableSecurity = () => {
    security.disable();
  };

  const enableSecurity = () => {
    security.enable();
  };

  return {
    initializeSecurity,
    disableSecurity,
    enableSecurity,
  };
}

{"name": "kangpcode", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@react-three/drei": "^10.4.4", "@react-three/fiber": "^9.2.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/better-sqlite3": "^7.6.13", "@types/three": "^0.178.0", "better-sqlite3": "^12.2.0", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "framer-motion": "^12.23.0", "next": "15.3.5", "next-i18next": "^15.4.2", "next-pwa": "^5.6.0", "pdf-lib": "^1.17.1", "puppeteer": "^24.12.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-pdf": "^10.0.1", "three": "^0.178.0", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "ts-node": "^10.9.2", "typescript": "^5"}, "trustedDependencies": ["@tailwindcss/oxide", "core-js", "unrs-resolver"]}
-- Initialize KangPCode Portfolio Database

CREATE TABLE IF NOT EXISTS analytics (
  id INTEGER PRIMARY KEY NOT NULL,
  event TEXT NOT NULL,
  target TEXT NOT NULL,
  data TEXT,
  user_agent TEXT,
  ip TEXT,
  timestamp INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS biodata (
  id INTEGER PRIMARY KEY NOT NULL,
  name TEXT NOT NULL,
  role TEXT NOT NULL,
  education TEXT NOT NULL,
  university TEXT NOT NULL,
  status TEXT NOT NULL,
  bio TEXT NOT NULL,
  github TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  location TEXT,
  avatar TEXT,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS book (
  id INTEGER PRIMARY KEY NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  github_url TEXT NOT NULL,
  pdf_url TEXT,
  cover_image TEXT,
  chapters TEXT,
  skills TEXT,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS figure (
  id INTEGER PRIMARY KEY NOT NULL,
  name TEXT NOT NULL,
  anime TEXT NOT NULL,
  quote TEXT NOT NULL,
  philosophy TEXT NOT NULL,
  image TEXT,
  position TEXT,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS game_score (
  id INTEGER PRIMARY KEY NOT NULL,
  game TEXT NOT NULL,
  score INTEGER NOT NULL,
  player_name TEXT,
  data TEXT,
  timestamp INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS id_card (
  id INTEGER PRIMARY KEY NOT NULL,
  name TEXT NOT NULL,
  role TEXT NOT NULL,
  status TEXT NOT NULL,
  photo TEXT NOT NULL,
  qr_code TEXT NOT NULL,
  github_url TEXT NOT NULL,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS project (
  id INTEGER PRIMARY KEY NOT NULL,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  stack TEXT NOT NULL,
  preview_image TEXT,
  demo_url TEXT,
  github_url TEXT,
  status TEXT NOT NULL,
  featured INTEGER DEFAULT 0,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS scientist (
  id INTEGER PRIMARY KEY NOT NULL,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  contribution TEXT NOT NULL,
  image TEXT,
  category TEXT NOT NULL,
  birth_year INTEGER,
  death_year INTEGER,
  nationality TEXT,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS user_settings (
  id INTEGER PRIMARY KEY NOT NULL,
  key TEXT NOT NULL UNIQUE,
  value TEXT NOT NULL,
  updated_at INTEGER NOT NULL
);

-- Insert sample data
INSERT OR REPLACE INTO biodata (
  id, name, role, education, university, status, bio, github, 
  email, phone, location, avatar, created_at, updated_at
) VALUES (
  1,
  'Dhafa Nazula Permadi',
  'Fullstack Developer & Content Creator',
  'Informatika',
  'Universitas XYZ',
  'Open for Collaboration',
  'KangPCode adalah developer dan kreator teknologi Nusantara yang berfokus pada pengembangan aplikasi web modern dan edukasi teknologi.',
  'https://github.com/kangpcode',
  '<EMAIL>',
  '+62-xxx-xxxx-xxxx',
  'Indonesia',
  '/assets/images/kangpcode-avatar.jpg',
  strftime('%s', 'now'),
  strftime('%s', 'now')
);

INSERT OR REPLACE INTO book (
  id, title, description, github_url, pdf_url, cover_image, 
  chapters, skills, created_at, updated_at
) VALUES (
  1,
  'Langkah Kode Nusantara',
  'Perjalanan belajar dan berkarya dalam dunia teknologi Indonesia',
  'https://github.com/kangpcode/langkah-kode-nusantara',
  '/assets/book/langkah-kode-nusantara.pdf',
  '/assets/images/book/cover.jpg',
  '["Pengenalan", "Dasar Programming", "Web Development", "Mobile Development", "DevOps"]',
  '["JavaScript", "TypeScript", "React", "Next.js", "Node.js", "Python", "Docker"]',
  strftime('%s', 'now'),
  strftime('%s', 'now')
);

INSERT OR REPLACE INTO id_card (
  id, name, role, status, photo, qr_code, github_url, created_at, updated_at
) VALUES (
  1,
  'Dhafa Nazula Permadi',
  'Fullstack Developer',
  'Open Collaboration',
  '/assets/images/idcard/photo.jpg',
  '/assets/images/idcard/qr-github.png',
  'https://github.com/kangpcode',
  strftime('%s', 'now'),
  strftime('%s', 'now')
);

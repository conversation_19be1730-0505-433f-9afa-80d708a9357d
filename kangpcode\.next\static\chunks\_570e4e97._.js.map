{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/LoadingScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface LoadingScreenProps {\n  onComplete: () => void;\n}\n\nexport default function LoadingScreen({ onComplete }: LoadingScreenProps) {\n  const [progress, setProgress] = useState(0);\n  const [loadingText, setLoadingText] = useState('Memuat Dunia KangPCode...');\n\n  const loadingSteps = [\n    'Memuat Dunia KangPCode...',\n    'Menyiapkan Kamar Virtual...',\n    'Mengaktifkan Komputer 3D...',\n    'Memuat Poster Ilmuwan...',\n    'Menyiapkan Action Figures...',\n    'Dunia KangPCode Siap!'\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setProgress(prev => {\n        const newProgress = prev + 1;\n        \n        // Update loading text based on progress\n        const stepIndex = Math.floor((newProgress / 100) * loadingSteps.length);\n        if (stepIndex < loadingSteps.length) {\n          setLoadingText(loadingSteps[stepIndex]);\n        }\n\n        if (newProgress >= 100) {\n          clearInterval(interval);\n          setTimeout(() => onComplete(), 500);\n          return 100;\n        }\n        return newProgress;\n      });\n    }, 50); // Complete in ~5 seconds\n\n    return () => clearInterval(interval);\n  }, [onComplete]);\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        className=\"fixed inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center z-50\"\n      >\n        <div className=\"text-center space-y-8\">\n          {/* Logo/Avatar */}\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\n            className=\"w-32 h-32 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center shadow-2xl\"\n          >\n            <span className=\"text-4xl font-bold text-white\">KC</span>\n          </motion.div>\n\n          {/* Title */}\n          <motion.h1\n            initial={{ y: 20, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ delay: 0.4 }}\n            className=\"text-4xl md:text-6xl font-bold text-white mb-4\"\n          >\n            KangPCode\n          </motion.h1>\n\n          <motion.p\n            initial={{ y: 20, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ delay: 0.6 }}\n            className=\"text-xl text-blue-200 mb-8\"\n          >\n            Portfolio 3D Interaktif\n          </motion.p>\n\n          {/* Loading Bar */}\n          <div className=\"w-80 mx-auto\">\n            <motion.div\n              initial={{ width: 0 }}\n              animate={{ width: '100%' }}\n              transition={{ delay: 0.8 }}\n              className=\"h-2 bg-gray-700 rounded-full overflow-hidden\"\n            >\n              <motion.div\n                className=\"h-full bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full\"\n                style={{ width: `${progress}%` }}\n                transition={{ duration: 0.1 }}\n              />\n            </motion.div>\n            \n            <div className=\"flex justify-between mt-2 text-sm text-blue-200\">\n              <span>{progress}%</span>\n              <span>Loading...</span>\n            </div>\n          </div>\n\n          {/* Loading Text */}\n          <motion.p\n            key={loadingText}\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -10 }}\n            className=\"text-lg text-cyan-300 font-medium\"\n          >\n            {loadingText}\n          </motion.p>\n\n          {/* Animated Dots */}\n          <div className=\"flex justify-center space-x-2\">\n            {[0, 1, 2].map((i) => (\n              <motion.div\n                key={i}\n                className=\"w-3 h-3 bg-cyan-400 rounded-full\"\n                animate={{\n                  scale: [1, 1.2, 1],\n                  opacity: [0.5, 1, 0.5],\n                }}\n                transition={{\n                  duration: 1.5,\n                  repeat: Infinity,\n                  delay: i * 0.2,\n                }}\n              />\n            ))}\n          </div>\n        </div>\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AASe,SAAS,cAAc,EAAE,UAAU,EAAsB;;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,WAAW;oDAAY;oBAC3B;4DAAY,CAAA;4BACV,MAAM,cAAc,OAAO;4BAE3B,wCAAwC;4BACxC,MAAM,YAAY,KAAK,KAAK,CAAC,AAAC,cAAc,MAAO,aAAa,MAAM;4BACtE,IAAI,YAAY,aAAa,MAAM,EAAE;gCACnC,eAAe,YAAY,CAAC,UAAU;4BACxC;4BAEA,IAAI,eAAe,KAAK;gCACtB,cAAc;gCACd;wEAAW,IAAM;uEAAc;gCAC/B,OAAO;4BACT;4BACA,OAAO;wBACT;;gBACF;mDAAG,KAAK,yBAAyB;YAEjC;2CAAO,IAAM,cAAc;;QAC7B;kCAAG;QAAC;KAAW;IAEf,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;sBAEV,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BAAE,OAAO;4BAAK,MAAM;4BAAU,WAAW;wBAAI;wBACzD,WAAU;kCAEV,cAAA,6LAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;kCAIlD,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCACX;;;;;;kCAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCACX;;;;;;kCAKD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAO;gCACzB,YAAY;oCAAE,OAAO;gCAAI;gCACzB,WAAU;0CAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,SAAS,CAAC,CAAC;oCAAC;oCAC/B,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;0CAIhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAM;4CAAS;;;;;;;kDAChB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAKV,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBAEP,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,WAAU;kCAET;uBANI;;;;;kCAUP,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,SAAS;oCACP,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;oCAClB,SAAS;wCAAC;wCAAK;wCAAG;qCAAI;gCACxB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,OAAO,IAAI;gCACb;+BAVK;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBrB;GAhIwB;KAAA", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/ComputerGUI.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface ComputerGUIProps {\n  isVisible: boolean;\n  onClose: () => void;\n  onOpenTerminal?: () => void;\n  onOpenResume?: () => void;\n  onOpenGames?: () => void;\n}\n\ntype AppType = 'terminal' | 'resume' | 'projects' | 'games' | 'vscode' | 'browser' | 'settings' | 'info';\n\nexport default function ComputerGUI({ isVisible, onClose, onOpenTerminal, onOpenResume, onOpenGames }: ComputerGUIProps) {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [activeApp, setActiveApp] = useState<AppType | null>(null);\n  const [isBooting, setIsBooting] = useState(true);\n\n  // Update time every second\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Boot sequence\n  useEffect(() => {\n    if (isVisible) {\n      setIsBooting(true);\n      const bootTimer = setTimeout(() => {\n        setIsBooting(false);\n      }, 2000);\n      return () => clearTimeout(bootTimer);\n    }\n  }, [isVisible]);\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString('id-ID', { \n      hour: '2-digit', \n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n\n  const formatDate = (date: Date) => {\n    return date.toLocaleDateString('id-ID', { \n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const desktopApps = [\n    { id: 'terminal', name: 'Terminal AI', icon: '🧠', color: 'bg-green-600', action: onOpenTerminal },\n    { id: 'resume', name: 'Resume', icon: '📄', color: 'bg-blue-600', action: onOpenResume },\n    { id: 'projects', name: 'My Projects', icon: '💻', color: 'bg-purple-600' },\n    { id: 'games', name: 'Mini Games', icon: '🎮', color: 'bg-red-600', action: onOpenGames },\n  ];\n\n  const taskbarApps = [\n    { id: 'terminal', name: 'Terminal AI', icon: '🧠' },\n    { id: 'vscode', name: 'VSCode', icon: '💻' },\n    { id: 'browser', name: 'Browser', icon: '🌐' },\n    { id: 'settings', name: 'Settings', icon: '⚙️' },\n    { id: 'info', name: 'System Info', icon: '🧮' },\n    { id: 'games', name: 'Games', icon: '🎮' },\n  ];\n\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50 overflow-hidden\">\n      <AnimatePresence>\n        {isBooting ? (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"flex items-center justify-center h-full bg-black text-white\"\n          >\n            <div className=\"text-center space-y-4\">\n              <div className=\"text-6xl mb-4\">🐧</div>\n              <h2 className=\"text-2xl font-bold\">KangPCode ArchLinux</h2>\n              <p className=\"text-lg\">Booting KDE Plasma...</p>\n              <div className=\"flex justify-center space-x-1 mt-4\">\n                {[0, 1, 2].map((i) => (\n                  <motion.div\n                    key={i}\n                    className=\"w-3 h-3 bg-blue-500 rounded-full\"\n                    animate={{\n                      scale: [1, 1.2, 1],\n                      opacity: [0.5, 1, 0.5],\n                    }}\n                    transition={{\n                      duration: 1,\n                      repeat: Infinity,\n                      delay: i * 0.2,\n                    }}\n                  />\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        ) : (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"h-full bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 relative\"\n          >\n            {/* Desktop Wallpaper */}\n            <div className=\"absolute inset-0 bg-gradient-to-br from-blue-800/50 to-purple-800/50\" />\n            \n            {/* Top Panel */}\n            <div className=\"absolute top-0 left-0 right-0 h-8 bg-gray-900/90 backdrop-blur-sm border-b border-gray-700 flex items-center justify-between px-4 text-white text-sm z-10\">\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"font-bold\">kangpcode@archlinux</span>\n                <span>KDE Plasma 5.27</span>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <span>{formatDate(currentTime)}</span>\n                <span className=\"font-mono\">{formatTime(currentTime)}</span>\n                <button\n                  onClick={onClose}\n                  className=\"hover:bg-red-600 px-2 py-1 rounded transition-colors\"\n                >\n                  ✕\n                </button>\n              </div>\n            </div>\n\n            {/* Desktop Icons */}\n            <div className=\"absolute top-12 left-4 grid grid-cols-1 gap-4 z-10\">\n              {desktopApps.map((app) => (\n                <motion.button\n                  key={app.id}\n                  onClick={() => {\n                    if (app.action) {\n                      app.action();\n                    } else {\n                      setActiveApp(app.id as AppType);\n                    }\n                  }}\n                  className=\"flex flex-col items-center space-y-1 p-2 rounded hover:bg-white/10 transition-colors group\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <div className={`w-12 h-12 ${app.color} rounded-lg flex items-center justify-center text-2xl shadow-lg group-hover:shadow-xl transition-shadow`}>\n                    {app.icon}\n                  </div>\n                  <span className=\"text-white text-xs font-medium\">{app.name}</span>\n                </motion.button>\n              ))}\n            </div>\n\n            {/* Bottom Taskbar */}\n            <div className=\"absolute bottom-0 left-0 right-0 h-12 bg-gray-900/90 backdrop-blur-sm border-t border-gray-700 flex items-center justify-between px-4 z-10\">\n              {/* Start Menu */}\n              <button className=\"flex items-center space-x-2 px-3 py-2 hover:bg-gray-700/50 rounded transition-colors text-white\">\n                <span className=\"text-xl\">🐧</span>\n                <span className=\"font-medium\">Menu</span>\n              </button>\n\n              {/* App Icons */}\n              <div className=\"flex items-center space-x-2\">\n                {taskbarApps.map((app) => (\n                  <motion.button\n                    key={app.id}\n                    onClick={() => setActiveApp(app.id as AppType)}\n                    className={`w-10 h-10 rounded-lg flex items-center justify-center text-xl transition-colors ${\n                      activeApp === app.id \n                        ? 'bg-blue-600 text-white' \n                        : 'hover:bg-gray-700/50 text-gray-300'\n                    }`}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    title={app.name}\n                  >\n                    {app.icon}\n                  </motion.button>\n                ))}\n              </div>\n\n              {/* System Tray */}\n              <div className=\"flex items-center space-x-2 text-white\">\n                <span className=\"text-sm\">🔊 🔋 📶</span>\n                <span className=\"text-sm font-mono\">{formatTime(currentTime)}</span>\n              </div>\n            </div>\n\n            {/* Active Application Window */}\n            <AnimatePresence>\n              {activeApp && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  exit={{ opacity: 0, scale: 0.9 }}\n                  className=\"absolute inset-8 bg-gray-800 rounded-lg shadow-2xl border border-gray-600 overflow-hidden z-20\"\n                >\n                  {/* Window Title Bar */}\n                  <div className=\"h-8 bg-gray-700 flex items-center justify-between px-4 border-b border-gray-600\">\n                    <span className=\"text-white font-medium\">\n                      {taskbarApps.find(app => app.id === activeApp)?.name}\n                    </span>\n                    <div className=\"flex space-x-2\">\n                      <button className=\"w-4 h-4 bg-yellow-500 rounded-full hover:bg-yellow-400\"></button>\n                      <button className=\"w-4 h-4 bg-green-500 rounded-full hover:bg-green-400\"></button>\n                      <button \n                        onClick={() => setActiveApp(null)}\n                        className=\"w-4 h-4 bg-red-500 rounded-full hover:bg-red-400\"\n                      ></button>\n                    </div>\n                  </div>\n\n                  {/* Window Content */}\n                  <div className=\"flex-1 p-4 text-white overflow-auto\">\n                    {activeApp === 'terminal' && (\n                      <div className=\"font-mono text-green-400 space-y-2\">\n                        <div>KangPCode Terminal AI v1.0</div>\n                        <div>Type 'help' for available commands</div>\n                        <div className=\"text-white\">$ _</div>\n                      </div>\n                    )}\n                    \n                    {activeApp === 'resume' && (\n                      <div className=\"text-center space-y-4\">\n                        <h3 className=\"text-xl font-bold\">Resume Generator</h3>\n                        <p>Generate PDF resume dari data KangPCode</p>\n                        <button className=\"bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors\">\n                          Generate PDF\n                        </button>\n                      </div>\n                    )}\n                    \n                    {activeApp === 'projects' && (\n                      <div className=\"space-y-4\">\n                        <h3 className=\"text-xl font-bold\">My Projects</h3>\n                        <div className=\"grid grid-cols-2 gap-4\">\n                          <div className=\"bg-gray-700 p-4 rounded\">\n                            <h4 className=\"font-bold\">Portfolio 3D</h4>\n                            <p className=\"text-sm text-gray-300\">Interactive 3D portfolio website</p>\n                          </div>\n                          <div className=\"bg-gray-700 p-4 rounded\">\n                            <h4 className=\"font-bold\">Langkah Kode</h4>\n                            <p className=\"text-sm text-gray-300\">Educational programming book</p>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                    \n                    {activeApp === 'games' && (\n                      <div className=\"text-center space-y-4\">\n                        <h3 className=\"text-xl font-bold\">Mini Games</h3>\n                        <div className=\"grid grid-cols-3 gap-4\">\n                          <button className=\"bg-green-600 hover:bg-green-700 p-4 rounded transition-colors\">\n                            🐍 Snake\n                          </button>\n                          <button className=\"bg-blue-600 hover:bg-blue-700 p-4 rounded transition-colors\">\n                            ⭕ TicTacToe\n                          </button>\n                          <button className=\"bg-purple-600 hover:bg-purple-700 p-4 rounded transition-colors\">\n                            🧠 Trivia\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                    \n                    {(activeApp === 'vscode' || activeApp === 'browser' || activeApp === 'settings' || activeApp === 'info') && (\n                      <div className=\"text-center space-y-4\">\n                        <h3 className=\"text-xl font-bold\">Coming Soon</h3>\n                        <p>Aplikasi ini sedang dalam pengembangan</p>\n                      </div>\n                    )}\n                  </div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAee,SAAS,YAAY,EAAE,SAAS,EAAE,OAAO,EAAE,cAAc,EAAE,YAAY,EAAE,WAAW,EAAoB;;IACrH,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,QAAQ;+CAAY;oBACxB,eAAe,IAAI;gBACrB;8CAAG;YACH;yCAAO,IAAM,cAAc;;QAC7B;gCAAG,EAAE;IAEL,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,WAAW;gBACb,aAAa;gBACb,MAAM,YAAY;uDAAW;wBAC3B,aAAa;oBACf;sDAAG;gBACH;6CAAO,IAAM,aAAa;;YAC5B;QACF;gCAAG;QAAC;KAAU;IAEd,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,cAAc;QAClB;YAAE,IAAI;YAAY,MAAM;YAAe,MAAM;YAAM,OAAO;YAAgB,QAAQ;QAAe;QACjG;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM;YAAM,OAAO;YAAe,QAAQ;QAAa;QACvF;YAAE,IAAI;YAAY,MAAM;YAAe,MAAM;YAAM,OAAO;QAAgB;QAC1E;YAAE,IAAI;YAAS,MAAM;YAAc,MAAM;YAAM,OAAO;YAAc,QAAQ;QAAY;KACzF;IAED,MAAM,cAAc;QAClB;YAAE,IAAI;YAAY,MAAM;YAAe,MAAM;QAAK;QAClD;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM;QAAK;QAC3C;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAK;QAC7C;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;QAC/C;YAAE,IAAI;YAAQ,MAAM;YAAe,MAAM;QAAK;QAC9C;YAAE,IAAI;YAAS,MAAM;YAAS,MAAM;QAAK;KAC1C;IAED,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;sBACb,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,6LAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,6LAAC;4BAAE,WAAU;sCAAU;;;;;;sCACvB,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;wCAClB,SAAS;4CAAC;4CAAK;4CAAG;yCAAI;oCACxB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO,IAAI;oCACb;mCAVK;;;;;;;;;;;;;;;;;;;;qCAiBf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;;kCAGV,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAY;;;;;;kDAC5B,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAM,WAAW;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAa,WAAW;;;;;;kDACxC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,oBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS;oCACP,IAAI,IAAI,MAAM,EAAE;wCACd,IAAI,MAAM;oCACZ,OAAO;wCACL,aAAa,IAAI,EAAE;oCACrB;gCACF;gCACA,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,6LAAC;wCAAI,WAAW,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,uGAAuG,CAAC;kDAC5I,IAAI,IAAI;;;;;;kDAEX,6LAAC;wCAAK,WAAU;kDAAkC,IAAI,IAAI;;;;;;;+BAfrD,IAAI,EAAE;;;;;;;;;;kCAqBjB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAIhC,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,oBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS,IAAM,aAAa,IAAI,EAAE;wCAClC,WAAW,CAAC,gFAAgF,EAC1F,cAAc,IAAI,EAAE,GAChB,2BACA,sCACJ;wCACF,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;wCACvB,OAAO,IAAI,IAAI;kDAEd,IAAI,IAAI;uCAXJ,IAAI,EAAE;;;;;;;;;;0CAiBjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAK,WAAU;kDAAqB,WAAW;;;;;;;;;;;;;;;;;;kCAKpD,6LAAC,4LAAA,CAAA,kBAAe;kCACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAC/B,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDACb,YAAY,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY;;;;;;sDAElD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;;;;;;8DAClB,6LAAC;oDAAO,WAAU;;;;;;8DAClB,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAU;;;;;;;;;;;;;;;;;;8CAMhB,6LAAC;oCAAI,WAAU;;wCACZ,cAAc,4BACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;8DACL,6LAAC;oDAAI,WAAU;8DAAa;;;;;;;;;;;;wCAI/B,cAAc,0BACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,6LAAC;8DAAE;;;;;;8DACH,6LAAC;oDAAO,WAAU;8DAAoE;;;;;;;;;;;;wCAMzF,cAAc,4BACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAY;;;;;;8EAC1B,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAY;;;;;;8EAC1B,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;wCAM5C,cAAc,yBACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAO,WAAU;sEAAgE;;;;;;sEAGlF,6LAAC;4DAAO,WAAU;sEAA8D;;;;;;sEAGhF,6LAAC;4DAAO,WAAU;sEAAkE;;;;;;;;;;;;;;;;;;wCAOzF,CAAC,cAAc,YAAY,cAAc,aAAa,cAAc,cAAc,cAAc,MAAM,mBACrG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,6LAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3B;GA9QwB;KAAA", "debugId": null}}, {"offset": {"line": 1037, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/TerminalAI.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface TerminalAIProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\ninterface TerminalLine {\n  type: 'input' | 'output' | 'error';\n  content: string;\n  timestamp: Date;\n}\n\nexport default function TerminalAI({ isVisible, onClose }: TerminalAIProps) {\n  const [input, setInput] = useState('');\n  const [history, setHistory] = useState<TerminalLine[]>([]);\n  const [isTyping, setIsTyping] = useState(false);\n  const terminalRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // AI Knowledge Base\n  const knowledgeBase = {\n    'siapa kangpcode': 'KangPCode ad<PERSON><PERSON>, developer dan kreator teknologi Nusantara yang berfokus pada pengembangan aplikasi web modern dan edukasi teknologi.',\n    'pendidikan': 'Informatika – Universitas XYZ, dengan fokus pada pengembangan web dan teknologi modern.',\n    'skill': 'Next.js, TailwindCSS, SQLite, Laravel, Bun, React, PWA, Three.js, TypeScript, Python, Docker, dan teknologi web modern lainnya.',\n    'pengalaman': 'Magang di CV Bintang Gumilang, freelance proyek TI lokal, dan berbagai proyek pengembangan web untuk klien Indonesia.',\n    'hobi': 'Ngoding, mempelajari sejarah tokoh teknologi, menonton anime, menulis, dan berbagi pengetahuan teknologi.',\n    'buku': 'Langkah Kode Nusantara - Perjalanan belajar dan berkarya dalam dunia teknologi Indonesia (GitHub: kangpcode/langkah-kode-nusantara)',\n    'proyek': 'Portfolio 3D Interaktif, Langkah Kode Nusantara, berbagai aplikasi web dengan Next.js dan React, serta proyek edukasi teknologi.',\n    'kontak': 'GitHub: github.com/kangpcode | Email: <EMAIL> | Status: Open for Collaboration',\n    'teknologi': 'Spesialisasi dalam JavaScript/TypeScript ecosystem, React/Next.js, modern CSS frameworks, database design, dan 3D web development.',\n    'visi': 'Membangun ekosistem teknologi Indonesia yang kuat melalui edukasi, open source, dan kolaborasi komunitas developer lokal.',\n  };\n\n  const commands = {\n    'help': 'Perintah yang tersedia:\\n- siapa kangpcode\\n- pendidikan\\n- skill\\n- pengalaman\\n- hobi\\n- buku\\n- proyek\\n- kontak\\n- teknologi\\n- visi\\n- clear\\n- help',\n    'clear': 'CLEAR_TERMINAL',\n    'ls': 'projects/\\nbooks/\\nskills/\\ncontacts/\\nexperience/',\n    'pwd': '/home/<USER>/portfolio',\n    'whoami': 'kangpcode (Dhafa Nazula Permadi)',\n    'date': new Date().toLocaleString('id-ID'),\n    'uname': 'KangPCode Terminal AI v1.0 - Interactive Portfolio Assistant',\n  };\n\n  useEffect(() => {\n    if (isVisible) {\n      setHistory([\n        {\n          type: 'output',\n          content: '🧠 KangPCode Terminal AI v1.0\\nSelamat datang! Ketik \"help\" untuk melihat perintah yang tersedia.\\nAtau tanyakan tentang KangPCode dengan bahasa natural.',\n          timestamp: new Date()\n        }\n      ]);\n      setTimeout(() => {\n        inputRef.current?.focus();\n      }, 100);\n    }\n  }, [isVisible]);\n\n  useEffect(() => {\n    if (terminalRef.current) {\n      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;\n    }\n  }, [history]);\n\n  const processCommand = async (cmd: string) => {\n    const command = cmd.toLowerCase().trim();\n    \n    // Add user input to history\n    setHistory(prev => [...prev, {\n      type: 'input',\n      content: `$ ${cmd}`,\n      timestamp: new Date()\n    }]);\n\n    setIsTyping(true);\n\n    // Simulate AI thinking delay\n    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));\n\n    let response = '';\n\n    // Check exact commands first\n    if (commands[command as keyof typeof commands]) {\n      const result = commands[command as keyof typeof commands];\n      if (result === 'CLEAR_TERMINAL') {\n        setHistory([]);\n        setIsTyping(false);\n        return;\n      }\n      response = result;\n    }\n    // Check knowledge base\n    else if (knowledgeBase[command as keyof typeof knowledgeBase]) {\n      response = `🧠 ${knowledgeBase[command as keyof typeof knowledgeBase]}`;\n    }\n    // Natural language processing (simple keyword matching)\n    else {\n      const keywords = Object.keys(knowledgeBase);\n      const matchedKeyword = keywords.find(keyword => \n        command.includes(keyword) || keyword.includes(command.split(' ')[0])\n      );\n      \n      if (matchedKeyword) {\n        response = `🧠 ${knowledgeBase[matchedKeyword as keyof typeof knowledgeBase]}`;\n      } else if (command.includes('halo') || command.includes('hai') || command.includes('hello')) {\n        response = '👋 Halo! Saya AI Assistant KangPCode. Ada yang bisa saya bantu? Ketik \"help\" untuk melihat perintah yang tersedia.';\n      } else if (command.includes('terima kasih') || command.includes('thanks')) {\n        response = '🙏 Sama-sama! Senang bisa membantu. Ada pertanyaan lain tentang KangPCode?';\n      } else if (command.includes('bye') || command.includes('exit') || command.includes('quit')) {\n        response = '👋 Sampai jumpa! Terima kasih telah menggunakan KangPCode Terminal AI.';\n      } else {\n        response = `❓ Maaf, saya tidak mengerti perintah \"${cmd}\". Ketik \"help\" untuk melihat perintah yang tersedia atau tanyakan tentang KangPCode.`;\n      }\n    }\n\n    // Add AI response to history\n    setHistory(prev => [...prev, {\n      type: 'output',\n      content: response,\n      timestamp: new Date()\n    }]);\n\n    setIsTyping(false);\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (input.trim()) {\n      processCommand(input);\n      setInput('');\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className=\"fixed inset-4 bg-black rounded-lg shadow-2xl border border-green-500/30 overflow-hidden z-50\"\n    >\n      {/* Terminal Header */}\n      <div className=\"h-8 bg-gray-900 flex items-center justify-between px-4 border-b border-green-500/30\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n          <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n          <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n          <span className=\"text-green-400 text-sm font-mono ml-4\">🧠 KangPCode Terminal AI</span>\n        </div>\n        <button\n          onClick={onClose}\n          className=\"text-gray-400 hover:text-white transition-colors\"\n        >\n          ✕\n        </button>\n      </div>\n\n      {/* Terminal Content */}\n      <div \n        ref={terminalRef}\n        className=\"flex-1 p-4 font-mono text-sm text-green-400 bg-black overflow-y-auto h-[calc(100%-8rem)]\"\n      >\n        {history.map((line, index) => (\n          <motion.div\n            key={index}\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            className={`mb-2 ${\n              line.type === 'input' \n                ? 'text-cyan-400' \n                : line.type === 'error' \n                ? 'text-red-400' \n                : 'text-green-400'\n            }`}\n          >\n            <pre className=\"whitespace-pre-wrap font-mono\">{line.content}</pre>\n          </motion.div>\n        ))}\n        \n        {isTyping && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"text-yellow-400 flex items-center space-x-2\"\n          >\n            <span>🧠 AI sedang berpikir</span>\n            <div className=\"flex space-x-1\">\n              {[0, 1, 2].map((i) => (\n                <motion.div\n                  key={i}\n                  className=\"w-1 h-1 bg-yellow-400 rounded-full\"\n                  animate={{\n                    scale: [1, 1.5, 1],\n                    opacity: [0.5, 1, 0.5],\n                  }}\n                  transition={{\n                    duration: 1,\n                    repeat: Infinity,\n                    delay: i * 0.2,\n                  }}\n                />\n              ))}\n            </div>\n          </motion.div>\n        )}\n      </div>\n\n      {/* Terminal Input */}\n      <div className=\"h-12 bg-gray-900 border-t border-green-500/30 flex items-center px-4\">\n        <form onSubmit={handleSubmit} className=\"flex-1 flex items-center space-x-2\">\n          <span className=\"text-cyan-400 font-mono\">$</span>\n          <input\n            ref={inputRef}\n            type=\"text\"\n            value={input}\n            onChange={(e) => setInput(e.target.value)}\n            className=\"flex-1 bg-transparent text-green-400 font-mono outline-none placeholder-gray-500\"\n            placeholder=\"Ketik perintah atau tanyakan tentang KangPCode...\"\n            disabled={isTyping}\n          />\n        </form>\n      </div>\n\n      {/* Terminal Footer */}\n      <div className=\"h-6 bg-gray-800 border-t border-green-500/30 flex items-center justify-between px-4 text-xs text-gray-400\">\n        <span>KangPCode Terminal AI - Interactive Portfolio Assistant</span>\n        <span>Press Ctrl+C to exit</span>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAgBe,SAAS,WAAW,EAAE,SAAS,EAAE,OAAO,EAAmB;;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,oBAAoB;IACpB,MAAM,gBAAgB;QACpB,mBAAmB;QACnB,cAAc;QACd,SAAS;QACT,cAAc;QACd,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,UAAU;QACV,aAAa;QACb,QAAQ;IACV;IAEA,MAAM,WAAW;QACf,QAAQ;QACR,SAAS;QACT,MAAM;QACN,OAAO;QACP,UAAU;QACV,QAAQ,IAAI,OAAO,cAAc,CAAC;QAClC,SAAS;IACX;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,WAAW;gBACb,WAAW;oBACT;wBACE,MAAM;wBACN,SAAS;wBACT,WAAW,IAAI;oBACjB;iBACD;gBACD;4CAAW;wBACT,SAAS,OAAO,EAAE;oBACpB;2CAAG;YACL;QACF;+BAAG;QAAC;KAAU;IAEd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,SAAS,GAAG,YAAY,OAAO,CAAC,YAAY;YAClE;QACF;+BAAG;QAAC;KAAQ;IAEZ,MAAM,iBAAiB,OAAO;QAC5B,MAAM,UAAU,IAAI,WAAW,GAAG,IAAI;QAEtC,4BAA4B;QAC5B,WAAW,CAAA,OAAQ;mBAAI;gBAAM;oBAC3B,MAAM;oBACN,SAAS,CAAC,EAAE,EAAE,KAAK;oBACnB,WAAW,IAAI;gBACjB;aAAE;QAEF,YAAY;QAEZ,6BAA6B;QAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,MAAM,KAAK,MAAM,KAAK;QAEvE,IAAI,WAAW;QAEf,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,QAAiC,EAAE;YAC9C,MAAM,SAAS,QAAQ,CAAC,QAAiC;YACzD,IAAI,WAAW,kBAAkB;gBAC/B,WAAW,EAAE;gBACb,YAAY;gBACZ;YACF;YACA,WAAW;QACb,OAEK,IAAI,aAAa,CAAC,QAAsC,EAAE;YAC7D,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,QAAsC,EAAE;QACzE,OAEK;YACH,MAAM,WAAW,OAAO,IAAI,CAAC;YAC7B,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAA,UACnC,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ,KAAK,CAAC,IAAI,CAAC,EAAE;YAGrE,IAAI,gBAAgB;gBAClB,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,eAA6C,EAAE;YAChF,OAAO,IAAI,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,UAAU;gBAC3F,WAAW;YACb,OAAO,IAAI,QAAQ,QAAQ,CAAC,mBAAmB,QAAQ,QAAQ,CAAC,WAAW;gBACzE,WAAW;YACb,OAAO,IAAI,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,SAAS;gBAC1F,WAAW;YACb,OAAO;gBACL,WAAW,CAAC,sCAAsC,EAAE,IAAI,qFAAqF,CAAC;YAChJ;QACF;QAEA,6BAA6B;QAC7B,WAAW,CAAA,OAAQ;mBAAI;gBAAM;oBAC3B,MAAM;oBACN,SAAS;oBACT,WAAW,IAAI;gBACjB;aAAE;QAEF,YAAY;IACd;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,MAAM,IAAI,IAAI;YAChB,eAAe;YACf,SAAS;QACX;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAC/B,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAwC;;;;;;;;;;;;kCAE1D,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC;gBACC,KAAK;gBACL,WAAU;;oBAET,QAAQ,GAAG,CAAC,CAAC,MAAM,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAW,CAAC,KAAK,EACf,KAAK,IAAI,KAAK,UACV,kBACA,KAAK,IAAI,KAAK,UACd,iBACA,kBACJ;sCAEF,cAAA,6LAAC;gCAAI,WAAU;0CAAiC,KAAK,OAAO;;;;;;2BAXvD;;;;;oBAeR,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,WAAU;;0CAEV,6LAAC;0CAAK;;;;;;0CACN,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,SAAS;4CACP,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;4CAClB,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;wCACxB;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,OAAO,IAAI;wCACb;uCAVK;;;;;;;;;;;;;;;;;;;;;;0BAmBjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;4BAAK,WAAU;sCAA0B;;;;;;sCAC1C,6LAAC;4BACC,KAAK;4BACL,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,WAAU;4BACV,aAAY;4BACZ,UAAU;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAK;;;;;;kCACN,6LAAC;kCAAK;;;;;;;;;;;;;;;;;;AAId;GA3NwB;KAAA", "debugId": null}}, {"offset": {"line": 1408, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/LaptopWindows.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface LaptopWindowsProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\ninterface GitHubRepo {\n  id: number;\n  name: string;\n  description: string;\n  language: string;\n  stargazers_count: number;\n  forks_count: number;\n  html_url: string;\n  updated_at: string;\n}\n\ninterface GitHubUser {\n  login: string;\n  name: string;\n  bio: string;\n  public_repos: number;\n  followers: number;\n  following: number;\n  avatar_url: string;\n  html_url: string;\n}\n\nexport default function LaptopWindows({ isVisible, onClose }: LaptopWindowsProps) {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [isBooting, setIsBooting] = useState(true);\n  const [githubUser, setGithubUser] = useState<GitHubUser | null>(null);\n  const [githubRepos, setGithubRepos] = useState<GitHubRepo[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState<'profile' | 'repos' | 'stats'>('profile');\n\n  // Update time every second\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Boot sequence\n  useEffect(() => {\n    if (isVisible) {\n      setIsBooting(true);\n      const bootTimer = setTimeout(() => {\n        setIsBooting(false);\n        fetchGitHubData();\n      }, 2000);\n      return () => clearTimeout(bootTimer);\n    }\n  }, [isVisible]);\n\n  const fetchGitHubData = async () => {\n    setIsLoading(true);\n    try {\n      // Fetch user data\n      const userResponse = await fetch('https://api.github.com/users/kangpcode');\n      if (userResponse.ok) {\n        const userData = await userResponse.json();\n        setGithubUser(userData);\n      }\n\n      // Fetch repositories\n      const reposResponse = await fetch('https://api.github.com/users/kangpcode/repos?sort=updated&per_page=10');\n      if (reposResponse.ok) {\n        const reposData = await reposResponse.json();\n        setGithubRepos(reposData);\n      }\n    } catch (error) {\n      console.error('Error fetching GitHub data:', error);\n      // Set mock data if API fails\n      setGithubUser({\n        login: 'kangpcode',\n        name: 'Dhafa Nazula Permadi',\n        bio: 'Fullstack Developer & Content Creator | Building the future of Indonesian tech',\n        public_repos: 25,\n        followers: 150,\n        following: 80,\n        avatar_url: '/assets/images/kangpcode-avatar.jpg',\n        html_url: 'https://github.com/kangpcode'\n      });\n      \n      setGithubRepos([\n        {\n          id: 1,\n          name: 'portfolio-3d',\n          description: 'Interactive 3D Portfolio Website with Three.js',\n          language: 'TypeScript',\n          stargazers_count: 45,\n          forks_count: 12,\n          html_url: 'https://github.com/kangpcode/portfolio-3d',\n          updated_at: '2024-01-15T10:30:00Z'\n        },\n        {\n          id: 2,\n          name: 'langkah-kode-nusantara',\n          description: 'Educational programming book for Indonesian developers',\n          language: 'Markdown',\n          stargazers_count: 89,\n          forks_count: 23,\n          html_url: 'https://github.com/kangpcode/langkah-kode-nusantara',\n          updated_at: '2024-01-10T14:20:00Z'\n        }\n      ]);\n    }\n    setIsLoading(false);\n  };\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString('en-US', { \n      hour: '2-digit', \n      minute: '2-digit',\n      hour12: true\n    });\n  };\n\n  const formatDate = (date: Date) => {\n    return date.toLocaleDateString('en-US', { \n      weekday: 'short',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  const getLanguageColor = (language: string) => {\n    const colors: { [key: string]: string } = {\n      'TypeScript': '#3178c6',\n      'JavaScript': '#f1e05a',\n      'Python': '#3572A5',\n      'Java': '#b07219',\n      'C++': '#f34b7d',\n      'HTML': '#e34c26',\n      'CSS': '#1572B6',\n      'Markdown': '#083fa1',\n      'PHP': '#4F5D95'\n    };\n    return colors[language] || '#6b7280';\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50 overflow-hidden\">\n      <AnimatePresence>\n        {isBooting ? (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"flex items-center justify-center h-full bg-blue-600 text-white\"\n          >\n            <div className=\"text-center space-y-4\">\n              <div className=\"text-6xl mb-4\">🪟</div>\n              <h2 className=\"text-2xl font-bold\">Windows 11</h2>\n              <p className=\"text-lg\">Starting GitHub Viewer...</p>\n              <div className=\"flex justify-center space-x-1 mt-4\">\n                {[0, 1, 2, 3, 4].map((i) => (\n                  <motion.div\n                    key={i}\n                    className=\"w-2 h-2 bg-white rounded-full\"\n                    animate={{\n                      scale: [1, 1.2, 1],\n                      opacity: [0.5, 1, 0.5],\n                    }}\n                    transition={{\n                      duration: 1,\n                      repeat: Infinity,\n                      delay: i * 0.1,\n                    }}\n                  />\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        ) : (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"h-full bg-gradient-to-br from-blue-100 to-blue-200 relative\"\n          >\n            {/* Windows Taskbar */}\n            <div className=\"absolute bottom-0 left-0 right-0 h-12 bg-gray-900/95 backdrop-blur-sm flex items-center justify-between px-4 z-10\">\n              {/* Start Button */}\n              <button className=\"flex items-center space-x-2 px-3 py-2 hover:bg-gray-700/50 rounded transition-colors text-white\">\n                <span className=\"text-xl\">🪟</span>\n                <span className=\"font-medium\">Start</span>\n              </button>\n\n              {/* App Icons */}\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-xl text-white\">\n                  🌐\n                </div>\n                <div className=\"w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center text-xl text-white\">\n                  📁\n                </div>\n              </div>\n\n              {/* System Tray */}\n              <div className=\"flex items-center space-x-4 text-white text-sm\">\n                <span>🔊 🔋 📶</span>\n                <div className=\"text-right\">\n                  <div className=\"font-mono\">{formatTime(currentTime)}</div>\n                  <div className=\"text-xs\">{formatDate(currentTime)}</div>\n                </div>\n                <button\n                  onClick={onClose}\n                  className=\"hover:bg-red-600 px-2 py-1 rounded transition-colors\"\n                >\n                  ✕\n                </button>\n              </div>\n            </div>\n\n            {/* Main Content - GitHub Viewer */}\n            <div className=\"h-[calc(100%-3rem)] p-4\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"h-full bg-white rounded-lg shadow-2xl overflow-hidden\"\n              >\n                {/* Browser Header */}\n                <div className=\"h-12 bg-gray-100 border-b flex items-center justify-between px-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n                      <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n                      <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                    </div>\n                    <div className=\"ml-4 bg-gray-200 rounded px-3 py-1 text-sm text-gray-600\">\n                      🔒 github.com/kangpcode\n                    </div>\n                  </div>\n                  <div className=\"text-lg font-bold text-gray-700\">GitHub Profile</div>\n                </div>\n\n                {/* Tab Navigation */}\n                <div className=\"h-10 bg-gray-50 border-b flex items-center px-4\">\n                  {[\n                    { id: 'profile', name: 'Profile', icon: '👤' },\n                    { id: 'repos', name: 'Repositories', icon: '📁' },\n                    { id: 'stats', name: 'Statistics', icon: '📊' }\n                  ].map((tab) => (\n                    <button\n                      key={tab.id}\n                      onClick={() => setActiveTab(tab.id as any)}\n                      className={`flex items-center space-x-2 px-4 py-2 rounded-t transition-colors ${\n                        activeTab === tab.id \n                          ? 'bg-white border-t-2 border-blue-500 text-blue-600' \n                          : 'hover:bg-gray-100 text-gray-600'\n                      }`}\n                    >\n                      <span>{tab.icon}</span>\n                      <span className=\"font-medium\">{tab.name}</span>\n                    </button>\n                  ))}\n                </div>\n\n                {/* Content Area */}\n                <div className=\"flex-1 p-6 overflow-y-auto\">\n                  {isLoading ? (\n                    <div className=\"flex items-center justify-center h-64\">\n                      <div className=\"text-center space-y-4\">\n                        <div className=\"text-4xl\">⏳</div>\n                        <p className=\"text-gray-600\">Loading GitHub data...</p>\n                      </div>\n                    </div>\n                  ) : (\n                    <AnimatePresence mode=\"wait\">\n                      {activeTab === 'profile' && githubUser && (\n                        <motion.div\n                          key=\"profile\"\n                          initial={{ opacity: 0, x: 20 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          exit={{ opacity: 0, x: -20 }}\n                          className=\"space-y-6\"\n                        >\n                          <div className=\"flex items-start space-x-6\">\n                            <div className=\"w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center text-4xl\">\n                              👨‍💻\n                            </div>\n                            <div className=\"flex-1\">\n                              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">{githubUser.name}</h1>\n                              <p className=\"text-xl text-gray-600 mb-4\">@{githubUser.login}</p>\n                              <p className=\"text-gray-700 mb-4\">{githubUser.bio}</p>\n                              <div className=\"flex space-x-6 text-sm text-gray-600\">\n                                <span>👥 {githubUser.followers} followers</span>\n                                <span>👤 {githubUser.following} following</span>\n                                <span>📁 {githubUser.public_repos} repositories</span>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      )}\n\n                      {activeTab === 'repos' && (\n                        <motion.div\n                          key=\"repos\"\n                          initial={{ opacity: 0, x: 20 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          exit={{ opacity: 0, x: -20 }}\n                          className=\"space-y-4\"\n                        >\n                          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Recent Repositories</h2>\n                          {githubRepos.map((repo) => (\n                            <div key={repo.id} className=\"border rounded-lg p-4 hover:shadow-md transition-shadow\">\n                              <div className=\"flex items-start justify-between\">\n                                <div className=\"flex-1\">\n                                  <h3 className=\"text-lg font-semibold text-blue-600 mb-2\">{repo.name}</h3>\n                                  <p className=\"text-gray-700 mb-3\">{repo.description}</p>\n                                  <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                                    <span className=\"flex items-center space-x-1\">\n                                      <div \n                                        className=\"w-3 h-3 rounded-full\" \n                                        style={{ backgroundColor: getLanguageColor(repo.language) }}\n                                      ></div>\n                                      <span>{repo.language}</span>\n                                    </span>\n                                    <span>⭐ {repo.stargazers_count}</span>\n                                    <span>🍴 {repo.forks_count}</span>\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </motion.div>\n                      )}\n\n                      {activeTab === 'stats' && githubUser && (\n                        <motion.div\n                          key=\"stats\"\n                          initial={{ opacity: 0, x: 20 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          exit={{ opacity: 0, x: -20 }}\n                          className=\"space-y-6\"\n                        >\n                          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">GitHub Statistics</h2>\n                          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                            <div className=\"bg-blue-50 p-4 rounded-lg text-center\">\n                              <div className=\"text-2xl font-bold text-blue-600\">{githubUser.public_repos}</div>\n                              <div className=\"text-sm text-gray-600\">Repositories</div>\n                            </div>\n                            <div className=\"bg-green-50 p-4 rounded-lg text-center\">\n                              <div className=\"text-2xl font-bold text-green-600\">{githubUser.followers}</div>\n                              <div className=\"text-sm text-gray-600\">Followers</div>\n                            </div>\n                            <div className=\"bg-purple-50 p-4 rounded-lg text-center\">\n                              <div className=\"text-2xl font-bold text-purple-600\">{githubUser.following}</div>\n                              <div className=\"text-sm text-gray-600\">Following</div>\n                            </div>\n                            <div className=\"bg-orange-50 p-4 rounded-lg text-center\">\n                              <div className=\"text-2xl font-bold text-orange-600\">\n                                {githubRepos.reduce((sum, repo) => sum + repo.stargazers_count, 0)}\n                              </div>\n                              <div className=\"text-sm text-gray-600\">Total Stars</div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      )}\n                    </AnimatePresence>\n                  )}\n                </div>\n              </motion.div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAgCe,SAAS,cAAc,EAAE,SAAS,EAAE,OAAO,EAAsB;;IAC9E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC;IAE1E,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,QAAQ;iDAAY;oBACxB,eAAe,IAAI;gBACrB;gDAAG;YACH;2CAAO,IAAM,cAAc;;QAC7B;kCAAG,EAAE;IAEL,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,WAAW;gBACb,aAAa;gBACb,MAAM,YAAY;yDAAW;wBAC3B,aAAa;wBACb;oBACF;wDAAG;gBACH;+CAAO,IAAM,aAAa;;YAC5B;QACF;kCAAG;QAAC;KAAU;IAEd,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,kBAAkB;YAClB,MAAM,eAAe,MAAM,MAAM;YACjC,IAAI,aAAa,EAAE,EAAE;gBACnB,MAAM,WAAW,MAAM,aAAa,IAAI;gBACxC,cAAc;YAChB;YAEA,qBAAqB;YACrB,MAAM,gBAAgB,MAAM,MAAM;YAClC,IAAI,cAAc,EAAE,EAAE;gBACpB,MAAM,YAAY,MAAM,cAAc,IAAI;gBAC1C,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,6BAA6B;YAC7B,cAAc;gBACZ,OAAO;gBACP,MAAM;gBACN,KAAK;gBACL,cAAc;gBACd,WAAW;gBACX,WAAW;gBACX,YAAY;gBACZ,UAAU;YACZ;YAEA,eAAe;gBACb;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,kBAAkB;oBAClB,aAAa;oBACb,UAAU;oBACV,YAAY;gBACd;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,kBAAkB;oBAClB,aAAa;oBACb,UAAU;oBACV,YAAY;gBACd;aACD;QACH;QACA,aAAa;IACf;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,SAAS;YACT,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAoC;YACxC,cAAc;YACd,cAAc;YACd,UAAU;YACV,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,OAAO;YACP,YAAY;YACZ,OAAO;QACT;QACA,OAAO,MAAM,CAAC,SAAS,IAAI;IAC7B;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;sBACb,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,6LAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,6LAAC;4BAAE,WAAU;sCAAU;;;;;;sCACvB,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;wCAClB,SAAS;4CAAC;4CAAK;4CAAG;yCAAI;oCACxB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO,IAAI;oCACb;mCAVK;;;;;;;;;;;;;;;;;;;;qCAiBf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;;kCAGV,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAIhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAuF;;;;;;kDAGtG,6LAAC;wCAAI,WAAU;kDAAuF;;;;;;;;;;;;0CAMxG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAa,WAAW;;;;;;0DACvC,6LAAC;gDAAI,WAAU;0DAAW,WAAW;;;;;;;;;;;;kDAEvC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,6LAAC;oDAAI,WAAU;8DAA2D;;;;;;;;;;;;sDAI5E,6LAAC;4CAAI,WAAU;sDAAkC;;;;;;;;;;;;8CAInD,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,IAAI;4CAAW,MAAM;4CAAW,MAAM;wCAAK;wCAC7C;4CAAE,IAAI;4CAAS,MAAM;4CAAgB,MAAM;wCAAK;wCAChD;4CAAE,IAAI;4CAAS,MAAM;4CAAc,MAAM;wCAAK;qCAC/C,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;4CAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4CAClC,WAAW,CAAC,kEAAkE,EAC5E,cAAc,IAAI,EAAE,GAChB,sDACA,mCACJ;;8DAEF,6LAAC;8DAAM,IAAI,IAAI;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAe,IAAI,IAAI;;;;;;;2CATlC,IAAI,EAAE;;;;;;;;;;8CAejB,6LAAC;oCAAI,WAAU;8CACZ,0BACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAW;;;;;;8DAC1B,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;6DAIjC,6LAAC,4LAAA,CAAA,kBAAe;wCAAC,MAAK;;4CACnB,cAAc,aAAa,4BAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA+E;;;;;;sEAG9F,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAyC,WAAW,IAAI;;;;;;8EACtE,6LAAC;oEAAE,WAAU;;wEAA6B;wEAAE,WAAW,KAAK;;;;;;;8EAC5D,6LAAC;oEAAE,WAAU;8EAAsB,WAAW,GAAG;;;;;;8EACjD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;gFAAK;gFAAI,WAAW,SAAS;gFAAC;;;;;;;sFAC/B,6LAAC;;gFAAK;gFAAI,WAAW,SAAS;gFAAC;;;;;;;sFAC/B,6LAAC;;gFAAK;gFAAI,WAAW,YAAY;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;;+CAjBpC;;;;;4CAwBP,cAAc,yBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,WAAU;;kEAEV,6LAAC;wDAAG,WAAU;kEAAwC;;;;;;oDACrD,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;4DAAkB,WAAU;sEAC3B,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAA4C,KAAK,IAAI;;;;;;sFACnE,6LAAC;4EAAE,WAAU;sFAAsB,KAAK,WAAW;;;;;;sFACnD,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;;sGACd,6LAAC;4FACC,WAAU;4FACV,OAAO;gGAAE,iBAAiB,iBAAiB,KAAK,QAAQ;4FAAE;;;;;;sGAE5D,6LAAC;sGAAM,KAAK,QAAQ;;;;;;;;;;;;8FAEtB,6LAAC;;wFAAK;wFAAG,KAAK,gBAAgB;;;;;;;8FAC9B,6LAAC;;wFAAK;wFAAI,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;2DAdxB,KAAK,EAAE;;;;;;+CARf;;;;;4CA+BP,cAAc,WAAW,4BACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,WAAU;;kEAEV,6LAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAoC,WAAW,YAAY;;;;;;kFAC1E,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAqC,WAAW,SAAS;;;;;;kFACxE,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAsC,WAAW,SAAS;;;;;;kFACzE,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,gBAAgB,EAAE;;;;;;kFAElE,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;+CAxBvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuC9B;GAzVwB;KAAA", "debugId": null}}, {"offset": {"line": 2336, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/lib/api.ts"], "sourcesContent": ["// API service layer for frontend-backend communication\n\ninterface ApiResponse<T> {\n  data?: T;\n  error?: string;\n  success: boolean;\n}\n\nclass ApiService {\n  private baseUrl: string;\n\n  constructor() {\n    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || '';\n  }\n\n  private async request<T>(\n    endpoint: string,\n    options: RequestInit = {}\n  ): Promise<ApiResponse<T>> {\n    try {\n      const url = `${this.baseUrl}/api${endpoint}`;\n      const response = await fetch(url, {\n        headers: {\n          'Content-Type': 'application/json',\n          ...options.headers,\n        },\n        ...options,\n      });\n\n      if (!response.ok) {\n        const errorData = await response.json().catch(() => ({}));\n        return {\n          success: false,\n          error: errorData.error || `HTTP ${response.status}: ${response.statusText}`,\n        };\n      }\n\n      const data = await response.json();\n      return {\n        success: true,\n        data,\n      };\n    } catch (error) {\n      return {\n        success: false,\n        error: error instanceof Error ? error.message : 'Unknown error occurred',\n      };\n    }\n  }\n\n  // Biodata API\n  async getBiodata() {\n    return this.request<any>('/biodata');\n  }\n\n  async updateBiodata(data: any) {\n    return this.request<any>('/biodata', {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    });\n  }\n\n  // Projects API\n  async getProjects(params?: { featured?: boolean; status?: string; limit?: number }) {\n    const searchParams = new URLSearchParams();\n    if (params?.featured) searchParams.set('featured', 'true');\n    if (params?.status) searchParams.set('status', params.status);\n    if (params?.limit) searchParams.set('limit', params.limit.toString());\n\n    const query = searchParams.toString();\n    return this.request<any[]>(`/projects${query ? `?${query}` : ''}`);\n  }\n\n  async createProject(data: any) {\n    return this.request<any>('/projects', {\n      method: 'POST',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async updateProject(data: any) {\n    return this.request<any>('/projects', {\n      method: 'PUT',\n      body: JSON.stringify(data),\n    });\n  }\n\n  async deleteProject(id: number) {\n    return this.request<any>(`/projects?id=${id}`, {\n      method: 'DELETE',\n    });\n  }\n\n  // Analytics API\n  async trackEvent(event: string, target: string, data?: any) {\n    return this.request<any>('/analytics', {\n      method: 'POST',\n      body: JSON.stringify({ event, target, data }),\n    });\n  }\n\n  async getAnalytics(params?: { limit?: number; event?: string; target?: string }) {\n    const searchParams = new URLSearchParams();\n    if (params?.limit) searchParams.set('limit', params.limit.toString());\n    if (params?.event) searchParams.set('event', params.event);\n    if (params?.target) searchParams.set('target', params.target);\n\n    const query = searchParams.toString();\n    return this.request<any>(`/analytics${query ? `?${query}` : ''}`);\n  }\n\n  // Game Scores API\n  async getGameScores(game?: string, limit?: number) {\n    const searchParams = new URLSearchParams();\n    if (game) searchParams.set('game', game);\n    if (limit) searchParams.set('limit', limit.toString());\n\n    const query = searchParams.toString();\n    return this.request<any[]>(`/game-scores${query ? `?${query}` : ''}`);\n  }\n\n  async saveGameScore(game: string, score: number, playerName?: string, data?: any) {\n    return this.request<any>('/game-scores', {\n      method: 'POST',\n      body: JSON.stringify({ game, score, playerName, data }),\n    });\n  }\n\n  // GitHub API (proxy through our backend)\n  async getGitHubRepos(username: string = 'kangpcode') {\n    return this.request<any[]>(`/github/repos?username=${username}`);\n  }\n\n  async getGitHubUser(username: string = 'kangpcode') {\n    return this.request<any>(`/github/user?username=${username}`);\n  }\n\n  // Book API\n  async getBookData() {\n    return this.request<any>('/book');\n  }\n\n  // Scientists API\n  async getScientists(category?: string) {\n    const query = category ? `?category=${category}` : '';\n    return this.request<any[]>(`/scientists${query}`);\n  }\n\n  // Figures API\n  async getFigures() {\n    return this.request<any[]>('/figures');\n  }\n\n  // Settings API\n  async getSettings() {\n    return this.request<any[]>('/settings');\n  }\n\n  async updateSetting(key: string, value: string) {\n    return this.request<any>('/settings', {\n      method: 'PUT',\n      body: JSON.stringify({ key, value }),\n    });\n  }\n}\n\n// Export singleton instance\nexport const apiService = new ApiService();\n\n// React hooks for API calls\nexport function useApi() {\n  return apiService;\n}\n\n// Specific hooks for common operations\nexport function useBiodata() {\n  const api = useApi();\n  \n  const getBiodata = async () => {\n    const response = await api.getBiodata();\n    if (!response.success) {\n      console.error('Failed to fetch biodata:', response.error);\n      return null;\n    }\n    return response.data;\n  };\n\n  const updateBiodata = async (data: any) => {\n    const response = await api.updateBiodata(data);\n    if (!response.success) {\n      console.error('Failed to update biodata:', response.error);\n      return false;\n    }\n    return true;\n  };\n\n  return { getBiodata, updateBiodata };\n}\n\nexport function useProjects() {\n  const api = useApi();\n  \n  const getProjects = async (params?: { featured?: boolean; status?: string; limit?: number }) => {\n    const response = await api.getProjects(params);\n    if (!response.success) {\n      console.error('Failed to fetch projects:', response.error);\n      return [];\n    }\n    return response.data || [];\n  };\n\n  const createProject = async (data: any) => {\n    const response = await api.createProject(data);\n    if (!response.success) {\n      console.error('Failed to create project:', response.error);\n      return false;\n    }\n    return true;\n  };\n\n  return { getProjects, createProject };\n}\n\nexport function useAnalytics() {\n  const api = useApi();\n  \n  const trackEvent = async (event: string, target: string, data?: any) => {\n    // Don't block UI for analytics\n    api.trackEvent(event, target, data).catch(error => {\n      console.warn('Analytics tracking failed:', error);\n    });\n  };\n\n  const getAnalytics = async (params?: { limit?: number; event?: string; target?: string }) => {\n    const response = await api.getAnalytics(params);\n    if (!response.success) {\n      console.error('Failed to fetch analytics:', response.error);\n      return null;\n    }\n    return response.data;\n  };\n\n  return { trackEvent, getAnalytics };\n}\n\nexport function useGameScores() {\n  const api = useApi();\n  \n  const getScores = async (game?: string, limit?: number) => {\n    const response = await api.getGameScores(game, limit);\n    if (!response.success) {\n      console.error('Failed to fetch game scores:', response.error);\n      return [];\n    }\n    return response.data || [];\n  };\n\n  const saveScore = async (game: string, score: number, playerName?: string, data?: any) => {\n    const response = await api.saveGameScore(game, score, playerName, data);\n    if (!response.success) {\n      console.error('Failed to save game score:', response.error);\n      return false;\n    }\n    return true;\n  };\n\n  return { getScores, saveScore };\n}\n\nexport function useGitHub() {\n  const api = useApi();\n  \n  const getRepos = async (username?: string) => {\n    const response = await api.getGitHubRepos(username);\n    if (!response.success) {\n      console.error('Failed to fetch GitHub repos:', response.error);\n      return [];\n    }\n    return response.data || [];\n  };\n\n  const getUser = async (username?: string) => {\n    const response = await api.getGitHubUser(username);\n    if (!response.success) {\n      console.error('Failed to fetch GitHub user:', response.error);\n      return null;\n    }\n    return response.data;\n  };\n\n  return { getRepos, getUser };\n}\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;;;;;;AAYpC;;AAJnB,MAAM;IACI,QAAgB;IAExB,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG,gKAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;IACpD;IAEA,MAAc,QACZ,QAAgB,EAChB,UAAuB,CAAC,CAAC,EACA;QACzB,IAAI;YACF,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,UAAU;YAC5C,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC,SAAS;oBACP,gBAAgB;oBAChB,GAAG,QAAQ,OAAO;gBACpB;gBACA,GAAG,OAAO;YACZ;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;gBACvD,OAAO;oBACL,SAAS;oBACT,OAAO,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;gBAC7E;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,OAAO;gBACL,SAAS;gBACT;YACF;QACF,EAAE,OAAO,OAAO;YACd,OAAO;gBACL,SAAS;gBACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YAClD;QACF;IACF;IAEA,cAAc;IACd,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,OAAO,CAAM;IAC3B;IAEA,MAAM,cAAc,IAAS,EAAE;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAM,YAAY;YACnC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,eAAe;IACf,MAAM,YAAY,MAAgE,EAAE;QAClF,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,UAAU,aAAa,GAAG,CAAC,YAAY;QACnD,IAAI,QAAQ,QAAQ,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;QAC5D,IAAI,QAAQ,OAAO,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QAElE,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACnE;IAEA,MAAM,cAAc,IAAS,EAAE;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAM,aAAa;YACpC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,IAAS,EAAE;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAM,aAAa;YACpC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;QACvB;IACF;IAEA,MAAM,cAAc,EAAU,EAAE;QAC9B,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,aAAa,EAAE,IAAI,EAAE;YAC7C,QAAQ;QACV;IACF;IAEA,gBAAgB;IAChB,MAAM,WAAW,KAAa,EAAE,MAAc,EAAE,IAAU,EAAE;QAC1D,OAAO,IAAI,CAAC,OAAO,CAAM,cAAc;YACrC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAO;gBAAQ;YAAK;QAC7C;IACF;IAEA,MAAM,aAAa,MAA4D,EAAE;QAC/E,MAAM,eAAe,IAAI;QACzB,IAAI,QAAQ,OAAO,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QAClE,IAAI,QAAQ,OAAO,aAAa,GAAG,CAAC,SAAS,OAAO,KAAK;QACzD,IAAI,QAAQ,QAAQ,aAAa,GAAG,CAAC,UAAU,OAAO,MAAM;QAE5D,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IAClE;IAEA,kBAAkB;IAClB,MAAM,cAAc,IAAa,EAAE,KAAc,EAAE;QACjD,MAAM,eAAe,IAAI;QACzB,IAAI,MAAM,aAAa,GAAG,CAAC,QAAQ;QACnC,IAAI,OAAO,aAAa,GAAG,CAAC,SAAS,MAAM,QAAQ;QAEnD,MAAM,QAAQ,aAAa,QAAQ;QACnC,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC,EAAE,OAAO,GAAG,IAAI;IACtE;IAEA,MAAM,cAAc,IAAY,EAAE,KAAa,EAAE,UAAmB,EAAE,IAAU,EAAE;QAChF,OAAO,IAAI,CAAC,OAAO,CAAM,gBAAgB;YACvC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAM;gBAAO;gBAAY;YAAK;QACvD;IACF;IAEA,yCAAyC;IACzC,MAAM,eAAe,WAAmB,WAAW,EAAE;QACnD,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,uBAAuB,EAAE,UAAU;IACjE;IAEA,MAAM,cAAc,WAAmB,WAAW,EAAE;QAClD,OAAO,IAAI,CAAC,OAAO,CAAM,CAAC,sBAAsB,EAAE,UAAU;IAC9D;IAEA,WAAW;IACX,MAAM,cAAc;QAClB,OAAO,IAAI,CAAC,OAAO,CAAM;IAC3B;IAEA,iBAAiB;IACjB,MAAM,cAAc,QAAiB,EAAE;QACrC,MAAM,QAAQ,WAAW,CAAC,UAAU,EAAE,UAAU,GAAG;QACnD,OAAO,IAAI,CAAC,OAAO,CAAQ,CAAC,WAAW,EAAE,OAAO;IAClD;IAEA,cAAc;IACd,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,OAAO,CAAQ;IAC7B;IAEA,eAAe;IACf,MAAM,cAAc;QAClB,OAAO,IAAI,CAAC,OAAO,CAAQ;IAC7B;IAEA,MAAM,cAAc,GAAW,EAAE,KAAa,EAAE;QAC9C,OAAO,IAAI,CAAC,OAAO,CAAM,aAAa;YACpC,QAAQ;YACR,MAAM,KAAK,SAAS,CAAC;gBAAE;gBAAK;YAAM;QACpC;IACF;AACF;AAGO,MAAM,aAAa,IAAI;AAGvB,SAAS;IACd,OAAO;AACT;AAGO,SAAS;;IACd,MAAM,MAAM;IAEZ,MAAM,aAAa;QACjB,MAAM,WAAW,MAAM,IAAI,UAAU;QACrC,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,QAAQ,KAAK,CAAC,4BAA4B,SAAS,KAAK;YACxD,OAAO;QACT;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,gBAAgB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,aAAa,CAAC;QACzC,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,QAAQ,KAAK,CAAC,6BAA6B,SAAS,KAAK;YACzD,OAAO;QACT;QACA,OAAO;IACT;IAEA,OAAO;QAAE;QAAY;IAAc;AACrC;GAtBgB;;QACF;;;AAuBP,SAAS;;IACd,MAAM,MAAM;IAEZ,MAAM,cAAc,OAAO;QACzB,MAAM,WAAW,MAAM,IAAI,WAAW,CAAC;QACvC,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,QAAQ,KAAK,CAAC,6BAA6B,SAAS,KAAK;YACzD,OAAO,EAAE;QACX;QACA,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA,MAAM,gBAAgB,OAAO;QAC3B,MAAM,WAAW,MAAM,IAAI,aAAa,CAAC;QACzC,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,QAAQ,KAAK,CAAC,6BAA6B,SAAS,KAAK;YACzD,OAAO;QACT;QACA,OAAO;IACT;IAEA,OAAO;QAAE;QAAa;IAAc;AACtC;IAtBgB;;QACF;;;AAuBP,SAAS;;IACd,MAAM,MAAM;IAEZ,MAAM,aAAa,OAAO,OAAe,QAAgB;QACvD,+BAA+B;QAC/B,IAAI,UAAU,CAAC,OAAO,QAAQ,MAAM,KAAK,CAAC,CAAA;YACxC,QAAQ,IAAI,CAAC,8BAA8B;QAC7C;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,WAAW,MAAM,IAAI,YAAY,CAAC;QACxC,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,QAAQ,KAAK,CAAC,8BAA8B,SAAS,KAAK;YAC1D,OAAO;QACT;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;QAAE;QAAY;IAAa;AACpC;IApBgB;;QACF;;;AAqBP,SAAS;;IACd,MAAM,MAAM;IAEZ,MAAM,YAAY,OAAO,MAAe;QACtC,MAAM,WAAW,MAAM,IAAI,aAAa,CAAC,MAAM;QAC/C,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,QAAQ,KAAK,CAAC,gCAAgC,SAAS,KAAK;YAC5D,OAAO,EAAE;QACX;QACA,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA,MAAM,YAAY,OAAO,MAAc,OAAe,YAAqB;QACzE,MAAM,WAAW,MAAM,IAAI,aAAa,CAAC,MAAM,OAAO,YAAY;QAClE,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,QAAQ,KAAK,CAAC,8BAA8B,SAAS,KAAK;YAC1D,OAAO;QACT;QACA,OAAO;IACT;IAEA,OAAO;QAAE;QAAW;IAAU;AAChC;IAtBgB;;QACF;;;AAuBP,SAAS;;IACd,MAAM,MAAM;IAEZ,MAAM,WAAW,OAAO;QACtB,MAAM,WAAW,MAAM,IAAI,cAAc,CAAC;QAC1C,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,QAAQ,KAAK,CAAC,iCAAiC,SAAS,KAAK;YAC7D,OAAO,EAAE;QACX;QACA,OAAO,SAAS,IAAI,IAAI,EAAE;IAC5B;IAEA,MAAM,UAAU,OAAO;QACrB,MAAM,WAAW,MAAM,IAAI,aAAa,CAAC;QACzC,IAAI,CAAC,SAAS,OAAO,EAAE;YACrB,QAAQ,KAAK,CAAC,gCAAgC,SAAS,KAAK;YAC5D,OAAO;QACT;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,OAAO;QAAE;QAAU;IAAQ;AAC7B;IAtBgB;;QACF", "debugId": null}}, {"offset": {"line": 2655, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/BookKangPCode.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useApi } from '@/lib/api';\nimport { useAnalytics } from '@/lib/analytics';\n\ninterface BookKangPCodeProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\nexport default function BookKangPCode({ isVisible, onClose }: BookKangPCodeProps) {\n  const [currentPage, setCurrentPage] = useState(0);\n  const [isFlipping, setIsFlipping] = useState(false);\n  const [bookData, setBookData] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const api = useApi();\n  const { trackClick, trackPageView } = useAnalytics();\n\n  // Fetch book data from API\n  useEffect(() => {\n    if (isVisible) {\n      fetchBookData();\n      trackPageView('book');\n    }\n  }, [isVisible]);\n\n  const fetchBookData = async () => {\n    try {\n      setLoading(true);\n      const response = await api.request<any>('/book');\n      if (response.success) {\n        setBookData(response.data);\n      }\n    } catch (error) {\n      console.error('Failed to fetch book data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Fallback data if API fails\n  const defaultBookData = {\n    title: \"Langkah Kode Nusantara\",\n    subtitle: \"Perjalanan Belajar & Berkarya dalam Dunia Teknologi Indonesia\",\n    author: \"Dhafa Nazula Permadi (KangPCode)\",\n    description: \"Sebuah panduan komprehensif untuk developer Indonesia yang ingin membangun karir di dunia teknologi dengan tetap mengangkat nilai-nilai lokal dan berkontribusi untuk kemajuan bangsa.\",\n    githubUrl: \"https://github.com/kangpcode/langkah-kode-nusantara\",\n    pdfUrl: \"/assets/book/langkah-kode-nusantara-sample.pdf\",\n    coverImage: \"/assets/images/book/cover.jpg\",\n    chapters: [\n      {\n        number: 1,\n        title: \"Pengenalan Dunia Teknologi Indonesia\",\n        description: \"Sejarah perkembangan teknologi di Indonesia dan peluang masa depan\"\n      },\n      {\n        number: 2,\n        title: \"Dasar-Dasar Programming\",\n        description: \"Fundamental programming dengan pendekatan yang mudah dipahami\"\n      },\n      {\n        number: 3,\n        title: \"Web Development Modern\",\n        description: \"Membangun aplikasi web dengan teknologi terkini\"\n      },\n      {\n        number: 4,\n        title: \"Mobile Development\",\n        description: \"Pengembangan aplikasi mobile untuk platform Android dan iOS\"\n      },\n      {\n        number: 5,\n        title: \"DevOps & Deployment\",\n        description: \"Praktik terbaik dalam deployment dan maintenance aplikasi\"\n      },\n      {\n        number: 6,\n        title: \"Membangun Karir Tech di Indonesia\",\n        description: \"Strategi membangun karir teknologi yang berkelanjutan\"\n      }\n    ],\n    skills: [\n      \"JavaScript & TypeScript\",\n      \"React & Next.js\",\n      \"Node.js & Express\",\n      \"Python & Django\",\n      \"Database Design\",\n      \"Docker & Kubernetes\",\n      \"AWS & Cloud Computing\",\n      \"Git & Version Control\",\n      \"Testing & Quality Assurance\",\n      \"UI/UX Design Principles\"\n    ],\n    stats: {\n      pages: 350,\n      chapters: 6,\n      codeExamples: 150,\n      projects: 12\n    }\n  };\n\n  const currentBookData = bookData || defaultBookData;\n\n  const handlePageChange = (pageIndex: number) => {\n    setCurrentPage(pageIndex);\n    trackClick('book_page_change', { page: pageIndex });\n  };\n\n  const handlePrevPage = () => {\n    const newPage = Math.max(0, currentPage - 1);\n    setCurrentPage(newPage);\n    trackClick('book_prev_page', { page: newPage });\n  };\n\n  const handleNextPage = () => {\n    const newPage = Math.min(pages.length - 1, currentPage + 1);\n    setCurrentPage(newPage);\n    trackClick('book_next_page', { page: newPage });\n  };\n\n  const handleDownload = () => {\n    trackClick('book_download', { format: 'pdf' });\n  };\n\n  const handleGitHub = () => {\n    trackClick('book_github');\n  };\n\n  const pages = [\n    // Cover Page\n    {\n      type: 'cover',\n      content: (\n        <div className=\"h-full bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white flex flex-col justify-center items-center p-8 text-center\">\n          <div className=\"text-6xl mb-6\">📘</div>\n          <h1 className=\"text-4xl font-bold mb-4\">{currentBookData.title}</h1>\n          <h2 className=\"text-xl mb-6 text-blue-200\">{currentBookData.subtitle}</h2>\n          <p className=\"text-lg mb-8\">oleh {currentBookData.author}</p>\n          <div className=\"grid grid-cols-2 gap-4 text-sm\">\n            <div className=\"bg-white/10 p-3 rounded\">\n              <div className=\"text-2xl font-bold\">{currentBookData.stats.pages}</div>\n              <div>Halaman</div>\n            </div>\n            <div className=\"bg-white/10 p-3 rounded\">\n              <div className=\"text-2xl font-bold\">{currentBookData.stats.chapters}</div>\n              <div>Bab</div>\n            </div>\n            <div className=\"bg-white/10 p-3 rounded\">\n              <div className=\"text-2xl font-bold\">{currentBookData.stats.codeExamples}</div>\n              <div>Contoh Kode</div>\n            </div>\n            <div className=\"bg-white/10 p-3 rounded\">\n              <div className=\"text-2xl font-bold\">{currentBookData.stats.projects}</div>\n              <div>Proyek</div>\n            </div>\n          </div>\n        </div>\n      )\n    },\n    // About Page\n    {\n      type: 'about',\n      content: (\n        <div className=\"h-full bg-white p-8 overflow-y-auto\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">Tentang Buku</h2>\n          <p className=\"text-lg text-gray-700 mb-6 leading-relaxed\">\n            {bookData.description}\n          </p>\n          \n          <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">Mengapa Buku Ini?</h3>\n          <ul className=\"list-disc list-inside text-gray-700 space-y-2 mb-6\">\n            <li>Pendekatan pembelajaran yang disesuaikan dengan konteks Indonesia</li>\n            <li>Studi kasus dari startup dan perusahaan teknologi lokal</li>\n            <li>Panduan praktis membangun portfolio yang menarik</li>\n            <li>Tips networking dan membangun personal branding</li>\n            <li>Strategi menghadapi tantangan industri teknologi Indonesia</li>\n          </ul>\n\n          <h3 className=\"text-2xl font-bold text-gray-900 mb-4\">Target Pembaca</h3>\n          <ul className=\"list-disc list-inside text-gray-700 space-y-2\">\n            <li>Mahasiswa informatika dan teknik komputer</li>\n            <li>Fresh graduate yang ingin berkarir di bidang teknologi</li>\n            <li>Developer yang ingin meningkatkan skill dan karir</li>\n            <li>Entrepreneur yang ingin membangun startup teknologi</li>\n            <li>Siapa saja yang tertarik dengan dunia teknologi Indonesia</li>\n          </ul>\n        </div>\n      )\n    },\n    // Chapters Page\n    {\n      type: 'chapters',\n      content: (\n        <div className=\"h-full bg-white p-8 overflow-y-auto\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">Daftar Isi</h2>\n          <div className=\"space-y-4\">\n            {currentBookData.chapters.map((chapter: any) => (\n              <div key={chapter.number} className=\"border-l-4 border-blue-500 pl-4 py-2\">\n                <h3 className=\"text-xl font-semibold text-gray-900\">\n                  Bab {chapter.number}: {chapter.title}\n                </h3>\n                <p className=\"text-gray-600 mt-1\">{chapter.description}</p>\n              </div>\n            ))}\n          </div>\n        </div>\n      )\n    },\n    // Skills Page\n    {\n      type: 'skills',\n      content: (\n        <div className=\"h-full bg-white p-8 overflow-y-auto\">\n          <h2 className=\"text-3xl font-bold text-gray-900 mb-6\">Teknologi yang Dibahas</h2>\n          <div className=\"grid grid-cols-2 gap-4\">\n            {currentBookData.skills.map((skill: string, index: number) => (\n              <motion.div\n                key={skill}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1 }}\n                className=\"bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200\"\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <div className=\"w-3 h-3 bg-blue-500 rounded-full\"></div>\n                  <span className=\"font-medium text-gray-900\">{skill}</span>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n          \n          <div className=\"mt-8 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200\">\n            <h3 className=\"text-xl font-bold text-gray-900 mb-3\">💡 Bonus Content</h3>\n            <ul className=\"list-disc list-inside text-gray-700 space-y-1\">\n              <li>Template CV dan Portfolio untuk Developer</li>\n              <li>Cheat Sheet untuk Interview Technical</li>\n              <li>Resource Learning Path yang Terstruktur</li>\n              <li>Komunitas Developer Indonesia</li>\n            </ul>\n          </div>\n        </div>\n      )\n    }\n  ];\n\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className=\"fixed inset-4 bg-white rounded-lg shadow-2xl border overflow-hidden z-50\"\n    >\n      {/* Book Header */}\n      <div className=\"h-16 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between px-6 text-white\">\n        <div className=\"flex items-center space-x-3\">\n          <span className=\"text-2xl\">📘</span>\n          <div>\n            <h2 className=\"font-bold\">{bookData.title}</h2>\n            <p className=\"text-sm text-blue-100\">by {bookData.author}</p>\n          </div>\n        </div>\n        <button\n          onClick={onClose}\n          className=\"hover:bg-white/20 p-2 rounded transition-colors\"\n        >\n          ✕\n        </button>\n      </div>\n\n      {/* Book Content */}\n      <div className=\"flex h-[calc(100%-4rem)]\">\n        {/* Page Content */}\n        <div className=\"flex-1\">\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={currentPage}\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -20 }}\n              className=\"h-full\"\n            >\n              {pages[currentPage]?.content}\n            </motion.div>\n          </AnimatePresence>\n        </div>\n      </div>\n\n      {/* Book Navigation */}\n      <div className=\"h-16 bg-gray-100 border-t flex items-center justify-between px-6\">\n        <div className=\"flex space-x-2\">\n          {pages.map((_, index) => (\n            <button\n              key={index}\n              onClick={() => setCurrentPage(index)}\n              className={`w-3 h-3 rounded-full transition-colors ${\n                currentPage === index ? 'bg-blue-600' : 'bg-gray-300 hover:bg-gray-400'\n              }`}\n            />\n          ))}\n        </div>\n\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}\n            disabled={currentPage === 0}\n            className=\"px-4 py-2 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed rounded transition-colors\"\n          >\n            ← Prev\n          </button>\n          \n          <span className=\"text-sm text-gray-600\">\n            {currentPage + 1} / {pages.length}\n          </span>\n          \n          <button\n            onClick={() => setCurrentPage(Math.min(pages.length - 1, currentPage + 1))}\n            disabled={currentPage === pages.length - 1}\n            className=\"px-4 py-2 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed rounded transition-colors\"\n          >\n            Next →\n          </button>\n        </div>\n\n        <div className=\"flex space-x-2\">\n          <a\n            href={bookData.githubUrl}\n            target=\"_blank\"\n            rel=\"noopener noreferrer\"\n            className=\"px-4 py-2 bg-gray-900 hover:bg-gray-800 text-white rounded transition-colors\"\n          >\n            📁 GitHub\n          </a>\n          <a\n            href={bookData.pdfUrl}\n            download\n            className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors\"\n          >\n            📄 Download PDF\n          </a>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAYe,SAAS,cAAc,EAAE,SAAS,EAAE,OAAO,EAAsB;;IAC9E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,MAAM,CAAA,GAAA,6GAAA,CAAA,SAAM,AAAD;IACjB,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD;IAEjD,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,WAAW;gBACb;gBACA,cAAc;YAChB;QACF;kCAAG;QAAC;KAAU;IAEd,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,IAAI,OAAO,CAAM;YACxC,IAAI,SAAS,OAAO,EAAE;gBACpB,YAAY,SAAS,IAAI;YAC3B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,WAAW;QACb;IACF;IAEA,6BAA6B;IAC7B,MAAM,kBAAkB;QACtB,OAAO;QACP,UAAU;QACV,QAAQ;QACR,aAAa;QACb,WAAW;QACX,QAAQ;QACR,YAAY;QACZ,UAAU;YACR;gBACE,QAAQ;gBACR,OAAO;gBACP,aAAa;YACf;YACA;gBACE,QAAQ;gBACR,OAAO;gBACP,aAAa;YACf;YACA;gBACE,QAAQ;gBACR,OAAO;gBACP,aAAa;YACf;YACA;gBACE,QAAQ;gBACR,OAAO;gBACP,aAAa;YACf;YACA;gBACE,QAAQ;gBACR,OAAO;gBACP,aAAa;YACf;YACA;gBACE,QAAQ;gBACR,OAAO;gBACP,aAAa;YACf;SACD;QACD,QAAQ;YACN;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,OAAO;YACL,OAAO;YACP,UAAU;YACV,cAAc;YACd,UAAU;QACZ;IACF;IAEA,MAAM,kBAAkB,YAAY;IAEpC,MAAM,mBAAmB,CAAC;QACxB,eAAe;QACf,WAAW,oBAAoB;YAAE,MAAM;QAAU;IACnD;IAEA,MAAM,iBAAiB;QACrB,MAAM,UAAU,KAAK,GAAG,CAAC,GAAG,cAAc;QAC1C,eAAe;QACf,WAAW,kBAAkB;YAAE,MAAM;QAAQ;IAC/C;IAEA,MAAM,iBAAiB;QACrB,MAAM,UAAU,KAAK,GAAG,CAAC,MAAM,MAAM,GAAG,GAAG,cAAc;QACzD,eAAe;QACf,WAAW,kBAAkB;YAAE,MAAM;QAAQ;IAC/C;IAEA,MAAM,iBAAiB;QACrB,WAAW,iBAAiB;YAAE,QAAQ;QAAM;IAC9C;IAEA,MAAM,eAAe;QACnB,WAAW;IACb;IAEA,MAAM,QAAQ;QACZ,aAAa;QACb;YACE,MAAM;YACN,uBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,6LAAC;wBAAG,WAAU;kCAA2B,gBAAgB,KAAK;;;;;;kCAC9D,6LAAC;wBAAG,WAAU;kCAA8B,gBAAgB,QAAQ;;;;;;kCACpE,6LAAC;wBAAE,WAAU;;4BAAe;4BAAM,gBAAgB,MAAM;;;;;;;kCACxD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAsB,gBAAgB,KAAK,CAAC,KAAK;;;;;;kDAChE,6LAAC;kDAAI;;;;;;;;;;;;0CAEP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAsB,gBAAgB,KAAK,CAAC,QAAQ;;;;;;kDACnE,6LAAC;kDAAI;;;;;;;;;;;;0CAEP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAsB,gBAAgB,KAAK,CAAC,YAAY;;;;;;kDACvE,6LAAC;kDAAI;;;;;;;;;;;;0CAEP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAsB,gBAAgB,KAAK,CAAC,QAAQ;;;;;;kDACnE,6LAAC;kDAAI;;;;;;;;;;;;;;;;;;;;;;;;QAKf;QACA,aAAa;QACb;YACE,MAAM;YACN,uBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCACV,SAAS,WAAW;;;;;;kCAGvB,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;kCAGN,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAG,WAAU;;0CACZ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;0CACJ,6LAAC;0CAAG;;;;;;;;;;;;;;;;;;QAIZ;QACA,gBAAgB;QAChB;YACE,MAAM;YACN,uBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,QAAQ,CAAC,GAAG,CAAC,CAAC,wBAC7B,6LAAC;gCAAyB,WAAU;;kDAClC,6LAAC;wCAAG,WAAU;;4CAAsC;4CAC7C,QAAQ,MAAM;4CAAC;4CAAG,QAAQ,KAAK;;;;;;;kDAEtC,6LAAC;wCAAE,WAAU;kDAAsB,QAAQ,WAAW;;;;;;;+BAJ9C,QAAQ,MAAM;;;;;;;;;;;;;;;;QAUlC;QACA,cAAc;QACd;YACE,MAAM;YACN,uBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAI,WAAU;kCACZ,gBAAgB,MAAM,CAAC,GAAG,CAAC,CAAC,OAAe,sBAC1C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAI;gCACjC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAK,WAAU;sDAA6B;;;;;;;;;;;;+BAR1C;;;;;;;;;;kCAcX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;kDACJ,6LAAC;kDAAG;;;;;;;;;;;;;;;;;;;;;;;;QAKd;KACD;IAED,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAC/B,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAW;;;;;;0CAC3B,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAa,SAAS,KAAK;;;;;;kDACzC,6LAAC;wCAAE,WAAU;;4CAAwB;4CAAI,SAAS,MAAM;;;;;;;;;;;;;;;;;;;kCAG5D,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;wBAAC,MAAK;kCACpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAET,KAAK,CAAC,YAAY,EAAE;2BANhB;;;;;;;;;;;;;;;;;;;;0BAab,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,GAAG,sBACb,6LAAC;gCAEC,SAAS,IAAM,eAAe;gCAC9B,WAAW,CAAC,uCAAuC,EACjD,gBAAgB,QAAQ,gBAAgB,iCACxC;+BAJG;;;;;;;;;;kCASX,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;gCACxD,UAAU,gBAAgB;gCAC1B,WAAU;0CACX;;;;;;0CAID,6LAAC;gCAAK,WAAU;;oCACb,cAAc;oCAAE;oCAAI,MAAM,MAAM;;;;;;;0CAGnC,6LAAC;gCACC,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,MAAM,MAAM,GAAG,GAAG,cAAc;gCACvE,UAAU,gBAAgB,MAAM,MAAM,GAAG;gCACzC,WAAU;0CACX;;;;;;;;;;;;kCAKH,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,MAAM,SAAS,SAAS;gCACxB,QAAO;gCACP,KAAI;gCACJ,WAAU;0CACX;;;;;;0CAGD,6LAAC;gCACC,MAAM,SAAS,MAAM;gCACrB,QAAQ;gCACR,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX;GA/UwB;;QAKV,6GAAA,CAAA,SAAM;QACoB,mHAAA,CAAA,eAAY;;;KAN5B", "debugId": null}}, {"offset": {"line": 3501, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/IDCardLanyard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface IDCardLanyardProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\nexport default function IDCardLanyard({ isVisible, onClose }: IDCardLanyardProps) {\n  const [isFlipped, setIsFlipped] = useState(false);\n\n  const cardData = {\n    name: \"<PERSON><PERSON><PERSON>\",\n    nickname: \"KangPC<PERSON>\",\n    role: \"Fullstack Developer\",\n    specialization: \"Web & Mobile Development\",\n    status: \"Open for Collaboration\",\n    email: \"<EMAIL>\",\n    github: \"github.com/kangpcode\",\n    linkedin: \"linkedin.com/in/kangpcode\",\n    website: \"kangpcode.dev\",\n    photo: \"/assets/images/idcard/photo.jpg\",\n    qrCode: \"/assets/images/idcard/qr-github.png\",\n    skills: [\n      \"JavaScript/TypeScript\",\n      \"React/Next.js\",\n      \"Node.js\",\n      \"Python\",\n      \"Docker\",\n      \"AWS\"\n    ],\n    achievements: [\n      \"🏆 Best Portfolio 2024\",\n      \"📚 Author of 'Langkah <PERSON>'\",\n      \"🎯 100+ Projects Completed\",\n      \"👥 Active in Tech Community\"\n    ],\n    experience: \"3+ Years\",\n    location: \"Indonesia\",\n    languages: [\"Indonesian\", \"English\"],\n    motto: \"Building the future of Indonesian tech, one line of code at a time.\"\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4\"\n    >\n      <div className=\"relative\">\n        {/* Lanyard */}\n        <div className=\"absolute -top-20 left-1/2 transform -translate-x-1/2\">\n          <div className=\"w-6 h-20 bg-gradient-to-b from-blue-600 to-blue-800 rounded-t-full\"></div>\n          <div className=\"w-8 h-4 bg-gray-300 rounded-full -mt-2 -ml-1 border-2 border-gray-400\"></div>\n        </div>\n\n        {/* ID Card Container */}\n        <motion.div\n          className=\"relative w-96 h-64 cursor-pointer\"\n          style={{ perspective: \"1000px\" }}\n          onClick={() => setIsFlipped(!isFlipped)}\n          whileHover={{ y: -5 }}\n        >\n          <motion.div\n            className=\"relative w-full h-full\"\n            style={{ transformStyle: \"preserve-3d\" }}\n            animate={{ rotateY: isFlipped ? 180 : 0 }}\n            transition={{ duration: 0.6 }}\n          >\n            {/* Front Side */}\n            <div\n              className=\"absolute inset-0 w-full h-full bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl shadow-2xl border-2 border-white/20\"\n              style={{ backfaceVisibility: \"hidden\" }}\n            >\n              {/* Card Header */}\n              <div className=\"bg-white/10 backdrop-blur-sm p-3 rounded-t-xl border-b border-white/20\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"text-white\">\n                    <div className=\"text-xs font-medium opacity-80\">DEVELOPER ID</div>\n                    <div className=\"text-sm font-bold\">KangPCode Portfolio</div>\n                  </div>\n                  <div className=\"text-2xl\">💻</div>\n                </div>\n              </div>\n\n              {/* Card Content */}\n              <div className=\"p-4 text-white\">\n                <div className=\"flex items-start space-x-4\">\n                  {/* Photo */}\n                  <div className=\"w-20 h-20 bg-gray-300 rounded-lg flex items-center justify-center text-3xl border-2 border-white/30\">\n                    👨‍💻\n                  </div>\n\n                  {/* Info */}\n                  <div className=\"flex-1\">\n                    <h2 className=\"text-xl font-bold mb-1\">{cardData.name}</h2>\n                    <p className=\"text-blue-200 text-sm mb-1\">\"{cardData.nickname}\"</p>\n                    <p className=\"text-white/90 text-sm mb-2\">{cardData.role}</p>\n                    <p className=\"text-blue-200 text-xs\">{cardData.specialization}</p>\n                  </div>\n                </div>\n\n                {/* Status */}\n                <div className=\"mt-4 p-2 bg-green-500/20 rounded-lg border border-green-400/30\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"w-2 h-2 bg-green-400 rounded-full animate-pulse\"></div>\n                    <span className=\"text-green-200 text-sm font-medium\">{cardData.status}</span>\n                  </div>\n                </div>\n\n                {/* Quick Stats */}\n                <div className=\"mt-3 grid grid-cols-3 gap-2 text-center\">\n                  <div className=\"bg-white/10 rounded p-1\">\n                    <div className=\"text-xs font-bold\">{cardData.experience}</div>\n                    <div className=\"text-xs opacity-80\">Experience</div>\n                  </div>\n                  <div className=\"bg-white/10 rounded p-1\">\n                    <div className=\"text-xs font-bold\">100+</div>\n                    <div className=\"text-xs opacity-80\">Projects</div>\n                  </div>\n                  <div className=\"bg-white/10 rounded p-1\">\n                    <div className=\"text-xs font-bold\">{cardData.skills.length}</div>\n                    <div className=\"text-xs opacity-80\">Skills</div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Card Footer */}\n              <div className=\"absolute bottom-0 left-0 right-0 bg-white/10 backdrop-blur-sm p-2 rounded-b-xl border-t border-white/20\">\n                <div className=\"text-center text-white text-xs\">\n                  Click to flip • {cardData.location}\n                </div>\n              </div>\n            </div>\n\n            {/* Back Side */}\n            <div\n              className=\"absolute inset-0 w-full h-full bg-gradient-to-br from-gray-800 via-gray-900 to-black rounded-xl shadow-2xl border-2 border-gray-600\"\n              style={{ \n                backfaceVisibility: \"hidden\",\n                transform: \"rotateY(180deg)\"\n              }}\n            >\n              {/* Back Header */}\n              <div className=\"bg-gray-700/50 backdrop-blur-sm p-3 rounded-t-xl border-b border-gray-600\">\n                <div className=\"flex items-center justify-between text-white\">\n                  <div>\n                    <div className=\"text-xs font-medium opacity-80\">CONTACT & SKILLS</div>\n                    <div className=\"text-sm font-bold\">Professional Details</div>\n                  </div>\n                  <div className=\"text-2xl\">🔗</div>\n                </div>\n              </div>\n\n              {/* Back Content */}\n              <div className=\"p-4 text-white space-y-3\">\n                {/* Contact Info */}\n                <div>\n                  <h3 className=\"text-sm font-bold mb-2 text-blue-300\">Contact</h3>\n                  <div className=\"space-y-1 text-xs\">\n                    <div>📧 {cardData.email}</div>\n                    <div>🐙 {cardData.github}</div>\n                    <div>🌐 {cardData.website}</div>\n                  </div>\n                </div>\n\n                {/* Skills */}\n                <div>\n                  <h3 className=\"text-sm font-bold mb-2 text-green-300\">Core Skills</h3>\n                  <div className=\"grid grid-cols-2 gap-1\">\n                    {cardData.skills.map((skill, index) => (\n                      <div key={index} className=\"text-xs bg-gray-700/50 px-2 py-1 rounded\">\n                        {skill}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* QR Code */}\n                <div className=\"flex items-center justify-between\">\n                  <div>\n                    <h3 className=\"text-sm font-bold mb-1 text-purple-300\">Quick Access</h3>\n                    <div className=\"text-xs opacity-80\">Scan for GitHub</div>\n                  </div>\n                  <div className=\"w-12 h-12 bg-white rounded flex items-center justify-center\">\n                    <div className=\"text-black text-xs font-bold\">QR</div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Back Footer */}\n              <div className=\"absolute bottom-0 left-0 right-0 bg-gray-700/50 backdrop-blur-sm p-2 rounded-b-xl border-t border-gray-600\">\n                <div className=\"text-center text-white text-xs\">\n                  \"{cardData.motto}\"\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        </motion.div>\n\n        {/* Close Button */}\n        <button\n          onClick={onClose}\n          className=\"absolute -top-8 -right-8 w-8 h-8 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors\"\n        >\n          ✕\n        </button>\n\n        {/* Flip Instruction */}\n        <div className=\"absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-white text-sm text-center\">\n          <div className=\"bg-black/50 px-3 py-1 rounded-full\">\n            🖱️ Click to flip card\n          </div>\n        </div>\n      </div>\n\n      {/* Background Actions */}\n      <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-4\">\n        <motion.a\n          href={`https://${cardData.github}`}\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          className=\"bg-gray-900 hover:bg-gray-800 text-white px-4 py-2 rounded-lg transition-colors\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          🐙 GitHub\n        </motion.a>\n        <motion.a\n          href={`mailto:${cardData.email}`}\n          className=\"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          📧 Contact\n        </motion.a>\n        <motion.button\n          onClick={() => {\n            navigator.clipboard.writeText(`${cardData.name} - ${cardData.role}\\n${cardData.email}\\n${cardData.github}`);\n            alert('Contact info copied to clipboard!');\n          }}\n          className=\"bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors\"\n          whileHover={{ scale: 1.05 }}\n          whileTap={{ scale: 0.95 }}\n        >\n          📋 Copy Info\n        </motion.button>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,cAAc,EAAE,SAAS,EAAE,OAAO,EAAsB;;IAC9E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,WAAW;QACf,MAAM;QACN,UAAU;QACV,MAAM;QACN,gBAAgB;QAChB,QAAQ;QACR,OAAO;QACP,QAAQ;QACR,UAAU;QACV,SAAS;QACT,OAAO;QACP,QAAQ;QACR,QAAQ;YACN;YACA;YACA;YACA;YACA;YACA;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;SACD;QACD,YAAY;QACZ,UAAU;QACV,WAAW;YAAC;YAAc;SAAU;QACpC,OAAO;IACT;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAC/B,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,OAAO;4BAAE,aAAa;wBAAS;wBAC/B,SAAS,IAAM,aAAa,CAAC;wBAC7B,YAAY;4BAAE,GAAG,CAAC;wBAAE;kCAEpB,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,OAAO;gCAAE,gBAAgB;4BAAc;4BACvC,SAAS;gCAAE,SAAS,YAAY,MAAM;4BAAE;4BACxC,YAAY;gCAAE,UAAU;4BAAI;;8CAG5B,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,oBAAoB;oCAAS;;sDAGtC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAiC;;;;;;0EAChD,6LAAC;gEAAI,WAAU;0EAAoB;;;;;;;;;;;;kEAErC,6LAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;;;;;;;sDAK9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEAEb,6LAAC;4DAAI,WAAU;sEAAsG;;;;;;sEAKrH,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAA0B,SAAS,IAAI;;;;;;8EACrD,6LAAC;oEAAE,WAAU;;wEAA6B;wEAAE,SAAS,QAAQ;wEAAC;;;;;;;8EAC9D,6LAAC;oEAAE,WAAU;8EAA8B,SAAS,IAAI;;;;;;8EACxD,6LAAC;oEAAE,WAAU;8EAAyB,SAAS,cAAc;;;;;;;;;;;;;;;;;;8DAKjE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;gEAAK,WAAU;0EAAsC,SAAS,MAAM;;;;;;;;;;;;;;;;;8DAKzE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqB,SAAS,UAAU;;;;;;8EACvD,6LAAC;oEAAI,WAAU;8EAAqB;;;;;;;;;;;;sEAEtC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAoB;;;;;;8EACnC,6LAAC;oEAAI,WAAU;8EAAqB;;;;;;;;;;;;sEAEtC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EAAqB,SAAS,MAAM,CAAC,MAAM;;;;;;8EAC1D,6LAAC;oEAAI,WAAU;8EAAqB;;;;;;;;;;;;;;;;;;;;;;;;sDAM1C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;oDAAiC;oDAC7B,SAAS,QAAQ;;;;;;;;;;;;;;;;;;8CAMxC,6LAAC;oCACC,WAAU;oCACV,OAAO;wCACL,oBAAoB;wCACpB,WAAW;oCACb;;sDAGA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAAiC;;;;;;0EAChD,6LAAC;gEAAI,WAAU;0EAAoB;;;;;;;;;;;;kEAErC,6LAAC;wDAAI,WAAU;kEAAW;;;;;;;;;;;;;;;;;sDAK9B,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAuC;;;;;;sEACrD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;;wEAAI;wEAAI,SAAS,KAAK;;;;;;;8EACvB,6LAAC;;wEAAI;wEAAI,SAAS,MAAM;;;;;;;8EACxB,6LAAC;;wEAAI;wEAAI,SAAS,OAAO;;;;;;;;;;;;;;;;;;;8DAK7B,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAwC;;;;;;sEACtD,6LAAC;4DAAI,WAAU;sEACZ,SAAS,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC3B,6LAAC;oEAAgB,WAAU;8EACxB;mEADO;;;;;;;;;;;;;;;;8DAQhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAAyC;;;;;;8EACvD,6LAAC;oEAAI,WAAU;8EAAqB;;;;;;;;;;;;sEAEtC,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAI,WAAU;0EAA+B;;;;;;;;;;;;;;;;;;;;;;;sDAMpD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;oDAAiC;oDAC5C,SAAS,KAAK;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ3B,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAKD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCAAqC;;;;;;;;;;;;;;;;;0BAOxD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,MAAM,CAAC,QAAQ,EAAE,SAAS,MAAM,EAAE;wBAClC,QAAO;wBACP,KAAI;wBACJ,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;kCAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,MAAM,CAAC,OAAO,EAAE,SAAS,KAAK,EAAE;wBAChC,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;kCAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;4BACP,UAAU,SAAS,CAAC,SAAS,CAAC,GAAG,SAAS,IAAI,CAAC,GAAG,EAAE,SAAS,IAAI,CAAC,EAAE,EAAE,SAAS,KAAK,CAAC,EAAE,EAAE,SAAS,MAAM,EAAE;4BAC1G,MAAM;wBACR;wBACA,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;kCACzB;;;;;;;;;;;;;;;;;;AAMT;GArPwB;KAAA", "debugId": null}}, {"offset": {"line": 4220, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/PosterIlmuwan.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface PosterIlmuwanProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\ninterface Scientist {\n  id: number;\n  name: string;\n  description: string;\n  contribution: string;\n  category: 'teknologi' | 'nusantara' | 'dunia';\n  birthYear?: number;\n  deathYear?: number;\n  nationality: string;\n  image: string;\n  quotes: string[];\n  achievements: string[];\n  legacy: string;\n}\n\nexport default function PosterIlmuwan({ isVisible, onClose }: PosterIlmuwanProps) {\n  const [selectedScientist, setSelectedScientist] = useState<Scientist | null>(null);\n  const [activeCategory, setActiveCategory] = useState<'teknologi' | 'nusantara' | 'dunia'>('teknologi');\n\n  const scientists: Scientist[] = [\n    // Teknologi Modern\n    {\n      id: 1,\n      name: \"Nikola Tesla\",\n      description: \"Penemu motor induksi, listrik AC modern\",\n      contribution: \"Sistem tenaga listrik AC, motor induksi, teknologi nirkabel\",\n      category: \"teknologi\",\n      birthYear: 1856,\n      deathYear: 1943,\n      nationality: \"Serbia-Amerika\",\n      image: \"🔌\",\n      quotes: [\n        \"The present is theirs; the future, for which I really worked, is mine.\",\n        \"If you want to find the secrets of the universe, think in terms of energy, frequency and vibration.\"\n      ],\n      achievements: [\n        \"Sistem distribusi listrik AC\",\n        \"Motor induksi polyphase\",\n        \"Teknologi transmisi nirkabel\",\n        \"Lebih dari 300 paten\"\n      ],\n      legacy: \"Teknologi listrik modern yang kita gunakan hari ini sebagian besar berdasarkan penemuan Tesla.\"\n    },\n    {\n      id: 2,\n      name: \"Albert Einstein\",\n      description: \"Teori relativitas, dasar teknologi GPS\",\n      contribution: \"Teori relativitas khusus dan umum, efek fotolistrik\",\n      category: \"teknologi\",\n      birthYear: 1879,\n      deathYear: 1955,\n      nationality: \"Jerman-Amerika\",\n      image: \"🧠\",\n      quotes: [\n        \"Imagination is more important than knowledge.\",\n        \"The important thing is not to stop questioning.\"\n      ],\n      achievements: [\n        \"Teori Relativitas Khusus (1905)\",\n        \"Teori Relativitas Umum (1915)\",\n        \"Nobel Prize in Physics (1921)\",\n        \"Dasar teknologi GPS dan laser\"\n      ],\n      legacy: \"Teorinya memungkinkan teknologi GPS, laser, dan pemahaman modern tentang alam semesta.\"\n    },\n    // Nusantara\n    {\n      id: 3,\n      name: \"Onno W. Purbo\",\n      description: \"Pelopor internet Indonesia, edukator teknologi\",\n      contribution: \"Pengembangan internet di Indonesia, edukasi teknologi\",\n      category: \"nusantara\",\n      birthYear: 1962,\n      nationality: \"Indonesia\",\n      image: \"🌐\",\n      quotes: [\n        \"Internet adalah alat untuk mencerdaskan bangsa.\",\n        \"Teknologi harus bisa diakses oleh semua lapisan masyarakat.\"\n      ],\n      achievements: [\n        \"Pelopor internet di Indonesia\",\n        \"Pendiri komunitas Linux Indonesia\",\n        \"Penulis 40+ buku teknologi\",\n        \"Advokat open source\"\n      ],\n      legacy: \"Membangun fondasi internet Indonesia dan menginspirasi generasi teknologi lokal.\"\n    },\n    {\n      id: 4,\n      name: \"B.J. Habibie\",\n      description: \"Bapak teknologi Indonesia, ahli pesawat terbang\",\n      contribution: \"Teknologi pesawat terbang, industrialisasi Indonesia\",\n      category: \"nusantara\",\n      birthYear: 1936,\n      deathYear: 2019,\n      nationality: \"Indonesia\",\n      image: \"✈️\",\n      quotes: [\n        \"Teknologi adalah kunci kemajuan bangsa.\",\n        \"Kita harus menjadi bangsa yang mandiri dalam teknologi.\"\n      ],\n      achievements: [\n        \"Ahli teknologi pesawat terbang\",\n        \"Presiden RI ke-3\",\n        \"Pendiri industri pesawat Indonesia\",\n        \"Doktor Honoris Causa dari 23 universitas\"\n      ],\n      legacy: \"Membangun fondasi industri teknologi Indonesia dan menginspirasi kemandirian teknologi.\"\n    },\n    // Dunia Klasik\n    {\n      id: 5,\n      name: \"Al-Khawarizmi\",\n      description: \"Bapak algoritma, dasar ilmu komputasi\",\n      contribution: \"Algoritma, aljabar, sistem bilangan\",\n      category: \"dunia\",\n      birthYear: 780,\n      deathYear: 850,\n      nationality: \"Persia\",\n      image: \"🔢\",\n      quotes: [\n        \"That which is sought is found by methodical calculation.\",\n        \"Mathematics is the key to understanding the universe.\"\n      ],\n      achievements: [\n        \"Menciptakan konsep algoritma\",\n        \"Mengembangkan aljabar\",\n        \"Sistem bilangan Hindu-Arab\",\n        \"Dasar matematika modern\"\n      ],\n      legacy: \"Algoritma yang dia ciptakan menjadi dasar semua komputasi modern.\"\n    },\n    {\n      id: 6,\n      name: \"Al-Jazari\",\n      description: \"Bapak robotika, desain mesin otomatis\",\n      contribution: \"Mesin otomatis, robotika awal, jam air\",\n      category: \"dunia\",\n      birthYear: 1136,\n      deathYear: 1206,\n      nationality: \"Arab\",\n      image: \"🤖\",\n      quotes: [\n        \"Innovation comes from understanding the principles of nature.\",\n        \"Machines should serve humanity's needs.\"\n      ],\n      achievements: [\n        \"Menciptakan robot humanoid pertama\",\n        \"Jam air otomatis\",\n        \"Sistem kontrol otomatis\",\n        \"Buku 'The Book of Knowledge of Ingenious Mechanical Devices'\"\n      ],\n      legacy: \"Desainnya menjadi dasar robotika dan otomasi modern.\"\n    }\n  ];\n\n  const categories = [\n    { id: 'teknologi', name: 'Teknologi Modern', icon: '💻', color: 'blue' },\n    { id: 'nusantara', name: 'Tokoh Nusantara', icon: '🇮🇩', color: 'red' },\n    { id: 'dunia', name: 'Tokoh Dunia', icon: '🌍', color: 'green' }\n  ];\n\n  const filteredScientists = scientists.filter(s => s.category === activeCategory);\n\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className=\"fixed inset-4 bg-white rounded-lg shadow-2xl border overflow-hidden z-50\"\n    >\n      {/* Header */}\n      <div className=\"h-16 bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-between px-6 text-white\">\n        <div className=\"flex items-center space-x-3\">\n          <span className=\"text-2xl\">👨‍🔬</span>\n          <div>\n            <h2 className=\"font-bold text-lg\">Poster Ilmuwan Teknologi</h2>\n            <p className=\"text-sm text-purple-100\">Inspirasi dari Para Pionir</p>\n          </div>\n        </div>\n        <button\n          onClick={onClose}\n          className=\"hover:bg-white/20 p-2 rounded transition-colors\"\n        >\n          ✕\n        </button>\n      </div>\n\n      {/* Category Tabs */}\n      <div className=\"h-12 bg-gray-100 border-b flex items-center px-6\">\n        {categories.map((category) => (\n          <button\n            key={category.id}\n            onClick={() => setActiveCategory(category.id as any)}\n            className={`flex items-center space-x-2 px-4 py-2 rounded-t transition-colors mr-2 ${\n              activeCategory === category.id\n                ? (category.color === 'blue' ? 'bg-blue-500 text-white' :\n                   category.color === 'red' ? 'bg-red-500 text-white' :\n                   'bg-green-500 text-white')\n                : 'hover:bg-gray-200 text-gray-600'\n            }`}\n          >\n            <span>{category.icon}</span>\n            <span className=\"font-medium\">{category.name}</span>\n          </button>\n        ))}\n      </div>\n\n      {/* Content */}\n      <div className=\"flex h-[calc(100%-7rem)]\">\n        {/* Scientists Grid */}\n        <div className=\"w-1/2 p-6 overflow-y-auto border-r\">\n          <div className=\"grid grid-cols-1 gap-4\">\n            {filteredScientists.map((scientist) => (\n              <motion.div\n                key={scientist.id}\n                onClick={() => setSelectedScientist(scientist)}\n                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${\n                  selectedScientist?.id === scientist.id\n                    ? 'border-blue-500 bg-blue-50'\n                    : 'border-gray-200 hover:border-gray-300 hover:shadow-md'\n                }`}\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <div className=\"flex items-start space-x-4\">\n                  <div className=\"text-4xl\">{scientist.image}</div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"font-bold text-lg text-gray-900\">{scientist.name}</h3>\n                    <p className=\"text-sm text-gray-600 mb-2\">\n                      {scientist.birthYear && scientist.deathYear \n                        ? `${scientist.birthYear} - ${scientist.deathYear}`\n                        : scientist.birthYear \n                        ? `${scientist.birthYear} - sekarang`\n                        : 'Klasik'\n                      } • {scientist.nationality}\n                    </p>\n                    <p className=\"text-gray-700\">{scientist.description}</p>\n                  </div>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* Scientist Detail */}\n        <div className=\"w-1/2 p-6 overflow-y-auto\">\n          <AnimatePresence mode=\"wait\">\n            {selectedScientist ? (\n              <motion.div\n                key={selectedScientist.id}\n                initial={{ opacity: 0, x: 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: -20 }}\n                className=\"space-y-6\"\n              >\n                {/* Scientist Header */}\n                <div className=\"text-center\">\n                  <div className=\"text-6xl mb-4\">{selectedScientist.image}</div>\n                  <h2 className=\"text-3xl font-bold text-gray-900 mb-2\">{selectedScientist.name}</h2>\n                  <p className=\"text-lg text-gray-600 mb-4\">{selectedScientist.description}</p>\n                  <div className=\"inline-block bg-gray-100 px-3 py-1 rounded-full text-sm text-gray-600\">\n                    {selectedScientist.nationality}\n                  </div>\n                </div>\n\n                {/* Contribution */}\n                <div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">🔬 Kontribusi Utama</h3>\n                  <p className=\"text-gray-700 leading-relaxed\">{selectedScientist.contribution}</p>\n                </div>\n\n                {/* Achievements */}\n                <div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">🏆 Pencapaian</h3>\n                  <ul className=\"space-y-2\">\n                    {selectedScientist.achievements.map((achievement, index) => (\n                      <li key={index} className=\"flex items-start space-x-2\">\n                        <span className=\"text-blue-500 mt-1\">•</span>\n                        <span className=\"text-gray-700\">{achievement}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Quotes */}\n                <div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">💭 Kutipan Inspiratif</h3>\n                  <div className=\"space-y-3\">\n                    {selectedScientist.quotes.map((quote, index) => (\n                      <blockquote key={index} className=\"border-l-4 border-blue-500 pl-4 italic text-gray-700\">\n                        \"{quote}\"\n                      </blockquote>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Legacy */}\n                <div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-3\">🌟 Warisan</h3>\n                  <p className=\"text-gray-700 leading-relaxed bg-yellow-50 p-4 rounded-lg border border-yellow-200\">\n                    {selectedScientist.legacy}\n                  </p>\n                </div>\n              </motion.div>\n            ) : (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                className=\"flex items-center justify-center h-full text-center\"\n              >\n                <div className=\"space-y-4\">\n                  <div className=\"text-6xl\">👨‍🔬</div>\n                  <h3 className=\"text-xl font-bold text-gray-900\">Pilih Ilmuwan</h3>\n                  <p className=\"text-gray-600\">Klik pada salah satu ilmuwan di sebelah kiri untuk melihat detail</p>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n      </div>\n\n      {/* Footer */}\n      <div className=\"h-12 bg-gray-100 border-t flex items-center justify-between px-6\">\n        <div className=\"text-sm text-gray-600\">\n          Menampilkan {filteredScientists.length} ilmuwan dalam kategori {categories.find(c => c.id === activeCategory)?.name}\n        </div>\n        <div className=\"text-sm text-gray-500\">\n          Inspirasi untuk generasi teknologi Indonesia 🇮🇩\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAyBe,SAAS,cAAc,EAAE,SAAS,EAAE,OAAO,EAAsB;;IAC9E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC7E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC;IAE1F,MAAM,aAA0B;QAC9B,mBAAmB;QACnB;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,cAAc;YACd,UAAU;YACV,WAAW;YACX,WAAW;YACX,aAAa;YACb,OAAO;YACP,QAAQ;gBACN;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;QACV;QACA;YACE,IAAI;YAC<PERSON>,MAAM;YAC<PERSON>,aAAa;YACb,cAAc;YACd,UAAU;YACV,WAAW;YACX,WAAW;YACX,aAAa;YACb,OAAO;YACP,QAAQ;gBACN;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;QACV;QACA,YAAY;QACZ;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,cAAc;YACd,UAAU;YACV,WAAW;YACX,aAAa;YACb,OAAO;YACP,QAAQ;gBACN;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,cAAc;YACd,UAAU;YACV,WAAW;YACX,WAAW;YACX,aAAa;YACb,OAAO;YACP,QAAQ;gBACN;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;QACV;QACA,eAAe;QACf;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,cAAc;YACd,UAAU;YACV,WAAW;YACX,WAAW;YACX,aAAa;YACb,OAAO;YACP,QAAQ;gBACN;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;QACV;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,cAAc;YACd,UAAU;YACV,WAAW;YACX,WAAW;YACX,aAAa;YACb,OAAO;YACP,QAAQ;gBACN;gBACA;aACD;YACD,cAAc;gBACZ;gBACA;gBACA;gBACA;aACD;YACD,QAAQ;QACV;KACD;IAED,MAAM,aAAa;QACjB;YAAE,IAAI;YAAa,MAAM;YAAoB,MAAM;YAAM,OAAO;QAAO;QACvE;YAAE,IAAI;YAAa,MAAM;YAAmB,MAAM;YAAQ,OAAO;QAAM;QACvE;YAAE,IAAI;YAAS,MAAM;YAAe,MAAM;YAAM,OAAO;QAAQ;KAChE;IAED,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK;IAEjE,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAC/B,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAW;;;;;;0CAC3B,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoB;;;;;;kDAClC,6LAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;kCAG3C,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC,yBACf,6LAAC;wBAEC,SAAS,IAAM,kBAAkB,SAAS,EAAE;wBAC5C,WAAW,CAAC,uEAAuE,EACjF,mBAAmB,SAAS,EAAE,GACzB,SAAS,KAAK,KAAK,SAAS,2BAC5B,SAAS,KAAK,KAAK,QAAQ,0BAC3B,4BACD,mCACJ;;0CAEF,6LAAC;0CAAM,SAAS,IAAI;;;;;;0CACpB,6LAAC;gCAAK,WAAU;0CAAe,SAAS,IAAI;;;;;;;uBAXvC,SAAS,EAAE;;;;;;;;;;0BAiBtB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,mBAAmB,GAAG,CAAC,CAAC,0BACvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS,IAAM,qBAAqB;oCACpC,WAAW,CAAC,sDAAsD,EAChE,mBAAmB,OAAO,UAAU,EAAE,GAClC,+BACA,yDACJ;oCACF,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;8CAExB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAY,UAAU,KAAK;;;;;;0DAC1C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAmC,UAAU,IAAI;;;;;;kEAC/D,6LAAC;wDAAE,WAAU;;4DACV,UAAU,SAAS,IAAI,UAAU,SAAS,GACvC,GAAG,UAAU,SAAS,CAAC,GAAG,EAAE,UAAU,SAAS,EAAE,GACjD,UAAU,SAAS,GACnB,GAAG,UAAU,SAAS,CAAC,WAAW,CAAC,GACnC;4DACH;4DAAI,UAAU,WAAW;;;;;;;kEAE5B,6LAAC;wDAAE,WAAU;kEAAiB,UAAU,WAAW;;;;;;;;;;;;;;;;;;mCAtBlD,UAAU,EAAE;;;;;;;;;;;;;;;kCA+BzB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;4BAAC,MAAK;sCACnB,kCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,WAAU;;kDAGV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAiB,kBAAkB,KAAK;;;;;;0DACvD,6LAAC;gDAAG,WAAU;0DAAyC,kBAAkB,IAAI;;;;;;0DAC7E,6LAAC;gDAAE,WAAU;0DAA8B,kBAAkB,WAAW;;;;;;0DACxE,6LAAC;gDAAI,WAAU;0DACZ,kBAAkB,WAAW;;;;;;;;;;;;kDAKlC,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAE,WAAU;0DAAiC,kBAAkB,YAAY;;;;;;;;;;;;kDAI9E,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAG,WAAU;0DACX,kBAAkB,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBAChD,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAK,WAAU;0EAAqB;;;;;;0EACrC,6LAAC;gEAAK,WAAU;0EAAiB;;;;;;;uDAF1B;;;;;;;;;;;;;;;;kDASf,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAI,WAAU;0DACZ,kBAAkB,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBACpC,6LAAC;wDAAuB,WAAU;;4DAAuD;4DACrF;4DAAM;;uDADO;;;;;;;;;;;;;;;;kDAQvB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAE,WAAU;0DACV,kBAAkB,MAAM;;;;;;;;;;;;;+BAnDxB,kBAAkB,EAAE;;;;qDAwD3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAW;;;;;;sDAC1B,6LAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAChD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BAAwB;4BACxB,mBAAmB,MAAM;4BAAC;4BAAyB,WAAW,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,iBAAiB;;;;;;;kCAEjH,6LAAC;wBAAI,WAAU;kCAAwB;;;;;;;;;;;;;;;;;;AAM/C;GAhUwB;KAAA", "debugId": null}}, {"offset": {"line": 4893, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/ActionFigure.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface ActionFigureProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\ninterface Figure {\n  id: number;\n  name: string;\n  anime: string;\n  quote: string;\n  philosophy: string;\n  image: string;\n  color: string;\n  personality: string;\n  lifeLesson: string;\n  relevantToTech: string;\n  position: { x: number; y: number; z: number };\n}\n\nexport default function ActionFigure({ isVisible, onClose }: ActionFigureProps) {\n  const [selectedFigure, setSelectedFigure] = useState<Figure | null>(null);\n  const [currentView, setCurrentView] = useState<'shelf' | 'detail'>('shelf');\n\n  const figures: Figure[] = [\n    {\n      id: 1,\n      name: \"Monkey D. <PERSON>\",\n      anime: \"One Piece\",\n      quote: \"Aku akan menjadi Raja Bajak Laut!\",\n      philosophy: \"Tidak pernah menyerah pada impian, selalu optimis menghadapi tantangan\",\n      image: \"🏴‍☠️\",\n      color: \"red\",\n      personality: \"Opti<PERSON>, pantang menyerah, loyal pada teman\",\n      lifeLesson: \"Impian yang besar membutuhkan tekad yang kuat dan kerja keras yang konsisten\",\n      relevantToTech: \"Seperti developer yang bermimpi membuat aplikasi yang mengubah dunia - butuh persistence dan passion\",\n      position: { x: -3, y: 0, z: 0 }\n    },\n    {\n      id: 2,\n      name: \"Shanks\",\n      anime: \"One Piece\",\n      quote: \"Percaya itu yang paling penting dalam dunia ini.\",\n      philosophy: \"Kepercayaan adalah fondasi dari semua hubungan yang kuat\",\n      image: \"⚔️\",\n      color: \"orange\",\n      personality: \"Bijaksana, tenang, inspiratif\",\n      lifeLesson: \"Membangun kepercayaan membutuhkan waktu, tapi sekali rusak sulit diperbaiki\",\n      relevantToTech: \"Dalam tim development, trust adalah kunci kolaborasi yang efektif\",\n      position: { x: -1, y: 0, z: 0 }\n    },\n    {\n      id: 3,\n      name: \"Uzumaki Naruto\",\n      anime: \"Naruto\",\n      quote: \"Aku tidak akan mundur atau menarik kata-kataku!\",\n      philosophy: \"Komitmen pada janji dan prinsip adalah kekuatan sejati\",\n      image: \"🍥\",\n      color: \"orange\",\n      personality: \"Gigih, setia kawan, tidak pernah menyerah\",\n      lifeLesson: \"Kegagalan adalah bagian dari perjalanan menuju kesuksesan\",\n      relevantToTech: \"Debugging code yang sulit membutuhkan persistence seperti Naruto\",\n      position: { x: 1, y: 0, z: 0 }\n    },\n    {\n      id: 4,\n      name: \"Hatake Kakashi\",\n      anime: \"Naruto\",\n      quote: \"Mereka yang meninggalkan teman adalah sampah.\",\n      philosophy: \"Loyalitas dan kerja sama tim lebih penting dari prestasi individual\",\n      image: \"⚡\",\n      color: \"blue\",\n      personality: \"Tenang, strategis, peduli pada tim\",\n      lifeLesson: \"Kesuksesan sejati adalah ketika kita bisa membawa orang lain ikut sukses\",\n      relevantToTech: \"Code review dan mentoring junior developer adalah bentuk loyalitas pada tim\",\n      position: { x: 3, y: 0, z: 0 }\n    },\n    {\n      id: 5,\n      name: \"Senku Ishigami\",\n      anime: \"Dr. Stone\",\n      quote: \"This is exhilarating! Science is the power to overcome any obstacle!\",\n      philosophy: \"Sains dan logika adalah kunci untuk memecahkan masalah apapun\",\n      image: \"🧪\",\n      color: \"green\",\n      personality: \"Analitis, inovatif, rasional\",\n      lifeLesson: \"Pengetahuan adalah kekuatan yang paling powerful untuk mengubah dunia\",\n      relevantToTech: \"Approach scientific dalam problem solving adalah essence dari programming\",\n      position: { x: 0, y: 1, z: -1 }\n    },\n    {\n      id: 6,\n      name: \"Edward Elric\",\n      anime: \"Fullmetal Alchemist\",\n      quote: \"To obtain something, something of equal value must be lost.\",\n      philosophy: \"Hukum pertukaran setara - tidak ada yang gratis di dunia ini\",\n      image: \"⚗️\",\n      color: \"yellow\",\n      personality: \"Tekun, bertanggung jawab, protective\",\n      lifeLesson: \"Setiap pencapaian membutuhkan pengorbanan dan kerja keras\",\n      relevantToTech: \"Untuk menjadi expert developer, harus sacrifice time dan comfort zone\",\n      position: { x: -2, y: 1, z: -1 }\n    }\n  ];\n\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className=\"fixed inset-4 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 rounded-lg shadow-2xl border overflow-hidden z-50\"\n    >\n      {/* Header */}\n      <div className=\"h-16 bg-black/30 backdrop-blur-sm flex items-center justify-between px-6 text-white border-b border-white/20\">\n        <div className=\"flex items-center space-x-3\">\n          <span className=\"text-2xl\">🧸</span>\n          <div>\n            <h2 className=\"font-bold text-lg\">Action Figure Collection</h2>\n            <p className=\"text-sm text-purple-200\">Filosofi Hidup dari Karakter Anime</p>\n          </div>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          <button\n            onClick={() => setCurrentView(currentView === 'shelf' ? 'detail' : 'shelf')}\n            className=\"px-3 py-1 bg-white/20 hover:bg-white/30 rounded transition-colors text-sm\"\n          >\n            {currentView === 'shelf' ? '📋 Detail View' : '🏠 Shelf View'}\n          </button>\n          <button\n            onClick={onClose}\n            className=\"hover:bg-white/20 p-2 rounded transition-colors\"\n          >\n            ✕\n          </button>\n        </div>\n      </div>\n\n      <AnimatePresence mode=\"wait\">\n        {currentView === 'shelf' ? (\n          <motion.div\n            key=\"shelf\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -20 }}\n            className=\"h-[calc(100%-4rem)] p-8\"\n          >\n            {/* 3D Shelf Simulation */}\n            <div className=\"h-full relative bg-gradient-to-b from-amber-100 to-amber-200 rounded-lg border-4 border-amber-800 shadow-inner\">\n              {/* Shelf Background */}\n              <div className=\"absolute inset-4 bg-gradient-to-b from-amber-50 to-amber-100 rounded border-2 border-amber-600\">\n                {/* Shelf Lines */}\n                <div className=\"absolute top-1/3 left-0 right-0 h-1 bg-amber-600\"></div>\n                <div className=\"absolute top-2/3 left-0 right-0 h-1 bg-amber-600\"></div>\n              </div>\n\n              {/* Figures on Shelf */}\n              <div className=\"relative h-full p-8\">\n                {/* Top Shelf */}\n                <div className=\"absolute top-8 left-8 right-8 h-32 flex items-end justify-around\">\n                  {figures.slice(0, 3).map((figure, index) => (\n                    <motion.div\n                      key={figure.id}\n                      onClick={() => setSelectedFigure(figure)}\n                      className=\"cursor-pointer group\"\n                      whileHover={{ scale: 1.1, y: -5 }}\n                      whileTap={{ scale: 0.95 }}\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: index * 0.2 }}\n                    >\n                      <div className={`w-16 h-20 bg-gradient-to-b from-${figure.color}-400 to-${figure.color}-600 rounded-lg shadow-lg flex items-center justify-center text-2xl border-2 border-${figure.color}-700 group-hover:shadow-xl transition-all`}>\n                        {figure.image}\n                      </div>\n                      <div className=\"text-center mt-2\">\n                        <div className=\"text-xs font-bold text-gray-800\">{figure.name}</div>\n                        <div className=\"text-xs text-gray-600\">{figure.anime}</div>\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n\n                {/* Bottom Shelf */}\n                <div className=\"absolute bottom-8 left-8 right-8 h-32 flex items-end justify-around\">\n                  {figures.slice(3).map((figure, index) => (\n                    <motion.div\n                      key={figure.id}\n                      onClick={() => setSelectedFigure(figure)}\n                      className=\"cursor-pointer group\"\n                      whileHover={{ scale: 1.1, y: -5 }}\n                      whileTap={{ scale: 0.95 }}\n                      initial={{ opacity: 0, y: 20 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      transition={{ delay: (index + 3) * 0.2 }}\n                    >\n                      <div className={`w-16 h-20 bg-gradient-to-b from-${figure.color}-400 to-${figure.color}-600 rounded-lg shadow-lg flex items-center justify-center text-2xl border-2 border-${figure.color}-700 group-hover:shadow-xl transition-all`}>\n                        {figure.image}\n                      </div>\n                      <div className=\"text-center mt-2\">\n                        <div className=\"text-xs font-bold text-gray-800\">{figure.name}</div>\n                        <div className=\"text-xs text-gray-600\">{figure.anime}</div>\n                      </div>\n                    </motion.div>\n                  ))}\n                </div>\n\n                {/* Instructions */}\n                <div className=\"absolute bottom-4 left-1/2 transform -translate-x-1/2 text-center\">\n                  <div className=\"bg-black/20 backdrop-blur-sm text-white px-4 py-2 rounded-lg\">\n                    🖱️ Klik figure untuk melihat filosofi hidup\n                  </div>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        ) : (\n          <motion.div\n            key=\"detail\"\n            initial={{ opacity: 0, x: 20 }}\n            animate={{ opacity: 1, x: 0 }}\n            exit={{ opacity: 0, x: -20 }}\n            className=\"h-[calc(100%-4rem)] flex\"\n          >\n            {/* Figure List */}\n            <div className=\"w-1/3 p-4 border-r border-white/20 overflow-y-auto\">\n              <h3 className=\"text-white font-bold mb-4\">Pilih Figure</h3>\n              <div className=\"space-y-2\">\n                {figures.map((figure) => (\n                  <motion.button\n                    key={figure.id}\n                    onClick={() => setSelectedFigure(figure)}\n                    className={`w-full p-3 rounded-lg text-left transition-all ${\n                      selectedFigure?.id === figure.id\n                        ? 'bg-white/20 border border-white/40'\n                        : 'bg-white/10 hover:bg-white/15'\n                    }`}\n                    whileHover={{ scale: 1.02 }}\n                    whileTap={{ scale: 0.98 }}\n                  >\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-2xl\">{figure.image}</span>\n                      <div>\n                        <div className=\"text-white font-medium\">{figure.name}</div>\n                        <div className=\"text-purple-200 text-sm\">{figure.anime}</div>\n                      </div>\n                    </div>\n                  </motion.button>\n                ))}\n              </div>\n            </div>\n\n            {/* Figure Detail */}\n            <div className=\"flex-1 p-6 overflow-y-auto\">\n              <AnimatePresence mode=\"wait\">\n                {selectedFigure ? (\n                  <motion.div\n                    key={selectedFigure.id}\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: -20 }}\n                    className=\"space-y-6 text-white\"\n                  >\n                    {/* Character Header */}\n                    <div className=\"text-center\">\n                      <div className=\"text-8xl mb-4\">{selectedFigure.image}</div>\n                      <h2 className=\"text-3xl font-bold mb-2\">{selectedFigure.name}</h2>\n                      <p className=\"text-xl text-purple-200 mb-4\">{selectedFigure.anime}</p>\n                      <div className=\"inline-block bg-white/20 px-4 py-2 rounded-full\">\n                        {selectedFigure.personality}\n                      </div>\n                    </div>\n\n                    {/* Quote */}\n                    <div className=\"bg-white/10 backdrop-blur-sm p-6 rounded-lg border border-white/20\">\n                      <h3 className=\"text-xl font-bold mb-3 text-yellow-300\">💬 Quote Ikonik</h3>\n                      <blockquote className=\"text-lg italic text-center\">\n                        \"{selectedFigure.quote}\"\n                      </blockquote>\n                    </div>\n\n                    {/* Philosophy */}\n                    <div className=\"bg-white/10 backdrop-blur-sm p-6 rounded-lg border border-white/20\">\n                      <h3 className=\"text-xl font-bold mb-3 text-blue-300\">🧠 Filosofi Hidup</h3>\n                      <p className=\"leading-relaxed\">{selectedFigure.philosophy}</p>\n                    </div>\n\n                    {/* Life Lesson */}\n                    <div className=\"bg-white/10 backdrop-blur-sm p-6 rounded-lg border border-white/20\">\n                      <h3 className=\"text-xl font-bold mb-3 text-green-300\">📚 Pelajaran Hidup</h3>\n                      <p className=\"leading-relaxed\">{selectedFigure.lifeLesson}</p>\n                    </div>\n\n                    {/* Tech Relevance */}\n                    <div className=\"bg-white/10 backdrop-blur-sm p-6 rounded-lg border border-white/20\">\n                      <h3 className=\"text-xl font-bold mb-3 text-purple-300\">💻 Relevansi dengan Tech</h3>\n                      <p className=\"leading-relaxed\">{selectedFigure.relevantToTech}</p>\n                    </div>\n                  </motion.div>\n                ) : (\n                  <motion.div\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    className=\"flex items-center justify-center h-full text-center\"\n                  >\n                    <div className=\"space-y-4 text-white\">\n                      <div className=\"text-6xl\">🧸</div>\n                      <h3 className=\"text-xl font-bold\">Pilih Action Figure</h3>\n                      <p className=\"text-purple-200\">Klik pada figure di sebelah kiri untuk melihat filosofi hidup</p>\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAwBe,SAAS,aAAa,EAAE,SAAS,EAAE,OAAO,EAAqB;;IAC5E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IAEnE,MAAM,UAAoB;QACxB;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,YAAY;YACZ,OAAO;YACP,OAAO;YACP,aAAa;YACb,YAAY;YACZ,gBAAgB;YAChB,UAAU;gBAAE,GAAG,CAAC;gBAAG,GAAG;gBAAG,GAAG;YAAE;QAChC;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,YAAY;YACZ,OAAO;YACP,OAAO;YACP,aAAa;YACb,YAAY;YACZ,gBAAgB;YAChB,UAAU;gBAAE,GAAG,CAAC;gBAAG,GAAG;gBAAG,GAAG;YAAE;QAChC;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,YAAY;YACZ,OAAO;YACP,OAAO;YACP,aAAa;YACb,YAAY;YACZ,gBAAgB;YAChB,UAAU;gBAAE,GAAG;gBAAG,GAAG;gBAAG,GAAG;YAAE;QAC/B;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,YAAY;YACZ,OAAO;YACP,OAAO;YACP,aAAa;YACb,YAAY;YACZ,gBAAgB;YAChB,UAAU;gBAAE,GAAG;gBAAG,GAAG;gBAAG,GAAG;YAAE;QAC/B;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,YAAY;YACZ,OAAO;YACP,OAAO;YACP,aAAa;YACb,YAAY;YACZ,gBAAgB;YAChB,UAAU;gBAAE,GAAG;gBAAG,GAAG;gBAAG,GAAG,CAAC;YAAE;QAChC;QACA;YACE,IAAI;YACJ,MAAM;YACN,OAAO;YACP,OAAO;YACP,YAAY;YACZ,OAAO;YACP,OAAO;YACP,aAAa;YACb,YAAY;YACZ,gBAAgB;YAChB,UAAU;gBAAE,GAAG,CAAC;gBAAG,GAAG;gBAAG,GAAG,CAAC;YAAE;QACjC;KACD;IAED,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAC/B,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAW;;;;;;0CAC3B,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoB;;;;;;kDAClC,6LAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;kCAG3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,eAAe,gBAAgB,UAAU,WAAW;gCACnE,WAAU;0CAET,gBAAgB,UAAU,mBAAmB;;;;;;0CAEhD,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAML,6LAAC,4LAAA,CAAA,kBAAe;gBAAC,MAAK;0BACnB,gBAAgB,wBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;8BAGV,cAAA,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAIjB,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,QAAQ,sBAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS,IAAM,kBAAkB;gDACjC,WAAU;gDACV,YAAY;oDAAE,OAAO;oDAAK,GAAG,CAAC;gDAAE;gDAChC,UAAU;oDAAE,OAAO;gDAAK;gDACxB,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO,QAAQ;gDAAI;;kEAEjC,6LAAC;wDAAI,WAAW,CAAC,gCAAgC,EAAE,OAAO,KAAK,CAAC,QAAQ,EAAE,OAAO,KAAK,CAAC,oFAAoF,EAAE,OAAO,KAAK,CAAC,yCAAyC,CAAC;kEACjO,OAAO,KAAK;;;;;;kEAEf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAmC,OAAO,IAAI;;;;;;0EAC7D,6LAAC;gEAAI,WAAU;0EAAyB,OAAO,KAAK;;;;;;;;;;;;;+CAdjD,OAAO,EAAE;;;;;;;;;;kDAqBpB,6LAAC;wCAAI,WAAU;kDACZ,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,sBAC7B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS,IAAM,kBAAkB;gDACjC,WAAU;gDACV,YAAY;oDAAE,OAAO;oDAAK,GAAG,CAAC;gDAAE;gDAChC,UAAU;oDAAE,OAAO;gDAAK;gDACxB,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO,CAAC,QAAQ,CAAC,IAAI;gDAAI;;kEAEvC,6LAAC;wDAAI,WAAW,CAAC,gCAAgC,EAAE,OAAO,KAAK,CAAC,QAAQ,EAAE,OAAO,KAAK,CAAC,oFAAoF,EAAE,OAAO,KAAK,CAAC,yCAAyC,CAAC;kEACjO,OAAO,KAAK;;;;;;kEAEf,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAAmC,OAAO,IAAI;;;;;;0EAC7D,6LAAC;gEAAI,WAAU;0EAAyB,OAAO,KAAK;;;;;;;;;;;;;+CAdjD,OAAO,EAAE;;;;;;;;;;kDAqBpB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDAA+D;;;;;;;;;;;;;;;;;;;;;;;mBAnEhF;;;;yCA2EN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,MAAM;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC3B,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA4B;;;;;;8CAC1C,6LAAC;oCAAI,WAAU;8CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;4CAEZ,SAAS,IAAM,kBAAkB;4CACjC,WAAW,CAAC,+CAA+C,EACzD,gBAAgB,OAAO,OAAO,EAAE,GAC5B,uCACA,iCACJ;4CACF,YAAY;gDAAE,OAAO;4CAAK;4CAC1B,UAAU;gDAAE,OAAO;4CAAK;sDAExB,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAY,OAAO,KAAK;;;;;;kEACxC,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;0EAA0B,OAAO,IAAI;;;;;;0EACpD,6LAAC;gEAAI,WAAU;0EAA2B,OAAO,KAAK;;;;;;;;;;;;;;;;;;2CAdrD,OAAO,EAAE;;;;;;;;;;;;;;;;sCAuBtB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;gCAAC,MAAK;0CACnB,+BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC3B,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAiB,eAAe,KAAK;;;;;;8DACpD,6LAAC;oDAAG,WAAU;8DAA2B,eAAe,IAAI;;;;;;8DAC5D,6LAAC;oDAAE,WAAU;8DAAgC,eAAe,KAAK;;;;;;8DACjE,6LAAC;oDAAI,WAAU;8DACZ,eAAe,WAAW;;;;;;;;;;;;sDAK/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,6LAAC;oDAAW,WAAU;;wDAA6B;wDAC/C,eAAe,KAAK;wDAAC;;;;;;;;;;;;;sDAK3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAuC;;;;;;8DACrD,6LAAC;oDAAE,WAAU;8DAAmB,eAAe,UAAU;;;;;;;;;;;;sDAI3D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAwC;;;;;;8DACtD,6LAAC;oDAAE,WAAU;8DAAmB,eAAe,UAAU;;;;;;;;;;;;sDAI3D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;8DACvD,6LAAC;oDAAE,WAAU;8DAAmB,eAAe,cAAc;;;;;;;;;;;;;mCAvC1D,eAAe,EAAE;;;;yDA2CxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,WAAU;8CAEV,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAW;;;;;;0DAC1B,6LAAC;gDAAG,WAAU;0DAAoB;;;;;;0DAClC,6LAAC;gDAAE,WAAU;0DAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;mBA1FrC;;;;;;;;;;;;;;;;AAqGhB;GA3SwB;KAAA", "debugId": null}}, {"offset": {"line": 5668, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/WhiteboardProject.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface WhiteboardProjectProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\ninterface Project {\n  id: number;\n  name: string;\n  description: string;\n  stack: string[];\n  previewImage: string;\n  demoUrl?: string;\n  githubUrl?: string;\n  status: 'completed' | 'in-progress' | 'planned';\n  featured: boolean;\n  category: 'web' | 'mobile' | 'desktop' | 'ai' | 'other';\n  completionDate?: string;\n  highlights: string[];\n  challenges: string[];\n  learnings: string[];\n}\n\nexport default function WhiteboardProject({ isVisible, onClose }: WhiteboardProjectProps) {\n  const [selectedProject, setSelectedProject] = useState<Project | null>(null);\n  const [filterStatus, setFilterStatus] = useState<'all' | 'completed' | 'in-progress' | 'planned'>('all');\n\n  const projects: Project[] = [\n    {\n      id: 1,\n      name: \"Portfolio 3D Interaktif\",\n      description: \"Website portfolio berbasis 3D dengan Three.js yang menampilkan pengalaman game-like untuk eksplorasi karir dan proyek developer\",\n      stack: [\"Next.js\", \"Three.js\", \"TypeScript\", \"TailwindCSS\", \"Framer Motion\", \"SQLite\"],\n      previewImage: \"🌐\",\n      demoUrl: \"https://kangpcode.dev\",\n      githubUrl: \"https://github.com/kangpcode/portfolio-3d\",\n      status: \"in-progress\",\n      featured: true,\n      category: \"web\",\n      highlights: [\n        \"Interactive 3D room environment\",\n        \"Real-time GitHub integration\",\n        \"AI-powered terminal assistant\",\n        \"PWA with offline capabilities\"\n      ],\n      challenges: [\n        \"Optimizing 3D performance on mobile\",\n        \"Creating intuitive 3D interactions\",\n        \"Balancing visual appeal with functionality\"\n      ],\n      learnings: [\n        \"Advanced Three.js techniques\",\n        \"3D UI/UX design principles\",\n        \"Performance optimization for 3D web apps\"\n      ]\n    },\n    {\n      id: 2,\n      name: \"Langkah Kode Nusantara\",\n      description: \"Buku digital interaktif untuk developer Indonesia yang ingin membangun karir di dunia teknologi dengan tetap mengangkat nilai-nilai lokal\",\n      stack: [\"Markdown\", \"GitBook\", \"React\", \"PDF.js\"],\n      previewImage: \"📚\",\n      githubUrl: \"https://github.com/kangpcode/langkah-kode-nusantara\",\n      status: \"completed\",\n      featured: true,\n      category: \"other\",\n      completionDate: \"2024-01-15\",\n      highlights: [\n        \"350+ halaman konten berkualitas\",\n        \"150+ contoh kode praktis\",\n        \"12 proyek hands-on\",\n        \"Fokus pada konteks Indonesia\"\n      ],\n      challenges: [\n        \"Menyesuaikan konten dengan konteks lokal\",\n        \"Membuat contoh yang relevan\",\n        \"Balancing theory dan practice\"\n      ],\n      learnings: [\n        \"Technical writing skills\",\n        \"Content structuring\",\n        \"Educational material design\"\n      ]\n    },\n    {\n      id: 3,\n      name: \"DevTools Indonesia\",\n      description: \"Platform kolaboratif untuk developer Indonesia dengan fitur code sharing, mentoring, dan job matching berbasis skill assessment\",\n      stack: [\"Next.js\", \"Node.js\", \"PostgreSQL\", \"Redis\", \"Docker\", \"AWS\"],\n      previewImage: \"🛠️\",\n      status: \"planned\",\n      featured: true,\n      category: \"web\",\n      highlights: [\n        \"Real-time code collaboration\",\n        \"AI-powered skill assessment\",\n        \"Mentoring marketplace\",\n        \"Local job opportunities\"\n      ],\n      challenges: [\n        \"Building scalable real-time features\",\n        \"Creating fair skill assessment\",\n        \"Community building strategies\"\n      ],\n      learnings: [\n        \"Real-time architecture design\",\n        \"Community platform development\",\n        \"AI integration in web apps\"\n      ]\n    },\n    {\n      id: 4,\n      name: \"Smart Campus IoT\",\n      description: \"Sistem IoT untuk monitoring dan otomasi kampus menggunakan sensor dan dashboard real-time untuk efisiensi energi\",\n      stack: [\"Arduino\", \"Raspberry Pi\", \"Python\", \"React\", \"InfluxDB\", \"Grafana\"],\n      previewImage: \"🏫\",\n      status: \"completed\",\n      featured: false,\n      category: \"other\",\n      completionDate: \"2023-12-10\",\n      highlights: [\n        \"50+ sensor nodes deployed\",\n        \"Real-time monitoring dashboard\",\n        \"30% energy efficiency improvement\",\n        \"Automated alert system\"\n      ],\n      challenges: [\n        \"Sensor network reliability\",\n        \"Data processing at scale\",\n        \"Hardware-software integration\"\n      ],\n      learnings: [\n        \"IoT architecture design\",\n        \"Time-series data handling\",\n        \"Hardware programming\"\n      ]\n    },\n    {\n      id: 5,\n      name: \"EduGame Math\",\n      description: \"Game edukasi matematika untuk anak SD dengan pendekatan gamifikasi dan adaptive learning algorithm\",\n      stack: [\"Unity\", \"C#\", \"Firebase\", \"Analytics\"],\n      previewImage: \"🎮\",\n      status: \"completed\",\n      featured: false,\n      category: \"mobile\",\n      completionDate: \"2023-08-20\",\n      highlights: [\n        \"Adaptive difficulty system\",\n        \"Progress tracking for parents\",\n        \"Engaging mini-games\",\n        \"1000+ downloads\"\n      ],\n      challenges: [\n        \"Balancing fun and education\",\n        \"Creating adaptive algorithms\",\n        \"Child-friendly UI design\"\n      ],\n      learnings: [\n        \"Game development with Unity\",\n        \"Educational game design\",\n        \"Mobile app optimization\"\n      ]\n    },\n    {\n      id: 6,\n      name: \"AI Code Reviewer\",\n      description: \"Tool AI untuk review kode otomatis dengan fokus pada best practices, security, dan performance optimization\",\n      stack: [\"Python\", \"TensorFlow\", \"FastAPI\", \"Docker\", \"GitHub API\"],\n      previewImage: \"🤖\",\n      status: \"in-progress\",\n      featured: false,\n      category: \"ai\",\n      highlights: [\n        \"Multi-language support\",\n        \"Security vulnerability detection\",\n        \"Performance optimization suggestions\",\n        \"GitHub integration\"\n      ],\n      challenges: [\n        \"Training accurate models\",\n        \"Handling diverse codebases\",\n        \"Providing actionable feedback\"\n      ],\n      learnings: [\n        \"Machine learning for code analysis\",\n        \"Natural language processing\",\n        \"API design for AI services\"\n      ]\n    }\n  ];\n\n  const statusColors = {\n    completed: 'green',\n    'in-progress': 'blue',\n    planned: 'yellow'\n  };\n\n  const categoryIcons = {\n    web: '🌐',\n    mobile: '📱',\n    desktop: '💻',\n    ai: '🤖',\n    other: '📦'\n  };\n\n  const filteredProjects = filterStatus === 'all' \n    ? projects \n    : projects.filter(p => p.status === filterStatus);\n\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className=\"fixed inset-4 bg-white rounded-lg shadow-2xl border overflow-hidden z-50\"\n    >\n      {/* Whiteboard Header */}\n      <div className=\"h-16 bg-gradient-to-r from-gray-100 to-gray-200 flex items-center justify-between px-6 border-b-4 border-gray-800\">\n        <div className=\"flex items-center space-x-3\">\n          <span className=\"text-2xl\">📌</span>\n          <div>\n            <h2 className=\"font-bold text-lg text-gray-900\">Project Whiteboard</h2>\n            <p className=\"text-sm text-gray-600\">Pinned Projects & Ideas</p>\n          </div>\n        </div>\n        <button\n          onClick={onClose}\n          className=\"hover:bg-gray-300 p-2 rounded transition-colors text-gray-700\"\n        >\n          ✕\n        </button>\n      </div>\n\n      {/* Filter Tabs */}\n      <div className=\"h-12 bg-gray-50 border-b flex items-center px-6\">\n        {[\n          { id: 'all', name: 'All Projects', count: projects.length },\n          { id: 'completed', name: 'Completed', count: projects.filter(p => p.status === 'completed').length },\n          { id: 'in-progress', name: 'In Progress', count: projects.filter(p => p.status === 'in-progress').length },\n          { id: 'planned', name: 'Planned', count: projects.filter(p => p.status === 'planned').length }\n        ].map((filter) => (\n          <button\n            key={filter.id}\n            onClick={() => setFilterStatus(filter.id as any)}\n            className={`flex items-center space-x-2 px-4 py-2 rounded transition-colors mr-2 ${\n              filterStatus === filter.id \n                ? 'bg-blue-500 text-white' \n                : 'hover:bg-gray-200 text-gray-600'\n            }`}\n          >\n            <span>{filter.name}</span>\n            <span className=\"text-xs bg-white/20 px-2 py-1 rounded-full\">{filter.count}</span>\n          </button>\n        ))}\n      </div>\n\n      {/* Content */}\n      <div className=\"flex h-[calc(100%-7rem)]\">\n        {/* Projects Grid */}\n        <div className=\"w-1/2 p-6 overflow-y-auto border-r bg-gray-50\">\n          <div className=\"grid grid-cols-1 gap-4\">\n            {filteredProjects.map((project) => (\n              <motion.div\n                key={project.id}\n                onClick={() => setSelectedProject(project)}\n                className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all bg-white shadow-sm ${\n                  selectedProject?.id === project.id\n                    ? 'border-blue-500 shadow-md'\n                    : 'border-gray-200 hover:border-gray-300 hover:shadow-md'\n                } ${project.featured ? 'ring-2 ring-yellow-300' : ''}`}\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n                layout\n              >\n                {/* Featured Badge */}\n                {project.featured && (\n                  <div className=\"absolute -top-2 -right-2 bg-yellow-500 text-white text-xs px-2 py-1 rounded-full\">\n                    ⭐ Featured\n                  </div>\n                )}\n\n                {/* Project Header */}\n                <div className=\"flex items-start space-x-4 mb-3\">\n                  <div className=\"text-3xl\">{project.previewImage}</div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"font-bold text-lg text-gray-900\">{project.name}</h3>\n                    <div className=\"flex items-center space-x-2 mt-1\">\n                      <span className=\"text-sm text-gray-500\">{categoryIcons[project.category]}</span>\n                      <span className={`text-xs px-2 py-1 rounded-full ${\n                        project.status === 'completed' ? 'bg-green-100 text-green-800' :\n                        project.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :\n                        'bg-yellow-100 text-yellow-800'\n                      }`}>\n                        {project.status.replace('-', ' ')}\n                      </span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Description */}\n                <p className=\"text-gray-700 text-sm mb-3 line-clamp-2\">{project.description}</p>\n\n                {/* Tech Stack */}\n                <div className=\"flex flex-wrap gap-1\">\n                  {project.stack.slice(0, 4).map((tech, index) => (\n                    <span key={index} className=\"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded\">\n                      {tech}\n                    </span>\n                  ))}\n                  {project.stack.length > 4 && (\n                    <span className=\"text-xs text-gray-500\">+{project.stack.length - 4} more</span>\n                  )}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* Project Detail */}\n        <div className=\"w-1/2 p-6 overflow-y-auto\">\n          <AnimatePresence mode=\"wait\">\n            {selectedProject ? (\n              <motion.div\n                key={selectedProject.id}\n                initial={{ opacity: 0, x: 20 }}\n                animate={{ opacity: 1, x: 0 }}\n                exit={{ opacity: 0, x: -20 }}\n                className=\"space-y-6\"\n              >\n                {/* Project Header */}\n                <div className=\"text-center\">\n                  <div className=\"text-6xl mb-4\">{selectedProject.previewImage}</div>\n                  <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">{selectedProject.name}</h2>\n                  <p className=\"text-gray-600 leading-relaxed mb-4\">{selectedProject.description}</p>\n                  \n                  <div className=\"flex justify-center space-x-4 mb-4\">\n                    {selectedProject.demoUrl && (\n                      <a\n                        href={selectedProject.demoUrl}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors\"\n                      >\n                        🌐 Live Demo\n                      </a>\n                    )}\n                    {selectedProject.githubUrl && (\n                      <a\n                        href={selectedProject.githubUrl}\n                        target=\"_blank\"\n                        rel=\"noopener noreferrer\"\n                        className=\"px-4 py-2 bg-gray-900 hover:bg-gray-800 text-white rounded transition-colors\"\n                      >\n                        📁 GitHub\n                      </a>\n                    )}\n                  </div>\n                </div>\n\n                {/* Tech Stack */}\n                <div>\n                  <h3 className=\"text-lg font-bold text-gray-900 mb-3\">🛠️ Tech Stack</h3>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {selectedProject.stack.map((tech, index) => (\n                      <span key={index} className=\"bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm\">\n                        {tech}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Highlights */}\n                <div>\n                  <h3 className=\"text-lg font-bold text-gray-900 mb-3\">✨ Key Highlights</h3>\n                  <ul className=\"space-y-2\">\n                    {selectedProject.highlights.map((highlight, index) => (\n                      <li key={index} className=\"flex items-start space-x-2\">\n                        <span className=\"text-green-500 mt-1\">•</span>\n                        <span className=\"text-gray-700\">{highlight}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Challenges */}\n                <div>\n                  <h3 className=\"text-lg font-bold text-gray-900 mb-3\">🎯 Challenges</h3>\n                  <ul className=\"space-y-2\">\n                    {selectedProject.challenges.map((challenge, index) => (\n                      <li key={index} className=\"flex items-start space-x-2\">\n                        <span className=\"text-orange-500 mt-1\">•</span>\n                        <span className=\"text-gray-700\">{challenge}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Learnings */}\n                <div>\n                  <h3 className=\"text-lg font-bold text-gray-900 mb-3\">📚 Key Learnings</h3>\n                  <ul className=\"space-y-2\">\n                    {selectedProject.learnings.map((learning, index) => (\n                      <li key={index} className=\"flex items-start space-x-2\">\n                        <span className=\"text-blue-500 mt-1\">•</span>\n                        <span className=\"text-gray-700\">{learning}</span>\n                      </li>\n                    ))}\n                  </ul>\n                </div>\n\n                {/* Completion Date */}\n                {selectedProject.completionDate && (\n                  <div className=\"bg-green-50 p-4 rounded-lg border border-green-200\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-green-600\">✅</span>\n                      <span className=\"text-green-800 font-medium\">\n                        Completed on {new Date(selectedProject.completionDate).toLocaleDateString('id-ID')}\n                      </span>\n                    </div>\n                  </div>\n                )}\n              </motion.div>\n            ) : (\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                className=\"flex items-center justify-center h-full text-center\"\n              >\n                <div className=\"space-y-4\">\n                  <div className=\"text-6xl\">📌</div>\n                  <h3 className=\"text-xl font-bold text-gray-900\">Select a Project</h3>\n                  <p className=\"text-gray-600\">Click on any project card to see detailed information</p>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AA2Be,SAAS,kBAAkB,EAAE,SAAS,EAAE,OAAO,EAA0B;;IACtF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IACvE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmD;IAElG,MAAM,WAAsB;QAC1B;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;gBAAC;gBAAW;gBAAY;gBAAc;gBAAe;gBAAiB;aAAS;YACtF,cAAc;YACd,SAAS;YACT,WAAW;YACX,QAAQ;YACR,UAAU;YACV,UAAU;YACV,YAAY;gBACV;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV;gBACA;gBACA;aACD;YACD,WAAW;gBACT;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;gBAAC;gBAAY;gBAAW;gBAAS;aAAS;YACjD,cAAc;YACd,WAAW;YACX,QAAQ;YACR,UAAU;YACV,UAAU;YACV,gBAAgB;YAChB,YAAY;gBACV;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV;gBACA;gBACA;aACD;YACD,WAAW;gBACT;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;gBAAC;gBAAW;gBAAW;gBAAc;gBAAS;gBAAU;aAAM;YACrE,cAAc;YACd,QAAQ;YACR,UAAU;YACV,UAAU;YACV,YAAY;gBACV;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV;gBACA;gBACA;aACD;YACD,WAAW;gBACT;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;gBAAC;gBAAW;gBAAgB;gBAAU;gBAAS;gBAAY;aAAU;YAC5E,cAAc;YACd,QAAQ;YACR,UAAU;YACV,UAAU;YACV,gBAAgB;YAChB,YAAY;gBACV;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV;gBACA;gBACA;aACD;YACD,WAAW;gBACT;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;gBAAC;gBAAS;gBAAM;gBAAY;aAAY;YAC/C,cAAc;YACd,QAAQ;YACR,UAAU;YACV,UAAU;YACV,gBAAgB;YAChB,YAAY;gBACV;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV;gBACA;gBACA;aACD;YACD,WAAW;gBACT;gBACA;gBACA;aACD;QACH;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,OAAO;gBAAC;gBAAU;gBAAc;gBAAW;gBAAU;aAAa;YAClE,cAAc;YACd,QAAQ;YACR,UAAU;YACV,UAAU;YACV,YAAY;gBACV;gBACA;gBACA;gBACA;aACD;YACD,YAAY;gBACV;gBACA;gBACA;aACD;YACD,WAAW;gBACT;gBACA;gBACA;aACD;QACH;KACD;IAED,MAAM,eAAe;QACnB,WAAW;QACX,eAAe;QACf,SAAS;IACX;IAEA,MAAM,gBAAgB;QACpB,KAAK;QACL,QAAQ;QACR,SAAS;QACT,IAAI;QACJ,OAAO;IACT;IAEA,MAAM,mBAAmB,iBAAiB,QACtC,WACA,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;IAEtC,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAC/B,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAW;;;;;;0CAC3B,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAGzC,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC;gBAAI,WAAU;0BACZ;oBACC;wBAAE,IAAI;wBAAO,MAAM;wBAAgB,OAAO,SAAS,MAAM;oBAAC;oBAC1D;wBAAE,IAAI;wBAAa,MAAM;wBAAa,OAAO,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aAAa,MAAM;oBAAC;oBACnG;wBAAE,IAAI;wBAAe,MAAM;wBAAe,OAAO,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,eAAe,MAAM;oBAAC;oBACzG;wBAAE,IAAI;wBAAW,MAAM;wBAAW,OAAO,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;oBAAC;iBAC9F,CAAC,GAAG,CAAC,CAAC,uBACL,6LAAC;wBAEC,SAAS,IAAM,gBAAgB,OAAO,EAAE;wBACxC,WAAW,CAAC,qEAAqE,EAC/E,iBAAiB,OAAO,EAAE,GACtB,2BACA,mCACJ;;0CAEF,6LAAC;0CAAM,OAAO,IAAI;;;;;;0CAClB,6LAAC;gCAAK,WAAU;0CAA8C,OAAO,KAAK;;;;;;;uBATrE,OAAO,EAAE;;;;;;;;;;0BAepB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAC,wBACrB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS,IAAM,mBAAmB;oCAClC,WAAW,CAAC,kFAAkF,EAC5F,iBAAiB,OAAO,QAAQ,EAAE,GAC9B,8BACA,wDACL,CAAC,EAAE,QAAQ,QAAQ,GAAG,2BAA2B,IAAI;oCACtD,YAAY;wCAAE,OAAO;oCAAK;oCAC1B,UAAU;wCAAE,OAAO;oCAAK;oCACxB,MAAM;;wCAGL,QAAQ,QAAQ,kBACf,6LAAC;4CAAI,WAAU;sDAAmF;;;;;;sDAMpG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAY,QAAQ,YAAY;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAmC,QAAQ,IAAI;;;;;;sEAC7D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAyB,aAAa,CAAC,QAAQ,QAAQ,CAAC;;;;;;8EACxE,6LAAC;oEAAK,WAAW,CAAC,+BAA+B,EAC/C,QAAQ,MAAM,KAAK,cAAc,gCACjC,QAAQ,MAAM,KAAK,gBAAgB,8BACnC,iCACA;8EACC,QAAQ,MAAM,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;sDAOrC,6LAAC;4CAAE,WAAU;sDAA2C,QAAQ,WAAW;;;;;;sDAG3E,6LAAC;4CAAI,WAAU;;gDACZ,QAAQ,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,sBACpC,6LAAC;wDAAiB,WAAU;kEACzB;uDADQ;;;;;gDAIZ,QAAQ,KAAK,CAAC,MAAM,GAAG,mBACtB,6LAAC;oDAAK,WAAU;;wDAAwB;wDAAE,QAAQ,KAAK,CAAC,MAAM,GAAG;wDAAE;;;;;;;;;;;;;;mCA/ClE,QAAQ,EAAE;;;;;;;;;;;;;;;kCAwDvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;4BAAC,MAAK;sCACnB,gCACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,MAAM;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC3B,WAAU;;kDAGV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAiB,gBAAgB,YAAY;;;;;;0DAC5D,6LAAC;gDAAG,WAAU;0DAAyC,gBAAgB,IAAI;;;;;;0DAC3E,6LAAC;gDAAE,WAAU;0DAAsC,gBAAgB,WAAW;;;;;;0DAE9E,6LAAC;gDAAI,WAAU;;oDACZ,gBAAgB,OAAO,kBACtB,6LAAC;wDACC,MAAM,gBAAgB,OAAO;wDAC7B,QAAO;wDACP,KAAI;wDACJ,WAAU;kEACX;;;;;;oDAIF,gBAAgB,SAAS,kBACxB,6LAAC;wDACC,MAAM,gBAAgB,SAAS;wDAC/B,QAAO;wDACP,KAAI;wDACJ,WAAU;kEACX;;;;;;;;;;;;;;;;;;kDAQP,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAI,WAAU;0DACZ,gBAAgB,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAChC,6LAAC;wDAAiB,WAAU;kEACzB;uDADQ;;;;;;;;;;;;;;;;kDAQjB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAG,WAAU;0DACX,gBAAgB,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBAC1C,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAK,WAAU;0EAAsB;;;;;;0EACtC,6LAAC;gEAAK,WAAU;0EAAiB;;;;;;;uDAF1B;;;;;;;;;;;;;;;;kDASf,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAG,WAAU;0DACX,gBAAgB,UAAU,CAAC,GAAG,CAAC,CAAC,WAAW,sBAC1C,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAK,WAAU;0EAAuB;;;;;;0EACvC,6LAAC;gEAAK,WAAU;0EAAiB;;;;;;;uDAF1B;;;;;;;;;;;;;;;;kDASf,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,6LAAC;gDAAG,WAAU;0DACX,gBAAgB,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBACxC,6LAAC;wDAAe,WAAU;;0EACxB,6LAAC;gEAAK,WAAU;0EAAqB;;;;;;0EACrC,6LAAC;gEAAK,WAAU;0EAAiB;;;;;;;uDAF1B;;;;;;;;;;;;;;;;oCASd,gBAAgB,cAAc,kBAC7B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAiB;;;;;;8DACjC,6LAAC;oDAAK,WAAU;;wDAA6B;wDAC7B,IAAI,KAAK,gBAAgB,cAAc,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;;+BA7F7E,gBAAgB,EAAE;;;;qDAoGzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAW;;;;;;sDAC1B,6LAAC;4CAAG,WAAU;sDAAkC;;;;;;sDAChD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C;GApawB;KAAA", "debugId": null}}, {"offset": {"line": 6544, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/ResumePDF.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface ResumePDFProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\nexport default function ResumePDF({ isVisible, onClose }: ResumePDFProps) {\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [selectedTemplate, setSelectedTemplate] = useState<'modern' | 'classic' | 'creative'>('modern');\n\n  const resumeData = {\n    personalInfo: {\n      name: \"<PERSON><PERSON><PERSON>\",\n      title: \"Fullstack Developer & Content Creator\",\n      email: \"<EMAIL>\",\n      phone: \"+62-xxx-xxxx-xxxx\",\n      location: \"Indonesia\",\n      website: \"kangpcode.dev\",\n      github: \"github.com/kangpcode\",\n      linkedin: \"linkedin.com/in/kangpcode\"\n    },\n    summary: \"Passionate fullstack developer with 3+ years of experience building modern web applications. Specialized in React/Next.js ecosystem with strong background in backend development. Active in tech community and committed to sharing knowledge through content creation and mentoring.\",\n    experience: [\n      {\n        title: \"Fullstack Developer\",\n        company: \"CV Bintang Gumilang\",\n        period: \"2023 - Present\",\n        location: \"Indonesia\",\n        responsibilities: [\n          \"Developed and maintained web applications using React, Next.js, and Node.js\",\n          \"Collaborated with cross-functional teams to deliver high-quality software solutions\",\n          \"Implemented responsive designs and optimized application performance\",\n          \"Mentored junior developers and conducted code reviews\"\n        ]\n      },\n      {\n        title: \"Freelance Developer\",\n        company: \"Various Clients\",\n        period: \"2022 - 2023\",\n        location: \"Remote\",\n        responsibilities: [\n          \"Built custom web applications for local businesses\",\n          \"Provided technical consultation and project planning\",\n          \"Delivered projects on time and within budget\",\n          \"Maintained long-term client relationships\"\n        ]\n      }\n    ],\n    education: [\n      {\n        degree: \"Bachelor of Computer Science\",\n        institution: \"Universitas XYZ\",\n        period: \"2020 - 2024\",\n        location: \"Indonesia\",\n        gpa: \"3.8/4.0\"\n      }\n    ],\n    skills: {\n      frontend: [\"React\", \"Next.js\", \"TypeScript\", \"TailwindCSS\", \"Three.js\"],\n      backend: [\"Node.js\", \"Express\", \"Python\", \"Django\", \"FastAPI\"],\n      database: [\"PostgreSQL\", \"MongoDB\", \"SQLite\", \"Redis\"],\n      tools: [\"Git\", \"Docker\", \"AWS\", \"Vercel\", \"Figma\"],\n      other: [\"REST APIs\", \"GraphQL\", \"Testing\", \"CI/CD\", \"Agile\"]\n    },\n    projects: [\n      {\n        name: \"Portfolio 3D Interaktif\",\n        description: \"Interactive 3D portfolio website with game-like experience\",\n        technologies: [\"Next.js\", \"Three.js\", \"TypeScript\", \"TailwindCSS\"],\n        link: \"kangpcode.dev\"\n      },\n      {\n        name: \"Langkah Kode Nusantara\",\n        description: \"Educational programming book for Indonesian developers\",\n        technologies: [\"Markdown\", \"GitBook\", \"React\"],\n        link: \"github.com/kangpcode/langkah-kode-nusantara\"\n      }\n    ],\n    achievements: [\n      \"Author of 'Langkah Kode Nusantara' programming book\",\n      \"Active contributor to open source projects\",\n      \"Speaker at local tech meetups\",\n      \"Mentor for junior developers\"\n    ]\n  };\n\n  const templates = [\n    {\n      id: 'modern',\n      name: 'Modern',\n      description: 'Clean and professional design with modern typography',\n      preview: '📄'\n    },\n    {\n      id: 'classic',\n      name: 'Classic',\n      description: 'Traditional resume format with elegant styling',\n      preview: '📋'\n    },\n    {\n      id: 'creative',\n      name: 'Creative',\n      description: 'Eye-catching design perfect for creative roles',\n      preview: '🎨'\n    }\n  ];\n\n  const generatePDF = async () => {\n    setIsGenerating(true);\n    \n    try {\n      // Simulate PDF generation\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      \n      // Create a simple HTML content for PDF\n      const htmlContent = `\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset=\"utf-8\">\n          <title>Resume - ${resumeData.personalInfo.name}</title>\n          <style>\n            body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }\n            .header { text-align: center; margin-bottom: 30px; }\n            .name { font-size: 28px; font-weight: bold; margin-bottom: 10px; }\n            .title { font-size: 18px; color: #666; margin-bottom: 20px; }\n            .contact { font-size: 14px; }\n            .section { margin: 30px 0; }\n            .section-title { font-size: 20px; font-weight: bold; border-bottom: 2px solid #333; padding-bottom: 5px; margin-bottom: 15px; }\n            .experience-item { margin-bottom: 20px; }\n            .job-title { font-weight: bold; font-size: 16px; }\n            .company { color: #666; }\n            .period { float: right; color: #666; }\n            .skills-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }\n            .skill-category { margin-bottom: 15px; }\n            .skill-category-title { font-weight: bold; margin-bottom: 5px; }\n            ul { margin: 10px 0; padding-left: 20px; }\n          </style>\n        </head>\n        <body>\n          <div class=\"header\">\n            <div class=\"name\">${resumeData.personalInfo.name}</div>\n            <div class=\"title\">${resumeData.personalInfo.title}</div>\n            <div class=\"contact\">\n              ${resumeData.personalInfo.email} | ${resumeData.personalInfo.phone} | ${resumeData.personalInfo.location}<br>\n              ${resumeData.personalInfo.website} | ${resumeData.personalInfo.github}\n            </div>\n          </div>\n          \n          <div class=\"section\">\n            <div class=\"section-title\">Professional Summary</div>\n            <p>${resumeData.summary}</p>\n          </div>\n          \n          <div class=\"section\">\n            <div class=\"section-title\">Experience</div>\n            ${resumeData.experience.map(exp => `\n              <div class=\"experience-item\">\n                <div class=\"job-title\">${exp.title}</div>\n                <div class=\"company\">${exp.company} <span class=\"period\">${exp.period}</span></div>\n                <ul>\n                  ${exp.responsibilities.map(resp => `<li>${resp}</li>`).join('')}\n                </ul>\n              </div>\n            `).join('')}\n          </div>\n          \n          <div class=\"section\">\n            <div class=\"section-title\">Education</div>\n            ${resumeData.education.map(edu => `\n              <div class=\"experience-item\">\n                <div class=\"job-title\">${edu.degree}</div>\n                <div class=\"company\">${edu.institution} <span class=\"period\">${edu.period}</span></div>\n                <p>GPA: ${edu.gpa}</p>\n              </div>\n            `).join('')}\n          </div>\n          \n          <div class=\"section\">\n            <div class=\"section-title\">Technical Skills</div>\n            <div class=\"skills-grid\">\n              <div class=\"skill-category\">\n                <div class=\"skill-category-title\">Frontend</div>\n                <div>${resumeData.skills.frontend.join(', ')}</div>\n              </div>\n              <div class=\"skill-category\">\n                <div class=\"skill-category-title\">Backend</div>\n                <div>${resumeData.skills.backend.join(', ')}</div>\n              </div>\n              <div class=\"skill-category\">\n                <div class=\"skill-category-title\">Database</div>\n                <div>${resumeData.skills.database.join(', ')}</div>\n              </div>\n              <div class=\"skill-category\">\n                <div class=\"skill-category-title\">Tools & Others</div>\n                <div>${resumeData.skills.tools.concat(resumeData.skills.other).join(', ')}</div>\n              </div>\n            </div>\n          </div>\n          \n          <div class=\"section\">\n            <div class=\"section-title\">Key Projects</div>\n            ${resumeData.projects.map(project => `\n              <div class=\"experience-item\">\n                <div class=\"job-title\">${project.name}</div>\n                <p>${project.description}</p>\n                <p><strong>Technologies:</strong> ${project.technologies.join(', ')}</p>\n                <p><strong>Link:</strong> ${project.link}</p>\n              </div>\n            `).join('')}\n          </div>\n          \n          <div class=\"section\">\n            <div class=\"section-title\">Achievements</div>\n            <ul>\n              ${resumeData.achievements.map(achievement => `<li>${achievement}</li>`).join('')}\n            </ul>\n          </div>\n        </body>\n        </html>\n      `;\n      \n      // Create blob and download\n      const blob = new Blob([htmlContent], { type: 'text/html' });\n      const url = URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `Resume_${resumeData.personalInfo.name.replace(/\\s+/g, '_')}_${selectedTemplate}.html`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      URL.revokeObjectURL(url);\n      \n    } catch (error) {\n      console.error('Error generating PDF:', error);\n      alert('Error generating PDF. Please try again.');\n    } finally {\n      setIsGenerating(false);\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className=\"fixed inset-4 bg-white rounded-lg shadow-2xl border overflow-hidden z-50\"\n    >\n      {/* Header */}\n      <div className=\"h-16 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between px-6 text-white\">\n        <div className=\"flex items-center space-x-3\">\n          <span className=\"text-2xl\">📄</span>\n          <div>\n            <h2 className=\"font-bold text-lg\">Resume PDF Generator</h2>\n            <p className=\"text-sm text-blue-100\">Generate professional resume in PDF format</p>\n          </div>\n        </div>\n        <button\n          onClick={onClose}\n          className=\"hover:bg-white/20 p-2 rounded transition-colors\"\n        >\n          ✕\n        </button>\n      </div>\n\n      {/* Content */}\n      <div className=\"flex h-[calc(100%-4rem)]\">\n        {/* Template Selection */}\n        <div className=\"w-1/3 p-6 border-r bg-gray-50\">\n          <h3 className=\"text-lg font-bold text-gray-900 mb-4\">Choose Template</h3>\n          <div className=\"space-y-3\">\n            {templates.map((template) => (\n              <motion.button\n                key={template.id}\n                onClick={() => setSelectedTemplate(template.id as any)}\n                className={`w-full p-4 rounded-lg border-2 text-left transition-all ${\n                  selectedTemplate === template.id\n                    ? 'border-blue-500 bg-blue-50'\n                    : 'border-gray-200 hover:border-gray-300 bg-white'\n                }`}\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                <div className=\"flex items-start space-x-3\">\n                  <span className=\"text-2xl\">{template.preview}</span>\n                  <div>\n                    <h4 className=\"font-bold text-gray-900\">{template.name}</h4>\n                    <p className=\"text-sm text-gray-600\">{template.description}</p>\n                  </div>\n                </div>\n              </motion.button>\n            ))}\n          </div>\n\n          {/* Generate Button */}\n          <motion.button\n            onClick={generatePDF}\n            disabled={isGenerating}\n            className=\"w-full mt-6 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition-colors\"\n            whileHover={{ scale: isGenerating ? 1 : 1.02 }}\n            whileTap={{ scale: isGenerating ? 1 : 0.98 }}\n          >\n            {isGenerating ? (\n              <div className=\"flex items-center justify-center space-x-2\">\n                <div className=\"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"></div>\n                <span>Generating PDF...</span>\n              </div>\n            ) : (\n              <div className=\"flex items-center justify-center space-x-2\">\n                <span>📄</span>\n                <span>Generate PDF Resume</span>\n              </div>\n            )}\n          </motion.button>\n        </div>\n\n        {/* Resume Preview */}\n        <div className=\"flex-1 p-6 overflow-y-auto\">\n          <div className=\"max-w-2xl mx-auto bg-white border rounded-lg p-8 shadow-sm\">\n            {/* Header */}\n            <div className=\"text-center mb-6\">\n              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">{resumeData.personalInfo.name}</h1>\n              <h2 className=\"text-xl text-gray-600 mb-4\">{resumeData.personalInfo.title}</h2>\n              <div className=\"text-sm text-gray-600 space-y-1\">\n                <div>{resumeData.personalInfo.email} | {resumeData.personalInfo.phone}</div>\n                <div>{resumeData.personalInfo.website} | {resumeData.personalInfo.github}</div>\n                <div>{resumeData.personalInfo.location}</div>\n              </div>\n            </div>\n\n            {/* Summary */}\n            <div className=\"mb-6\">\n              <h3 className=\"text-lg font-bold text-gray-900 border-b-2 border-gray-300 pb-1 mb-3\">Professional Summary</h3>\n              <p className=\"text-gray-700 leading-relaxed\">{resumeData.summary}</p>\n            </div>\n\n            {/* Experience */}\n            <div className=\"mb-6\">\n              <h3 className=\"text-lg font-bold text-gray-900 border-b-2 border-gray-300 pb-1 mb-3\">Experience</h3>\n              {resumeData.experience.map((exp, index) => (\n                <div key={index} className=\"mb-4\">\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <div>\n                      <h4 className=\"font-bold text-gray-900\">{exp.title}</h4>\n                      <p className=\"text-gray-600\">{exp.company}</p>\n                    </div>\n                    <span className=\"text-sm text-gray-500\">{exp.period}</span>\n                  </div>\n                  <ul className=\"list-disc list-inside text-gray-700 space-y-1\">\n                    {exp.responsibilities.map((resp, idx) => (\n                      <li key={idx} className=\"text-sm\">{resp}</li>\n                    ))}\n                  </ul>\n                </div>\n              ))}\n            </div>\n\n            {/* Skills */}\n            <div className=\"mb-6\">\n              <h3 className=\"text-lg font-bold text-gray-900 border-b-2 border-gray-300 pb-1 mb-3\">Technical Skills</h3>\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-1\">Frontend</h4>\n                  <p className=\"text-sm text-gray-700\">{resumeData.skills.frontend.join(', ')}</p>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-1\">Backend</h4>\n                  <p className=\"text-sm text-gray-700\">{resumeData.skills.backend.join(', ')}</p>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-1\">Database</h4>\n                  <p className=\"text-sm text-gray-700\">{resumeData.skills.database.join(', ')}</p>\n                </div>\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-1\">Tools</h4>\n                  <p className=\"text-sm text-gray-700\">{resumeData.skills.tools.join(', ')}</p>\n                </div>\n              </div>\n            </div>\n\n            {/* Projects */}\n            <div className=\"mb-6\">\n              <h3 className=\"text-lg font-bold text-gray-900 border-b-2 border-gray-300 pb-1 mb-3\">Key Projects</h3>\n              {resumeData.projects.map((project, index) => (\n                <div key={index} className=\"mb-3\">\n                  <h4 className=\"font-bold text-gray-900\">{project.name}</h4>\n                  <p className=\"text-sm text-gray-700 mb-1\">{project.description}</p>\n                  <p className=\"text-xs text-gray-600\">\n                    <strong>Tech:</strong> {project.technologies.join(', ')} | <strong>Link:</strong> {project.link}\n                  </p>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAUe,SAAS,UAAU,EAAE,SAAS,EAAE,OAAO,EAAkB;;IACtE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IAE5F,MAAM,aAAa;QACjB,cAAc;YACZ,MAAM;YACN,OAAO;YACP,OAAO;YACP,OAAO;YACP,UAAU;YACV,SAAS;YACT,QAAQ;YACR,UAAU;QACZ;QACA,SAAS;QACT,YAAY;YACV;gBACE,OAAO;gBACP,SAAS;gBACT,QAAQ;gBACR,UAAU;gBACV,kBAAkB;oBAChB;oBACA;oBACA;oBACA;iBACD;YACH;YACA;gBACE,OAAO;gBACP,SAAS;gBACT,QAAQ;gBACR,UAAU;gBACV,kBAAkB;oBAChB;oBACA;oBACA;oBACA;iBACD;YACH;SACD;QACD,WAAW;YACT;gBACE,QAAQ;gBACR,aAAa;gBACb,QAAQ;gBACR,UAAU;gBACV,KAAK;YACP;SACD;QACD,QAAQ;YACN,UAAU;gBAAC;gBAAS;gBAAW;gBAAc;gBAAe;aAAW;YACvE,SAAS;gBAAC;gBAAW;gBAAW;gBAAU;gBAAU;aAAU;YAC9D,UAAU;gBAAC;gBAAc;gBAAW;gBAAU;aAAQ;YACtD,OAAO;gBAAC;gBAAO;gBAAU;gBAAO;gBAAU;aAAQ;YAClD,OAAO;gBAAC;gBAAa;gBAAW;gBAAW;gBAAS;aAAQ;QAC9D;QACA,UAAU;YACR;gBACE,MAAM;gBACN,aAAa;gBACb,cAAc;oBAAC;oBAAW;oBAAY;oBAAc;iBAAc;gBAClE,MAAM;YACR;YACA;gBACE,MAAM;gBACN,aAAa;gBACb,cAAc;oBAAC;oBAAY;oBAAW;iBAAQ;gBAC9C,MAAM;YACR;SACD;QACD,cAAc;YACZ;YACA;YACA;YACA;SACD;IACH;IAEA,MAAM,YAAY;QAChB;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,SAAS;QACX;QACA;YACE,IAAI;YACJ,MAAM;YACN,aAAa;YACb,SAAS;QACX;KACD;IAED,MAAM,cAAc;QAClB,gBAAgB;QAEhB,IAAI;YACF,0BAA0B;YAC1B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,uCAAuC;YACvC,MAAM,cAAc,CAAC;;;;;0BAKD,EAAE,WAAW,YAAY,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;8BAqB3B,EAAE,WAAW,YAAY,CAAC,IAAI,CAAC;+BAC9B,EAAE,WAAW,YAAY,CAAC,KAAK,CAAC;;cAEjD,EAAE,WAAW,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,WAAW,YAAY,CAAC,KAAK,CAAC,GAAG,EAAE,WAAW,YAAY,CAAC,QAAQ,CAAC;cACzG,EAAE,WAAW,YAAY,CAAC,OAAO,CAAC,GAAG,EAAE,WAAW,YAAY,CAAC,MAAM,CAAC;;;;;;eAMrE,EAAE,WAAW,OAAO,CAAC;;;;;YAKxB,EAAE,WAAW,UAAU,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;;uCAET,EAAE,IAAI,KAAK,CAAC;qCACd,EAAE,IAAI,OAAO,CAAC,sBAAsB,EAAE,IAAI,MAAM,CAAC;;kBAEpE,EAAE,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI;;;YAGtE,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;YAKZ,EAAE,WAAW,SAAS,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;;uCAER,EAAE,IAAI,MAAM,CAAC;qCACf,EAAE,IAAI,WAAW,CAAC,sBAAsB,EAAE,IAAI,MAAM,CAAC;wBAClE,EAAE,IAAI,GAAG,CAAC;;YAEtB,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;;;qBAQH,EAAE,WAAW,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM;;;;qBAIxC,EAAE,WAAW,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM;;;;qBAIvC,EAAE,WAAW,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM;;;;qBAIxC,EAAE,WAAW,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,WAAW,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM;;;;;;;YAO9E,EAAE,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAA,UAAW,CAAC;;uCAEX,EAAE,QAAQ,IAAI,CAAC;mBACnC,EAAE,QAAQ,WAAW,CAAC;kDACS,EAAE,QAAQ,YAAY,CAAC,IAAI,CAAC,MAAM;0CAC1C,EAAE,QAAQ,IAAI,CAAC;;YAE7C,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;;cAMV,EAAE,WAAW,YAAY,CAAC,GAAG,CAAC,CAAA,cAAe,CAAC,IAAI,EAAE,YAAY,KAAK,CAAC,EAAE,IAAI,CAAC,IAAI;;;;;MAKzF,CAAC;YAED,2BAA2B;YAC3B,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAY,EAAE;gBAAE,MAAM;YAAY;YACzD,MAAM,MAAM,IAAI,eAAe,CAAC;YAChC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,OAAO,EAAE,WAAW,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,CAAC,EAAE,iBAAiB,KAAK,CAAC;YACnG,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,IAAI,eAAe,CAAC;QAEtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAC/B,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAW;;;;;;0CAC3B,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoB;;;;;;kDAClC,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAGzC,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;0CACrD,6LAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS,IAAM,oBAAoB,SAAS,EAAE;wCAC9C,WAAW,CAAC,wDAAwD,EAClE,qBAAqB,SAAS,EAAE,GAC5B,+BACA,kDACJ;wCACF,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAY,SAAS,OAAO;;;;;;8DAC5C,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA2B,SAAS,IAAI;;;;;;sEACtD,6LAAC;4DAAE,WAAU;sEAAyB,SAAS,WAAW;;;;;;;;;;;;;;;;;;uCAdzD,SAAS,EAAE;;;;;;;;;;0CAsBtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,SAAS;gCACT,UAAU;gCACV,WAAU;gCACV,YAAY;oCAAE,OAAO,eAAe,IAAI;gCAAK;gCAC7C,UAAU;oCAAE,OAAO,eAAe,IAAI;gCAAK;0CAE1C,6BACC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;sDAAK;;;;;;;;;;;yDAGR,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;sDAAK;;;;;;sDACN,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAOd,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAyC,WAAW,YAAY,CAAC,IAAI;;;;;;sDACnF,6LAAC;4CAAG,WAAU;sDAA8B,WAAW,YAAY,CAAC,KAAK;;;;;;sDACzE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;wDAAK,WAAW,YAAY,CAAC,KAAK;wDAAC;wDAAI,WAAW,YAAY,CAAC,KAAK;;;;;;;8DACrE,6LAAC;;wDAAK,WAAW,YAAY,CAAC,OAAO;wDAAC;wDAAI,WAAW,YAAY,CAAC,MAAM;;;;;;;8DACxE,6LAAC;8DAAK,WAAW,YAAY,CAAC,QAAQ;;;;;;;;;;;;;;;;;;8CAK1C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuE;;;;;;sDACrF,6LAAC;4CAAE,WAAU;sDAAiC,WAAW,OAAO;;;;;;;;;;;;8CAIlE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuE;;;;;;wCACpF,WAAW,UAAU,CAAC,GAAG,CAAC,CAAC,KAAK,sBAC/B,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAA2B,IAAI,KAAK;;;;;;kFAClD,6LAAC;wEAAE,WAAU;kFAAiB,IAAI,OAAO;;;;;;;;;;;;0EAE3C,6LAAC;gEAAK,WAAU;0EAAyB,IAAI,MAAM;;;;;;;;;;;;kEAErD,6LAAC;wDAAG,WAAU;kEACX,IAAI,gBAAgB,CAAC,GAAG,CAAC,CAAC,MAAM,oBAC/B,6LAAC;gEAAa,WAAU;0EAAW;+DAA1B;;;;;;;;;;;+CAVL;;;;;;;;;;;8CAkBd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuE;;;;;;sDACrF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAyB,WAAW,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;;;;;;;;;;;;8DAExE,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAyB,WAAW,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC;;;;;;;;;;;;8DAEvE,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAyB,WAAW,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC;;;;;;;;;;;;8DAExE,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAAmC;;;;;;sEACjD,6LAAC;4DAAE,WAAU;sEAAyB,WAAW,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAMzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuE;;;;;;wCACpF,WAAW,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBACjC,6LAAC;gDAAgB,WAAU;;kEACzB,6LAAC;wDAAG,WAAU;kEAA2B,QAAQ,IAAI;;;;;;kEACrD,6LAAC;wDAAE,WAAU;kEAA8B,QAAQ,WAAW;;;;;;kEAC9D,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;0EAAO;;;;;;4DAAc;4DAAE,QAAQ,YAAY,CAAC,IAAI,CAAC;4DAAM;0EAAG,6LAAC;0EAAO;;;;;;4DAAc;4DAAE,QAAQ,IAAI;;;;;;;;+CAJzF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAc1B;GA1YwB;KAAA", "debugId": null}}, {"offset": {"line": 7446, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/MiniGame.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useCallback } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface MiniGameProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\ntype GameType = 'menu' | 'snake' | 'tictactoe' | 'trivia';\n\ninterface TriviaQuestion {\n  question: string;\n  options: string[];\n  correct: number;\n  explanation: string;\n}\n\nexport default function MiniGame({ isVisible, onClose }: MiniGameProps) {\n  const [currentGame, setCurrentGame] = useState<GameType>('menu');\n  const [score, setScore] = useState(0);\n\n  // Snake Game State\n  const [snake, setSnake] = useState([[10, 10]]);\n  const [food, setFood] = useState([15, 15]);\n  const [direction, setDirection] = useState([0, 1]);\n  const [gameRunning, setGameRunning] = useState(false);\n\n  // TicTacToe State\n  const [board, setBoard] = useState(Array(9).fill(null));\n  const [isXNext, setIsXNext] = useState(true);\n  const [winner, setWinner] = useState<string | null>(null);\n\n  // Trivia State\n  const [currentQuestion, setCurrentQuestion] = useState(0);\n  const [triviaScore, setTriviaScore] = useState(0);\n  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);\n  const [showExplanation, setShowExplanation] = useState(false);\n\n  const triviaQuestions: TriviaQuestion[] = [\n    {\n      question: \"Siapa yang menciptakan konsep algoritma?\",\n      options: [\"Al-Khawarizmi\", \"Alan Turing\", \"Ada Lovelace\", \"Charles Babbage\"],\n      correct: 0,\n      explanation: \"Al-Khawarizmi (780-850 M) adalah matematikawan Persia yang menciptakan konsep algoritma. Kata 'algoritma' berasal dari nama latinnya 'Algorithmi'.\"\n    },\n    {\n      question: \"Bahasa pemrograman apa yang dibuat oleh Brendan Eich?\",\n      options: [\"Python\", \"Java\", \"JavaScript\", \"C++\"],\n      correct: 2,\n      explanation: \"JavaScript dibuat oleh Brendan Eich di Netscape pada tahun 1995 dalam waktu hanya 10 hari.\"\n    },\n    {\n      question: \"Apa kepanjangan dari HTML?\",\n      options: [\"Hyper Text Markup Language\", \"High Tech Modern Language\", \"Home Tool Markup Language\", \"Hyperlink Text Management Language\"],\n      correct: 0,\n      explanation: \"HTML adalah HyperText Markup Language, bahasa markup standar untuk membuat halaman web.\"\n    },\n    {\n      question: \"Siapa pendiri Linux?\",\n      options: [\"Bill Gates\", \"Steve Jobs\", \"Linus Torvalds\", \"Richard Stallman\"],\n      correct: 2,\n      explanation: \"Linus Torvalds menciptakan kernel Linux pada tahun 1991 ketika masih mahasiswa di University of Helsinki.\"\n    },\n    {\n      question: \"Apa yang dimaksud dengan 'Open Source'?\",\n      options: [\"Software berbayar\", \"Software dengan source code terbuka\", \"Software untuk perusahaan\", \"Software mobile\"],\n      correct: 1,\n      explanation: \"Open Source adalah software yang source code-nya tersedia untuk umum dan dapat dimodifikasi serta didistribusikan secara bebas.\"\n    }\n  ];\n\n  // Snake Game Logic\n  const moveSnake = useCallback(() => {\n    if (!gameRunning) return;\n\n    setSnake(currentSnake => {\n      const newSnake = [...currentSnake];\n      const head = [newSnake[0][0] + direction[0], newSnake[0][1] + direction[1]];\n\n      // Check collision with walls\n      if (head[0] < 0 || head[0] >= 20 || head[1] < 0 || head[1] >= 20) {\n        setGameRunning(false);\n        return currentSnake;\n      }\n\n      // Check collision with self\n      if (newSnake.some(segment => segment[0] === head[0] && segment[1] === head[1])) {\n        setGameRunning(false);\n        return currentSnake;\n      }\n\n      newSnake.unshift(head);\n\n      // Check if food is eaten\n      if (head[0] === food[0] && head[1] === food[1]) {\n        setScore(prev => prev + 10);\n        setFood([Math.floor(Math.random() * 20), Math.floor(Math.random() * 20)]);\n      } else {\n        newSnake.pop();\n      }\n\n      return newSnake;\n    });\n  }, [direction, food, gameRunning]);\n\n  useEffect(() => {\n    if (currentGame === 'snake' && gameRunning) {\n      const interval = setInterval(moveSnake, 200);\n      return () => clearInterval(interval);\n    }\n  }, [moveSnake, currentGame, gameRunning]);\n\n  // TicTacToe Logic\n  const checkWinner = (squares: (string | null)[]) => {\n    const lines = [\n      [0, 1, 2], [3, 4, 5], [6, 7, 8], // rows\n      [0, 3, 6], [1, 4, 7], [2, 5, 8], // columns\n      [0, 4, 8], [2, 4, 6] // diagonals\n    ];\n\n    for (let line of lines) {\n      const [a, b, c] = line;\n      if (squares[a] && squares[a] === squares[b] && squares[a] === squares[c]) {\n        return squares[a];\n      }\n    }\n    return null;\n  };\n\n  const handleTicTacToeClick = (index: number) => {\n    if (board[index] || winner) return;\n\n    const newBoard = [...board];\n    newBoard[index] = isXNext ? 'X' : 'O';\n    setBoard(newBoard);\n    setIsXNext(!isXNext);\n\n    const gameWinner = checkWinner(newBoard);\n    if (gameWinner) {\n      setWinner(gameWinner);\n      setScore(prev => prev + (gameWinner === 'X' ? 100 : 0));\n    }\n  };\n\n  // Trivia Logic\n  const handleTriviaAnswer = (answerIndex: number) => {\n    setSelectedAnswer(answerIndex);\n    setShowExplanation(true);\n    \n    if (answerIndex === triviaQuestions[currentQuestion].correct) {\n      setTriviaScore(prev => prev + 20);\n    }\n  };\n\n  const nextQuestion = () => {\n    if (currentQuestion < triviaQuestions.length - 1) {\n      setCurrentQuestion(prev => prev + 1);\n      setSelectedAnswer(null);\n      setShowExplanation(false);\n    } else {\n      // Game finished\n      setScore(triviaScore);\n      alert(`Trivia selesai! Skor akhir: ${triviaScore}/${triviaQuestions.length * 20}`);\n    }\n  };\n\n  // Reset functions\n  const resetSnake = () => {\n    setSnake([[10, 10]]);\n    setFood([15, 15]);\n    setDirection([0, 1]);\n    setGameRunning(false);\n    setScore(0);\n  };\n\n  const resetTicTacToe = () => {\n    setBoard(Array(9).fill(null));\n    setIsXNext(true);\n    setWinner(null);\n    setScore(0);\n  };\n\n  const resetTrivia = () => {\n    setCurrentQuestion(0);\n    setTriviaScore(0);\n    setSelectedAnswer(null);\n    setShowExplanation(false);\n    setScore(0);\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className=\"fixed inset-4 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 rounded-lg shadow-2xl border overflow-hidden z-50\"\n    >\n      {/* Header */}\n      <div className=\"h-16 bg-black/30 backdrop-blur-sm flex items-center justify-between px-6 text-white border-b border-white/20\">\n        <div className=\"flex items-center space-x-3\">\n          <span className=\"text-2xl\">🎮</span>\n          <div>\n            <h2 className=\"font-bold text-lg\">KangPCode Mini Games</h2>\n            <p className=\"text-sm text-purple-200\">Skor: {score}</p>\n          </div>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          {currentGame !== 'menu' && (\n            <button\n              onClick={() => setCurrentGame('menu')}\n              className=\"px-3 py-1 bg-white/20 hover:bg-white/30 rounded transition-colors text-sm\"\n            >\n              🏠 Menu\n            </button>\n          )}\n          <button\n            onClick={onClose}\n            className=\"hover:bg-white/20 p-2 rounded transition-colors\"\n          >\n            ✕\n          </button>\n        </div>\n      </div>\n\n      {/* Game Content */}\n      <div className=\"h-[calc(100%-4rem)] p-6\">\n        <AnimatePresence mode=\"wait\">\n          {currentGame === 'menu' && (\n            <motion.div\n              key=\"menu\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              exit={{ opacity: 0, y: -20 }}\n              className=\"h-full flex items-center justify-center\"\n            >\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl\">\n                <motion.button\n                  onClick={() => {\n                    setCurrentGame('snake');\n                    resetSnake();\n                  }}\n                  className=\"bg-green-600 hover:bg-green-700 text-white p-8 rounded-lg text-center transition-colors\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <div className=\"text-6xl mb-4\">🐍</div>\n                  <h3 className=\"text-xl font-bold mb-2\">Snake Game</h3>\n                  <p className=\"text-sm opacity-90\">Klasik game ular yang adiktif</p>\n                </motion.button>\n\n                <motion.button\n                  onClick={() => {\n                    setCurrentGame('tictactoe');\n                    resetTicTacToe();\n                  }}\n                  className=\"bg-blue-600 hover:bg-blue-700 text-white p-8 rounded-lg text-center transition-colors\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <div className=\"text-6xl mb-4\">⭕</div>\n                  <h3 className=\"text-xl font-bold mb-2\">Tic Tac Toe</h3>\n                  <p className=\"text-sm opacity-90\">Permainan strategi klasik</p>\n                </motion.button>\n\n                <motion.button\n                  onClick={() => {\n                    setCurrentGame('trivia');\n                    resetTrivia();\n                  }}\n                  className=\"bg-purple-600 hover:bg-purple-700 text-white p-8 rounded-lg text-center transition-colors\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <div className=\"text-6xl mb-4\">🧠</div>\n                  <h3 className=\"text-xl font-bold mb-2\">Tech Trivia</h3>\n                  <p className=\"text-sm opacity-90\">Kuis sejarah teknologi</p>\n                </motion.button>\n              </div>\n            </motion.div>\n          )}\n\n          {currentGame === 'snake' && (\n            <motion.div\n              key=\"snake\"\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -20 }}\n              className=\"h-full flex flex-col items-center justify-center text-white\"\n            >\n              <div className=\"mb-4 text-center\">\n                <h3 className=\"text-2xl font-bold mb-2\">🐍 Snake Game</h3>\n                <p className=\"text-lg\">Skor: {score}</p>\n              </div>\n              \n              <div className=\"grid grid-cols-20 gap-0 bg-gray-800 p-2 rounded-lg mb-4\">\n                {Array.from({ length: 400 }, (_, i) => {\n                  const x = i % 20;\n                  const y = Math.floor(i / 20);\n                  const isSnake = snake.some(segment => segment[0] === x && segment[1] === y);\n                  const isFood = food[0] === x && food[1] === y;\n                  \n                  return (\n                    <div\n                      key={i}\n                      className={`w-4 h-4 ${\n                        isSnake ? 'bg-green-500' : \n                        isFood ? 'bg-red-500' : \n                        'bg-gray-700'\n                      }`}\n                    />\n                  );\n                })}\n              </div>\n              \n              <div className=\"flex space-x-4\">\n                <button\n                  onClick={() => setGameRunning(!gameRunning)}\n                  className=\"px-6 py-2 bg-green-600 hover:bg-green-700 rounded transition-colors\"\n                >\n                  {gameRunning ? 'Pause' : 'Start'}\n                </button>\n                <button\n                  onClick={resetSnake}\n                  className=\"px-6 py-2 bg-red-600 hover:bg-red-700 rounded transition-colors\"\n                >\n                  Reset\n                </button>\n              </div>\n              \n              <div className=\"mt-4 text-center text-sm opacity-75\">\n                Gunakan WASD atau arrow keys untuk mengontrol ular\n              </div>\n            </motion.div>\n          )}\n\n          {currentGame === 'tictactoe' && (\n            <motion.div\n              key=\"tictactoe\"\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -20 }}\n              className=\"h-full flex flex-col items-center justify-center text-white\"\n            >\n              <div className=\"mb-6 text-center\">\n                <h3 className=\"text-2xl font-bold mb-2\">⭕ Tic Tac Toe</h3>\n                {winner ? (\n                  <p className=\"text-lg\">🎉 Pemenang: {winner}</p>\n                ) : (\n                  <p className=\"text-lg\">Giliran: {isXNext ? 'X' : 'O'}</p>\n                )}\n              </div>\n              \n              <div className=\"grid grid-cols-3 gap-2 mb-6\">\n                {board.map((cell, index) => (\n                  <button\n                    key={index}\n                    onClick={() => handleTicTacToeClick(index)}\n                    className=\"w-20 h-20 bg-gray-700 hover:bg-gray-600 rounded-lg text-3xl font-bold transition-colors\"\n                  >\n                    {cell}\n                  </button>\n                ))}\n              </div>\n              \n              <button\n                onClick={resetTicTacToe}\n                className=\"px-6 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors\"\n              >\n                Reset Game\n              </button>\n            </motion.div>\n          )}\n\n          {currentGame === 'trivia' && (\n            <motion.div\n              key=\"trivia\"\n              initial={{ opacity: 0, x: 20 }}\n              animate={{ opacity: 1, x: 0 }}\n              exit={{ opacity: 0, x: -20 }}\n              className=\"h-full flex flex-col items-center justify-center text-white max-w-2xl mx-auto\"\n            >\n              <div className=\"mb-6 text-center\">\n                <h3 className=\"text-2xl font-bold mb-2\">🧠 Tech Trivia</h3>\n                <p className=\"text-lg\">Pertanyaan {currentQuestion + 1} dari {triviaQuestions.length}</p>\n                <p className=\"text-lg\">Skor: {triviaScore}</p>\n              </div>\n              \n              <div className=\"bg-white/10 backdrop-blur-sm p-6 rounded-lg mb-6 w-full\">\n                <h4 className=\"text-xl font-bold mb-4\">{triviaQuestions[currentQuestion].question}</h4>\n                \n                <div className=\"space-y-3\">\n                  {triviaQuestions[currentQuestion].options.map((option, index) => (\n                    <button\n                      key={index}\n                      onClick={() => handleTriviaAnswer(index)}\n                      disabled={selectedAnswer !== null}\n                      className={`w-full p-3 rounded-lg text-left transition-colors ${\n                        selectedAnswer === null \n                          ? 'bg-white/20 hover:bg-white/30' \n                          : selectedAnswer === index\n                          ? index === triviaQuestions[currentQuestion].correct\n                            ? 'bg-green-600'\n                            : 'bg-red-600'\n                          : index === triviaQuestions[currentQuestion].correct\n                          ? 'bg-green-600'\n                          : 'bg-white/10'\n                      }`}\n                    >\n                      {String.fromCharCode(65 + index)}. {option}\n                    </button>\n                  ))}\n                </div>\n                \n                {showExplanation && (\n                  <div className=\"mt-4 p-4 bg-blue-600/50 rounded-lg\">\n                    <p className=\"text-sm\">{triviaQuestions[currentQuestion].explanation}</p>\n                  </div>\n                )}\n              </div>\n              \n              {showExplanation && (\n                <button\n                  onClick={nextQuestion}\n                  className=\"px-6 py-2 bg-purple-600 hover:bg-purple-700 rounded transition-colors\"\n                >\n                  {currentQuestion < triviaQuestions.length - 1 ? 'Pertanyaan Berikutnya' : 'Selesai'}\n                </button>\n              )}\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAmBe,SAAS,SAAS,EAAE,SAAS,EAAE,OAAO,EAAiB;;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;IACzD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,mBAAmB;IACnB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;YAAC;YAAI;SAAG;KAAC;IAC7C,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;QAAI;KAAG;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAC;QAAG;KAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kBAAkB;IAClB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,GAAG,IAAI,CAAC;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpD,eAAe;IACf,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,kBAAoC;QACxC;YACE,UAAU;YACV,SAAS;gBAAC;gBAAiB;gBAAe;gBAAgB;aAAkB;YAC5E,SAAS;YACT,aAAa;QACf;QACA;YACE,UAAU;YACV,SAAS;gBAAC;gBAAU;gBAAQ;gBAAc;aAAM;YAChD,SAAS;YACT,aAAa;QACf;QACA;YACE,UAAU;YACV,SAAS;gBAAC;gBAA8B;gBAA6B;gBAA6B;aAAqC;YACvI,SAAS;YACT,aAAa;QACf;QACA;YACE,UAAU;YACV,SAAS;gBAAC;gBAAc;gBAAc;gBAAkB;aAAmB;YAC3E,SAAS;YACT,aAAa;QACf;QACA;YACE,UAAU;YACV,SAAS;gBAAC;gBAAqB;gBAAuC;gBAA6B;aAAkB;YACrH,SAAS;YACT,aAAa;QACf;KACD;IAED,mBAAmB;IACnB,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE;YAC5B,IAAI,CAAC,aAAa;YAElB;mDAAS,CAAA;oBACP,MAAM,WAAW;2BAAI;qBAAa;oBAClC,MAAM,OAAO;wBAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;wBAAE,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;qBAAC;oBAE3E,6BAA6B;oBAC7B,IAAI,IAAI,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,EAAE,IAAI,MAAM,IAAI,CAAC,EAAE,GAAG,KAAK,IAAI,CAAC,EAAE,IAAI,IAAI;wBAChE,eAAe;wBACf,OAAO;oBACT;oBAEA,4BAA4B;oBAC5B,IAAI,SAAS,IAAI;2DAAC,CAAA,UAAW,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE;2DAAG;wBAC9E,eAAe;wBACf,OAAO;oBACT;oBAEA,SAAS,OAAO,CAAC;oBAEjB,yBAAyB;oBACzB,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE;wBAC9C;+DAAS,CAAA,OAAQ,OAAO;;wBACxB,QAAQ;4BAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;4BAAK,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;yBAAI;oBAC1E,OAAO;wBACL,SAAS,GAAG;oBACd;oBAEA,OAAO;gBACT;;QACF;0CAAG;QAAC;QAAW;QAAM;KAAY;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,IAAI,gBAAgB,WAAW,aAAa;gBAC1C,MAAM,WAAW,YAAY,WAAW;gBACxC;0CAAO,IAAM,cAAc;;YAC7B;QACF;6BAAG;QAAC;QAAW;QAAa;KAAY;IAExC,kBAAkB;IAClB,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ;YACZ;gBAAC;gBAAG;gBAAG;aAAE;YAAE;gBAAC;gBAAG;gBAAG;aAAE;YAAE;gBAAC;gBAAG;gBAAG;aAAE;YAC/B;gBAAC;gBAAG;gBAAG;aAAE;YAAE;gBAAC;gBAAG;gBAAG;aAAE;YAAE;gBAAC;gBAAG;gBAAG;aAAE;YAC/B;gBAAC;gBAAG;gBAAG;aAAE;YAAE;gBAAC;gBAAG;gBAAG;aAAE,CAAC,YAAY;SAClC;QAED,KAAK,IAAI,QAAQ,MAAO;YACtB,MAAM,CAAC,GAAG,GAAG,EAAE,GAAG;YAClB,IAAI,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,EAAE;gBACxE,OAAO,OAAO,CAAC,EAAE;YACnB;QACF;QACA,OAAO;IACT;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,KAAK,CAAC,MAAM,IAAI,QAAQ;QAE5B,MAAM,WAAW;eAAI;SAAM;QAC3B,QAAQ,CAAC,MAAM,GAAG,UAAU,MAAM;QAClC,SAAS;QACT,WAAW,CAAC;QAEZ,MAAM,aAAa,YAAY;QAC/B,IAAI,YAAY;YACd,UAAU;YACV,SAAS,CAAA,OAAQ,OAAO,CAAC,eAAe,MAAM,MAAM,CAAC;QACvD;IACF;IAEA,eAAe;IACf,MAAM,qBAAqB,CAAC;QAC1B,kBAAkB;QAClB,mBAAmB;QAEnB,IAAI,gBAAgB,eAAe,CAAC,gBAAgB,CAAC,OAAO,EAAE;YAC5D,eAAe,CAAA,OAAQ,OAAO;QAChC;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,kBAAkB,gBAAgB,MAAM,GAAG,GAAG;YAChD,mBAAmB,CAAA,OAAQ,OAAO;YAClC,kBAAkB;YAClB,mBAAmB;QACrB,OAAO;YACL,gBAAgB;YAChB,SAAS;YACT,MAAM,CAAC,4BAA4B,EAAE,YAAY,CAAC,EAAE,gBAAgB,MAAM,GAAG,IAAI;QACnF;IACF;IAEA,kBAAkB;IAClB,MAAM,aAAa;QACjB,SAAS;YAAC;gBAAC;gBAAI;aAAG;SAAC;QACnB,QAAQ;YAAC;YAAI;SAAG;QAChB,aAAa;YAAC;YAAG;SAAE;QACnB,eAAe;QACf,SAAS;IACX;IAEA,MAAM,iBAAiB;QACrB,SAAS,MAAM,GAAG,IAAI,CAAC;QACvB,WAAW;QACX,UAAU;QACV,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,mBAAmB;QACnB,eAAe;QACf,kBAAkB;QAClB,mBAAmB;QACnB,SAAS;IACX;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAC/B,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CAAW;;;;;;0CAC3B,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAoB;;;;;;kDAClC,6LAAC;wCAAE,WAAU;;4CAA0B;4CAAO;;;;;;;;;;;;;;;;;;;kCAGlD,6LAAC;wBAAI,WAAU;;4BACZ,gBAAgB,wBACf,6LAAC;gCACC,SAAS,IAAM,eAAe;gCAC9B,WAAU;0CACX;;;;;;0CAIH,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAOL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;oBAAC,MAAK;;wBACnB,gBAAgB,wBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;4CACP,eAAe;4CACf;wCACF;wCACA,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,6LAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAGpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;4CACP,eAAe;4CACf;wCACF;wCACA,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,6LAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;kDAGpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;4CACP,eAAe;4CACf;wCACF;wCACA,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,6LAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,6LAAC;gDAAG,WAAU;0DAAyB;;;;;;0DACvC,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;2BA9ClC;;;;;wBAoDP,gBAAgB,yBACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;;gDAAU;gDAAO;;;;;;;;;;;;;8CAGhC,6LAAC;oCAAI,WAAU;8CACZ,MAAM,IAAI,CAAC;wCAAE,QAAQ;oCAAI,GAAG,CAAC,GAAG;wCAC/B,MAAM,IAAI,IAAI;wCACd,MAAM,IAAI,KAAK,KAAK,CAAC,IAAI;wCACzB,MAAM,UAAU,MAAM,IAAI,CAAC,CAAA,UAAW,OAAO,CAAC,EAAE,KAAK,KAAK,OAAO,CAAC,EAAE,KAAK;wCACzE,MAAM,SAAS,IAAI,CAAC,EAAE,KAAK,KAAK,IAAI,CAAC,EAAE,KAAK;wCAE5C,qBACE,6LAAC;4CAEC,WAAW,CAAC,QAAQ,EAClB,UAAU,iBACV,SAAS,eACT,eACA;2CALG;;;;;oCAQX;;;;;;8CAGF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,eAAe,CAAC;4CAC/B,WAAU;sDAET,cAAc,UAAU;;;;;;sDAE3B,6LAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;;;;;;;8CAKH,6LAAC;oCAAI,WAAU;8CAAsC;;;;;;;2BA9CjD;;;;;wBAoDP,gBAAgB,6BACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;wCACvC,uBACC,6LAAC;4CAAE,WAAU;;gDAAU;gDAAc;;;;;;iEAErC,6LAAC;4CAAE,WAAU;;gDAAU;gDAAU,UAAU,MAAM;;;;;;;;;;;;;8CAIrD,6LAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC;4CAEC,SAAS,IAAM,qBAAqB;4CACpC,WAAU;sDAET;2CAJI;;;;;;;;;;8CASX,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;2BA9BG;;;;;wBAoCP,gBAAgB,0BACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;4BAAG;4BAC3B,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0B;;;;;;sDACxC,6LAAC;4CAAE,WAAU;;gDAAU;gDAAY,kBAAkB;gDAAE;gDAAO,gBAAgB,MAAM;;;;;;;sDACpF,6LAAC;4CAAE,WAAU;;gDAAU;gDAAO;;;;;;;;;;;;;8CAGhC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA0B,eAAe,CAAC,gBAAgB,CAAC,QAAQ;;;;;;sDAEjF,6LAAC;4CAAI,WAAU;sDACZ,eAAe,CAAC,gBAAgB,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACrD,6LAAC;oDAEC,SAAS,IAAM,mBAAmB;oDAClC,UAAU,mBAAmB;oDAC7B,WAAW,CAAC,kDAAkD,EAC5D,mBAAmB,OACf,kCACA,mBAAmB,QACnB,UAAU,eAAe,CAAC,gBAAgB,CAAC,OAAO,GAChD,iBACA,eACF,UAAU,eAAe,CAAC,gBAAgB,CAAC,OAAO,GAClD,iBACA,eACJ;;wDAED,OAAO,YAAY,CAAC,KAAK;wDAAO;wDAAG;;mDAf/B;;;;;;;;;;wCAoBV,iCACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;0DAAW,eAAe,CAAC,gBAAgB,CAAC,WAAW;;;;;;;;;;;;;;;;;gCAKzE,iCACC,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAET,kBAAkB,gBAAgB,MAAM,GAAG,IAAI,0BAA0B;;;;;;;2BAlD1E;;;;;;;;;;;;;;;;;;;;;;AA2DlB;GAnawB;KAAA", "debugId": null}}, {"offset": {"line": 8325, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/DarkModeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface DarkModeToggleProps {\n  onThemeChange?: (isDark: boolean) => void;\n}\n\nexport default function DarkModeToggle({ onThemeChange }: DarkModeToggleProps) {\n  const [isDark, setIsDark] = useState(false);\n  const [mounted, setMounted] = useState(false);\n\n  // Prevent hydration mismatch\n  useEffect(() => {\n    setMounted(true);\n    \n    // Check for saved theme preference or default to light mode\n    const savedTheme = localStorage.getItem('theme');\n    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n    \n    const shouldBeDark = savedTheme === 'dark' || (!savedTheme && prefersDark);\n    setIsDark(shouldBeDark);\n    \n    // Apply theme to document\n    if (shouldBeDark) {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n  }, []);\n\n  const toggleTheme = () => {\n    const newTheme = !isDark;\n    setIsDark(newTheme);\n    \n    // Save to localStorage\n    localStorage.setItem('theme', newTheme ? 'dark' : 'light');\n    \n    // Apply to document\n    if (newTheme) {\n      document.documentElement.classList.add('dark');\n    } else {\n      document.documentElement.classList.remove('dark');\n    }\n    \n    // Notify parent component\n    onThemeChange?.(newTheme);\n  };\n\n  if (!mounted) {\n    return null; // Prevent hydration mismatch\n  }\n\n  return (\n    <motion.button\n      onClick={toggleTheme}\n      className=\"relative w-16 h-8 bg-gray-300 dark:bg-gray-600 rounded-full p-1 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2\"\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n      aria-label={`Switch to ${isDark ? 'light' : 'dark'} mode`}\n    >\n      {/* Toggle Background */}\n      <div className=\"absolute inset-0 rounded-full bg-gradient-to-r from-yellow-400 to-orange-500 dark:from-blue-600 dark:to-purple-600 transition-all duration-300\" />\n      \n      {/* Toggle Circle */}\n      <motion.div\n        className=\"relative w-6 h-6 bg-white rounded-full shadow-lg flex items-center justify-center z-10\"\n        animate={{\n          x: isDark ? 32 : 0,\n        }}\n        transition={{\n          type: \"spring\",\n          stiffness: 500,\n          damping: 30\n        }}\n      >\n        {/* Icon */}\n        <motion.div\n          animate={{\n            rotate: isDark ? 180 : 0,\n          }}\n          transition={{ duration: 0.3 }}\n          className=\"text-sm\"\n        >\n          {isDark ? '🌙' : '☀️'}\n        </motion.div>\n      </motion.div>\n      \n      {/* Background Icons */}\n      <div className=\"absolute inset-0 flex items-center justify-between px-2 text-xs\">\n        <motion.span\n          animate={{\n            opacity: isDark ? 0.3 : 1,\n            scale: isDark ? 0.8 : 1,\n          }}\n          className=\"text-white\"\n        >\n          ☀️\n        </motion.span>\n        <motion.span\n          animate={{\n            opacity: isDark ? 1 : 0.3,\n            scale: isDark ? 1 : 0.8,\n          }}\n          className=\"text-white\"\n        >\n          🌙\n        </motion.span>\n      </div>\n    </motion.button>\n  );\n}\n\n// Hook for using theme in other components\nexport function useTheme() {\n  const [isDark, setIsDark] = useState(false);\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n    \n    const checkTheme = () => {\n      const isDarkMode = document.documentElement.classList.contains('dark');\n      setIsDark(isDarkMode);\n    };\n    \n    checkTheme();\n    \n    // Listen for theme changes\n    const observer = new MutationObserver(checkTheme);\n    observer.observe(document.documentElement, {\n      attributes: true,\n      attributeFilter: ['class']\n    });\n    \n    return () => observer.disconnect();\n  }, []);\n\n  return { isDark, mounted };\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AASe,SAAS,eAAe,EAAE,aAAa,EAAuB;;IAC3E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,6BAA6B;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,WAAW;YAEX,4DAA4D;YAC5D,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,MAAM,cAAc,OAAO,UAAU,CAAC,gCAAgC,OAAO;YAE7E,MAAM,eAAe,eAAe,UAAW,CAAC,cAAc;YAC9D,UAAU;YAEV,0BAA0B;YAC1B,IAAI,cAAc;gBAChB,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YACzC,OAAO;gBACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;YAC5C;QACF;mCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,MAAM,WAAW,CAAC;QAClB,UAAU;QAEV,uBAAuB;QACvB,aAAa,OAAO,CAAC,SAAS,WAAW,SAAS;QAElD,oBAAoB;QACpB,IAAI,UAAU;YACZ,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;QACzC,OAAO;YACL,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;QAC5C;QAEA,0BAA0B;QAC1B,gBAAgB;IAClB;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO,MAAM,6BAA6B;IAC5C;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACZ,SAAS;QACT,WAAU;QACV,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACxB,cAAY,CAAC,UAAU,EAAE,SAAS,UAAU,OAAO,KAAK,CAAC;;0BAGzD,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG,SAAS,KAAK;gBACnB;gBACA,YAAY;oBACV,MAAM;oBACN,WAAW;oBACX,SAAS;gBACX;0BAGA,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBACP,QAAQ,SAAS,MAAM;oBACzB;oBACA,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAET,SAAS,OAAO;;;;;;;;;;;0BAKrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,SAAS;4BACP,SAAS,SAAS,MAAM;4BACxB,OAAO,SAAS,MAAM;wBACxB;wBACA,WAAU;kCACX;;;;;;kCAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,SAAS;4BACP,SAAS,SAAS,IAAI;4BACtB,OAAO,SAAS,IAAI;wBACtB;wBACA,WAAU;kCACX;;;;;;;;;;;;;;;;;;AAMT;GAvGwB;KAAA;AA0GjB,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,WAAW;YAEX,MAAM;iDAAa;oBACjB,MAAM,aAAa,SAAS,eAAe,CAAC,SAAS,CAAC,QAAQ,CAAC;oBAC/D,UAAU;gBACZ;;YAEA;YAEA,2BAA2B;YAC3B,MAAM,WAAW,IAAI,iBAAiB;YACtC,SAAS,OAAO,CAAC,SAAS,eAAe,EAAE;gBACzC,YAAY;gBACZ,iBAAiB;oBAAC;iBAAQ;YAC5B;YAEA;sCAAO,IAAM,SAAS,UAAU;;QAClC;6BAAG,EAAE;IAEL,OAAO;QAAE;QAAQ;IAAQ;AAC3B;IAzBgB", "debugId": null}}, {"offset": {"line": 8508, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/LanguageSwitcher.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface Language {\n  code: string;\n  name: string;\n  flag: string;\n  nativeName: string;\n}\n\ninterface LanguageSwitcherProps {\n  onLanguageChange?: (language: string) => void;\n}\n\nexport default function LanguageSwitcher({ onLanguageChange }: LanguageSwitcherProps) {\n  const [currentLanguage, setCurrentLanguage] = useState('id');\n  const [isOpen, setIsOpen] = useState(false);\n  const [mounted, setMounted] = useState(false);\n\n  const languages: Language[] = [\n    {\n      code: 'id',\n      name: 'Indonesian',\n      flag: '🇮🇩',\n      nativeName: 'Bahasa Indonesia'\n    },\n    {\n      code: 'en',\n      name: 'English',\n      flag: '🇬🇧',\n      nativeName: 'English'\n    }\n  ];\n\n  useEffect(() => {\n    setMounted(true);\n    \n    // Check for saved language preference\n    const savedLanguage = localStorage.getItem('language');\n    const browserLanguage = navigator.language.split('-')[0];\n    \n    const defaultLanguage = savedLanguage || \n      (languages.find(lang => lang.code === browserLanguage)?.code) || \n      'id';\n    \n    setCurrentLanguage(defaultLanguage);\n  }, []);\n\n  const changeLanguage = (languageCode: string) => {\n    setCurrentLanguage(languageCode);\n    setIsOpen(false);\n    \n    // Save to localStorage\n    localStorage.setItem('language', languageCode);\n    \n    // Update document language\n    document.documentElement.lang = languageCode;\n    \n    // Notify parent component\n    onLanguageChange?.(languageCode);\n  };\n\n  const currentLang = languages.find(lang => lang.code === currentLanguage) || languages[0];\n\n  if (!mounted) {\n    return null; // Prevent hydration mismatch\n  }\n\n  return (\n    <div className=\"relative\">\n      {/* Current Language Button */}\n      <motion.button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"flex items-center space-x-2 px-3 py-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg border border-white/20 transition-colors\"\n        whileHover={{ scale: 1.05 }}\n        whileTap={{ scale: 0.95 }}\n        aria-label=\"Change language\"\n      >\n        <span className=\"text-lg\">{currentLang.flag}</span>\n        <span className=\"text-white font-medium text-sm\">{currentLang.code.toUpperCase()}</span>\n        <motion.span\n          animate={{ rotate: isOpen ? 180 : 0 }}\n          transition={{ duration: 0.2 }}\n          className=\"text-white text-xs\"\n        >\n          ▼\n        </motion.span>\n      </motion.button>\n\n      {/* Language Dropdown */}\n      <AnimatePresence>\n        {isOpen && (\n          <>\n            {/* Backdrop */}\n            <div\n              className=\"fixed inset-0 z-10\"\n              onClick={() => setIsOpen(false)}\n            />\n            \n            {/* Dropdown Menu */}\n            <motion.div\n              initial={{ opacity: 0, y: -10, scale: 0.95 }}\n              animate={{ opacity: 1, y: 0, scale: 1 }}\n              exit={{ opacity: 0, y: -10, scale: 0.95 }}\n              transition={{ duration: 0.2 }}\n              className=\"absolute top-full mt-2 right-0 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden z-20 min-w-48\"\n            >\n              {languages.map((language) => (\n                <motion.button\n                  key={language.code}\n                  onClick={() => changeLanguage(language.code)}\n                  className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${\n                    currentLanguage === language.code \n                      ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' \n                      : 'text-gray-700 dark:text-gray-300'\n                  }`}\n                  whileHover={{ x: 4 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <span className=\"text-xl\">{language.flag}</span>\n                  <div className=\"flex-1\">\n                    <div className=\"font-medium\">{language.nativeName}</div>\n                    <div className=\"text-xs opacity-70\">{language.name}</div>\n                  </div>\n                  {currentLanguage === language.code && (\n                    <motion.span\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      className=\"text-blue-500\"\n                    >\n                      ✓\n                    </motion.span>\n                  )}\n                </motion.button>\n              ))}\n              \n              {/* Language Info */}\n              <div className=\"border-t border-gray-200 dark:border-gray-700 p-3 bg-gray-50 dark:bg-gray-800\">\n                <div className=\"text-xs text-gray-500 dark:text-gray-400 text-center\">\n                  🌐 Multilingual Portfolio\n                </div>\n              </div>\n            </motion.div>\n          </>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n\n// Hook for using current language in other components\nexport function useLanguage() {\n  const [currentLanguage, setCurrentLanguage] = useState('id');\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n    \n    const savedLanguage = localStorage.getItem('language');\n    if (savedLanguage) {\n      setCurrentLanguage(savedLanguage);\n    }\n    \n    // Listen for language changes\n    const handleStorageChange = (e: StorageEvent) => {\n      if (e.key === 'language' && e.newValue) {\n        setCurrentLanguage(e.newValue);\n      }\n    };\n    \n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, []);\n\n  return { currentLanguage, mounted };\n}\n\n// Translation helper\nexport const translations = {\n  id: {\n    // Navigation\n    'nav.home': 'Beranda',\n    'nav.about': 'Tentang',\n    'nav.projects': 'Proyek',\n    'nav.contact': 'Kontak',\n    \n    // Loading\n    'loading.title': 'Memuat Dunia KangPCode...',\n    'loading.preparing': 'Menyiapkan Kamar Virtual...',\n    'loading.computer': 'Mengaktifkan Komputer 3D...',\n    'loading.posters': 'Memuat Poster Ilmuwan...',\n    'loading.figures': 'Menyiapkan Action Figures...',\n    'loading.ready': 'Dunia KangPCode Siap!',\n    \n    // Intro\n    'intro.title': 'KangPCode',\n    'intro.subtitle': 'Portfolio 3D Interaktif',\n    'intro.description': 'Selamat datang di dunia virtual KangPCode! Jelajahi kamar virtual yang penuh dengan teknologi, proyek, dan inspirasi dari seorang developer Indonesia.',\n    'intro.enter': 'Masuk ke Kamar KangPCode',\n    'intro.click': 'Klik pintu untuk memulai petualangan 3D Anda',\n    \n    // Room\n    'room.title': 'Kamar KangPCode',\n    'room.instruction': 'Klik objek untuk berinteraksi',\n    'room.exit': 'Keluar Kamar',\n    \n    // Components\n    'computer.title': 'Komputer KDE Plasma',\n    'laptop.title': 'GitHub Viewer',\n    'book.title': 'Langkah Kode Nusantara',\n    'idcard.title': 'ID Card KangPCode',\n    'poster.title': 'Poster Ilmuwan Teknologi',\n    'figures.title': 'Action Figure Collection',\n    'whiteboard.title': 'Project Whiteboard',\n    \n    // Common\n    'common.close': 'Tutup',\n    'common.loading': 'Memuat...',\n    'common.error': 'Terjadi kesalahan',\n    'common.retry': 'Coba Lagi',\n  },\n  en: {\n    // Navigation\n    'nav.home': 'Home',\n    'nav.about': 'About',\n    'nav.projects': 'Projects',\n    'nav.contact': 'Contact',\n    \n    // Loading\n    'loading.title': 'Loading KangPCode World...',\n    'loading.preparing': 'Preparing Virtual Room...',\n    'loading.computer': 'Activating 3D Computer...',\n    'loading.posters': 'Loading Scientist Posters...',\n    'loading.figures': 'Preparing Action Figures...',\n    'loading.ready': 'KangPCode World Ready!',\n    \n    // Intro\n    'intro.title': 'KangPCode',\n    'intro.subtitle': 'Interactive 3D Portfolio',\n    'intro.description': 'Welcome to KangPCode\\'s virtual world! Explore a virtual room full of technology, projects, and inspiration from an Indonesian developer.',\n    'intro.enter': 'Enter KangPCode\\'s Room',\n    'intro.click': 'Click the door to start your 3D adventure',\n    \n    // Room\n    'room.title': 'KangPCode\\'s Room',\n    'room.instruction': 'Click objects to interact',\n    'room.exit': 'Exit Room',\n    \n    // Components\n    'computer.title': 'KDE Plasma Computer',\n    'laptop.title': 'GitHub Viewer',\n    'book.title': 'Langkah Kode Nusantara',\n    'idcard.title': 'KangPCode ID Card',\n    'poster.title': 'Technology Scientists Poster',\n    'figures.title': 'Action Figure Collection',\n    'whiteboard.title': 'Project Whiteboard',\n    \n    // Common\n    'common.close': 'Close',\n    'common.loading': 'Loading...',\n    'common.error': 'An error occurred',\n    'common.retry': 'Try Again',\n  }\n};\n\nexport function useTranslation() {\n  const { currentLanguage } = useLanguage();\n  \n  const t = (key: string): string => {\n    const keys = key.split('.');\n    let value: any = translations[currentLanguage as keyof typeof translations];\n    \n    for (const k of keys) {\n      value = value?.[k];\n    }\n    \n    return value || key;\n  };\n  \n  return { t, currentLanguage };\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAAA;;;AAHA;;;AAgBe,SAAS,iBAAiB,EAAE,gBAAgB,EAAyB;;IAClF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,YAAwB;QAC5B;YACE,MAAM;YACN,MAAM;YACN,MAAM;YACN,YAAY;QACd;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM;YACN,YAAY;QACd;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,WAAW;YAEX,sCAAsC;YACtC,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,MAAM,kBAAkB,UAAU,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;YAExD,MAAM,kBAAkB,iBACrB,UAAU,IAAI;8CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;8CAAkB,QACxD;YAEF,mBAAmB;QACrB;qCAAG,EAAE;IAEL,MAAM,iBAAiB,CAAC;QACtB,mBAAmB;QACnB,UAAU;QAEV,uBAAuB;QACvB,aAAa,OAAO,CAAC,YAAY;QAEjC,2BAA2B;QAC3B,SAAS,eAAe,CAAC,IAAI,GAAG;QAEhC,0BAA0B;QAC1B,mBAAmB;IACrB;IAEA,MAAM,cAAc,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,oBAAoB,SAAS,CAAC,EAAE;IAEzF,IAAI,CAAC,SAAS;QACZ,OAAO,MAAM,6BAA6B;IAC5C;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS,IAAM,UAAU,CAAC;gBAC1B,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;gBACxB,cAAW;;kCAEX,6LAAC;wBAAK,WAAU;kCAAW,YAAY,IAAI;;;;;;kCAC3C,6LAAC;wBAAK,WAAU;kCAAkC,YAAY,IAAI,CAAC,WAAW;;;;;;kCAC9E,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;wBACV,SAAS;4BAAE,QAAQ,SAAS,MAAM;wBAAE;wBACpC,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC;;sCAEE,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,UAAU;;;;;;sCAI3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG,CAAC;gCAAI,OAAO;4BAAK;4BAC3C,SAAS;gCAAE,SAAS;gCAAG,GAAG;gCAAG,OAAO;4BAAE;4BACtC,MAAM;gCAAE,SAAS;gCAAG,GAAG,CAAC;gCAAI,OAAO;4BAAK;4BACxC,YAAY;gCAAE,UAAU;4BAAI;4BAC5B,WAAU;;gCAET,UAAU,GAAG,CAAC,CAAC,yBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS,IAAM,eAAe,SAAS,IAAI;wCAC3C,WAAW,CAAC,kHAAkH,EAC5H,oBAAoB,SAAS,IAAI,GAC7B,oEACA,oCACJ;wCACF,YAAY;4CAAE,GAAG;wCAAE;wCACnB,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,6LAAC;gDAAK,WAAU;0DAAW,SAAS,IAAI;;;;;;0DACxC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAe,SAAS,UAAU;;;;;;kEACjD,6LAAC;wDAAI,WAAU;kEAAsB,SAAS,IAAI;;;;;;;;;;;;4CAEnD,oBAAoB,SAAS,IAAI,kBAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gDACV,SAAS;oDAAE,OAAO;gDAAE;gDACpB,SAAS;oDAAE,OAAO;gDAAE;gDACpB,WAAU;0DACX;;;;;;;uCApBE,SAAS,IAAI;;;;;8CA4BtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUtF;GAtIwB;KAAA;AAyIjB,SAAS;;IACd,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,WAAW;YAEX,MAAM,gBAAgB,aAAa,OAAO,CAAC;YAC3C,IAAI,eAAe;gBACjB,mBAAmB;YACrB;YAEA,8BAA8B;YAC9B,MAAM;6DAAsB,CAAC;oBAC3B,IAAI,EAAE,GAAG,KAAK,cAAc,EAAE,QAAQ,EAAE;wBACtC,mBAAmB,EAAE,QAAQ;oBAC/B;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;gCAAG,EAAE;IAEL,OAAO;QAAE;QAAiB;IAAQ;AACpC;IAxBgB;AA2BT,MAAM,eAAe;IAC1B,IAAI;QACF,aAAa;QACb,YAAY;QACZ,aAAa;QACb,gBAAgB;QAChB,eAAe;QAEf,UAAU;QACV,iBAAiB;QACjB,qBAAqB;QACrB,oBAAoB;QACpB,mBAAmB;QACnB,mBAAmB;QACnB,iBAAiB;QAEjB,QAAQ;QACR,eAAe;QACf,kBAAkB;QAClB,qBAAqB;QACrB,eAAe;QACf,eAAe;QAEf,OAAO;QACP,cAAc;QACd,oBAAoB;QACpB,aAAa;QAEb,aAAa;QACb,kBAAkB;QAClB,gBAAgB;QAChB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,oBAAoB;QAEpB,SAAS;QACT,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,gBAAgB;IAClB;IACA,IAAI;QACF,aAAa;QACb,YAAY;QACZ,aAAa;QACb,gBAAgB;QAChB,eAAe;QAEf,UAAU;QACV,iBAAiB;QACjB,qBAAqB;QACrB,oBAAoB;QACpB,mBAAmB;QACnB,mBAAmB;QACnB,iBAAiB;QAEjB,QAAQ;QACR,eAAe;QACf,kBAAkB;QAClB,qBAAqB;QACrB,eAAe;QACf,eAAe;QAEf,OAAO;QACP,cAAc;QACd,oBAAoB;QACpB,aAAa;QAEb,aAAa;QACb,kBAAkB;QAClB,gBAAgB;QAChB,cAAc;QACd,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,oBAAoB;QAEpB,SAAS;QACT,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,gBAAgB;IAClB;AACF;AAEO,SAAS;;IACd,MAAM,EAAE,eAAe,EAAE,GAAG;IAE5B,MAAM,IAAI,CAAC;QACT,MAAM,OAAO,IAAI,KAAK,CAAC;QACvB,IAAI,QAAa,YAAY,CAAC,gBAA6C;QAE3E,KAAK,MAAM,KAAK,KAAM;YACpB,QAAQ,OAAO,CAAC,EAAE;QACpB;QAEA,OAAO,SAAS;IAClB;IAEA,OAAO;QAAE;QAAG;IAAgB;AAC9B;IAfgB;;QACc", "debugId": null}}, {"offset": {"line": 8889, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/MusicToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface MusicToggleProps {\n  onVolumeChange?: (volume: number) => void;\n}\n\nexport default function MusicToggle({ onVolumeChange }: MusicToggleProps) {\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [volume, setVolume] = useState(0.3);\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [showVolumeSlider, setShowVolumeSlider] = useState(false);\n  const audioRef = useRef<HTMLAudioElement>(null);\n\n  useEffect(() => {\n    // Load saved preferences\n    const savedVolume = localStorage.getItem('music-volume');\n    const savedPlaying = localStorage.getItem('music-playing');\n    \n    if (savedVolume) {\n      const vol = parseFloat(savedVolume);\n      setVolume(vol);\n      onVolumeChange?.(vol);\n    }\n    \n    if (savedPlaying === 'true') {\n      setIsPlaying(true);\n    }\n  }, [onVolumeChange]);\n\n  useEffect(() => {\n    if (audioRef.current) {\n      audioRef.current.volume = volume;\n      \n      if (isPlaying && isLoaded) {\n        audioRef.current.play().catch(console.error);\n      } else {\n        audioRef.current.pause();\n      }\n    }\n  }, [isPlaying, volume, isLoaded]);\n\n  const toggleMusic = () => {\n    if (!isLoaded) return;\n    \n    const newPlaying = !isPlaying;\n    setIsPlaying(newPlaying);\n    localStorage.setItem('music-playing', newPlaying.toString());\n  };\n\n  const handleVolumeChange = (newVolume: number) => {\n    setVolume(newVolume);\n    localStorage.setItem('music-volume', newVolume.toString());\n    onVolumeChange?.(newVolume);\n  };\n\n  const handleAudioLoad = () => {\n    setIsLoaded(true);\n  };\n\n  const handleAudioError = () => {\n    console.warn('Failed to load background music');\n    setIsLoaded(false);\n  };\n\n  return (\n    <div className=\"relative\">\n      {/* Hidden Audio Element */}\n      <audio\n        ref={audioRef}\n        loop\n        preload=\"auto\"\n        onCanPlayThrough={handleAudioLoad}\n        onError={handleAudioError}\n      >\n        <source src=\"/assets/audio/lofi.mp3\" type=\"audio/mpeg\" />\n        <source src=\"/assets/audio/lofi.ogg\" type=\"audio/ogg\" />\n        Your browser does not support the audio element.\n      </audio>\n\n      {/* Music Control Button */}\n      <motion.button\n        onClick={toggleMusic}\n        onMouseEnter={() => setShowVolumeSlider(true)}\n        onMouseLeave={() => setShowVolumeSlider(false)}\n        className={`relative w-12 h-12 rounded-full flex items-center justify-center transition-all duration-300 ${\n          isPlaying \n            ? 'bg-green-500 hover:bg-green-600 shadow-lg shadow-green-500/30' \n            : 'bg-gray-500 hover:bg-gray-600 shadow-lg'\n        } ${!isLoaded ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n        disabled={!isLoaded}\n        aria-label={isPlaying ? 'Pause music' : 'Play music'}\n      >\n        {/* Music Icon */}\n        <motion.div\n          animate={{\n            scale: isPlaying ? [1, 1.2, 1] : 1,\n          }}\n          transition={{\n            duration: 1,\n            repeat: isPlaying ? Infinity : 0,\n            ease: \"easeInOut\"\n          }}\n          className=\"text-white text-lg\"\n        >\n          {!isLoaded ? '⏳' : isPlaying ? '🎵' : '🔇'}\n        </motion.div>\n\n        {/* Sound Waves Animation */}\n        {isPlaying && (\n          <div className=\"absolute -right-1 top-1/2 transform -translate-y-1/2\">\n            {[0, 1, 2].map((i) => (\n              <motion.div\n                key={i}\n                className=\"absolute w-1 bg-white rounded-full\"\n                style={{\n                  left: `${i * 3}px`,\n                  height: '4px',\n                }}\n                animate={{\n                  height: ['4px', '12px', '4px'],\n                  opacity: [0.4, 1, 0.4],\n                }}\n                transition={{\n                  duration: 0.8,\n                  repeat: Infinity,\n                  delay: i * 0.1,\n                  ease: \"easeInOut\"\n                }}\n              />\n            ))}\n          </div>\n        )}\n\n        {/* Loading Spinner */}\n        {!isLoaded && (\n          <motion.div\n            className=\"absolute inset-0 border-2 border-white border-t-transparent rounded-full\"\n            animate={{ rotate: 360 }}\n            transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n          />\n        )}\n      </motion.button>\n\n      {/* Volume Slider */}\n      <motion.div\n        initial={{ opacity: 0, x: -20, scale: 0.8 }}\n        animate={{\n          opacity: showVolumeSlider && isLoaded ? 1 : 0,\n          x: showVolumeSlider && isLoaded ? -60 : -20,\n          scale: showVolumeSlider && isLoaded ? 1 : 0.8,\n        }}\n        transition={{ duration: 0.2 }}\n        className=\"absolute right-full top-1/2 transform -translate-y-1/2 mr-2 pointer-events-none\"\n        style={{ pointerEvents: showVolumeSlider && isLoaded ? 'auto' : 'none' }}\n      >\n        <div className=\"bg-black/80 backdrop-blur-sm rounded-lg p-3 flex items-center space-x-3 border border-white/20\">\n          <span className=\"text-white text-xs\">🔊</span>\n          <input\n            type=\"range\"\n            min=\"0\"\n            max=\"1\"\n            step=\"0.1\"\n            value={volume}\n            onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}\n            className=\"w-20 h-1 bg-gray-600 rounded-lg appearance-none cursor-pointer slider\"\n            style={{\n              background: `linear-gradient(to right, #10b981 0%, #10b981 ${volume * 100}%, #4b5563 ${volume * 100}%, #4b5563 100%)`\n            }}\n          />\n          <span className=\"text-white text-xs font-mono w-8\">\n            {Math.round(volume * 100)}%\n          </span>\n        </div>\n      </motion.div>\n\n      {/* Music Info Tooltip */}\n      <motion.div\n        initial={{ opacity: 0, y: 10 }}\n        animate={{\n          opacity: showVolumeSlider && isLoaded ? 1 : 0,\n          y: showVolumeSlider && isLoaded ? 0 : 10,\n        }}\n        transition={{ duration: 0.2, delay: 0.1 }}\n        className=\"absolute top-full mt-2 left-1/2 transform -translate-x-1/2 pointer-events-none\"\n      >\n        <div className=\"bg-black/80 backdrop-blur-sm rounded-lg px-3 py-2 border border-white/20\">\n          <div className=\"text-white text-xs text-center\">\n            <div className=\"font-medium\">🎧 Lofi Campus Vibes</div>\n            <div className=\"opacity-70\">Background Music</div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Custom Slider Styles */}\n      <style jsx>{`\n        .slider::-webkit-slider-thumb {\n          appearance: none;\n          width: 16px;\n          height: 16px;\n          border-radius: 50%;\n          background: #10b981;\n          cursor: pointer;\n          border: 2px solid #ffffff;\n          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n        }\n        \n        .slider::-moz-range-thumb {\n          width: 16px;\n          height: 16px;\n          border-radius: 50%;\n          background: #10b981;\n          cursor: pointer;\n          border: 2px solid #ffffff;\n          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);\n        }\n      `}</style>\n    </div>\n  );\n}\n\n// Hook for controlling music from other components\nexport function useMusic() {\n  const [isPlaying, setIsPlaying] = useState(false);\n  const [volume, setVolume] = useState(0.3);\n\n  useEffect(() => {\n    const savedPlaying = localStorage.getItem('music-playing');\n    const savedVolume = localStorage.getItem('music-volume');\n    \n    if (savedPlaying) {\n      setIsPlaying(savedPlaying === 'true');\n    }\n    \n    if (savedVolume) {\n      setVolume(parseFloat(savedVolume));\n    }\n    \n    // Listen for changes\n    const handleStorageChange = (e: StorageEvent) => {\n      if (e.key === 'music-playing' && e.newValue) {\n        setIsPlaying(e.newValue === 'true');\n      }\n      if (e.key === 'music-volume' && e.newValue) {\n        setVolume(parseFloat(e.newValue));\n      }\n    };\n    \n    window.addEventListener('storage', handleStorageChange);\n    return () => window.removeEventListener('storage', handleStorageChange);\n  }, []);\n\n  const toggleMusic = () => {\n    const newPlaying = !isPlaying;\n    setIsPlaying(newPlaying);\n    localStorage.setItem('music-playing', newPlaying.toString());\n  };\n\n  const setMusicVolume = (newVolume: number) => {\n    setVolume(newVolume);\n    localStorage.setItem('music-volume', newVolume.toString());\n  };\n\n  return {\n    isPlaying,\n    volume,\n    toggleMusic,\n    setVolume: setMusicVolume,\n  };\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;;;AAHA;;;;AASe,SAAS,YAAY,EAAE,cAAc,EAAoB;;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,yBAAyB;YACzB,MAAM,cAAc,aAAa,OAAO,CAAC;YACzC,MAAM,eAAe,aAAa,OAAO,CAAC;YAE1C,IAAI,aAAa;gBACf,MAAM,MAAM,WAAW;gBACvB,UAAU;gBACV,iBAAiB;YACnB;YAEA,IAAI,iBAAiB,QAAQ;gBAC3B,aAAa;YACf;QACF;gCAAG;QAAC;KAAe;IAEnB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,SAAS,OAAO,EAAE;gBACpB,SAAS,OAAO,CAAC,MAAM,GAAG;gBAE1B,IAAI,aAAa,UAAU;oBACzB,SAAS,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,QAAQ,KAAK;gBAC7C,OAAO;oBACL,SAAS,OAAO,CAAC,KAAK;gBACxB;YACF;QACF;gCAAG;QAAC;QAAW;QAAQ;KAAS;IAEhC,MAAM,cAAc;QAClB,IAAI,CAAC,UAAU;QAEf,MAAM,aAAa,CAAC;QACpB,aAAa;QACb,aAAa,OAAO,CAAC,iBAAiB,WAAW,QAAQ;IAC3D;IAEA,MAAM,qBAAqB,CAAC;QAC1B,UAAU;QACV,aAAa,OAAO,CAAC,gBAAgB,UAAU,QAAQ;QACvD,iBAAiB;IACnB;IAEA,MAAM,kBAAkB;QACtB,YAAY;IACd;IAEA,MAAM,mBAAmB;QACvB,QAAQ,IAAI,CAAC;QACb,YAAY;IACd;IAEA,qBACE,6LAAC;kDAAc;;0BAEb,6LAAC;gBACC,KAAK;gBACL,IAAI;gBACJ,SAAQ;gBACR,kBAAkB;gBAClB,SAAS;;;kCAET,6LAAC;wBAAO,KAAI;wBAAyB,MAAK;;;;;;;kCAC1C,6LAAC;wBAAO,KAAI;wBAAyB,MAAK;;;;;;;oBAAc;;;;;;;0BAK1D,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,cAAc,IAAM,oBAAoB;gBACxC,cAAc,IAAM,oBAAoB;gBACxC,WAAW,CAAC,6FAA6F,EACvG,YACI,kEACA,0CACL,CAAC,EAAE,CAAC,WAAW,kCAAkC,kBAAkB;gBACpE,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAI;gBACvB,UAAU,CAAC;gBACX,cAAY,YAAY,gBAAgB;;kCAGxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BACP,OAAO,YAAY;gCAAC;gCAAG;gCAAK;6BAAE,GAAG;wBACnC;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ,YAAY,WAAW;4BAC/B,MAAM;wBACR;wBACA,WAAU;kCAET,CAAC,WAAW,MAAM,YAAY,OAAO;;;;;;oBAIvC,2BACC,6LAAC;kEAAc;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,IAAI,EAAE,EAAE,CAAC;oCAClB,QAAQ;gCACV;gCACA,SAAS;oCACP,QAAQ;wCAAC;wCAAO;wCAAQ;qCAAM;oCAC9B,SAAS;wCAAC;wCAAK;wCAAG;qCAAI;gCACxB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,OAAO,IAAI;oCACX,MAAM;gCACR;+BAfK;;;;;;;;;;oBAsBZ,CAAC,0BACA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,QAAQ;wBAAI;wBACvB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;4BAAU,MAAM;wBAAS;;;;;;;;;;;;0BAMlE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;oBAAI,OAAO;gBAAI;gBAC1C,SAAS;oBACP,SAAS,oBAAoB,WAAW,IAAI;oBAC5C,GAAG,oBAAoB,WAAW,CAAC,KAAK,CAAC;oBACzC,OAAO,oBAAoB,WAAW,IAAI;gBAC5C;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;gBACV,OAAO;oBAAE,eAAe,oBAAoB,WAAW,SAAS;gBAAO;0BAEvE,cAAA,6LAAC;8DAAc;;sCACb,6LAAC;sEAAe;sCAAqB;;;;;;sCACrC,6LAAC;4BACC,MAAK;4BACL,KAAI;4BACJ,KAAI;4BACJ,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,mBAAmB,WAAW,EAAE,MAAM,CAAC,KAAK;4BAE7D,OAAO;gCACL,YAAY,CAAC,8CAA8C,EAAE,SAAS,IAAI,WAAW,EAAE,SAAS,IAAI,gBAAgB,CAAC;4BACvH;sEAHU;;;;;;sCAKZ,6LAAC;sEAAe;;gCACb,KAAK,KAAK,CAAC,SAAS;gCAAK;;;;;;;;;;;;;;;;;;0BAMhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBACP,SAAS,oBAAoB,WAAW,IAAI;oBAC5C,GAAG,oBAAoB,WAAW,IAAI;gBACxC;gBACA,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;gBACxC,WAAU;0BAEV,cAAA,6LAAC;8DAAc;8BACb,cAAA,6LAAC;kEAAc;;0CACb,6LAAC;0EAAc;0CAAc;;;;;;0CAC7B,6LAAC;0EAAc;0CAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BxC;GAtNwB;KAAA;AAyNjB,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR,MAAM,eAAe,aAAa,OAAO,CAAC;YAC1C,MAAM,cAAc,aAAa,OAAO,CAAC;YAEzC,IAAI,cAAc;gBAChB,aAAa,iBAAiB;YAChC;YAEA,IAAI,aAAa;gBACf,UAAU,WAAW;YACvB;YAEA,qBAAqB;YACrB,MAAM;0DAAsB,CAAC;oBAC3B,IAAI,EAAE,GAAG,KAAK,mBAAmB,EAAE,QAAQ,EAAE;wBAC3C,aAAa,EAAE,QAAQ,KAAK;oBAC9B;oBACA,IAAI,EAAE,GAAG,KAAK,kBAAkB,EAAE,QAAQ,EAAE;wBAC1C,UAAU,WAAW,EAAE,QAAQ;oBACjC;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;sCAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;6BAAG,EAAE;IAEL,MAAM,cAAc;QAClB,MAAM,aAAa,CAAC;QACpB,aAAa;QACb,aAAa,OAAO,CAAC,iBAAiB,WAAW,QAAQ;IAC3D;IAEA,MAAM,iBAAiB,CAAC;QACtB,UAAU;QACV,aAAa,OAAO,CAAC,gBAAgB,UAAU,QAAQ;IACzD;IAEA,OAAO;QACL;QACA;QACA;QACA,WAAW;IACb;AACF;IA/CgB", "debugId": null}}, {"offset": {"line": 9282, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/PWAInstallPrompt.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface BeforeInstallPromptEvent extends Event {\n  readonly platforms: string[];\n  readonly userChoice: Promise<{\n    outcome: 'accepted' | 'dismissed';\n    platform: string;\n  }>;\n  prompt(): Promise<void>;\n}\n\nexport default function PWAInstallPrompt() {\n  const [deferredPrompt, setDeferredPrompt] = useState<BeforeInstallPromptEvent | null>(null);\n  const [showPrompt, setShowPrompt] = useState(false);\n  const [isInstalled, setIsInstalled] = useState(false);\n  const [isStandalone, setIsStandalone] = useState(false);\n\n  useEffect(() => {\n    // Check if app is already installed or running in standalone mode\n    const checkInstallStatus = () => {\n      const isStandaloneMode = window.matchMedia('(display-mode: standalone)').matches ||\n                              (window.navigator as any).standalone ||\n                              document.referrer.includes('android-app://');\n      \n      setIsStandalone(isStandaloneMode);\n      setIsInstalled(isStandaloneMode);\n    };\n\n    checkInstallStatus();\n\n    // Listen for the beforeinstallprompt event\n    const handleBeforeInstallPrompt = (e: Event) => {\n      e.preventDefault();\n      setDeferredPrompt(e as BeforeInstallPromptEvent);\n      \n      // Show prompt after a delay if not dismissed before\n      const hasBeenDismissed = localStorage.getItem('pwa-install-dismissed');\n      if (!hasBeenDismissed && !isInstalled) {\n        setTimeout(() => {\n          setShowPrompt(true);\n        }, 10000); // Show after 10 seconds\n      }\n    };\n\n    // Listen for app installed event\n    const handleAppInstalled = () => {\n      setIsInstalled(true);\n      setShowPrompt(false);\n      setDeferredPrompt(null);\n      localStorage.setItem('pwa-installed', 'true');\n    };\n\n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n    window.addEventListener('appinstalled', handleAppInstalled);\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n      window.removeEventListener('appinstalled', handleAppInstalled);\n    };\n  }, [isInstalled]);\n\n  const handleInstallClick = async () => {\n    if (!deferredPrompt) return;\n\n    try {\n      await deferredPrompt.prompt();\n      const { outcome } = await deferredPrompt.userChoice;\n      \n      if (outcome === 'accepted') {\n        console.log('User accepted the install prompt');\n      } else {\n        console.log('User dismissed the install prompt');\n        localStorage.setItem('pwa-install-dismissed', 'true');\n      }\n      \n      setDeferredPrompt(null);\n      setShowPrompt(false);\n    } catch (error) {\n      console.error('Error during installation:', error);\n    }\n  };\n\n  const handleDismiss = () => {\n    setShowPrompt(false);\n    localStorage.setItem('pwa-install-dismissed', 'true');\n  };\n\n  const handleManualInstall = () => {\n    setShowPrompt(true);\n  };\n\n  // Don't show if already installed or in standalone mode\n  if (isInstalled || isStandalone) {\n    return null;\n  }\n\n  return (\n    <>\n      {/* Manual Install Button (always visible when not installed) */}\n      <motion.button\n        onClick={handleManualInstall}\n        className=\"fixed bottom-4 right-4 bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-full shadow-lg z-40\"\n        whileHover={{ scale: 1.1 }}\n        whileTap={{ scale: 0.9 }}\n        initial={{ opacity: 0, y: 100 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 2 }}\n        title=\"Install as App\"\n      >\n        <span className=\"text-xl\">📱</span>\n      </motion.button>\n\n      {/* Install Prompt Modal */}\n      <AnimatePresence>\n        {showPrompt && deferredPrompt && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4\"\n          >\n            <motion.div\n              initial={{ opacity: 0, scale: 0.9, y: 20 }}\n              animate={{ opacity: 1, scale: 1, y: 0 }}\n              exit={{ opacity: 0, scale: 0.9, y: 20 }}\n              className=\"bg-white dark:bg-gray-800 rounded-xl shadow-2xl max-w-md w-full p-6 border border-gray-200 dark:border-gray-700\"\n            >\n              {/* Header */}\n              <div className=\"text-center mb-6\">\n                <div className=\"text-6xl mb-4\">📱</div>\n                <h2 className=\"text-2xl font-bold text-gray-900 dark:text-white mb-2\">\n                  Install KangPCode Portfolio\n                </h2>\n                <p className=\"text-gray-600 dark:text-gray-300\">\n                  Get the full experience with our Progressive Web App\n                </p>\n              </div>\n\n              {/* Features */}\n              <div className=\"space-y-3 mb-6\">\n                <div className=\"flex items-center space-x-3 text-gray-700 dark:text-gray-300\">\n                  <span className=\"text-green-500\">✓</span>\n                  <span>Works offline</span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-gray-700 dark:text-gray-300\">\n                  <span className=\"text-green-500\">✓</span>\n                  <span>Fast loading</span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-gray-700 dark:text-gray-300\">\n                  <span className=\"text-green-500\">✓</span>\n                  <span>App-like experience</span>\n                </div>\n                <div className=\"flex items-center space-x-3 text-gray-700 dark:text-gray-300\">\n                  <span className=\"text-green-500\">✓</span>\n                  <span>No app store required</span>\n                </div>\n              </div>\n\n              {/* Buttons */}\n              <div className=\"flex space-x-3\">\n                <motion.button\n                  onClick={handleInstallClick}\n                  className=\"flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors\"\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  Install App\n                </motion.button>\n                <motion.button\n                  onClick={handleDismiss}\n                  className=\"flex-1 bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 font-medium py-3 px-4 rounded-lg transition-colors\"\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  Maybe Later\n                </motion.button>\n              </div>\n\n              {/* Installation Instructions for different browsers */}\n              <div className=\"mt-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg\">\n                <p className=\"text-xs text-gray-600 dark:text-gray-400 text-center\">\n                  💡 On mobile: Tap \"Add to Home Screen\" in your browser menu\n                </p>\n              </div>\n            </motion.div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </>\n  );\n}\n\n// Hook for PWA status\nexport function usePWA() {\n  const [isInstalled, setIsInstalled] = useState(false);\n  const [isStandalone, setIsStandalone] = useState(false);\n  const [canInstall, setCanInstall] = useState(false);\n\n  useEffect(() => {\n    const checkPWAStatus = () => {\n      const standalone = window.matchMedia('(display-mode: standalone)').matches ||\n                        (window.navigator as any).standalone ||\n                        document.referrer.includes('android-app://');\n      \n      setIsStandalone(standalone);\n      setIsInstalled(standalone || localStorage.getItem('pwa-installed') === 'true');\n    };\n\n    const handleBeforeInstallPrompt = () => {\n      setCanInstall(true);\n    };\n\n    const handleAppInstalled = () => {\n      setIsInstalled(true);\n      setCanInstall(false);\n    };\n\n    checkPWAStatus();\n    \n    window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n    window.addEventListener('appinstalled', handleAppInstalled);\n\n    return () => {\n      window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt);\n      window.removeEventListener('appinstalled', handleAppInstalled);\n    };\n  }, []);\n\n  return {\n    isInstalled,\n    isStandalone,\n    canInstall,\n  };\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;;;AAHA;;;AAce,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IACtF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,kEAAkE;YAClE,MAAM;iEAAqB;oBACzB,MAAM,mBAAmB,OAAO,UAAU,CAAC,8BAA8B,OAAO,IACxD,AAAC,OAAO,SAAS,CAAS,UAAU,IACpC,SAAS,QAAQ,CAAC,QAAQ,CAAC;oBAEnD,gBAAgB;oBAChB,eAAe;gBACjB;;YAEA;YAEA,2CAA2C;YAC3C,MAAM;wEAA4B,CAAC;oBACjC,EAAE,cAAc;oBAChB,kBAAkB;oBAElB,oDAAoD;oBACpD,MAAM,mBAAmB,aAAa,OAAO,CAAC;oBAC9C,IAAI,CAAC,oBAAoB,CAAC,aAAa;wBACrC;oFAAW;gCACT,cAAc;4BAChB;mFAAG,QAAQ,wBAAwB;oBACrC;gBACF;;YAEA,iCAAiC;YACjC,MAAM;iEAAqB;oBACzB,eAAe;oBACf,cAAc;oBACd,kBAAkB;oBAClB,aAAa,OAAO,CAAC,iBAAiB;gBACxC;;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAC/C,OAAO,gBAAgB,CAAC,gBAAgB;YAExC;8CAAO;oBACL,OAAO,mBAAmB,CAAC,uBAAuB;oBAClD,OAAO,mBAAmB,CAAC,gBAAgB;gBAC7C;;QACF;qCAAG;QAAC;KAAY;IAEhB,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB;QAErB,IAAI;YACF,MAAM,eAAe,MAAM;YAC3B,MAAM,EAAE,OAAO,EAAE,GAAG,MAAM,eAAe,UAAU;YAEnD,IAAI,YAAY,YAAY;gBAC1B,QAAQ,GAAG,CAAC;YACd,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,aAAa,OAAO,CAAC,yBAAyB;YAChD;YAEA,kBAAkB;YAClB,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,gBAAgB;QACpB,cAAc;QACd,aAAa,OAAO,CAAC,yBAAyB;IAChD;IAEA,MAAM,sBAAsB;QAC1B,cAAc;IAChB;IAEA,wDAAwD;IACxD,IAAI,eAAe,cAAc;QAC/B,OAAO;IACT;IAEA,qBACE;;0BAEE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;gBACT,WAAU;gBACV,YAAY;oBAAE,OAAO;gBAAI;gBACzB,UAAU;oBAAE,OAAO;gBAAI;gBACvB,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAI;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAE;gBACvB,OAAM;0BAEN,cAAA,6LAAC;oBAAK,WAAU;8BAAU;;;;;;;;;;;0BAI5B,6LAAC,4LAAA,CAAA,kBAAe;0BACb,cAAc,gCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;8BAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,OAAO;4BAAK,GAAG;wBAAG;wBACzC,SAAS;4BAAE,SAAS;4BAAG,OAAO;4BAAG,GAAG;wBAAE;wBACtC,MAAM;4BAAE,SAAS;4BAAG,OAAO;4BAAK,GAAG;wBAAG;wBACtC,WAAU;;0CAGV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAG,WAAU;kDAAwD;;;;;;kDAGtE,6LAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;0CAMlD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAiB;;;;;;0DACjC,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAKV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDACzB;;;;;;kDAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDACzB;;;;;;;;;;;;0CAMH,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAE,WAAU;8CAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpF;GAnLwB;KAAA;AAsLjB,SAAS;;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;mDAAiB;oBACrB,MAAM,aAAa,OAAO,UAAU,CAAC,8BAA8B,OAAO,IACxD,AAAC,OAAO,SAAS,CAAS,UAAU,IACpC,SAAS,QAAQ,CAAC,QAAQ,CAAC;oBAE7C,gBAAgB;oBAChB,eAAe,cAAc,aAAa,OAAO,CAAC,qBAAqB;gBACzE;;YAEA,MAAM;8DAA4B;oBAChC,cAAc;gBAChB;;YAEA,MAAM;uDAAqB;oBACzB,eAAe;oBACf,cAAc;gBAChB;;YAEA;YAEA,OAAO,gBAAgB,CAAC,uBAAuB;YAC/C,OAAO,gBAAgB,CAAC,gBAAgB;YAExC;oCAAO;oBACL,OAAO,mBAAmB,CAAC,uBAAuB;oBAClD,OAAO,mBAAmB,CAAC,gBAAgB;gBAC7C;;QACF;2BAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;IACF;AACF;IAxCgB", "debugId": null}}, {"offset": {"line": 9710, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/RoomScene3D.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense, useRef, useState } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport {\n  OrbitControls,\n  Environment,\n  Text,\n  Box,\n  Plane,\n  Html,\n  useTexture,\n  PerspectiveCamera\n} from '@react-three/drei';\nimport { motion } from 'framer-motion';\nimport * as THREE from 'three';\nimport ComputerGUI from './ComputerGUI';\nimport TerminalAI from './TerminalAI';\nimport LaptopWindows from './LaptopWindows';\nimport BookKangPCode from './BookKangPCode';\nimport IDCardLanyard from './IDCardLanyard';\nimport PosterIlmuwan from './PosterIlmuwan';\nimport ActionFigure from './ActionFigure';\nimport WhiteboardProject from './WhiteboardProject';\nimport ResumePDF from './ResumePDF';\nimport MiniGame from './MiniGame';\nimport DarkModeToggle from './DarkModeToggle';\nimport LanguageSwitcher from './LanguageSwitcher';\nimport MusicToggle from './MusicToggle';\nimport PWAInstallPrompt from './PWAInstallPrompt';\n\n// Room Environment Component\nfunction Room() {\n  return (\n    <group>\n      {/* Floor */}\n      <Plane \n        args={[20, 20]} \n        rotation={[-Math.PI / 2, 0, 0]} \n        position={[0, -2, 0]}\n      >\n        <meshStandardMaterial color=\"#8B4513\" />\n      </Plane>\n\n      {/* Walls */}\n      <Plane args={[20, 10]} position={[0, 3, -10]}>\n        <meshStandardMaterial color=\"#F5F5DC\" />\n      </Plane>\n      \n      <Plane args={[20, 10]} position={[-10, 3, 0]} rotation={[0, Math.PI / 2, 0]}>\n        <meshStandardMaterial color=\"#F5F5DC\" />\n      </Plane>\n      \n      <Plane args={[20, 10]} position={[10, 3, 0]} rotation={[0, -Math.PI / 2, 0]}>\n        <meshStandardMaterial color=\"#F5F5DC\" />\n      </Plane>\n\n      {/* Ceiling */}\n      <Plane \n        args={[20, 20]} \n        rotation={[Math.PI / 2, 0, 0]} \n        position={[0, 8, 0]}\n      >\n        <meshStandardMaterial color=\"#FFFFFF\" />\n      </Plane>\n    </group>\n  );\n}\n\n// Computer 3D Component\nfunction Computer3D({ onComputerClick }: { onComputerClick: () => void }) {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const [isHovered, setIsHovered] = useState(false);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;\n    }\n  });\n\n  return (\n    <group position={[-4, -1, -2]}>\n      {/* Monitor */}\n      <Box ref={meshRef} args={[3, 2, 0.2]} position={[0, 1, 0]}>\n        <meshStandardMaterial color=\"#1a1a1a\" />\n      </Box>\n\n      {/* Screen */}\n      <Plane args={[2.8, 1.8]} position={[0, 1, 0.11]}>\n        <meshStandardMaterial\n          color={isHovered ? \"#001080\" : \"#000080\"}\n          emissive={isHovered ? \"#000060\" : \"#000040\"}\n        />\n      </Plane>\n\n      {/* KDE Plasma Logo on Screen */}\n      <Html position={[0, 1, 0.12]} center>\n        <div className=\"text-white text-center pointer-events-none\">\n          <div className=\"text-4xl mb-2\">🐧</div>\n          <div className=\"text-sm font-bold\">KDE Plasma</div>\n        </div>\n      </Html>\n\n      {/* Base */}\n      <Box args={[0.5, 0.8, 0.5]} position={[0, 0, 0]}>\n        <meshStandardMaterial color=\"#333333\" />\n      </Box>\n\n      {/* Keyboard */}\n      <Box args={[2, 0.1, 1]} position={[0, -1.5, 1]}>\n        <meshStandardMaterial color=\"#2a2a2a\" />\n      </Box>\n\n      {/* Click Area */}\n      <Html position={[0, 1, 0.2]} center>\n        <div\n          className=\"w-32 h-20 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onComputerClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Klik untuk membuka KDE Plasma\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              🖱️ Klik untuk membuka\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Laptop 3D Component\nfunction Laptop3D({ onLaptopClick }: { onLaptopClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[4, -1, -1]}>\n      {/* Laptop Base */}\n      <Box args={[2.5, 0.1, 1.8]} position={[0, -1.5, 0]}>\n        <meshStandardMaterial color=\"#C0C0C0\" />\n      </Box>\n\n      {/* Laptop Screen */}\n      <Box args={[2.5, 1.6, 0.1]} position={[0, 0, -0.8]} rotation={[-0.2, 0, 0]}>\n        <meshStandardMaterial color=\"#1a1a1a\" />\n      </Box>\n\n      {/* Screen Display */}\n      <Plane args={[2.3, 1.4]} position={[0, 0, -0.75]} rotation={[-0.2, 0, 0]}>\n        <meshStandardMaterial\n          color={isHovered ? \"#0088D4\" : \"#0078D4\"}\n          emissive={isHovered ? \"#004d8b\" : \"#003d6b\"}\n        />\n      </Plane>\n\n      {/* Windows Logo on Screen */}\n      <Html position={[0, 0, -0.74]} center>\n        <div className=\"text-white text-center pointer-events-none\" style={{ transform: 'rotateX(-11.5deg)' }}>\n          <div className=\"text-3xl mb-1\">🪟</div>\n          <div className=\"text-xs font-bold\">Windows 11</div>\n        </div>\n      </Html>\n\n      {/* Click Area */}\n      <Html position={[0, 0, -0.7]} center>\n        <div\n          className=\"w-24 h-16 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onLaptopClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Klik untuk membuka GitHub Viewer\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              🖱️ GitHub Viewer\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Book 3D Component\nfunction Book3D({ onBookClick }: { onBookClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[0, -1.3, 0]}>\n      <Box args={[1.5, 0.2, 1]} position={[0, 0, 0]}>\n        <meshStandardMaterial color={isHovered ? \"#A0522D\" : \"#8B4513\"} />\n      </Box>\n\n      {/* Book Cover Text */}\n      <Html position={[0, 0.15, 0.51]} center>\n        <div className=\"text-white text-center pointer-events-none text-xs\">\n          <div className=\"font-bold\">📘</div>\n          <div className=\"font-bold\">Langkah</div>\n          <div className=\"font-bold\">Kode</div>\n          <div className=\"font-bold\">Nusantara</div>\n        </div>\n      </Html>\n\n      <Html position={[0, 0.2, 0]} center>\n        <div\n          className=\"w-16 h-12 bg-transparent cursor-pointer hover:bg-yellow-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onBookClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Langkah Kode Nusantara\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              📖 Baca Buku\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// ID Card Component\nfunction IDCard3D({ onIDCardClick }: { onIDCardClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[0, -0.9, 0.3]}>\n      <Box args={[0.8, 0.05, 0.5]} position={[0, 0, 0]}>\n        <meshStandardMaterial color={isHovered ? \"#F0F0F0\" : \"#FFFFFF\"} />\n      </Box>\n\n      {/* ID Card Content */}\n      <Html position={[0, 0.03, 0.26]} center>\n        <div className=\"text-black text-center pointer-events-none text-xs\">\n          <div className=\"font-bold\">🪪 KangPCode</div>\n          <div className=\"text-xs\">Developer ID</div>\n        </div>\n      </Html>\n\n      <Html position={[0, 0.1, 0]} center>\n        <div\n          className=\"w-8 h-6 bg-transparent cursor-pointer hover:bg-green-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onIDCardClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"ID Card KangPCode\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              🪪 Lihat ID\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Poster Ilmuwan 3D Component\nfunction PosterIlmuwan3D({ onPosterClick }: { onPosterClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[-8, 2, -9]}>\n      {/* Poster Frame */}\n      <Box args={[3, 4, 0.1]} position={[0, 0, 0]}>\n        <meshStandardMaterial color={isHovered ? \"#8B4513\" : \"#654321\"} />\n      </Box>\n\n      {/* Poster Content */}\n      <Plane args={[2.8, 3.8]} position={[0, 0, 0.06]}>\n        <meshStandardMaterial color=\"#FFFFFF\" />\n      </Plane>\n\n      {/* Poster Text */}\n      <Html position={[0, 0, 0.07]} center>\n        <div className=\"text-black text-center pointer-events-none text-xs w-32\">\n          <div className=\"font-bold text-lg\">👨‍🔬</div>\n          <div className=\"font-bold\">POSTER</div>\n          <div className=\"font-bold\">ILMUWAN</div>\n          <div className=\"text-xs mt-2\">Tesla, Einstein,</div>\n          <div className=\"text-xs\">Al-Khawarizmi,</div>\n          <div className=\"text-xs\">Onno W. Purbo</div>\n        </div>\n      </Html>\n\n      <Html position={[0, 0, 0.1]} center>\n        <div\n          className=\"w-32 h-40 bg-transparent cursor-pointer hover:bg-purple-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onPosterClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Poster Ilmuwan Teknologi\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              👨‍🔬 Lihat Poster\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Action Figure Shelf 3D Component\nfunction ActionFigureShelf3D({ onShelfClick }: { onShelfClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[8, 0, -8]}>\n      {/* Shelf */}\n      <Box args={[2, 3, 0.5]} position={[0, 0, 0]}>\n        <meshStandardMaterial color={isHovered ? \"#D2B48C\" : \"#DEB887\"} />\n      </Box>\n\n      {/* Shelf Dividers */}\n      <Box args={[2, 0.1, 0.5]} position={[0, 1, 0]}>\n        <meshStandardMaterial color=\"#8B7355\" />\n      </Box>\n      <Box args={[2, 0.1, 0.5]} position={[0, -1, 0]}>\n        <meshStandardMaterial color=\"#8B7355\" />\n      </Box>\n\n      {/* Figures */}\n      <Html position={[0, 0, 0.3]} center>\n        <div className=\"text-center pointer-events-none\">\n          <div className=\"grid grid-cols-3 gap-1 text-lg\">\n            <div>🏴‍☠️</div>\n            <div>⚔️</div>\n            <div>🍥</div>\n            <div>⚡</div>\n            <div>🧪</div>\n            <div>⚗️</div>\n          </div>\n          <div className=\"text-xs font-bold mt-1\">Action Figures</div>\n        </div>\n      </Html>\n\n      <Html position={[0, 0, 0.4]} center>\n        <div\n          className=\"w-20 h-32 bg-transparent cursor-pointer hover:bg-orange-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onShelfClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Action Figure Collection\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              🧸 Lihat Koleksi\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Whiteboard 3D Component\nfunction Whiteboard3D({ onWhiteboardClick }: { onWhiteboardClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[0, 2, -9]}>\n      {/* Whiteboard Frame */}\n      <Box args={[4, 3, 0.1]} position={[0, 0, 0]}>\n        <meshStandardMaterial color=\"#2F4F4F\" />\n      </Box>\n\n      {/* Whiteboard Surface */}\n      <Plane args={[3.8, 2.8]} position={[0, 0, 0.06]}>\n        <meshStandardMaterial color={isHovered ? \"#F8F8FF\" : \"#FFFFFF\"} />\n      </Plane>\n\n      {/* Project Pins */}\n      <Html position={[0, 0, 0.07]} center>\n        <div className=\"text-black text-center pointer-events-none text-xs\">\n          <div className=\"font-bold text-lg\">📌</div>\n          <div className=\"font-bold\">PROJECT</div>\n          <div className=\"font-bold\">WHITEBOARD</div>\n          <div className=\"grid grid-cols-2 gap-2 mt-2 text-xs\">\n            <div className=\"bg-yellow-200 p-1 rounded\">Portfolio 3D</div>\n            <div className=\"bg-blue-200 p-1 rounded\">Langkah Kode</div>\n            <div className=\"bg-green-200 p-1 rounded\">DevTools ID</div>\n            <div className=\"bg-red-200 p-1 rounded\">Smart Campus</div>\n          </div>\n        </div>\n      </Html>\n\n      <Html position={[0, 0, 0.1]} center>\n        <div\n          className=\"w-40 h-32 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onWhiteboardClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Project Whiteboard\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              📌 Lihat Proyek\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Lighting Setup\nfunction Lighting() {\n  return (\n    <>\n      <ambientLight intensity={0.4} />\n      <pointLight position={[0, 6, 0]} intensity={0.8} />\n      <pointLight position={[-5, 4, -5]} intensity={0.6} color=\"#FFE4B5\" />\n      <pointLight position={[5, 4, -5]} intensity={0.6} color=\"#E6E6FA\" />\n      <directionalLight \n        position={[10, 10, 5]} \n        intensity={0.5}\n        castShadow\n        shadow-mapSize-width={2048}\n        shadow-mapSize-height={2048}\n      />\n    </>\n  );\n}\n\n// Main RoomScene3D Component\ninterface RoomScene3DProps {\n  onExitRoom?: () => void;\n}\n\nexport default function RoomScene3D({ onExitRoom }: RoomScene3DProps) {\n  const [isLoading, setIsLoading] = useState(true);\n  const [showComputerGUI, setShowComputerGUI] = useState(false);\n  const [showTerminalAI, setShowTerminalAI] = useState(false);\n  const [showLaptopWindows, setShowLaptopWindows] = useState(false);\n  const [showBook, setShowBook] = useState(false);\n  const [showIDCard, setShowIDCard] = useState(false);\n  const [showPoster, setShowPoster] = useState(false);\n  const [showActionFigure, setShowActionFigure] = useState(false);\n  const [showWhiteboard, setShowWhiteboard] = useState(false);\n  const [showResumePDF, setShowResumePDF] = useState(false);\n  const [showMiniGame, setShowMiniGame] = useState(false);\n\n  const handleComputerClick = () => {\n    setShowComputerGUI(true);\n  };\n\n  const handleLaptopClick = () => {\n    setShowLaptopWindows(true);\n  };\n\n  const handleBookClick = () => {\n    setShowBook(true);\n  };\n\n  const handleIDCardClick = () => {\n    setShowIDCard(true);\n  };\n\n  const handlePosterClick = () => {\n    setShowPoster(true);\n  };\n\n  const handleActionFigureClick = () => {\n    setShowActionFigure(true);\n  };\n\n  const handleWhiteboardClick = () => {\n    setShowWhiteboard(true);\n  };\n\n  const handleOpenTerminal = () => {\n    setShowTerminalAI(true);\n  };\n\n  const handleOpenResume = () => {\n    setShowResumePDF(true);\n  };\n\n  const handleOpenGames = () => {\n    setShowMiniGame(true);\n  };\n\n  const handleCloseComputer = () => {\n    setShowComputerGUI(false);\n    setShowTerminalAI(false);\n  };\n\n  const handleCloseLaptop = () => {\n    setShowLaptopWindows(false);\n  };\n\n  const handleCloseBook = () => {\n    setShowBook(false);\n  };\n\n  const handleCloseIDCard = () => {\n    setShowIDCard(false);\n  };\n\n  const handleClosePoster = () => {\n    setShowPoster(false);\n  };\n\n  const handleCloseActionFigure = () => {\n    setShowActionFigure(false);\n  };\n\n  const handleCloseWhiteboard = () => {\n    setShowWhiteboard(false);\n  };\n\n  const handleCloseResume = () => {\n    setShowResumePDF(false);\n  };\n\n  const handleCloseGames = () => {\n    setShowMiniGame(false);\n  };\n\n  return (\n    <div className=\"w-full h-screen relative\">\n      <Canvas\n        shadows\n        camera={{ position: [0, 2, 8], fov: 60 }}\n        onCreated={() => setIsLoading(false)}\n      >\n        <Suspense fallback={null}>\n          <Lighting />\n          <Room />\n          <Computer3D onComputerClick={handleComputerClick} />\n          <Laptop3D onLaptopClick={handleLaptopClick} />\n          <Book3D onBookClick={handleBookClick} />\n          <IDCard3D onIDCardClick={handleIDCardClick} />\n          <PosterIlmuwan3D onPosterClick={handlePosterClick} />\n          <ActionFigureShelf3D onShelfClick={handleActionFigureClick} />\n          <Whiteboard3D onWhiteboardClick={handleWhiteboardClick} />\n          \n          <OrbitControls\n            enablePan={true}\n            enableZoom={true}\n            enableRotate={true}\n            minDistance={3}\n            maxDistance={15}\n            minPolarAngle={0}\n            maxPolarAngle={Math.PI / 2}\n          />\n          \n          <Environment preset=\"apartment\" />\n        </Suspense>\n      </Canvas>\n\n      {/* UI Overlay */}\n      <div className=\"absolute top-4 left-4 z-10\">\n        <motion.div\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          className=\"bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white\"\n        >\n          <h2 className=\"text-xl font-bold mb-2\">Kamar KangPCode</h2>\n          <p className=\"text-sm text-gray-300\">\n            Klik objek untuk berinteraksi\n          </p>\n        </motion.div>\n      </div>\n\n      {/* UI Controls */}\n      <div className=\"absolute top-4 right-4 z-10 flex items-center space-x-3\">\n        <motion.div\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <MusicToggle />\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ delay: 0.4 }}\n        >\n          <DarkModeToggle />\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ delay: 0.6 }}\n        >\n          <LanguageSwitcher />\n        </motion.div>\n\n        <motion.button\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ delay: 0.8 }}\n          onClick={onExitRoom}\n          className=\"bg-red-500/80 hover:bg-red-600/80 backdrop-blur-sm rounded-lg px-4 py-2 text-white font-medium transition-colors\"\n        >\n          Keluar Kamar\n        </motion.button>\n      </div>\n\n      {/* Loading Overlay */}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center z-20\">\n          <div className=\"text-white text-xl\">Memuat Scene 3D...</div>\n        </div>\n      )}\n\n      {/* Computer GUI Overlay */}\n      <ComputerGUI\n        isVisible={showComputerGUI}\n        onClose={handleCloseComputer}\n        onOpenTerminal={handleOpenTerminal}\n        onOpenResume={handleOpenResume}\n        onOpenGames={handleOpenGames}\n      />\n\n      {/* Terminal AI Overlay */}\n      <TerminalAI\n        isVisible={showTerminalAI}\n        onClose={() => setShowTerminalAI(false)}\n      />\n\n      {/* Laptop Windows Overlay */}\n      <LaptopWindows\n        isVisible={showLaptopWindows}\n        onClose={handleCloseLaptop}\n      />\n\n      {/* Book Overlay */}\n      <BookKangPCode\n        isVisible={showBook}\n        onClose={handleCloseBook}\n      />\n\n      {/* ID Card Overlay */}\n      <IDCardLanyard\n        isVisible={showIDCard}\n        onClose={handleCloseIDCard}\n      />\n\n      {/* Poster Ilmuwan Overlay */}\n      <PosterIlmuwan\n        isVisible={showPoster}\n        onClose={handleClosePoster}\n      />\n\n      {/* Action Figure Overlay */}\n      <ActionFigure\n        isVisible={showActionFigure}\n        onClose={handleCloseActionFigure}\n      />\n\n      {/* Whiteboard Project Overlay */}\n      <WhiteboardProject\n        isVisible={showWhiteboard}\n        onClose={handleCloseWhiteboard}\n      />\n\n      {/* Resume PDF Overlay */}\n      <ResumePDF\n        isVisible={showResumePDF}\n        onClose={handleCloseResume}\n      />\n\n      {/* Mini Game Overlay */}\n      <MiniGame\n        isVisible={showMiniGame}\n        onClose={handleCloseGames}\n      />\n\n      {/* PWA Install Prompt */}\n      <PWAInstallPrompt />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAUA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA7BA;;;;;;;;;;;;;;;;;;;AA+BA,6BAA6B;AAC7B,SAAS;IACP,qBACE,6LAAC;;0BAEC,6LAAC,6JAAA,CAAA,QAAK;gBACJ,MAAM;oBAAC;oBAAI;iBAAG;gBACd,UAAU;oBAAC,CAAC,KAAK,EAAE,GAAG;oBAAG;oBAAG;iBAAE;gBAC9B,UAAU;oBAAC;oBAAG,CAAC;oBAAG;iBAAE;0BAEpB,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,6JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAG;0BAC1C,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAG9B,6LAAC,6JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAI;iBAAG;gBAAE,UAAU;oBAAC,CAAC;oBAAI;oBAAG;iBAAE;gBAAE,UAAU;oBAAC;oBAAG,KAAK,EAAE,GAAG;oBAAG;iBAAE;0BACzE,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAG9B,6LAAC,6JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAI;oBAAG;iBAAE;gBAAE,UAAU;oBAAC;oBAAG,CAAC,KAAK,EAAE,GAAG;oBAAG;iBAAE;0BACzE,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,6JAAA,CAAA,QAAK;gBACJ,MAAM;oBAAC;oBAAI;iBAAG;gBACd,UAAU;oBAAC,KAAK,EAAE,GAAG;oBAAG;oBAAG;iBAAE;gBAC7B,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAEnB,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;;;;;;;AAIpC;KAnCS;AAqCT,wBAAwB;AACxB,SAAS,WAAW,EAAE,eAAe,EAAmC;;IACtE,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAc;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;+BAAE,CAAC;YACR,IAAI,QAAQ,OAAO,EAAE;gBACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;YACzE;QACF;;IAEA,qBACE,6LAAC;QAAM,UAAU;YAAC,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;;0BAE3B,6LAAC,6JAAA,CAAA,MAAG;gBAAC,KAAK;gBAAS,MAAM;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BACvD,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,6JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;0BAC7C,cAAA,6LAAC;oBACC,OAAO,YAAY,YAAY;oBAC/B,UAAU,YAAY,YAAY;;;;;;;;;;;0BAKtC,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;gBAAE,MAAM;0BAClC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,6LAAC;4BAAI,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAKvC,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC7C,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAE;0BAC5C,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,MAAM;0BACjC,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,6LAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;GA7DS;;QAIP,kNAAA,CAAA,WAAQ;;;MAJD;AA+DT,sBAAsB;AACtB,SAAS,SAAS,EAAE,aAAa,EAAiC;;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAM,UAAU;YAAC;YAAG,CAAC;YAAG,CAAC;SAAE;;0BAE1B,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAE;0BAChD,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAI;gBAAE,UAAU;oBAAC,CAAC;oBAAK;oBAAG;iBAAE;0BACxE,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,6JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAK;gBAAE,UAAU;oBAAC,CAAC;oBAAK;oBAAG;iBAAE;0BACtE,cAAA,6LAAC;oBACC,OAAO,YAAY,YAAY;oBAC/B,UAAU,YAAY,YAAY;;;;;;;;;;;0BAKtC,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAK;gBAAE,MAAM;0BACnC,cAAA,6LAAC;oBAAI,WAAU;oBAA6C,OAAO;wBAAE,WAAW;oBAAoB;;sCAClG,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,6LAAC;4BAAI,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAKvC,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAI;gBAAE,MAAM;0BAClC,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,6LAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;IAjDS;MAAA;AAmDT,oBAAoB;AACpB,SAAS,OAAO,EAAE,WAAW,EAA+B;;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAM,UAAU;YAAC;YAAG,CAAC;YAAK;SAAE;;0BAC3B,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAE;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC3C,cAAA,6LAAC;oBAAqB,OAAO,YAAY,YAAY;;;;;;;;;;;0BAIvD,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAM;iBAAK;gBAAE,MAAM;0BACrC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,6LAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,6LAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,6LAAC;4BAAI,WAAU;sCAAY;;;;;;;;;;;;;;;;;0BAI/B,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAK;iBAAE;gBAAE,MAAM;0BACjC,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,6LAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;IApCS;MAAA;AAsCT,oBAAoB;AACpB,SAAS,SAAS,EAAE,aAAa,EAAiC;;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAM,UAAU;YAAC;YAAG,CAAC;YAAK;SAAI;;0BAC7B,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC9C,cAAA,6LAAC;oBAAqB,OAAO,YAAY,YAAY;;;;;;;;;;;0BAIvD,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAM;iBAAK;gBAAE,MAAM;0BACrC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,6LAAC;4BAAI,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAI7B,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAK;iBAAE;gBAAE,MAAM;0BACjC,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,6LAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;IAlCS;MAAA;AAoCT,8BAA8B;AAC9B,SAAS,gBAAgB,EAAE,aAAa,EAAiC;;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAM,UAAU;YAAC,CAAC;YAAG;YAAG,CAAC;SAAE;;0BAE1B,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BACzC,cAAA,6LAAC;oBAAqB,OAAO,YAAY,YAAY;;;;;;;;;;;0BAIvD,6LAAC,6JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;0BAC7C,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;gBAAE,MAAM;0BAClC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAoB;;;;;;sCACnC,6LAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,6LAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,6LAAC;4BAAI,WAAU;sCAAe;;;;;;sCAC9B,6LAAC;4BAAI,WAAU;sCAAU;;;;;;sCACzB,6LAAC;4BAAI,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAI7B,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,MAAM;0BACjC,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,6LAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;IA5CS;MAAA;AA8CT,mCAAmC;AACnC,SAAS,oBAAoB,EAAE,YAAY,EAAgC;;IACzE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAM,UAAU;YAAC;YAAG;YAAG,CAAC;SAAE;;0BAEzB,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BACzC,cAAA,6LAAC;oBAAqB,OAAO,YAAY,YAAY;;;;;;;;;;;0BAIvD,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC3C,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAE9B,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAG;iBAAE;0BAC5C,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,MAAM;0BACjC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAI;;;;;;8CACL,6LAAC;8CAAI;;;;;;8CACL,6LAAC;8CAAI;;;;;;8CACL,6LAAC;8CAAI;;;;;;8CACL,6LAAC;8CAAI;;;;;;8CACL,6LAAC;8CAAI;;;;;;;;;;;;sCAEP,6LAAC;4BAAI,WAAU;sCAAyB;;;;;;;;;;;;;;;;;0BAI5C,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,MAAM;0BACjC,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,6LAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;IAlDS;MAAA;AAoDT,0BAA0B;AAC1B,SAAS,aAAa,EAAE,iBAAiB,EAAqC;;IAC5E,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAM,UAAU;YAAC;YAAG;YAAG,CAAC;SAAE;;0BAEzB,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BACzC,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,6JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;0BAC7C,cAAA,6LAAC;oBAAqB,OAAO,YAAY,YAAY;;;;;;;;;;;0BAIvD,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;gBAAE,MAAM;0BAClC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAoB;;;;;;sCACnC,6LAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,6LAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAA4B;;;;;;8CAC3C,6LAAC;oCAAI,WAAU;8CAA0B;;;;;;8CACzC,6LAAC;oCAAI,WAAU;8CAA2B;;;;;;8CAC1C,6LAAC;oCAAI,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;;;;;;0BAK9C,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,MAAM;0BACjC,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,6LAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;IA/CS;MAAA;AAiDT,iBAAiB;AACjB,SAAS;IACP,qBACE;;0BACE,6LAAC;gBAAa,WAAW;;;;;;0BACzB,6LAAC;gBAAW,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,WAAW;;;;;;0BAC5C,6LAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAG;oBAAG,CAAC;iBAAE;gBAAE,WAAW;gBAAK,OAAM;;;;;;0BACzD,6LAAC;gBAAW,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAE;gBAAE,WAAW;gBAAK,OAAM;;;;;;0BACxD,6LAAC;gBACC,UAAU;oBAAC;oBAAI;oBAAI;iBAAE;gBACrB,WAAW;gBACX,UAAU;gBACV,wBAAsB;gBACtB,yBAAuB;;;;;;;;AAI/B;MAhBS;AAuBM,SAAS,YAAY,EAAE,UAAU,EAAoB;;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,sBAAsB;QAC1B,mBAAmB;IACrB;IAEA,MAAM,oBAAoB;QACxB,qBAAqB;IACvB;IAEA,MAAM,kBAAkB;QACtB,YAAY;IACd;IAEA,MAAM,oBAAoB;QACxB,cAAc;IAChB;IAEA,MAAM,oBAAoB;QACxB,cAAc;IAChB;IAEA,MAAM,0BAA0B;QAC9B,oBAAoB;IACtB;IAEA,MAAM,wBAAwB;QAC5B,kBAAkB;IACpB;IAEA,MAAM,qBAAqB;QACzB,kBAAkB;IACpB;IAEA,MAAM,mBAAmB;QACvB,iBAAiB;IACnB;IAEA,MAAM,kBAAkB;QACtB,gBAAgB;IAClB;IAEA,MAAM,sBAAsB;QAC1B,mBAAmB;QACnB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB;QACxB,qBAAqB;IACvB;IAEA,MAAM,kBAAkB;QACtB,YAAY;IACd;IAEA,MAAM,oBAAoB;QACxB,cAAc;IAChB;IAEA,MAAM,oBAAoB;QACxB,cAAc;IAChB;IAEA,MAAM,0BAA0B;QAC9B,oBAAoB;IACtB;IAEA,MAAM,wBAAwB;QAC5B,kBAAkB;IACpB;IAEA,MAAM,oBAAoB;QACxB,iBAAiB;IACnB;IAEA,MAAM,mBAAmB;QACvB,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,sMAAA,CAAA,SAAM;gBACL,OAAO;gBACP,QAAQ;oBAAE,UAAU;wBAAC;wBAAG;wBAAG;qBAAE;oBAAE,KAAK;gBAAG;gBACvC,WAAW,IAAM,aAAa;0BAE9B,cAAA,6LAAC,6JAAA,CAAA,WAAQ;oBAAC,UAAU;;sCAClB,6LAAC;;;;;sCACD,6LAAC;;;;;sCACD,6LAAC;4BAAW,iBAAiB;;;;;;sCAC7B,6LAAC;4BAAS,eAAe;;;;;;sCACzB,6LAAC;4BAAO,aAAa;;;;;;sCACrB,6LAAC;4BAAS,eAAe;;;;;;sCACzB,6LAAC;4BAAgB,eAAe;;;;;;sCAChC,6LAAC;4BAAoB,cAAc;;;;;;sCACnC,6LAAC;4BAAa,mBAAmB;;;;;;sCAEjC,6LAAC,oKAAA,CAAA,gBAAa;4BACZ,WAAW;4BACX,YAAY;4BACZ,cAAc;4BACd,aAAa;4BACb,aAAa;4BACb,eAAe;4BACf,eAAe,KAAK,EAAE,GAAG;;;;;;sCAG3B,6LAAC,kKAAA,CAAA,cAAW;4BAAC,QAAO;;;;;;;;;;;;;;;;;0BAKxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAyB;;;;;;sCACvC,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAOzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,6LAAC,6HAAA,CAAA,UAAW;;;;;;;;;;kCAGd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,6LAAC,gIAAA,CAAA,UAAc;;;;;;;;;;kCAGjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,6LAAC,kIAAA,CAAA,UAAgB;;;;;;;;;;kCAGnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;YAMF,2BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAAqB;;;;;;;;;;;0BAKxC,6LAAC,6HAAA,CAAA,UAAW;gBACV,WAAW;gBACX,SAAS;gBACT,gBAAgB;gBAChB,cAAc;gBACd,aAAa;;;;;;0BAIf,6LAAC,4HAAA,CAAA,UAAU;gBACT,WAAW;gBACX,SAAS,IAAM,kBAAkB;;;;;;0BAInC,6LAAC,+HAAA,CAAA,UAAa;gBACZ,WAAW;gBACX,SAAS;;;;;;0BAIX,6LAAC,+HAAA,CAAA,UAAa;gBACZ,WAAW;gBACX,SAAS;;;;;;0BAIX,6LAAC,+HAAA,CAAA,UAAa;gBACZ,WAAW;gBACX,SAAS;;;;;;0BAIX,6LAAC,+HAAA,CAAA,UAAa;gBACZ,WAAW;gBACX,SAAS;;;;;;0BAIX,6LAAC,8HAAA,CAAA,UAAY;gBACX,WAAW;gBACX,SAAS;;;;;;0BAIX,6LAAC,mIAAA,CAAA,UAAiB;gBAChB,WAAW;gBACX,SAAS;;;;;;0BAIX,6LAAC,2HAAA,CAAA,UAAS;gBACR,WAAW;gBACX,SAAS;;;;;;0BAIX,6LAAC,0HAAA,CAAA,UAAQ;gBACP,WAAW;gBACX,SAAS;;;;;;0BAIX,6LAAC,kIAAA,CAAA,UAAgB;;;;;;;;;;;AAGvB;IAvPwB;MAAA", "debugId": null}}, {"offset": {"line": 11560, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/lib/security.ts"], "sourcesContent": ["// Security utilities for protecting the portfolio\n\nexport class SecurityManager {\n  private static instance: SecurityManager;\n  private isDevMode: boolean;\n  private protectionEnabled: boolean;\n\n  private constructor() {\n    this.isDevMode = process.env.NODE_ENV === 'development';\n    this.protectionEnabled = !this.isDevMode;\n  }\n\n  static getInstance(): SecurityManager {\n    if (!SecurityManager.instance) {\n      SecurityManager.instance = new SecurityManager();\n    }\n    return SecurityManager.instance;\n  }\n\n  // Initialize all security measures\n  initialize() {\n    if (!this.protectionEnabled) {\n      console.log('🔓 Security protection disabled in development mode');\n      return;\n    }\n\n    this.disableRightClick();\n    this.disableKeyboardShortcuts();\n    this.disableTextSelection();\n    this.disableImageDragging();\n    this.detectDevTools();\n    this.obfuscateConsole();\n    this.preventFraming();\n  }\n\n  // Disable right-click context menu\n  private disableRightClick() {\n    document.addEventListener('contextmenu', (e) => {\n      e.preventDefault();\n      this.showSecurityMessage('Right-click disabled for security');\n      return false;\n    });\n  }\n\n  // Disable common keyboard shortcuts for developer tools\n  private disableKeyboardShortcuts() {\n    document.addEventListener('keydown', (e) => {\n      // Disable F12 (DevTools)\n      if (e.key === 'F12') {\n        e.preventDefault();\n        this.showSecurityMessage('Developer tools access restricted');\n        return false;\n      }\n\n      // Disable Ctrl+Shift+I (DevTools)\n      if (e.ctrlKey && e.shiftKey && e.key === 'I') {\n        e.preventDefault();\n        this.showSecurityMessage('Developer tools access restricted');\n        return false;\n      }\n\n      // Disable Ctrl+Shift+J (Console)\n      if (e.ctrlKey && e.shiftKey && e.key === 'J') {\n        e.preventDefault();\n        this.showSecurityMessage('Console access restricted');\n        return false;\n      }\n\n      // Disable Ctrl+U (View Source)\n      if (e.ctrlKey && e.key === 'u') {\n        e.preventDefault();\n        this.showSecurityMessage('Source code viewing restricted');\n        return false;\n      }\n\n      // Disable Ctrl+Shift+C (Element Inspector)\n      if (e.ctrlKey && e.shiftKey && e.key === 'C') {\n        e.preventDefault();\n        this.showSecurityMessage('Element inspector access restricted');\n        return false;\n      }\n\n      // Disable Ctrl+S (Save Page)\n      if (e.ctrlKey && e.key === 's') {\n        e.preventDefault();\n        this.showSecurityMessage('Page saving restricted');\n        return false;\n      }\n    });\n  }\n\n  // Disable text selection\n  private disableTextSelection() {\n    document.addEventListener('selectstart', (e) => {\n      e.preventDefault();\n      return false;\n    });\n\n    document.addEventListener('dragstart', (e) => {\n      e.preventDefault();\n      return false;\n    });\n  }\n\n  // Disable image dragging\n  private disableImageDragging() {\n    document.addEventListener('dragstart', (e) => {\n      if (e.target instanceof HTMLImageElement) {\n        e.preventDefault();\n        return false;\n      }\n    });\n  }\n\n  // Detect if developer tools are open\n  private detectDevTools() {\n    let devtools = { open: false, orientation: null };\n    \n    setInterval(() => {\n      if (window.outerHeight - window.innerHeight > 200 || \n          window.outerWidth - window.innerWidth > 200) {\n        if (!devtools.open) {\n          devtools.open = true;\n          this.handleDevToolsDetected();\n        }\n      } else {\n        devtools.open = false;\n      }\n    }, 500);\n\n    // Alternative detection method\n    let element = new Image();\n    Object.defineProperty(element, 'id', {\n      get: () => {\n        this.handleDevToolsDetected();\n        return 'devtools-detector';\n      }\n    });\n\n    setInterval(() => {\n      console.log(element);\n      console.clear();\n    }, 1000);\n  }\n\n  // Handle when developer tools are detected\n  private handleDevToolsDetected() {\n    console.clear();\n    console.log('%c🔒 KangPCode Portfolio Security', 'color: #ff6b6b; font-size: 20px; font-weight: bold;');\n    console.log('%cDeveloper tools detected. This portfolio is protected.', 'color: #ffa726; font-size: 14px;');\n    console.log('%cIf you\\'re interested in the code, check out the GitHub repository!', 'color: #66bb6a; font-size: 12px;');\n    console.log('%c🔗 https://github.com/kangpcode/portfolio-3d', 'color: #42a5f5; font-size: 12px;');\n    \n    // Optionally blur the page content\n    document.body.style.filter = 'blur(5px)';\n    setTimeout(() => {\n      document.body.style.filter = 'none';\n    }, 3000);\n  }\n\n  // Obfuscate console messages\n  private obfuscateConsole() {\n    // Override console methods\n    const originalLog = console.log;\n    const originalWarn = console.warn;\n    const originalError = console.error;\n\n    console.log = (...args) => {\n      if (this.isDevMode) {\n        originalLog.apply(console, args);\n      } else {\n        originalLog('%c🔒 Console access restricted', 'color: #ff6b6b;');\n      }\n    };\n\n    console.warn = (...args) => {\n      if (this.isDevMode) {\n        originalWarn.apply(console, args);\n      }\n    };\n\n    console.error = (...args) => {\n      if (this.isDevMode) {\n        originalError.apply(console, args);\n      }\n    };\n\n    // Add welcome message\n    setTimeout(() => {\n      console.clear();\n      console.log('%c👋 Welcome to KangPCode Portfolio!', 'color: #4CAF50; font-size: 24px; font-weight: bold;');\n      console.log('%c🚀 Built with Next.js, Three.js, and lots of ☕', 'color: #2196F3; font-size: 16px;');\n      console.log('%c📚 Interested in the code? Visit: https://github.com/kangpcode', 'color: #FF9800; font-size: 14px;');\n      console.log('%c💼 Looking for a developer? Let\\'s connect!', 'color: #9C27B0; font-size: 14px;');\n    }, 2000);\n  }\n\n  // Prevent the page from being embedded in frames\n  private preventFraming() {\n    if (window.top !== window.self) {\n      window.top!.location = window.self.location;\n    }\n  }\n\n  // Show security message to user\n  private showSecurityMessage(message: string) {\n    // Create a temporary notification\n    const notification = document.createElement('div');\n    notification.style.cssText = `\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      color: white;\n      padding: 12px 20px;\n      border-radius: 8px;\n      box-shadow: 0 4px 12px rgba(0,0,0,0.3);\n      z-index: 10000;\n      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n      font-size: 14px;\n      font-weight: 500;\n      animation: slideIn 0.3s ease-out;\n    `;\n\n    // Add animation keyframes\n    if (!document.getElementById('security-styles')) {\n      const style = document.createElement('style');\n      style.id = 'security-styles';\n      style.textContent = `\n        @keyframes slideIn {\n          from { transform: translateX(100%); opacity: 0; }\n          to { transform: translateX(0); opacity: 1; }\n        }\n        @keyframes slideOut {\n          from { transform: translateX(0); opacity: 1; }\n          to { transform: translateX(100%); opacity: 0; }\n        }\n      `;\n      document.head.appendChild(style);\n    }\n\n    notification.innerHTML = `\n      <div style=\"display: flex; align-items: center; gap: 8px;\">\n        <span>🔒</span>\n        <span>${message}</span>\n      </div>\n    `;\n\n    document.body.appendChild(notification);\n\n    // Remove notification after 3 seconds\n    setTimeout(() => {\n      notification.style.animation = 'slideOut 0.3s ease-in';\n      setTimeout(() => {\n        if (notification.parentNode) {\n          notification.parentNode.removeChild(notification);\n        }\n      }, 300);\n    }, 3000);\n  }\n\n  // Disable protection (for development)\n  disable() {\n    this.protectionEnabled = false;\n    console.log('🔓 Security protection disabled');\n  }\n\n  // Enable protection\n  enable() {\n    this.protectionEnabled = true;\n    this.initialize();\n    console.log('🔒 Security protection enabled');\n  }\n}\n\n// Export singleton instance\nexport const security = SecurityManager.getInstance();\n\n// React hook for security\nexport function useSecurity() {\n  const initializeSecurity = () => {\n    security.initialize();\n  };\n\n  const disableSecurity = () => {\n    security.disable();\n  };\n\n  const enableSecurity = () => {\n    security.enable();\n  };\n\n  return {\n    initializeSecurity,\n    disableSecurity,\n    enableSecurity,\n  };\n}\n"], "names": [], "mappings": "AAAA,kDAAkD;;;;;;AAQ7B;AANd,MAAM;IACX,OAAe,SAA0B;IACjC,UAAmB;IACnB,kBAA2B;IAEnC,aAAsB;QACpB,IAAI,CAAC,SAAS,GAAG,oDAAyB;QAC1C,IAAI,CAAC,iBAAiB,GAAG,CAAC,IAAI,CAAC,SAAS;IAC1C;IAEA,OAAO,cAA+B;QACpC,IAAI,CAAC,gBAAgB,QAAQ,EAAE;YAC7B,gBAAgB,QAAQ,GAAG,IAAI;QACjC;QACA,OAAO,gBAAgB,QAAQ;IACjC;IAEA,mCAAmC;IACnC,aAAa;QACX,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YAC3B,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,wBAAwB;QAC7B,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,cAAc;IACrB;IAEA,mCAAmC;IAC3B,oBAAoB;QAC1B,SAAS,gBAAgB,CAAC,eAAe,CAAC;YACxC,EAAE,cAAc;YAChB,IAAI,CAAC,mBAAmB,CAAC;YACzB,OAAO;QACT;IACF;IAEA,wDAAwD;IAChD,2BAA2B;QACjC,SAAS,gBAAgB,CAAC,WAAW,CAAC;YACpC,yBAAyB;YACzB,IAAI,EAAE,GAAG,KAAK,OAAO;gBACnB,EAAE,cAAc;gBAChB,IAAI,CAAC,mBAAmB,CAAC;gBACzB,OAAO;YACT;YAEA,kCAAkC;YAClC,IAAI,EAAE,OAAO,IAAI,EAAE,QAAQ,IAAI,EAAE,GAAG,KAAK,KAAK;gBAC5C,EAAE,cAAc;gBAChB,IAAI,CAAC,mBAAmB,CAAC;gBACzB,OAAO;YACT;YAEA,iCAAiC;YACjC,IAAI,EAAE,OAAO,IAAI,EAAE,QAAQ,IAAI,EAAE,GAAG,KAAK,KAAK;gBAC5C,EAAE,cAAc;gBAChB,IAAI,CAAC,mBAAmB,CAAC;gBACzB,OAAO;YACT;YAEA,+BAA+B;YAC/B,IAAI,EAAE,OAAO,IAAI,EAAE,GAAG,KAAK,KAAK;gBAC9B,EAAE,cAAc;gBAChB,IAAI,CAAC,mBAAmB,CAAC;gBACzB,OAAO;YACT;YAEA,2CAA2C;YAC3C,IAAI,EAAE,OAAO,IAAI,EAAE,QAAQ,IAAI,EAAE,GAAG,KAAK,KAAK;gBAC5C,EAAE,cAAc;gBAChB,IAAI,CAAC,mBAAmB,CAAC;gBACzB,OAAO;YACT;YAEA,6BAA6B;YAC7B,IAAI,EAAE,OAAO,IAAI,EAAE,GAAG,KAAK,KAAK;gBAC9B,EAAE,cAAc;gBAChB,IAAI,CAAC,mBAAmB,CAAC;gBACzB,OAAO;YACT;QACF;IACF;IAEA,yBAAyB;IACjB,uBAAuB;QAC7B,SAAS,gBAAgB,CAAC,eAAe,CAAC;YACxC,EAAE,cAAc;YAChB,OAAO;QACT;QAEA,SAAS,gBAAgB,CAAC,aAAa,CAAC;YACtC,EAAE,cAAc;YAChB,OAAO;QACT;IACF;IAEA,yBAAyB;IACjB,uBAAuB;QAC7B,SAAS,gBAAgB,CAAC,aAAa,CAAC;YACtC,IAAI,EAAE,MAAM,YAAY,kBAAkB;gBACxC,EAAE,cAAc;gBAChB,OAAO;YACT;QACF;IACF;IAEA,qCAAqC;IAC7B,iBAAiB;QACvB,IAAI,WAAW;YAAE,MAAM;YAAO,aAAa;QAAK;QAEhD,YAAY;YACV,IAAI,OAAO,WAAW,GAAG,OAAO,WAAW,GAAG,OAC1C,OAAO,UAAU,GAAG,OAAO,UAAU,GAAG,KAAK;gBAC/C,IAAI,CAAC,SAAS,IAAI,EAAE;oBAClB,SAAS,IAAI,GAAG;oBAChB,IAAI,CAAC,sBAAsB;gBAC7B;YACF,OAAO;gBACL,SAAS,IAAI,GAAG;YAClB;QACF,GAAG;QAEH,+BAA+B;QAC/B,IAAI,UAAU,IAAI;QAClB,OAAO,cAAc,CAAC,SAAS,MAAM;YACnC,KAAK;gBACH,IAAI,CAAC,sBAAsB;gBAC3B,OAAO;YACT;QACF;QAEA,YAAY;YACV,QAAQ,GAAG,CAAC;YACZ,QAAQ,KAAK;QACf,GAAG;IACL;IAEA,2CAA2C;IACnC,yBAAyB;QAC/B,QAAQ,KAAK;QACb,QAAQ,GAAG,CAAC,qCAAqC;QACjD,QAAQ,GAAG,CAAC,4DAA4D;QACxE,QAAQ,GAAG,CAAC,yEAAyE;QACrF,QAAQ,GAAG,CAAC,kDAAkD;QAE9D,mCAAmC;QACnC,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QAC7B,WAAW;YACT,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QAC/B,GAAG;IACL;IAEA,6BAA6B;IACrB,mBAAmB;QACzB,2BAA2B;QAC3B,MAAM,cAAc,QAAQ,GAAG;QAC/B,MAAM,eAAe,QAAQ,IAAI;QACjC,MAAM,gBAAgB,QAAQ,KAAK;QAEnC,QAAQ,GAAG,GAAG,CAAC,GAAG;YAChB,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,YAAY,KAAK,CAAC,SAAS;YAC7B,OAAO;gBACL,YAAY,kCAAkC;YAChD;QACF;QAEA,QAAQ,IAAI,GAAG,CAAC,GAAG;YACjB,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,aAAa,KAAK,CAAC,SAAS;YAC9B;QACF;QAEA,QAAQ,KAAK,GAAG,CAAC,GAAG;YAClB,IAAI,IAAI,CAAC,SAAS,EAAE;gBAClB,cAAc,KAAK,CAAC,SAAS;YAC/B;QACF;QAEA,sBAAsB;QACtB,WAAW;YACT,QAAQ,KAAK;YACb,QAAQ,GAAG,CAAC,wCAAwC;YACpD,QAAQ,GAAG,CAAC,oDAAoD;YAChE,QAAQ,GAAG,CAAC,oEAAoE;YAChF,QAAQ,GAAG,CAAC,iDAAiD;QAC/D,GAAG;IACL;IAEA,iDAAiD;IACzC,iBAAiB;QACvB,IAAI,OAAO,GAAG,KAAK,OAAO,IAAI,EAAE;YAC9B,OAAO,GAAG,CAAE,QAAQ,GAAG,OAAO,IAAI,CAAC,QAAQ;QAC7C;IACF;IAEA,gCAAgC;IACxB,oBAAoB,OAAe,EAAE;QAC3C,kCAAkC;QAClC,MAAM,eAAe,SAAS,aAAa,CAAC;QAC5C,aAAa,KAAK,CAAC,OAAO,GAAG,CAAC;;;;;;;;;;;;;;IAc9B,CAAC;QAED,0BAA0B;QAC1B,IAAI,CAAC,SAAS,cAAc,CAAC,oBAAoB;YAC/C,MAAM,QAAQ,SAAS,aAAa,CAAC;YACrC,MAAM,EAAE,GAAG;YACX,MAAM,WAAW,GAAG,CAAC;;;;;;;;;MASrB,CAAC;YACD,SAAS,IAAI,CAAC,WAAW,CAAC;QAC5B;QAEA,aAAa,SAAS,GAAG,CAAC;;;cAGhB,EAAE,QAAQ;;IAEpB,CAAC;QAED,SAAS,IAAI,CAAC,WAAW,CAAC;QAE1B,sCAAsC;QACtC,WAAW;YACT,aAAa,KAAK,CAAC,SAAS,GAAG;YAC/B,WAAW;gBACT,IAAI,aAAa,UAAU,EAAE;oBAC3B,aAAa,UAAU,CAAC,WAAW,CAAC;gBACtC;YACF,GAAG;QACL,GAAG;IACL;IAEA,uCAAuC;IACvC,UAAU;QACR,IAAI,CAAC,iBAAiB,GAAG;QACzB,QAAQ,GAAG,CAAC;IACd;IAEA,oBAAoB;IACpB,SAAS;QACP,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,UAAU;QACf,QAAQ,GAAG,CAAC;IACd;AACF;AAGO,MAAM,WAAW,gBAAgB,WAAW;AAG5C,SAAS;IACd,MAAM,qBAAqB;QACzB,SAAS,UAAU;IACrB;IAEA,MAAM,kBAAkB;QACtB,SAAS,OAAO;IAClB;IAEA,MAAM,iBAAiB;QACrB,SAAS,MAAM;IACjB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 11834, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport LoadingScreen from '@/components/LoadingScreen';\nimport RoomScene3D from '@/components/RoomScene3D';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { useAnalytics } from '@/lib/analytics';\nimport { useSecurity } from '@/lib/security';\n\ntype SceneType = 'loading' | 'intro' | 'room';\n\nexport default function Home() {\n  const [currentScene, setCurrentScene] = useState<SceneType>('loading');\n  const { trackPageView, trackClick } = useAnalytics();\n  const { initializeSecurity } = useSecurity();\n\n  // Initialize security and analytics\n  useEffect(() => {\n    initializeSecurity();\n    trackPageView('home');\n  }, [initializeSecurity, trackPageView]);\n\n  const handleLoadingComplete = () => {\n    setCurrentScene('intro');\n    trackPageView('intro');\n  };\n\n  const handleEnterRoom = () => {\n    setCurrentScene('room');\n    trackClick('enter_room');\n    trackPageView('room');\n  };\n\n  const handleExitRoom = () => {\n    setCurrentScene('intro');\n    trackClick('exit_room');\n    trackPageView('intro');\n  };\n\n  return (\n    <div className=\"w-full h-screen overflow-hidden\">\n      <AnimatePresence mode=\"wait\">\n        {currentScene === 'loading' && (\n          <LoadingScreen key=\"loading\" onComplete={handleLoadingComplete} />\n        )}\n\n        {currentScene === 'intro' && (\n          <motion.div\n            key=\"intro\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"w-full h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center relative overflow-hidden\"\n          >\n            {/* Background Animation */}\n            <div className=\"absolute inset-0\">\n              {[...Array(50)].map((_, i) => (\n                <motion.div\n                  key={i}\n                  className=\"absolute w-1 h-1 bg-white rounded-full\"\n                  style={{\n                    left: `${Math.random() * 100}%`,\n                    top: `${Math.random() * 100}%`,\n                  }}\n                  animate={{\n                    opacity: [0, 1, 0],\n                    scale: [0, 1, 0],\n                  }}\n                  transition={{\n                    duration: 3,\n                    repeat: Infinity,\n                    delay: Math.random() * 3,\n                  }}\n                />\n              ))}\n            </div>\n\n            {/* Main Content */}\n            <div className=\"text-center z-10 space-y-8\">\n              <motion.h1\n                initial={{ y: -50, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ delay: 0.2 }}\n                className=\"text-6xl md:text-8xl font-bold text-white mb-4\"\n              >\n                KangPCode\n              </motion.h1>\n\n              <motion.p\n                initial={{ y: 50, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ delay: 0.4 }}\n                className=\"text-2xl text-purple-200 mb-8\"\n              >\n                Portfolio 3D Interaktif\n              </motion.p>\n\n              <motion.p\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.6 }}\n                className=\"text-lg text-purple-300 mb-12 max-w-2xl mx-auto\"\n              >\n                Selamat datang di dunia virtual KangPCode! Jelajahi kamar virtual yang penuh dengan teknologi,\n                proyek, dan inspirasi dari seorang developer Indonesia.\n              </motion.p>\n\n              {/* Door to Room */}\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ delay: 0.8, type: \"spring\", stiffness: 200 }}\n                className=\"relative\"\n              >\n                <motion.button\n                  onClick={handleEnterRoom}\n                  className=\"group relative bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-bold py-4 px-8 rounded-lg text-xl shadow-2xl transition-all duration-300 transform hover:scale-105\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <span className=\"relative z-10\">🚪 Masuk ke Kamar KangPCode</span>\n\n                  {/* Glow Effect */}\n                  <motion.div\n                    className=\"absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-lg blur-lg opacity-30\"\n                    animate={{\n                      scale: [1, 1.1, 1],\n                      opacity: [0.3, 0.5, 0.3],\n                    }}\n                    transition={{\n                      duration: 2,\n                      repeat: Infinity,\n                    }}\n                  />\n                </motion.button>\n              </motion.div>\n\n              <motion.p\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 1 }}\n                className=\"text-sm text-purple-400 mt-4\"\n              >\n                Klik pintu untuk memulai petualangan 3D Anda\n              </motion.p>\n            </div>\n          </motion.div>\n        )}\n\n        {currentScene === 'room' && (\n          <motion.div\n            key=\"room\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n          >\n            <RoomScene3D onExitRoom={handleExitRoom} />\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;;;AAPA;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IAC5D,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD;IACjD,MAAM,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,kHAAA,CAAA,cAAW,AAAD;IAEzC,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR;YACA,cAAc;QAChB;yBAAG;QAAC;QAAoB;KAAc;IAEtC,MAAM,wBAAwB;QAC5B,gBAAgB;QAChB,cAAc;IAChB;IAEA,MAAM,kBAAkB;QACtB,gBAAgB;QAChB,WAAW;QACX,cAAc;IAChB;IAEA,MAAM,iBAAiB;QACrB,gBAAgB;QAChB,WAAW;QACX,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;YAAC,MAAK;;gBACnB,iBAAiB,2BAChB,6LAAC,+HAAA,CAAA,UAAa;oBAAe,YAAY;mBAAtB;;;;;gBAGpB,iBAAiB,yBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,OAAO;wCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAChC;oCACA,SAAS;wCACP,SAAS;4CAAC;4CAAG;4CAAG;yCAAE;wCAClB,OAAO;4CAAC;4CAAG;4CAAG;yCAAE;oCAClB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO,KAAK,MAAM,KAAK;oCACzB;mCAdK;;;;;;;;;;sCAoBX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,GAAG,CAAC;wCAAI,SAAS;oCAAE;oCAC9B,SAAS;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;8CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,SAAS;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;8CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;8CAMD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,YAAY;wCAAE,OAAO;wCAAK,MAAM;wCAAU,WAAW;oCAAI;oCACzD,WAAU;8CAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAGhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDACP,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;oDAClB,SAAS;wDAAC;wDAAK;wDAAK;qDAAI;gDAC1B;gDACA,YAAY;oDACV,UAAU;oDACV,QAAQ;gDACV;;;;;;;;;;;;;;;;;8CAKN,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,OAAO;oCAAE;oCACvB,WAAU;8CACX;;;;;;;;;;;;;mBA9FC;;;;;gBAqGP,iBAAiB,wBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;8BAEnB,cAAA,6LAAC,6HAAA,CAAA,UAAW;wBAAC,YAAY;;;;;;mBALrB;;;;;;;;;;;;;;;;AAWhB;GAvJwB;;QAEgB,mHAAA,CAAA,eAAY;QACnB,kHAAA,CAAA,cAAW;;;KAHpB", "debugId": null}}]}
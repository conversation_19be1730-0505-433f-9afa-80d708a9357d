'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface IDCardLanyardProps {
  isVisible: boolean;
  onClose: () => void;
}

export default function IDCardLanyard({ isVisible, onClose }: IDCardLanyardProps) {
  const [isFlipped, setIsFlipped] = useState(false);

  const cardData = {
    name: "<PERSON><PERSON><PERSON>",
    nickname: "KangPC<PERSON>",
    role: "Fullstack Developer",
    specialization: "Web & Mobile Development",
    status: "Open for Collaboration",
    email: "<EMAIL>",
    github: "github.com/kangpcode",
    linkedin: "linkedin.com/in/kangpcode",
    website: "kangpcode.dev",
    photo: "/assets/images/idcard/photo.jpg",
    qrCode: "/assets/images/idcard/qr-github.png",
    skills: [
      "JavaScript/TypeScript",
      "React/Next.js",
      "Node.js",
      "Python",
      "Docker",
      "AWS"
    ],
    achievements: [
      "🏆 Best Portfolio 2024",
      "📚 Author of 'Langkah <PERSON>'",
      "🎯 100+ Projects Completed",
      "👥 Active in Tech Community"
    ],
    experience: "3+ Years",
    location: "Indonesia",
    languages: ["Indonesian", "English"],
    motto: "Building the future of Indonesian tech, one line of code at a time."
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
    >
      <div className="relative">
        {/* Lanyard */}
        <div className="absolute -top-20 left-1/2 transform -translate-x-1/2">
          <div className="w-6 h-20 bg-gradient-to-b from-blue-600 to-blue-800 rounded-t-full"></div>
          <div className="w-8 h-4 bg-gray-300 rounded-full -mt-2 -ml-1 border-2 border-gray-400"></div>
        </div>

        {/* ID Card Container */}
        <motion.div
          className="relative w-96 h-64 cursor-pointer"
          style={{ perspective: "1000px" }}
          onClick={() => setIsFlipped(!isFlipped)}
          whileHover={{ y: -5 }}
        >
          <motion.div
            className="relative w-full h-full"
            style={{ transformStyle: "preserve-3d" }}
            animate={{ rotateY: isFlipped ? 180 : 0 }}
            transition={{ duration: 0.6 }}
          >
            {/* Front Side */}
            <div
              className="absolute inset-0 w-full h-full bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl shadow-2xl border-2 border-white/20"
              style={{ backfaceVisibility: "hidden" }}
            >
              {/* Card Header */}
              <div className="bg-white/10 backdrop-blur-sm p-3 rounded-t-xl border-b border-white/20">
                <div className="flex items-center justify-between">
                  <div className="text-white">
                    <div className="text-xs font-medium opacity-80">DEVELOPER ID</div>
                    <div className="text-sm font-bold">KangPCode Portfolio</div>
                  </div>
                  <div className="text-2xl">💻</div>
                </div>
              </div>

              {/* Card Content */}
              <div className="p-4 text-white">
                <div className="flex items-start space-x-4">
                  {/* Photo */}
                  <div className="w-20 h-20 bg-gray-300 rounded-lg flex items-center justify-center text-3xl border-2 border-white/30">
                    👨‍💻
                  </div>

                  {/* Info */}
                  <div className="flex-1">
                    <h2 className="text-xl font-bold mb-1">{cardData.name}</h2>
                    <p className="text-blue-200 text-sm mb-1">"{cardData.nickname}"</p>
                    <p className="text-white/90 text-sm mb-2">{cardData.role}</p>
                    <p className="text-blue-200 text-xs">{cardData.specialization}</p>
                  </div>
                </div>

                {/* Status */}
                <div className="mt-4 p-2 bg-green-500/20 rounded-lg border border-green-400/30">
                  <div className="flex items-center space-x-2">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span className="text-green-200 text-sm font-medium">{cardData.status}</span>
                  </div>
                </div>

                {/* Quick Stats */}
                <div className="mt-3 grid grid-cols-3 gap-2 text-center">
                  <div className="bg-white/10 rounded p-1">
                    <div className="text-xs font-bold">{cardData.experience}</div>
                    <div className="text-xs opacity-80">Experience</div>
                  </div>
                  <div className="bg-white/10 rounded p-1">
                    <div className="text-xs font-bold">100+</div>
                    <div className="text-xs opacity-80">Projects</div>
                  </div>
                  <div className="bg-white/10 rounded p-1">
                    <div className="text-xs font-bold">{cardData.skills.length}</div>
                    <div className="text-xs opacity-80">Skills</div>
                  </div>
                </div>
              </div>

              {/* Card Footer */}
              <div className="absolute bottom-0 left-0 right-0 bg-white/10 backdrop-blur-sm p-2 rounded-b-xl border-t border-white/20">
                <div className="text-center text-white text-xs">
                  Click to flip • {cardData.location}
                </div>
              </div>
            </div>

            {/* Back Side */}
            <div
              className="absolute inset-0 w-full h-full bg-gradient-to-br from-gray-800 via-gray-900 to-black rounded-xl shadow-2xl border-2 border-gray-600"
              style={{ 
                backfaceVisibility: "hidden",
                transform: "rotateY(180deg)"
              }}
            >
              {/* Back Header */}
              <div className="bg-gray-700/50 backdrop-blur-sm p-3 rounded-t-xl border-b border-gray-600">
                <div className="flex items-center justify-between text-white">
                  <div>
                    <div className="text-xs font-medium opacity-80">CONTACT & SKILLS</div>
                    <div className="text-sm font-bold">Professional Details</div>
                  </div>
                  <div className="text-2xl">🔗</div>
                </div>
              </div>

              {/* Back Content */}
              <div className="p-4 text-white space-y-3">
                {/* Contact Info */}
                <div>
                  <h3 className="text-sm font-bold mb-2 text-blue-300">Contact</h3>
                  <div className="space-y-1 text-xs">
                    <div>📧 {cardData.email}</div>
                    <div>🐙 {cardData.github}</div>
                    <div>🌐 {cardData.website}</div>
                  </div>
                </div>

                {/* Skills */}
                <div>
                  <h3 className="text-sm font-bold mb-2 text-green-300">Core Skills</h3>
                  <div className="grid grid-cols-2 gap-1">
                    {cardData.skills.map((skill, index) => (
                      <div key={index} className="text-xs bg-gray-700/50 px-2 py-1 rounded">
                        {skill}
                      </div>
                    ))}
                  </div>
                </div>

                {/* QR Code */}
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-sm font-bold mb-1 text-purple-300">Quick Access</h3>
                    <div className="text-xs opacity-80">Scan for GitHub</div>
                  </div>
                  <div className="w-12 h-12 bg-white rounded flex items-center justify-center">
                    <div className="text-black text-xs font-bold">QR</div>
                  </div>
                </div>
              </div>

              {/* Back Footer */}
              <div className="absolute bottom-0 left-0 right-0 bg-gray-700/50 backdrop-blur-sm p-2 rounded-b-xl border-t border-gray-600">
                <div className="text-center text-white text-xs">
                  "{cardData.motto}"
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute -top-8 -right-8 w-8 h-8 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center transition-colors"
        >
          ✕
        </button>

        {/* Flip Instruction */}
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-white text-sm text-center">
          <div className="bg-black/50 px-3 py-1 rounded-full">
            🖱️ Click to flip card
          </div>
        </div>
      </div>

      {/* Background Actions */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 flex space-x-4">
        <motion.a
          href={`https://${cardData.github}`}
          target="_blank"
          rel="noopener noreferrer"
          className="bg-gray-900 hover:bg-gray-800 text-white px-4 py-2 rounded-lg transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          🐙 GitHub
        </motion.a>
        <motion.a
          href={`mailto:${cardData.email}`}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          📧 Contact
        </motion.a>
        <motion.button
          onClick={() => {
            navigator.clipboard.writeText(`${cardData.name} - ${cardData.role}\n${cardData.email}\n${cardData.github}`);
            alert('Contact info copied to clipboard!');
          }}
          className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg transition-colors"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          📋 Copy Info
        </motion.button>
      </div>
    </motion.div>
  );
}

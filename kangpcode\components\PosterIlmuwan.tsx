'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PosterIlmuwanProps {
  isVisible: boolean;
  onClose: () => void;
}

interface Scientist {
  id: number;
  name: string;
  description: string;
  contribution: string;
  category: 'teknologi' | 'nusantara' | 'dunia';
  birthYear?: number;
  deathYear?: number;
  nationality: string;
  image: string;
  quotes: string[];
  achievements: string[];
  legacy: string;
}

export default function PosterIlmuwan({ isVisible, onClose }: PosterIlmuwanProps) {
  const [selectedScientist, setSelectedScientist] = useState<Scientist | null>(null);
  const [activeCategory, setActiveCategory] = useState<'teknologi' | 'nusantara' | 'dunia'>('teknologi');

  const scientists: Scientist[] = [
    // Teknologi Modern
    {
      id: 1,
      name: "Nikola Tesla",
      description: "Penemu motor induksi, listrik AC modern",
      contribution: "Sistem tenaga listrik AC, motor induksi, teknologi nirkabel",
      category: "teknologi",
      birthYear: 1856,
      deathYear: 1943,
      nationality: "Serbia-Amerika",
      image: "🔌",
      quotes: [
        "The present is theirs; the future, for which I really worked, is mine.",
        "If you want to find the secrets of the universe, think in terms of energy, frequency and vibration."
      ],
      achievements: [
        "Sistem distribusi listrik AC",
        "Motor induksi polyphase",
        "Teknologi transmisi nirkabel",
        "Lebih dari 300 paten"
      ],
      legacy: "Teknologi listrik modern yang kita gunakan hari ini sebagian besar berdasarkan penemuan Tesla."
    },
    {
      id: 2,
      name: "Albert Einstein",
      description: "Teori relativitas, dasar teknologi GPS",
      contribution: "Teori relativitas khusus dan umum, efek fotolistrik",
      category: "teknologi",
      birthYear: 1879,
      deathYear: 1955,
      nationality: "Jerman-Amerika",
      image: "🧠",
      quotes: [
        "Imagination is more important than knowledge.",
        "The important thing is not to stop questioning."
      ],
      achievements: [
        "Teori Relativitas Khusus (1905)",
        "Teori Relativitas Umum (1915)",
        "Nobel Prize in Physics (1921)",
        "Dasar teknologi GPS dan laser"
      ],
      legacy: "Teorinya memungkinkan teknologi GPS, laser, dan pemahaman modern tentang alam semesta."
    },
    // Nusantara
    {
      id: 3,
      name: "Onno W. Purbo",
      description: "Pelopor internet Indonesia, edukator teknologi",
      contribution: "Pengembangan internet di Indonesia, edukasi teknologi",
      category: "nusantara",
      birthYear: 1962,
      nationality: "Indonesia",
      image: "🌐",
      quotes: [
        "Internet adalah alat untuk mencerdaskan bangsa.",
        "Teknologi harus bisa diakses oleh semua lapisan masyarakat."
      ],
      achievements: [
        "Pelopor internet di Indonesia",
        "Pendiri komunitas Linux Indonesia",
        "Penulis 40+ buku teknologi",
        "Advokat open source"
      ],
      legacy: "Membangun fondasi internet Indonesia dan menginspirasi generasi teknologi lokal."
    },
    {
      id: 4,
      name: "B.J. Habibie",
      description: "Bapak teknologi Indonesia, ahli pesawat terbang",
      contribution: "Teknologi pesawat terbang, industrialisasi Indonesia",
      category: "nusantara",
      birthYear: 1936,
      deathYear: 2019,
      nationality: "Indonesia",
      image: "✈️",
      quotes: [
        "Teknologi adalah kunci kemajuan bangsa.",
        "Kita harus menjadi bangsa yang mandiri dalam teknologi."
      ],
      achievements: [
        "Ahli teknologi pesawat terbang",
        "Presiden RI ke-3",
        "Pendiri industri pesawat Indonesia",
        "Doktor Honoris Causa dari 23 universitas"
      ],
      legacy: "Membangun fondasi industri teknologi Indonesia dan menginspirasi kemandirian teknologi."
    },
    // Dunia Klasik
    {
      id: 5,
      name: "Al-Khawarizmi",
      description: "Bapak algoritma, dasar ilmu komputasi",
      contribution: "Algoritma, aljabar, sistem bilangan",
      category: "dunia",
      birthYear: 780,
      deathYear: 850,
      nationality: "Persia",
      image: "🔢",
      quotes: [
        "That which is sought is found by methodical calculation.",
        "Mathematics is the key to understanding the universe."
      ],
      achievements: [
        "Menciptakan konsep algoritma",
        "Mengembangkan aljabar",
        "Sistem bilangan Hindu-Arab",
        "Dasar matematika modern"
      ],
      legacy: "Algoritma yang dia ciptakan menjadi dasar semua komputasi modern."
    },
    {
      id: 6,
      name: "Al-Jazari",
      description: "Bapak robotika, desain mesin otomatis",
      contribution: "Mesin otomatis, robotika awal, jam air",
      category: "dunia",
      birthYear: 1136,
      deathYear: 1206,
      nationality: "Arab",
      image: "🤖",
      quotes: [
        "Innovation comes from understanding the principles of nature.",
        "Machines should serve humanity's needs."
      ],
      achievements: [
        "Menciptakan robot humanoid pertama",
        "Jam air otomatis",
        "Sistem kontrol otomatis",
        "Buku 'The Book of Knowledge of Ingenious Mechanical Devices'"
      ],
      legacy: "Desainnya menjadi dasar robotika dan otomasi modern."
    }
  ];

  const categories = [
    { id: 'teknologi', name: 'Teknologi Modern', icon: '💻', color: 'blue' },
    { id: 'nusantara', name: 'Tokoh Nusantara', icon: '🇮🇩', color: 'red' },
    { id: 'dunia', name: 'Tokoh Dunia', icon: '🌍', color: 'green' }
  ];

  const filteredScientists = scientists.filter(s => s.category === activeCategory);

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-4 bg-white rounded-lg shadow-2xl border overflow-hidden z-50"
    >
      {/* Header */}
      <div className="h-16 bg-gradient-to-r from-purple-600 to-blue-600 flex items-center justify-between px-6 text-white">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">👨‍🔬</span>
          <div>
            <h2 className="font-bold text-lg">Poster Ilmuwan Teknologi</h2>
            <p className="text-sm text-purple-100">Inspirasi dari Para Pionir</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="hover:bg-white/20 p-2 rounded transition-colors"
        >
          ✕
        </button>
      </div>

      {/* Category Tabs */}
      <div className="h-12 bg-gray-100 border-b flex items-center px-6">
        {categories.map((category) => (
          <button
            key={category.id}
            onClick={() => setActiveCategory(category.id as any)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-t transition-colors mr-2 ${
              activeCategory === category.id
                ? (category.color === 'blue' ? 'bg-blue-500 text-white' :
                   category.color === 'red' ? 'bg-red-500 text-white' :
                   'bg-green-500 text-white')
                : 'hover:bg-gray-200 text-gray-600'
            }`}
          >
            <span>{category.icon}</span>
            <span className="font-medium">{category.name}</span>
          </button>
        ))}
      </div>

      {/* Content */}
      <div className="flex h-[calc(100%-7rem)]">
        {/* Scientists Grid */}
        <div className="w-1/2 p-6 overflow-y-auto border-r">
          <div className="grid grid-cols-1 gap-4">
            {filteredScientists.map((scientist) => (
              <motion.div
                key={scientist.id}
                onClick={() => setSelectedScientist(scientist)}
                className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                  selectedScientist?.id === scientist.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 hover:shadow-md'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-start space-x-4">
                  <div className="text-4xl">{scientist.image}</div>
                  <div className="flex-1">
                    <h3 className="font-bold text-lg text-gray-900">{scientist.name}</h3>
                    <p className="text-sm text-gray-600 mb-2">
                      {scientist.birthYear && scientist.deathYear 
                        ? `${scientist.birthYear} - ${scientist.deathYear}`
                        : scientist.birthYear 
                        ? `${scientist.birthYear} - sekarang`
                        : 'Klasik'
                      } • {scientist.nationality}
                    </p>
                    <p className="text-gray-700">{scientist.description}</p>
                  </div>
                </div>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Scientist Detail */}
        <div className="w-1/2 p-6 overflow-y-auto">
          <AnimatePresence mode="wait">
            {selectedScientist ? (
              <motion.div
                key={selectedScientist.id}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-6"
              >
                {/* Scientist Header */}
                <div className="text-center">
                  <div className="text-6xl mb-4">{selectedScientist.image}</div>
                  <h2 className="text-3xl font-bold text-gray-900 mb-2">{selectedScientist.name}</h2>
                  <p className="text-lg text-gray-600 mb-4">{selectedScientist.description}</p>
                  <div className="inline-block bg-gray-100 px-3 py-1 rounded-full text-sm text-gray-600">
                    {selectedScientist.nationality}
                  </div>
                </div>

                {/* Contribution */}
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">🔬 Kontribusi Utama</h3>
                  <p className="text-gray-700 leading-relaxed">{selectedScientist.contribution}</p>
                </div>

                {/* Achievements */}
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">🏆 Pencapaian</h3>
                  <ul className="space-y-2">
                    {selectedScientist.achievements.map((achievement, index) => (
                      <li key={index} className="flex items-start space-x-2">
                        <span className="text-blue-500 mt-1">•</span>
                        <span className="text-gray-700">{achievement}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Quotes */}
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">💭 Kutipan Inspiratif</h3>
                  <div className="space-y-3">
                    {selectedScientist.quotes.map((quote, index) => (
                      <blockquote key={index} className="border-l-4 border-blue-500 pl-4 italic text-gray-700">
                        "{quote}"
                      </blockquote>
                    ))}
                  </div>
                </div>

                {/* Legacy */}
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">🌟 Warisan</h3>
                  <p className="text-gray-700 leading-relaxed bg-yellow-50 p-4 rounded-lg border border-yellow-200">
                    {selectedScientist.legacy}
                  </p>
                </div>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="flex items-center justify-center h-full text-center"
              >
                <div className="space-y-4">
                  <div className="text-6xl">👨‍🔬</div>
                  <h3 className="text-xl font-bold text-gray-900">Pilih Ilmuwan</h3>
                  <p className="text-gray-600">Klik pada salah satu ilmuwan di sebelah kiri untuk melihat detail</p>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </div>

      {/* Footer */}
      <div className="h-12 bg-gray-100 border-t flex items-center justify-between px-6">
        <div className="text-sm text-gray-600">
          Menampilkan {filteredScientists.length} ilmuwan dalam kategori {categories.find(c => c.id === activeCategory)?.name}
        </div>
        <div className="text-sm text-gray-500">
          Inspirasi untuk generasi teknologi Indonesia 🇮🇩
        </div>
      </div>
    </motion.div>
  );
}

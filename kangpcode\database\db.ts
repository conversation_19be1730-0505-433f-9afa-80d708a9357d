import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import { migrate } from 'drizzle-orm/better-sqlite3/migrator';
import * as schema from './schema';
import path from 'path';
import fs from 'fs';

// Ensure database directory exists
const dbDir = path.join(process.cwd(), 'database');
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Create database connection
const dbPath = path.join(dbDir, 'db.sqlite');
const sqlite = new Database(dbPath);

// Enable optimizations
sqlite.pragma('journal_mode = WAL');
sqlite.pragma('synchronous = NORMAL');
sqlite.pragma('cache_size = 1000000');
sqlite.pragma('foreign_keys = ON');
sqlite.pragma('temp_store = MEMORY');

// Create drizzle instance
export const db = drizzle(sqlite, { schema });

// Auto-migrate on startup in development
if (process.env.NODE_ENV === 'development') {
  try {
    const migrationsPath = path.join(process.cwd(), 'database', 'migrations');
    if (fs.existsSync(migrationsPath)) {
      migrate(db, { migrationsFolder: migrationsPath });
      console.log('✅ Database migrations applied successfully');
    }
  } catch (error) {
    console.warn('⚠️ Migration failed:', error);
  }
}

// Graceful shutdown
process.on('SIGINT', () => {
  sqlite.close();
  process.exit(0);
});

process.on('SIGTERM', () => {
  sqlite.close();
  process.exit(0);
});

export default db;
export { sqlite };

{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/warn-once.mjs"], "sourcesContent": ["const warned = new Set();\nfunction hasWarned(message) {\n    return warned.has(message);\n}\nfunction warnOnce(condition, message, element) {\n    if (condition || warned.has(message))\n        return;\n    console.warn(message);\n    if (element)\n        console.warn(element);\n    warned.add(message);\n}\n\nexport { hasWarned, warnOnce };\n"], "names": [], "mappings": ";;;;AAAA,MAAM,SAAS,IAAI;AACnB,SAAS,UAAU,OAAO;IACtB,OAAO,OAAO,GAAG,CAAC;AACtB;AACA,SAAS,SAAS,SAAS,EAAE,OAAO,EAAE,OAAO;IACzC,IAAI,aAAa,OAAO,GAAG,CAAC,UACxB;IACJ,QAAQ,IAAI,CAAC;IACb,IAAI,SACA,QAAQ,IAAI,CAAC;IACjB,OAAO,GAAG,CAAC;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/noop.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nconst noop = (any) => any;\n\nexport { noop };\n"], "names": [], "mappings": "AAAA,sBAAsB;;;AACtB,MAAM,OAAO,CAAC,MAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/global-config.mjs"], "sourcesContent": ["const MotionGlobalConfig = {};\n\nexport { MotionGlobalConfig };\n"], "names": [], "mappings": ";;;AAAA,MAAM,qBAAqB,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/array.mjs"], "sourcesContent": ["function addUniqueItem(arr, item) {\n    if (arr.indexOf(item) === -1)\n        arr.push(item);\n}\nfunction removeItem(arr, item) {\n    const index = arr.indexOf(item);\n    if (index > -1)\n        arr.splice(index, 1);\n}\n// Adapted from array-move\nfunction moveItem([...arr], fromIndex, toIndex) {\n    const startIndex = fromIndex < 0 ? arr.length + fromIndex : fromIndex;\n    if (startIndex >= 0 && startIndex < arr.length) {\n        const endIndex = toIndex < 0 ? arr.length + toIndex : toIndex;\n        const [item] = arr.splice(fromIndex, 1);\n        arr.splice(endIndex, 0, item);\n    }\n    return arr;\n}\n\nexport { addUniqueItem, moveItem, removeItem };\n"], "names": [], "mappings": ";;;;;AAAA,SAAS,cAAc,GAAG,EAAE,IAAI;IAC5B,IAAI,IAAI,OAAO,CAAC,UAAU,CAAC,GACvB,IAAI,IAAI,CAAC;AACjB;AACA,SAAS,WAAW,GAAG,EAAE,IAAI;IACzB,MAAM,QAAQ,IAAI,OAAO,CAAC;IAC1B,IAAI,QAAQ,CAAC,GACT,IAAI,MAAM,CAAC,OAAO;AAC1B;AACA,0BAA0B;AAC1B,SAAS,SAAS,CAAC,GAAG,IAAI,EAAE,SAAS,EAAE,OAAO;IAC1C,MAAM,aAAa,YAAY,IAAI,IAAI,MAAM,GAAG,YAAY;IAC5D,IAAI,cAAc,KAAK,aAAa,IAAI,MAAM,EAAE;QAC5C,MAAM,WAAW,UAAU,IAAI,IAAI,MAAM,GAAG,UAAU;QACtD,MAAM,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,WAAW;QACrC,IAAI,MAAM,CAAC,UAAU,GAAG;IAC5B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/subscription-manager.mjs"], "sourcesContent": ["import { addUniqueItem, removeItem } from './array.mjs';\n\nclass SubscriptionManager {\n    constructor() {\n        this.subscriptions = [];\n    }\n    add(handler) {\n        addUniqueItem(this.subscriptions, handler);\n        return () => removeItem(this.subscriptions, handler);\n    }\n    notify(a, b, c) {\n        const numSubscriptions = this.subscriptions.length;\n        if (!numSubscriptions)\n            return;\n        if (numSubscriptions === 1) {\n            /**\n             * If there's only a single handler we can just call it without invoking a loop.\n             */\n            this.subscriptions[0](a, b, c);\n        }\n        else {\n            for (let i = 0; i < numSubscriptions; i++) {\n                /**\n                 * Check whether the handler exists before firing as it's possible\n                 * the subscriptions were modified during this loop running.\n                 */\n                const handler = this.subscriptions[i];\n                handler && handler(a, b, c);\n            }\n        }\n    }\n    getSize() {\n        return this.subscriptions.length;\n    }\n    clear() {\n        this.subscriptions.length = 0;\n    }\n}\n\nexport { SubscriptionManager };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM;IACF,aAAc;QACV,IAAI,CAAC,aAAa,GAAG,EAAE;IAC3B;IACA,IAAI,OAAO,EAAE;QACT,CAAA,GAAA,uJAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE;QAClC,OAAO,IAAM,CAAA,GAAA,uJAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE;IAChD;IACA,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;QACZ,MAAM,mBAAmB,IAAI,CAAC,aAAa,CAAC,MAAM;QAClD,IAAI,CAAC,kBACD;QACJ,IAAI,qBAAqB,GAAG;YACxB;;aAEC,GACD,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,GAAG,GAAG;QAChC,OACK;YACD,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,IAAK;gBACvC;;;iBAGC,GACD,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC,EAAE;gBACrC,WAAW,QAAQ,GAAG,GAAG;YAC7B;QACJ;IACJ;IACA,UAAU;QACN,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM;IACpC;IACA,QAAQ;QACJ,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;IAChC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 123, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/velocity-per-second.mjs"], "sourcesContent": ["/*\n  Convert velocity into velocity per second\n\n  @param [number]: Unit per frame\n  @param [number]: Frame duration in ms\n*/\nfunction velocityPerSecond(velocity, frameDuration) {\n    return frameDuration ? velocity * (1000 / frameDuration) : 0;\n}\n\nexport { velocityPerSecond };\n"], "names": [], "mappings": "AAAA;;;;;AAKA;;;AACA,SAAS,kBAAkB,QAAQ,EAAE,aAAa;IAC9C,OAAO,gBAAgB,WAAW,CAAC,OAAO,aAAa,IAAI;AAC/D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/errors.mjs"], "sourcesContent": ["let warning = () => { };\nlet invariant = () => { };\nif (process.env.NODE_ENV !== \"production\") {\n    warning = (check, message) => {\n        if (!check && typeof console !== \"undefined\") {\n            console.warn(message);\n        }\n    };\n    invariant = (check, message) => {\n        if (!check) {\n            throw new Error(message);\n        }\n    };\n}\n\nexport { invariant, warning };\n"], "names": [], "mappings": ";;;;AAAA,IAAI,UAAU,KAAQ;AACtB,IAAI,YAAY,KAAQ;AACxB,wCAA2C;IACvC,UAAU,CAAC,OAAO;QACd,IAAI,CAAC,SAAS,OAAO,YAAY,aAAa;YAC1C,QAAQ,IAAI,CAAC;QACjB;IACJ;IACA,YAAY,CAAC,OAAO;QAChB,IAAI,CAAC,OAAO;YACR,MAAM,IAAI,MAAM;QACpB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/pipe.mjs"], "sourcesContent": ["/**\n * <PERSON><PERSON>\n * Compose other transformers to run linearily\n * pipe(min(20), max(40))\n * @param  {...functions} transformers\n * @return {function}\n */\nconst combineFunctions = (a, b) => (v) => b(a(v));\nconst pipe = (...transformers) => transformers.reduce(combineFunctions);\n\nexport { pipe };\n"], "names": [], "mappings": "AAAA;;;;;;CAMC;;;AACD,MAAM,mBAAmB,CAAC,GAAG,IAAM,CAAC,IAAM,EAAE,EAAE;AAC9C,MAAM,OAAO,CAAC,GAAG,eAAiB,aAAa,MAAM,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/clamp.mjs"], "sourcesContent": ["const clamp = (min, max, v) => {\n    if (v > max)\n        return max;\n    if (v < min)\n        return min;\n    return v;\n};\n\nexport { clamp };\n"], "names": [], "mappings": ";;;AAAA,MAAM,QAAQ,CAAC,KAAK,KAAK;IACrB,IAAI,IAAI,KACJ,OAAO;IACX,IAAI,IAAI,KACJ,OAAO;IACX,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 199, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/time-conversion.mjs"], "sourcesContent": ["/**\n * Converts seconds to milliseconds\n *\n * @param seconds - Time in seconds.\n * @return milliseconds - Converted time in milliseconds.\n */\n/*#__NO_SIDE_EFFECTS__*/\nconst secondsToMilliseconds = (seconds) => seconds * 1000;\n/*#__NO_SIDE_EFFECTS__*/\nconst millisecondsToSeconds = (milliseconds) => milliseconds / 1000;\n\nexport { millisecondsToSeconds, secondsToMilliseconds };\n"], "names": [], "mappings": "AAAA;;;;;CAKC,GACD,sBAAsB;;;;AACtB,MAAM,wBAAwB,CAAC,UAAY,UAAU;AACrD,sBAAsB,GACtB,MAAM,wBAAwB,CAAC,eAAiB,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/easing/cubic-bezier.mjs"], "sourcesContent": ["import { noop } from '../noop.mjs';\n\n/*\n  Bezier function generator\n  This has been modified from Gaë<PERSON>eau's BezierEasing\n  https://github.com/gre/bezier-easing/blob/master/src/index.js\n  https://github.com/gre/bezier-easing/blob/master/LICENSE\n  \n  I've removed the newtonRaphsonIterate algo because in benchmarking it\n  wasn't noticeably faster than binarySubdivision, indeed removing it\n  usually improved times, depending on the curve.\n  I also removed the lookup table, as for the added bundle size and loop we're\n  only cutting ~4 or so subdivision iterations. I bumped the max iterations up\n  to 12 to compensate and this still tended to be faster for no perceivable\n  loss in accuracy.\n  Usage\n    const easeOut = cubicBezier(.17,.67,.83,.67);\n    const x = easeOut(0.5); // returns 0.627...\n*/\n// Returns x(t) given t, x1, and x2, or y(t) given t, y1, and y2.\nconst calcBezier = (t, a1, a2) => (((1.0 - 3.0 * a2 + 3.0 * a1) * t + (3.0 * a2 - 6.0 * a1)) * t + 3.0 * a1) *\n    t;\nconst subdivisionPrecision = 0.0000001;\nconst subdivisionMaxIterations = 12;\nfunction binarySubdivide(x, lowerBound, upperBound, mX1, mX2) {\n    let currentX;\n    let currentT;\n    let i = 0;\n    do {\n        currentT = lowerBound + (upperBound - lowerBound) / 2.0;\n        currentX = calcBezier(currentT, mX1, mX2) - x;\n        if (currentX > 0.0) {\n            upperBound = currentT;\n        }\n        else {\n            lowerBound = currentT;\n        }\n    } while (Math.abs(currentX) > subdivisionPrecision &&\n        ++i < subdivisionMaxIterations);\n    return currentT;\n}\nfunction cubicBezier(mX1, mY1, mX2, mY2) {\n    // If this is a linear gradient, return linear easing\n    if (mX1 === mY1 && mX2 === mY2)\n        return noop;\n    const getTForX = (aX) => binarySubdivide(aX, 0, 1, mX1, mX2);\n    // If animation is at start/end, return t without easing\n    return (t) => t === 0 || t === 1 ? t : calcBezier(getTForX(t), mY1, mY2);\n}\n\nexport { cubicBezier };\n"], "names": [], "mappings": ";;;AAAA;;AAEA;;;;;;;;;;;;;;;;AAgBA,GACA,iEAAiE;AACjE,MAAM,aAAa,CAAC,GAAG,IAAI,KAAO,CAAC,CAAC,CAAC,MAAM,MAAM,KAAK,MAAM,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,MAAM,EAAE,CAAC,IAAI,IAAI,MAAM,EAAE,IACvG;AACJ,MAAM,uBAAuB;AAC7B,MAAM,2BAA2B;AACjC,SAAS,gBAAgB,CAAC,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,EAAE,GAAG;IACxD,IAAI;IACJ,IAAI;IACJ,IAAI,IAAI;IACR,GAAG;QACC,WAAW,aAAa,CAAC,aAAa,UAAU,IAAI;QACpD,WAAW,WAAW,UAAU,KAAK,OAAO;QAC5C,IAAI,WAAW,KAAK;YAChB,aAAa;QACjB,OACK;YACD,aAAa;QACjB;IACJ,QAAS,KAAK,GAAG,CAAC,YAAY,wBAC1B,EAAE,IAAI,yBAA0B;IACpC,OAAO;AACX;AACA,SAAS,YAAY,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG;IACnC,qDAAqD;IACrD,IAAI,QAAQ,OAAO,QAAQ,KACvB,OAAO,sJAAA,CAAA,OAAI;IACf,MAAM,WAAW,CAAC,KAAO,gBAAgB,IAAI,GAAG,GAAG,KAAK;IACxD,wDAAwD;IACxD,OAAO,CAAC,IAAM,MAAM,KAAK,MAAM,IAAI,IAAI,WAAW,SAAS,IAAI,KAAK;AACxE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/easing/ease.mjs"], "sourcesContent": ["import { cubicBezier } from './cubic-bezier.mjs';\n\nconst easeIn = /*@__PURE__*/ cubicBezier(0.42, 0, 1, 1);\nconst easeOut = /*@__PURE__*/ cubicBezier(0, 0, 0.58, 1);\nconst easeInOut = /*@__PURE__*/ cubicBezier(0.42, 0, 0.58, 1);\n\nexport { easeIn, easeInOut, easeOut };\n"], "names": [], "mappings": ";;;;;AAAA;;AAEA,MAAM,SAAS,WAAW,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE,MAAM,GAAG,GAAG;AACrD,MAAM,UAAU,WAAW,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE,GAAG,GAAG,MAAM;AACtD,MAAM,YAAY,WAAW,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE,MAAM,GAAG,MAAM", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 288, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/easing/utils/is-easing-array.mjs"], "sourcesContent": ["const isEasingArray = (ease) => {\n    return Array.isArray(ease) && typeof ease[0] !== \"number\";\n};\n\nexport { isEasingArray };\n"], "names": [], "mappings": ";;;AAAA,MAAM,gBAAgB,CAAC;IACnB,OAAO,MAAM,OAAO,CAAC,SAAS,OAAO,IAAI,CAAC,EAAE,KAAK;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/easing/modifiers/mirror.mjs"], "sourcesContent": ["// Accepts an easing function and returns a new one that outputs mirrored values for\n// the second half of the animation. Turns easeIn into easeInOut.\nconst mirrorEasing = (easing) => (p) => p <= 0.5 ? easing(2 * p) / 2 : (2 - easing(2 * (1 - p))) / 2;\n\nexport { mirrorEasing };\n"], "names": [], "mappings": "AAAA,oFAAoF;AACpF,iEAAiE;;;;AACjE,MAAM,eAAe,CAAC,SAAW,CAAC,IAAM,KAAK,MAAM,OAAO,IAAI,KAAK,IAAI,CAAC,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 314, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/easing/modifiers/reverse.mjs"], "sourcesContent": ["// Accepts an easing function and returns a new one that outputs reversed values.\n// Turns easeIn into easeOut.\nconst reverseEasing = (easing) => (p) => 1 - easing(1 - p);\n\nexport { reverseEasing };\n"], "names": [], "mappings": "AAAA,iFAAiF;AACjF,6BAA6B;;;;AAC7B,MAAM,gBAAgB,CAAC,SAAW,CAAC,IAAM,IAAI,OAAO,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/easing/back.mjs"], "sourcesContent": ["import { cubicBezier } from './cubic-bezier.mjs';\nimport { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst backOut = /*@__PURE__*/ cubicBezier(0.33, 1.53, 0.69, 0.99);\nconst backIn = /*@__PURE__*/ reverseEasing(backOut);\nconst backInOut = /*@__PURE__*/ mirrorEasing(backIn);\n\nexport { backIn, backInOut, backOut };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAEA,MAAM,UAAU,WAAW,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE,MAAM,MAAM,MAAM;AAC5D,MAAM,SAAS,WAAW,GAAG,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE;AAC3C,MAAM,YAAY,WAAW,GAAG,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/easing/anticipate.mjs"], "sourcesContent": ["import { backIn } from './back.mjs';\n\nconst anticipate = (p) => (p *= 2) < 1 ? 0.5 * backIn(p) : 0.5 * (2 - Math.pow(2, -10 * (p - 1)));\n\nexport { anticipate };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,aAAa,CAAC,IAAM,CAAC,KAAK,CAAC,IAAI,IAAI,MAAM,CAAA,GAAA,gKAAA,CAAA,SAAM,AAAD,EAAE,KAAK,MAAM,CAAC,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 361, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/easing/circ.mjs"], "sourcesContent": ["import { mirrorEasing } from './modifiers/mirror.mjs';\nimport { reverseEasing } from './modifiers/reverse.mjs';\n\nconst circIn = (p) => 1 - Math.sin(Math.acos(p));\nconst circOut = reverseEasing(circIn);\nconst circInOut = mirrorEasing(circIn);\n\nexport { circIn, circInOut, circOut };\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA,MAAM,SAAS,CAAC,IAAM,IAAI,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC;AAC7C,MAAM,UAAU,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE;AAC9B,MAAM,YAAY,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 380, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/easing/utils/is-bezier-definition.mjs"], "sourcesContent": ["const isBezierDefinition = (easing) => Array.isArray(easing) && typeof easing[0] === \"number\";\n\nexport { isBezierDefinition };\n"], "names": [], "mappings": ";;;AAAA,MAAM,qBAAqB,CAAC,SAAW,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,CAAC,EAAE,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 391, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/easing/utils/map.mjs"], "sourcesContent": ["import { invariant } from '../../errors.mjs';\nimport { noop } from '../../noop.mjs';\nimport { anticipate } from '../anticipate.mjs';\nimport { backIn, backInOut, backOut } from '../back.mjs';\nimport { circIn, circInOut, circOut } from '../circ.mjs';\nimport { cubicBezier } from '../cubic-bezier.mjs';\nimport { easeIn, easeInOut, easeOut } from '../ease.mjs';\nimport { isBezierDefinition } from './is-bezier-definition.mjs';\n\nconst easingLookup = {\n    linear: noop,\n    easeIn,\n    easeInOut,\n    easeOut,\n    circIn,\n    circInOut,\n    circOut,\n    backIn,\n    backInOut,\n    backOut,\n    anticipate,\n};\nconst isValidEasing = (easing) => {\n    return typeof easing === \"string\";\n};\nconst easingDefinitionToFunction = (definition) => {\n    if (isBezierDefinition(definition)) {\n        // If cubic bezier definition, create bezier curve\n        invariant(definition.length === 4, `Cubic bezier arrays must contain four numerical values.`);\n        const [x1, y1, x2, y2] = definition;\n        return cubicBezier(x1, y1, x2, y2);\n    }\n    else if (isValidEasing(definition)) {\n        // Else lookup from table\n        invariant(easingLookup[definition] !== undefined, `Invalid easing type '${definition}'`);\n        return easingLookup[definition];\n    }\n    return definition;\n};\n\nexport { easingDefinitionToFunction };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEA,MAAM,eAAe;IACjB,QAAQ,sJAAA,CAAA,OAAI;IACZ,QAAA,gKAAA,CAAA,SAAM;IACN,WAAA,gKAAA,CAAA,YAAS;IACT,SAAA,gKAAA,CAAA,UAAO;IACP,QAAA,gKAAA,CAAA,SAAM;IACN,WAAA,gKAAA,CAAA,YAAS;IACT,SAAA,gKAAA,CAAA,UAAO;IACP,QAAA,gKAAA,CAAA,SAAM;IACN,WAAA,gKAAA,CAAA,YAAS;IACT,SAAA,gKAAA,CAAA,UAAO;IACP,YAAA,sKAAA,CAAA,aAAU;AACd;AACA,MAAM,gBAAgB,CAAC;IACnB,OAAO,OAAO,WAAW;AAC7B;AACA,MAAM,6BAA6B,CAAC;IAChC,IAAI,CAAA,GAAA,+LAAA,CAAA,qBAAkB,AAAD,EAAE,aAAa;QAChC,kDAAkD;QAClD,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,WAAW,MAAM,KAAK,GAAG,CAAC,uDAAuD,CAAC;QAC5F,MAAM,CAAC,IAAI,IAAI,IAAI,GAAG,GAAG;QACzB,OAAO,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE,IAAI,IAAI,IAAI;IACnC,OACK,IAAI,cAAc,aAAa;QAChC,yBAAyB;QACzB,CAAA,GAAA,wJAAA,CAAA,YAAS,AAAD,EAAE,YAAY,CAAC,WAAW,KAAK,WAAW,CAAC,qBAAqB,EAAE,WAAW,CAAC,CAAC;QACvF,OAAO,YAAY,CAAC,WAAW;IACnC;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/progress.mjs"], "sourcesContent": ["/*\n  Progress within given range\n\n  Given a lower limit and an upper limit, we return the progress\n  (expressed as a number 0-1) represented by the given value, and\n  limit that progress to within 0-1.\n\n  @param [number]: Lower limit\n  @param [number]: Upper limit\n  @param [number]: Value to find progress within given range\n  @return [number]: Progress of value within range as expressed 0-1\n*/\n/*#__NO_SIDE_EFFECTS__*/\nconst progress = (from, to, value) => {\n    const toFromDifference = to - from;\n    return toFromDifference === 0 ? 1 : (value - from) / toFromDifference;\n};\n\nexport { progress };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;AAWA,GACA,sBAAsB;;;AACtB,MAAM,WAAW,CAAC,MAAM,IAAI;IACxB,MAAM,mBAAmB,KAAK;IAC9B,OAAO,qBAAqB,IAAI,IAAI,CAAC,QAAQ,IAAI,IAAI;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/memo.mjs"], "sourcesContent": ["/*#__NO_SIDE_EFFECTS__*/\nfunction memo(callback) {\n    let result;\n    return () => {\n        if (result === undefined)\n            result = callback();\n        return result;\n    };\n}\n\nexport { memo };\n"], "names": [], "mappings": "AAAA,sBAAsB;;;AACtB,SAAS,KAAK,QAAQ;IAClB,IAAI;IACJ,OAAO;QACH,IAAI,WAAW,WACX,SAAS;QACb,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/is-object.mjs"], "sourcesContent": ["function isObject(value) {\n    return typeof value === \"object\" && value !== null;\n}\n\nexport { isObject };\n"], "names": [], "mappings": ";;;AAAA,SAAS,SAAS,KAAK;IACnB,OAAO,OAAO,UAAU,YAAY,UAAU;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/is-numerical-string.mjs"], "sourcesContent": ["/**\n * Check if value is a numerical string, ie a string that is purely a number eg \"100\" or \"-100.1\"\n */\nconst isNumericalString = (v) => /^-?(?:\\d+(?:\\.\\d+)?|\\.\\d+)$/u.test(v);\n\nexport { isNumericalString };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,MAAM,oBAAoB,CAAC,IAAM,+BAA+B,IAAI,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 514, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/motion-utils/dist/es/is-zero-value-string.mjs"], "sourcesContent": ["/**\n * Check if the value is a zero value string like \"0px\" or \"0%\"\n */\nconst isZeroValueString = (v) => /^0[^.\\s]+$/u.test(v);\n\nexport { isZeroValueString };\n"], "names": [], "mappings": "AAAA;;CAEC;;;AACD,MAAM,oBAAoB,CAAC,IAAM,cAAc,IAAI,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 526, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    function useSyncExternalStore$2(subscribe, getSnapshot) {\n      didWarnOld18Alpha ||\n        void 0 === React.startTransition ||\n        ((didWarnOld18Alpha = !0),\n        console.error(\n          \"You are using an outdated, pre-release alpha of React 18 that does not support useSyncExternalStore. The use-sync-external-store shim will not work correctly. Upgrade to a newer pre-release.\"\n        ));\n      var value = getSnapshot();\n      if (!didWarnUncachedGetSnapshot) {\n        var cachedValue = getSnapshot();\n        objectIs(value, cachedValue) ||\n          (console.error(\n            \"The result of getSnapshot should be cached to avoid an infinite loop\"\n          ),\n          (didWarnUncachedGetSnapshot = !0));\n      }\n      cachedValue = useState({\n        inst: { value: value, getSnapshot: getSnapshot }\n      });\n      var inst = cachedValue[0].inst,\n        forceUpdate = cachedValue[1];\n      useLayoutEffect(\n        function () {\n          inst.value = value;\n          inst.getSnapshot = getSnapshot;\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n        },\n        [subscribe, value, getSnapshot]\n      );\n      useEffect(\n        function () {\n          checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          return subscribe(function () {\n            checkIfSnapshotChanged(inst) && forceUpdate({ inst: inst });\n          });\n        },\n        [subscribe]\n      );\n      useDebugValue(value);\n      return value;\n    }\n    function checkIfSnapshotChanged(inst) {\n      var latestGetSnapshot = inst.getSnapshot;\n      inst = inst.value;\n      try {\n        var nextValue = latestGetSnapshot();\n        return !objectIs(inst, nextValue);\n      } catch (error) {\n        return !0;\n      }\n    }\n    function useSyncExternalStore$1(subscribe, getSnapshot) {\n      return getSnapshot();\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useState = React.useState,\n      useEffect = React.useEffect,\n      useLayoutEffect = React.useLayoutEffect,\n      useDebugValue = React.useDebugValue,\n      didWarnOld18Alpha = !1,\n      didWarnUncachedGetSnapshot = !1,\n      shim =\n        \"undefined\" === typeof window ||\n        \"undefined\" === typeof window.document ||\n        \"undefined\" === typeof window.document.createElement\n          ? useSyncExternalStore$1\n          : useSyncExternalStore$2;\n    exports.useSyncExternalStore =\n      void 0 !== React.useSyncExternalStore ? React.useSyncExternalStore : shim;\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,qBACE,KAAK,MAAM,MAAM,eAAe,IAChC,CAAC,AAAC,oBAAoB,CAAC,GACvB,QAAQ,KAAK,CACX,iMACD;QACH,IAAI,QAAQ;QACZ,IAAI,CAAC,4BAA4B;YAC/B,IAAI,cAAc;YAClB,SAAS,OAAO,gBACd,CAAC,QAAQ,KAAK,CACZ,yEAED,6BAA6B,CAAC,CAAE;QACrC;QACA,cAAc,SAAS;YACrB,MAAM;gBAAE,OAAO;gBAAO,aAAa;YAAY;QACjD;QACA,IAAI,OAAO,WAAW,CAAC,EAAE,CAAC,IAAI,EAC5B,cAAc,WAAW,CAAC,EAAE;QAC9B,gBACE;YACE,KAAK,KAAK,GAAG;YACb,KAAK,WAAW,GAAG;YACnB,uBAAuB,SAAS,YAAY;gBAAE,MAAM;YAAK;QAC3D,GACA;YAAC;YAAW;YAAO;SAAY;QAEjC,UACE;YACE,uBAAuB,SAAS,YAAY;gBAAE,MAAM;YAAK;YACzD,OAAO,UAAU;gBACf,uBAAuB,SAAS,YAAY;oBAAE,MAAM;gBAAK;YAC3D;QACF,GACA;YAAC;SAAU;QAEb,cAAc;QACd,OAAO;IACT;IACA,SAAS,uBAAuB,IAAI;QAClC,IAAI,oBAAoB,KAAK,WAAW;QACxC,OAAO,KAAK,KAAK;QACjB,IAAI;YACF,IAAI,YAAY;YAChB,OAAO,CAAC,SAAS,MAAM;QACzB,EAAE,OAAO,OAAO;YACd,OAAO,CAAC;QACV;IACF;IACA,SAAS,uBAAuB,SAAS,EAAE,WAAW;QACpD,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,gJACF,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,WAAW,MAAM,QAAQ,EACzB,YAAY,MAAM,SAAS,EAC3B,kBAAkB,MAAM,eAAe,EACvC,gBAAgB,MAAM,aAAa,EACnC,oBAAoB,CAAC,GACrB,6BAA6B,CAAC,GAC9B,OACE,gBAAgB,OAAO,UACvB,gBAAgB,OAAO,OAAO,QAAQ,IACtC,gBAAgB,OAAO,OAAO,QAAQ,CAAC,aAAa,GAChD,yBACA;IACR,QAAQ,oBAAoB,GAC1B,KAAK,MAAM,MAAM,oBAAoB,GAAG,MAAM,oBAAoB,GAAG;IACvE,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/use-sync-external-store/shim/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 613, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim/with-selector.development.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim/with-selector.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function is(x, y) {\n      return (x === y && (0 !== x || 1 / x === 1 / y)) || (x !== x && y !== y);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    var React = require(\"react\"),\n      shim = require(\"use-sync-external-store/shim\"),\n      objectIs = \"function\" === typeof Object.is ? Object.is : is,\n      useSyncExternalStore = shim.useSyncExternalStore,\n      useRef = React.useRef,\n      useEffect = React.useEffect,\n      useMemo = React.useMemo,\n      useDebugValue = React.useDebugValue;\n    exports.useSyncExternalStoreWithSelector = function (\n      subscribe,\n      getSnapshot,\n      getServerSnapshot,\n      selector,\n      isEqual\n    ) {\n      var instRef = useRef(null);\n      if (null === instRef.current) {\n        var inst = { hasValue: !1, value: null };\n        instRef.current = inst;\n      } else inst = instRef.current;\n      instRef = useMemo(\n        function () {\n          function memoizedSelector(nextSnapshot) {\n            if (!hasMemo) {\n              hasMemo = !0;\n              memoizedSnapshot = nextSnapshot;\n              nextSnapshot = selector(nextSnapshot);\n              if (void 0 !== isEqual && inst.hasValue) {\n                var currentSelection = inst.value;\n                if (isEqual(currentSelection, nextSnapshot))\n                  return (memoizedSelection = currentSelection);\n              }\n              return (memoizedSelection = nextSnapshot);\n            }\n            currentSelection = memoizedSelection;\n            if (objectIs(memoizedSnapshot, nextSnapshot))\n              return currentSelection;\n            var nextSelection = selector(nextSnapshot);\n            if (void 0 !== isEqual && isEqual(currentSelection, nextSelection))\n              return (memoizedSnapshot = nextSnapshot), currentSelection;\n            memoizedSnapshot = nextSnapshot;\n            return (memoizedSelection = nextSelection);\n          }\n          var hasMemo = !1,\n            memoizedSnapshot,\n            memoizedSelection,\n            maybeGetServerSnapshot =\n              void 0 === getServerSnapshot ? null : getServerSnapshot;\n          return [\n            function () {\n              return memoizedSelector(getSnapshot());\n            },\n            null === maybeGetServerSnapshot\n              ? void 0\n              : function () {\n                  return memoizedSelector(maybeGetServerSnapshot());\n                }\n          ];\n        },\n        [getSnapshot, getServerSnapshot, selector, isEqual]\n      );\n      var value = useSyncExternalStore(subscribe, instRef[0], instRef[1]);\n      useEffect(\n        function () {\n          inst.hasValue = !0;\n          inst.value = value;\n        },\n        [value]\n      );\n      useDebugValue(value);\n      return value;\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED;AACA,oEACE,AAAC;IACC,SAAS,GAAG,CAAC,EAAE,CAAC;QACd,OAAO,AAAC,MAAM,KAAK,CAAC,MAAM,KAAK,IAAI,MAAM,IAAI,CAAC,KAAO,MAAM,KAAK,MAAM;IACxE;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,IAAI,gJACF,uHACA,WAAW,eAAe,OAAO,OAAO,EAAE,GAAG,OAAO,EAAE,GAAG,IACzD,uBAAuB,KAAK,oBAAoB,EAChD,SAAS,MAAM,MAAM,EACrB,YAAY,MAAM,SAAS,EAC3B,UAAU,MAAM,OAAO,EACvB,gBAAgB,MAAM,aAAa;IACrC,QAAQ,gCAAgC,GAAG,SACzC,SAAS,EACT,WAAW,EACX,iBAAiB,EACjB,QAAQ,EACR,OAAO;QAEP,IAAI,UAAU,OAAO;QACrB,IAAI,SAAS,QAAQ,OAAO,EAAE;YAC5B,IAAI,OAAO;gBAAE,UAAU,CAAC;gBAAG,OAAO;YAAK;YACvC,QAAQ,OAAO,GAAG;QACpB,OAAO,OAAO,QAAQ,OAAO;QAC7B,UAAU,QACR;YACE,SAAS,iBAAiB,YAAY;gBACpC,IAAI,CAAC,SAAS;oBACZ,UAAU,CAAC;oBACX,mBAAmB;oBACnB,eAAe,SAAS;oBACxB,IAAI,KAAK,MAAM,WAAW,KAAK,QAAQ,EAAE;wBACvC,IAAI,mBAAmB,KAAK,KAAK;wBACjC,IAAI,QAAQ,kBAAkB,eAC5B,OAAQ,oBAAoB;oBAChC;oBACA,OAAQ,oBAAoB;gBAC9B;gBACA,mBAAmB;gBACnB,IAAI,SAAS,kBAAkB,eAC7B,OAAO;gBACT,IAAI,gBAAgB,SAAS;gBAC7B,IAAI,KAAK,MAAM,WAAW,QAAQ,kBAAkB,gBAClD,OAAO,AAAC,mBAAmB,cAAe;gBAC5C,mBAAmB;gBACnB,OAAQ,oBAAoB;YAC9B;YACA,IAAI,UAAU,CAAC,GACb,kBACA,mBACA,yBACE,KAAK,MAAM,oBAAoB,OAAO;YAC1C,OAAO;gBACL;oBACE,OAAO,iBAAiB;gBAC1B;gBACA,SAAS,yBACL,KAAK,IACL;oBACE,OAAO,iBAAiB;gBAC1B;aACL;QACH,GACA;YAAC;YAAa;YAAmB;YAAU;SAAQ;QAErD,IAAI,QAAQ,qBAAqB,WAAW,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE;QAClE,UACE;YACE,KAAK,QAAQ,GAAG,CAAC;YACjB,KAAK,KAAK,GAAG;QACf,GACA;YAAC;SAAM;QAET,cAAc;QACd,OAAO;IACT;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/use-sync-external-store/shim/with-selector.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.production.js');\n} else {\n  module.exports = require('../cjs/use-sync-external-store-shim/with-selector.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 700, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/zustand/esm/vanilla.mjs"], "sourcesContent": ["const createStoreImpl = (createState) => {\n  let state;\n  const listeners = /* @__PURE__ */ new Set();\n  const setState = (partial, replace) => {\n    const nextState = typeof partial === \"function\" ? partial(state) : partial;\n    if (!Object.is(nextState, state)) {\n      const previousState = state;\n      state = (replace != null ? replace : typeof nextState !== \"object\" || nextState === null) ? nextState : Object.assign({}, state, nextState);\n      listeners.forEach((listener) => listener(state, previousState));\n    }\n  };\n  const getState = () => state;\n  const getInitialState = () => initialState;\n  const subscribe = (listener) => {\n    listeners.add(listener);\n    return () => listeners.delete(listener);\n  };\n  const api = { setState, getState, getInitialState, subscribe };\n  const initialState = state = createState(setState, getState, api);\n  return api;\n};\nconst createStore = (createState) => createState ? createStoreImpl(createState) : createStoreImpl;\n\nexport { createStore };\n"], "names": [], "mappings": ";;;AAAA,MAAM,kBAAkB,CAAC;IACvB,IAAI;IACJ,MAAM,YAAY,aAAa,GAAG,IAAI;IACtC,MAAM,WAAW,CAAC,SAAS;QACzB,MAAM,YAAY,OAAO,YAAY,aAAa,QAAQ,SAAS;QACnE,IAAI,CAAC,OAAO,EAAE,CAAC,WAAW,QAAQ;YAChC,MAAM,gBAAgB;YACtB,QAAQ,CAAC,WAAW,OAAO,UAAU,OAAO,cAAc,YAAY,cAAc,IAAI,IAAI,YAAY,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;YACjI,UAAU,OAAO,CAAC,CAAC,WAAa,SAAS,OAAO;QAClD;IACF;IACA,MAAM,WAAW,IAAM;IACvB,MAAM,kBAAkB,IAAM;IAC9B,MAAM,YAAY,CAAC;QACjB,UAAU,GAAG,CAAC;QACd,OAAO,IAAM,UAAU,MAAM,CAAC;IAChC;IACA,MAAM,MAAM;QAAE;QAAU;QAAU;QAAiB;IAAU;IAC7D,MAAM,eAAe,QAAQ,YAAY,UAAU,UAAU;IAC7D,OAAO;AACT;AACA,MAAM,cAAc,CAAC,cAAgB,cAAc,gBAAgB,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/zustand/esm/traditional.mjs"], "sourcesContent": ["import React from 'react';\nimport useSyncExternalStoreExports from 'use-sync-external-store/shim/with-selector.js';\nimport { createStore } from 'zustand/vanilla';\n\nconst { useSyncExternalStoreWithSelector } = useSyncExternalStoreExports;\nconst identity = (arg) => arg;\nfunction useStoreWithEqualityFn(api, selector = identity, equalityFn) {\n  const slice = useSyncExternalStoreWithSelector(\n    api.subscribe,\n    api.getState,\n    api.getInitialState,\n    selector,\n    equalityFn\n  );\n  React.useDebugValue(slice);\n  return slice;\n}\nconst createWithEqualityFnImpl = (createState, defaultEqualityFn) => {\n  const api = createStore(createState);\n  const useBoundStoreWithEqualityFn = (selector, equalityFn = defaultEqualityFn) => useStoreWithEqualityFn(api, selector, equalityFn);\n  Object.assign(useBoundStoreWithEqualityFn, api);\n  return useBoundStoreWithEqualityFn;\n};\nconst createWithEqualityFn = (createState, defaultEqualityFn) => createState ? createWithEqualityFnImpl(createState, defaultEqualityFn) : createWithEqualityFnImpl;\n\nexport { createWithEqualityFn, useStoreWithEqualityFn };\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,MAAM,EAAE,gCAAgC,EAAE,GAAG,4KAAA,CAAA,UAA2B;AACxE,MAAM,WAAW,CAAC,MAAQ;AAC1B,SAAS,uBAAuB,GAAG,EAAE,WAAW,QAAQ,EAAE,UAAU;IAClE,MAAM,QAAQ,iCACZ,IAAI,SAAS,EACb,IAAI,QAAQ,EACZ,IAAI,eAAe,EACnB,UACA;IAEF,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC;IACpB,OAAO;AACT;AACA,MAAM,2BAA2B,CAAC,aAAa;IAC7C,MAAM,MAAM,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE;IACxB,MAAM,8BAA8B,CAAC,UAAU,aAAa,iBAAiB,GAAK,uBAAuB,KAAK,UAAU;IACxH,OAAO,MAAM,CAAC,6BAA6B;IAC3C,OAAO;AACT;AACA,MAAM,uBAAuB,CAAC,aAAa,oBAAsB,cAAc,yBAAyB,aAAa,qBAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 767, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/scheduler/cjs/scheduler.development.js"], "sourcesContent": ["/**\n * @license React\n * scheduler.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function performWorkUntilDeadline() {\n      if (isMessageLoopRunning) {\n        var currentTime = exports.unstable_now();\n        startTime = currentTime;\n        var hasMoreWork = !0;\n        try {\n          a: {\n            isHostCallbackScheduled = !1;\n            isHostTimeoutScheduled &&\n              ((isHostTimeoutScheduled = !1),\n              localClearTimeout(taskTimeoutID),\n              (taskTimeoutID = -1));\n            isPerformingWork = !0;\n            var previousPriorityLevel = currentPriorityLevel;\n            try {\n              b: {\n                advanceTimers(currentTime);\n                for (\n                  currentTask = peek(taskQueue);\n                  null !== currentTask &&\n                  !(\n                    currentTask.expirationTime > currentTime &&\n                    shouldYieldToHost()\n                  );\n\n                ) {\n                  var callback = currentTask.callback;\n                  if (\"function\" === typeof callback) {\n                    currentTask.callback = null;\n                    currentPriorityLevel = currentTask.priorityLevel;\n                    var continuationCallback = callback(\n                      currentTask.expirationTime <= currentTime\n                    );\n                    currentTime = exports.unstable_now();\n                    if (\"function\" === typeof continuationCallback) {\n                      currentTask.callback = continuationCallback;\n                      advanceTimers(currentTime);\n                      hasMoreWork = !0;\n                      break b;\n                    }\n                    currentTask === peek(taskQueue) && pop(taskQueue);\n                    advanceTimers(currentTime);\n                  } else pop(taskQueue);\n                  currentTask = peek(taskQueue);\n                }\n                if (null !== currentTask) hasMoreWork = !0;\n                else {\n                  var firstTimer = peek(timerQueue);\n                  null !== firstTimer &&\n                    requestHostTimeout(\n                      handleTimeout,\n                      firstTimer.startTime - currentTime\n                    );\n                  hasMoreWork = !1;\n                }\n              }\n              break a;\n            } finally {\n              (currentTask = null),\n                (currentPriorityLevel = previousPriorityLevel),\n                (isPerformingWork = !1);\n            }\n            hasMoreWork = void 0;\n          }\n        } finally {\n          hasMoreWork\n            ? schedulePerformWorkUntilDeadline()\n            : (isMessageLoopRunning = !1);\n        }\n      }\n    }\n    function push(heap, node) {\n      var index = heap.length;\n      heap.push(node);\n      a: for (; 0 < index; ) {\n        var parentIndex = (index - 1) >>> 1,\n          parent = heap[parentIndex];\n        if (0 < compare(parent, node))\n          (heap[parentIndex] = node),\n            (heap[index] = parent),\n            (index = parentIndex);\n        else break a;\n      }\n    }\n    function peek(heap) {\n      return 0 === heap.length ? null : heap[0];\n    }\n    function pop(heap) {\n      if (0 === heap.length) return null;\n      var first = heap[0],\n        last = heap.pop();\n      if (last !== first) {\n        heap[0] = last;\n        a: for (\n          var index = 0, length = heap.length, halfLength = length >>> 1;\n          index < halfLength;\n\n        ) {\n          var leftIndex = 2 * (index + 1) - 1,\n            left = heap[leftIndex],\n            rightIndex = leftIndex + 1,\n            right = heap[rightIndex];\n          if (0 > compare(left, last))\n            rightIndex < length && 0 > compare(right, left)\n              ? ((heap[index] = right),\n                (heap[rightIndex] = last),\n                (index = rightIndex))\n              : ((heap[index] = left),\n                (heap[leftIndex] = last),\n                (index = leftIndex));\n          else if (rightIndex < length && 0 > compare(right, last))\n            (heap[index] = right),\n              (heap[rightIndex] = last),\n              (index = rightIndex);\n          else break a;\n        }\n      }\n      return first;\n    }\n    function compare(a, b) {\n      var diff = a.sortIndex - b.sortIndex;\n      return 0 !== diff ? diff : a.id - b.id;\n    }\n    function advanceTimers(currentTime) {\n      for (var timer = peek(timerQueue); null !== timer; ) {\n        if (null === timer.callback) pop(timerQueue);\n        else if (timer.startTime <= currentTime)\n          pop(timerQueue),\n            (timer.sortIndex = timer.expirationTime),\n            push(taskQueue, timer);\n        else break;\n        timer = peek(timerQueue);\n      }\n    }\n    function handleTimeout(currentTime) {\n      isHostTimeoutScheduled = !1;\n      advanceTimers(currentTime);\n      if (!isHostCallbackScheduled)\n        if (null !== peek(taskQueue))\n          (isHostCallbackScheduled = !0), requestHostCallback();\n        else {\n          var firstTimer = peek(timerQueue);\n          null !== firstTimer &&\n            requestHostTimeout(\n              handleTimeout,\n              firstTimer.startTime - currentTime\n            );\n        }\n    }\n    function shouldYieldToHost() {\n      return exports.unstable_now() - startTime < frameInterval ? !1 : !0;\n    }\n    function requestHostCallback() {\n      isMessageLoopRunning ||\n        ((isMessageLoopRunning = !0), schedulePerformWorkUntilDeadline());\n    }\n    function requestHostTimeout(callback, ms) {\n      taskTimeoutID = localSetTimeout(function () {\n        callback(exports.unstable_now());\n      }, ms);\n    }\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(Error());\n    exports.unstable_now = void 0;\n    if (\n      \"object\" === typeof performance &&\n      \"function\" === typeof performance.now\n    ) {\n      var localPerformance = performance;\n      exports.unstable_now = function () {\n        return localPerformance.now();\n      };\n    } else {\n      var localDate = Date,\n        initialTime = localDate.now();\n      exports.unstable_now = function () {\n        return localDate.now() - initialTime;\n      };\n    }\n    var taskQueue = [],\n      timerQueue = [],\n      taskIdCounter = 1,\n      currentTask = null,\n      currentPriorityLevel = 3,\n      isPerformingWork = !1,\n      isHostCallbackScheduled = !1,\n      isHostTimeoutScheduled = !1,\n      localSetTimeout = \"function\" === typeof setTimeout ? setTimeout : null,\n      localClearTimeout =\n        \"function\" === typeof clearTimeout ? clearTimeout : null,\n      localSetImmediate =\n        \"undefined\" !== typeof setImmediate ? setImmediate : null,\n      isMessageLoopRunning = !1,\n      taskTimeoutID = -1,\n      frameInterval = 5,\n      startTime = -1;\n    if (\"function\" === typeof localSetImmediate)\n      var schedulePerformWorkUntilDeadline = function () {\n        localSetImmediate(performWorkUntilDeadline);\n      };\n    else if (\"undefined\" !== typeof MessageChannel) {\n      var channel = new MessageChannel(),\n        port = channel.port2;\n      channel.port1.onmessage = performWorkUntilDeadline;\n      schedulePerformWorkUntilDeadline = function () {\n        port.postMessage(null);\n      };\n    } else\n      schedulePerformWorkUntilDeadline = function () {\n        localSetTimeout(performWorkUntilDeadline, 0);\n      };\n    exports.unstable_IdlePriority = 5;\n    exports.unstable_ImmediatePriority = 1;\n    exports.unstable_LowPriority = 4;\n    exports.unstable_NormalPriority = 3;\n    exports.unstable_Profiling = null;\n    exports.unstable_UserBlockingPriority = 2;\n    exports.unstable_cancelCallback = function (task) {\n      task.callback = null;\n    };\n    exports.unstable_continueExecution = function () {\n      isHostCallbackScheduled ||\n        isPerformingWork ||\n        ((isHostCallbackScheduled = !0), requestHostCallback());\n    };\n    exports.unstable_forceFrameRate = function (fps) {\n      0 > fps || 125 < fps\n        ? console.error(\n            \"forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported\"\n          )\n        : (frameInterval = 0 < fps ? Math.floor(1e3 / fps) : 5);\n    };\n    exports.unstable_getCurrentPriorityLevel = function () {\n      return currentPriorityLevel;\n    };\n    exports.unstable_getFirstCallbackNode = function () {\n      return peek(taskQueue);\n    };\n    exports.unstable_next = function (eventHandler) {\n      switch (currentPriorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n          var priorityLevel = 3;\n          break;\n        default:\n          priorityLevel = currentPriorityLevel;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_pauseExecution = function () {};\n    exports.unstable_requestPaint = function () {};\n    exports.unstable_runWithPriority = function (priorityLevel, eventHandler) {\n      switch (priorityLevel) {\n        case 1:\n        case 2:\n        case 3:\n        case 4:\n        case 5:\n          break;\n        default:\n          priorityLevel = 3;\n      }\n      var previousPriorityLevel = currentPriorityLevel;\n      currentPriorityLevel = priorityLevel;\n      try {\n        return eventHandler();\n      } finally {\n        currentPriorityLevel = previousPriorityLevel;\n      }\n    };\n    exports.unstable_scheduleCallback = function (\n      priorityLevel,\n      callback,\n      options\n    ) {\n      var currentTime = exports.unstable_now();\n      \"object\" === typeof options && null !== options\n        ? ((options = options.delay),\n          (options =\n            \"number\" === typeof options && 0 < options\n              ? currentTime + options\n              : currentTime))\n        : (options = currentTime);\n      switch (priorityLevel) {\n        case 1:\n          var timeout = -1;\n          break;\n        case 2:\n          timeout = 250;\n          break;\n        case 5:\n          timeout = 1073741823;\n          break;\n        case 4:\n          timeout = 1e4;\n          break;\n        default:\n          timeout = 5e3;\n      }\n      timeout = options + timeout;\n      priorityLevel = {\n        id: taskIdCounter++,\n        callback: callback,\n        priorityLevel: priorityLevel,\n        startTime: options,\n        expirationTime: timeout,\n        sortIndex: -1\n      };\n      options > currentTime\n        ? ((priorityLevel.sortIndex = options),\n          push(timerQueue, priorityLevel),\n          null === peek(taskQueue) &&\n            priorityLevel === peek(timerQueue) &&\n            (isHostTimeoutScheduled\n              ? (localClearTimeout(taskTimeoutID), (taskTimeoutID = -1))\n              : (isHostTimeoutScheduled = !0),\n            requestHostTimeout(handleTimeout, options - currentTime)))\n        : ((priorityLevel.sortIndex = timeout),\n          push(taskQueue, priorityLevel),\n          isHostCallbackScheduled ||\n            isPerformingWork ||\n            ((isHostCallbackScheduled = !0), requestHostCallback()));\n      return priorityLevel;\n    };\n    exports.unstable_shouldYield = shouldYieldToHost;\n    exports.unstable_wrapCallback = function (callback) {\n      var parentPriorityLevel = currentPriorityLevel;\n      return function () {\n        var previousPriorityLevel = currentPriorityLevel;\n        currentPriorityLevel = parentPriorityLevel;\n        try {\n          return callback.apply(this, arguments);\n        } finally {\n          currentPriorityLevel = previousPriorityLevel;\n        }\n      };\n    };\n    \"undefined\" !== typeof __REACT_DEVTOOLS_GLOBAL_HOOK__ &&\n      \"function\" ===\n        typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop &&\n      __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(Error());\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAED;AACA,oEACE,AAAC;IACC,SAAS;QACP,IAAI,sBAAsB;YACxB,IAAI,cAAc,QAAQ,YAAY;YACtC,YAAY;YACZ,IAAI,cAAc,CAAC;YACnB,IAAI;gBACF,GAAG;oBACD,0BAA0B,CAAC;oBAC3B,0BACE,CAAC,AAAC,yBAAyB,CAAC,GAC5B,kBAAkB,gBACjB,gBAAgB,CAAC,CAAE;oBACtB,mBAAmB,CAAC;oBACpB,IAAI,wBAAwB;oBAC5B,IAAI;wBACF,GAAG;4BACD,cAAc;4BACd,IACE,cAAc,KAAK,YACnB,SAAS,eACT,CAAC,CACC,YAAY,cAAc,GAAG,eAC7B,mBACF,GAEA;gCACA,IAAI,WAAW,YAAY,QAAQ;gCACnC,IAAI,eAAe,OAAO,UAAU;oCAClC,YAAY,QAAQ,GAAG;oCACvB,uBAAuB,YAAY,aAAa;oCAChD,IAAI,uBAAuB,SACzB,YAAY,cAAc,IAAI;oCAEhC,cAAc,QAAQ,YAAY;oCAClC,IAAI,eAAe,OAAO,sBAAsB;wCAC9C,YAAY,QAAQ,GAAG;wCACvB,cAAc;wCACd,cAAc,CAAC;wCACf,MAAM;oCACR;oCACA,gBAAgB,KAAK,cAAc,IAAI;oCACvC,cAAc;gCAChB,OAAO,IAAI;gCACX,cAAc,KAAK;4BACrB;4BACA,IAAI,SAAS,aAAa,cAAc,CAAC;iCACpC;gCACH,IAAI,aAAa,KAAK;gCACtB,SAAS,cACP,mBACE,eACA,WAAW,SAAS,GAAG;gCAE3B,cAAc,CAAC;4BACjB;wBACF;wBACA,MAAM;oBACR,SAAU;wBACP,cAAc,MACZ,uBAAuB,uBACvB,mBAAmB,CAAC;oBACzB;oBACA,cAAc,KAAK;gBACrB;YACF,SAAU;gBACR,cACI,qCACC,uBAAuB,CAAC;YAC/B;QACF;IACF;IACA,SAAS,KAAK,IAAI,EAAE,IAAI;QACtB,IAAI,QAAQ,KAAK,MAAM;QACvB,KAAK,IAAI,CAAC;QACV,GAAG,MAAO,IAAI,OAAS;YACrB,IAAI,cAAc,AAAC,QAAQ,MAAO,GAChC,SAAS,IAAI,CAAC,YAAY;YAC5B,IAAI,IAAI,QAAQ,QAAQ,OACtB,AAAC,IAAI,CAAC,YAAY,GAAG,MAClB,IAAI,CAAC,MAAM,GAAG,QACd,QAAQ;iBACR,MAAM;QACb;IACF;IACA,SAAS,KAAK,IAAI;QAChB,OAAO,MAAM,KAAK,MAAM,GAAG,OAAO,IAAI,CAAC,EAAE;IAC3C;IACA,SAAS,IAAI,IAAI;QACf,IAAI,MAAM,KAAK,MAAM,EAAE,OAAO;QAC9B,IAAI,QAAQ,IAAI,CAAC,EAAE,EACjB,OAAO,KAAK,GAAG;QACjB,IAAI,SAAS,OAAO;YAClB,IAAI,CAAC,EAAE,GAAG;YACV,GAAG,IACD,IAAI,QAAQ,GAAG,SAAS,KAAK,MAAM,EAAE,aAAa,WAAW,GAC7D,QAAQ,YAER;gBACA,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,IAAI,GAChC,OAAO,IAAI,CAAC,UAAU,EACtB,aAAa,YAAY,GACzB,QAAQ,IAAI,CAAC,WAAW;gBAC1B,IAAI,IAAI,QAAQ,MAAM,OACpB,aAAa,UAAU,IAAI,QAAQ,OAAO,QACtC,CAAC,AAAC,IAAI,CAAC,MAAM,GAAG,OACf,IAAI,CAAC,WAAW,GAAG,MACnB,QAAQ,UAAW,IACpB,CAAC,AAAC,IAAI,CAAC,MAAM,GAAG,MACf,IAAI,CAAC,UAAU,GAAG,MAClB,QAAQ,SAAU;qBACpB,IAAI,aAAa,UAAU,IAAI,QAAQ,OAAO,OACjD,AAAC,IAAI,CAAC,MAAM,GAAG,OACZ,IAAI,CAAC,WAAW,GAAG,MACnB,QAAQ;qBACR,MAAM;YACb;QACF;QACA,OAAO;IACT;IACA,SAAS,QAAQ,CAAC,EAAE,CAAC;QACnB,IAAI,OAAO,EAAE,SAAS,GAAG,EAAE,SAAS;QACpC,OAAO,MAAM,OAAO,OAAO,EAAE,EAAE,GAAG,EAAE,EAAE;IACxC;IACA,SAAS,cAAc,WAAW;QAChC,IAAK,IAAI,QAAQ,KAAK,aAAa,SAAS,OAAS;YACnD,IAAI,SAAS,MAAM,QAAQ,EAAE,IAAI;iBAC5B,IAAI,MAAM,SAAS,IAAI,aAC1B,IAAI,aACD,MAAM,SAAS,GAAG,MAAM,cAAc,EACvC,KAAK,WAAW;iBACf;YACL,QAAQ,KAAK;QACf;IACF;IACA,SAAS,cAAc,WAAW;QAChC,yBAAyB,CAAC;QAC1B,cAAc;QACd,IAAI,CAAC,yBACH,IAAI,SAAS,KAAK,YAChB,AAAC,0BAA0B,CAAC,GAAI;aAC7B;YACH,IAAI,aAAa,KAAK;YACtB,SAAS,cACP,mBACE,eACA,WAAW,SAAS,GAAG;QAE7B;IACJ;IACA,SAAS;QACP,OAAO,QAAQ,YAAY,KAAK,YAAY,gBAAgB,CAAC,IAAI,CAAC;IACpE;IACA,SAAS;QACP,wBACE,CAAC,AAAC,uBAAuB,CAAC,GAAI,kCAAkC;IACpE;IACA,SAAS,mBAAmB,QAAQ,EAAE,EAAE;QACtC,gBAAgB,gBAAgB;YAC9B,SAAS,QAAQ,YAAY;QAC/B,GAAG;IACL;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,2BAA2B,IACnE,+BAA+B,2BAA2B,CAAC;IAC7D,QAAQ,YAAY,GAAG,KAAK;IAC5B,IACE,aAAa,OAAO,eACpB,eAAe,OAAO,YAAY,GAAG,EACrC;QACA,IAAI,mBAAmB;QACvB,QAAQ,YAAY,GAAG;YACrB,OAAO,iBAAiB,GAAG;QAC7B;IACF,OAAO;QACL,IAAI,YAAY,MACd,cAAc,UAAU,GAAG;QAC7B,QAAQ,YAAY,GAAG;YACrB,OAAO,UAAU,GAAG,KAAK;QAC3B;IACF;IACA,IAAI,YAAY,EAAE,EAChB,aAAa,EAAE,EACf,gBAAgB,GAChB,cAAc,MACd,uBAAuB,GACvB,mBAAmB,CAAC,GACpB,0BAA0B,CAAC,GAC3B,yBAAyB,CAAC,GAC1B,kBAAkB,eAAe,OAAO,aAAa,aAAa,MAClE,oBACE,eAAe,OAAO,eAAe,eAAe,MACtD,oBACE,gBAAgB,OAAO,eAAe,eAAe,MACvD,uBAAuB,CAAC,GACxB,gBAAgB,CAAC,GACjB,gBAAgB,GAChB,YAAY,CAAC;IACf,IAAI,eAAe,OAAO,mBACxB,IAAI,mCAAmC;QACrC,kBAAkB;IACpB;SACG,IAAI,gBAAgB,OAAO,gBAAgB;QAC9C,IAAI,UAAU,IAAI,kBAChB,OAAO,QAAQ,KAAK;QACtB,QAAQ,KAAK,CAAC,SAAS,GAAG;QAC1B,mCAAmC;YACjC,KAAK,WAAW,CAAC;QACnB;IACF,OACE,mCAAmC;QACjC,gBAAgB,0BAA0B;IAC5C;IACF,QAAQ,qBAAqB,GAAG;IAChC,QAAQ,0BAA0B,GAAG;IACrC,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,uBAAuB,GAAG;IAClC,QAAQ,kBAAkB,GAAG;IAC7B,QAAQ,6BAA6B,GAAG;IACxC,QAAQ,uBAAuB,GAAG,SAAU,IAAI;QAC9C,KAAK,QAAQ,GAAG;IAClB;IACA,QAAQ,0BAA0B,GAAG;QACnC,2BACE,oBACA,CAAC,AAAC,0BAA0B,CAAC,GAAI,qBAAqB;IAC1D;IACA,QAAQ,uBAAuB,GAAG,SAAU,GAAG;QAC7C,IAAI,OAAO,MAAM,MACb,QAAQ,KAAK,CACX,qHAED,gBAAgB,IAAI,MAAM,KAAK,KAAK,CAAC,MAAM,OAAO;IACzD;IACA,QAAQ,gCAAgC,GAAG;QACzC,OAAO;IACT;IACA,QAAQ,6BAA6B,GAAG;QACtC,OAAO,KAAK;IACd;IACA,QAAQ,aAAa,GAAG,SAAU,YAAY;QAC5C,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;gBACH,IAAI,gBAAgB;gBACpB;YACF;gBACE,gBAAgB;QACpB;QACA,IAAI,wBAAwB;QAC5B,uBAAuB;QACvB,IAAI;YACF,OAAO;QACT,SAAU;YACR,uBAAuB;QACzB;IACF;IACA,QAAQ,uBAAuB,GAAG,YAAa;IAC/C,QAAQ,qBAAqB,GAAG,YAAa;IAC7C,QAAQ,wBAAwB,GAAG,SAAU,aAAa,EAAE,YAAY;QACtE,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH;YACF;gBACE,gBAAgB;QACpB;QACA,IAAI,wBAAwB;QAC5B,uBAAuB;QACvB,IAAI;YACF,OAAO;QACT,SAAU;YACR,uBAAuB;QACzB;IACF;IACA,QAAQ,yBAAyB,GAAG,SAClC,aAAa,EACb,QAAQ,EACR,OAAO;QAEP,IAAI,cAAc,QAAQ,YAAY;QACtC,aAAa,OAAO,WAAW,SAAS,UACpC,CAAC,AAAC,UAAU,QAAQ,KAAK,EACxB,UACC,aAAa,OAAO,WAAW,IAAI,UAC/B,cAAc,UACd,WAAY,IACjB,UAAU;QACf,OAAQ;YACN,KAAK;gBACH,IAAI,UAAU,CAAC;gBACf;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,UAAU;gBACV;YACF;gBACE,UAAU;QACd;QACA,UAAU,UAAU;QACpB,gBAAgB;YACd,IAAI;YACJ,UAAU;YACV,eAAe;YACf,WAAW;YACX,gBAAgB;YAChB,WAAW,CAAC;QACd;QACA,UAAU,cACN,CAAC,AAAC,cAAc,SAAS,GAAG,SAC5B,KAAK,YAAY,gBACjB,SAAS,KAAK,cACZ,kBAAkB,KAAK,eACvB,CAAC,yBACG,CAAC,kBAAkB,gBAAiB,gBAAgB,CAAC,CAAE,IACtD,yBAAyB,CAAC,GAC/B,mBAAmB,eAAe,UAAU,YAAY,CAAC,IAC3D,CAAC,AAAC,cAAc,SAAS,GAAG,SAC5B,KAAK,WAAW,gBAChB,2BACE,oBACA,CAAC,AAAC,0BAA0B,CAAC,GAAI,qBAAqB,CAAC;QAC7D,OAAO;IACT;IACA,QAAQ,oBAAoB,GAAG;IAC/B,QAAQ,qBAAqB,GAAG,SAAU,QAAQ;QAChD,IAAI,sBAAsB;QAC1B,OAAO;YACL,IAAI,wBAAwB;YAC5B,uBAAuB;YACvB,IAAI;gBACF,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;YAC9B,SAAU;gBACR,uBAAuB;YACzB;QACF;IACF;IACA,gBAAgB,OAAO,kCACrB,eACE,OAAO,+BAA+B,0BAA0B,IAClE,+BAA+B,0BAA0B,CAAC;AAC9D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/scheduler/index.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/scheduler.production.js');\n} else {\n  module.exports = require('./cjs/scheduler.development.js');\n}\n"], "names": [], "mappings": "AAAA;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/suspend-react/index.js"], "sourcesContent": ["const isPromise = promise => typeof promise === 'object' && typeof promise.then === 'function';\n\nconst globalCache = [];\n\nfunction shallowEqualArrays(arrA, arrB, equal = (a, b) => a === b) {\n  if (arrA === arrB) return true;\n  if (!arrA || !arrB) return false;\n  const len = arrA.length;\n  if (arrB.length !== len) return false;\n\n  for (let i = 0; i < len; i++) if (!equal(arrA[i], arrB[i])) return false;\n\n  return true;\n}\n\nfunction query(fn, keys = null, preload = false, config = {}) {\n  // If no keys were given, the function is the key\n  if (keys === null) keys = [fn];\n\n  for (const entry of globalCache) {\n    // Find a match\n    if (shallowEqualArrays(keys, entry.keys, entry.equal)) {\n      // If we're pre-loading and the element is present, just return\n      if (preload) return undefined; // If an error occurred, throw\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'error')) throw entry.error; // If a response was successful, return\n\n      if (Object.prototype.hasOwnProperty.call(entry, 'response')) {\n        if (config.lifespan && config.lifespan > 0) {\n          if (entry.timeout) clearTimeout(entry.timeout);\n          entry.timeout = setTimeout(entry.remove, config.lifespan);\n        }\n\n        return entry.response;\n      } // If the promise is still unresolved, throw\n\n\n      if (!preload) throw entry.promise;\n    }\n  } // The request is new or has changed.\n\n\n  const entry = {\n    keys,\n    equal: config.equal,\n    remove: () => {\n      const index = globalCache.indexOf(entry);\n      if (index !== -1) globalCache.splice(index, 1);\n    },\n    promise: // Execute the promise\n    (isPromise(fn) ? fn : fn(...keys) // When it resolves, store its value\n    ).then(response => {\n      entry.response = response; // Remove the entry in time if a lifespan was given\n\n      if (config.lifespan && config.lifespan > 0) {\n        entry.timeout = setTimeout(entry.remove, config.lifespan);\n      }\n    }) // Store caught errors, they will be thrown in the render-phase to bubble into an error-bound\n    .catch(error => entry.error = error)\n  }; // Register the entry\n\n  globalCache.push(entry); // And throw the promise, this yields control back to React\n\n  if (!preload) throw entry.promise;\n  return undefined;\n}\n\nconst suspend = (fn, keys, config) => query(fn, keys, false, config);\n\nconst preload = (fn, keys, config) => void query(fn, keys, true, config);\n\nconst peek = keys => {\n  var _globalCache$find;\n\n  return (_globalCache$find = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal))) == null ? void 0 : _globalCache$find.response;\n};\n\nconst clear = keys => {\n  if (keys === undefined || keys.length === 0) globalCache.splice(0, globalCache.length);else {\n    const entry = globalCache.find(entry => shallowEqualArrays(keys, entry.keys, entry.equal));\n    if (entry) entry.remove();\n  }\n};\n\nexport { clear, peek, preload, suspend };\n"], "names": [], "mappings": ";;;;;;AAAA,MAAM,YAAY,CAAA,UAAW,OAAO,YAAY,YAAY,OAAO,QAAQ,IAAI,KAAK;AAEpF,MAAM,cAAc,EAAE;AAEtB,SAAS,mBAAmB,IAAI,EAAE,IAAI,EAAE,QAAQ,CAAC,GAAG,IAAM,MAAM,CAAC;IAC/D,IAAI,SAAS,MAAM,OAAO;IAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO;IAC3B,MAAM,MAAM,KAAK,MAAM;IACvB,IAAI,KAAK,MAAM,KAAK,KAAK,OAAO;IAEhC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK,IAAI,CAAC,MAAM,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,GAAG,OAAO;IAEnE,OAAO;AACT;AAEA,SAAS,MAAM,EAAE,EAAE,OAAO,IAAI,EAAE,UAAU,KAAK,EAAE,SAAS,CAAC,CAAC;IAC1D,iDAAiD;IACjD,IAAI,SAAS,MAAM,OAAO;QAAC;KAAG;IAE9B,KAAK,MAAM,SAAS,YAAa;QAC/B,eAAe;QACf,IAAI,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK,GAAG;YACrD,+DAA+D;YAC/D,IAAI,SAAS,OAAO,WAAW,8BAA8B;YAE7D,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,UAAU,MAAM,MAAM,KAAK,EAAE,uCAAuC;YAEpH,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,aAAa;gBAC3D,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG;oBAC1C,IAAI,MAAM,OAAO,EAAE,aAAa,MAAM,OAAO;oBAC7C,MAAM,OAAO,GAAG,WAAW,MAAM,MAAM,EAAE,OAAO,QAAQ;gBAC1D;gBAEA,OAAO,MAAM,QAAQ;YACvB,EAAE,4CAA4C;YAG9C,IAAI,CAAC,SAAS,MAAM,MAAM,OAAO;QACnC;IACF,EAAE,qCAAqC;IAGvC,MAAM,QAAQ;QACZ;QACA,OAAO,OAAO,KAAK;QACnB,QAAQ;YACN,MAAM,QAAQ,YAAY,OAAO,CAAC;YAClC,IAAI,UAAU,CAAC,GAAG,YAAY,MAAM,CAAC,OAAO;QAC9C;QACA,SACA,CAAC,UAAU,MAAM,KAAK,MAAM,MAAM,oCAAoC;QACtE,EAAE,IAAI,CAAC,CAAA;YACL,MAAM,QAAQ,GAAG,UAAU,mDAAmD;YAE9E,IAAI,OAAO,QAAQ,IAAI,OAAO,QAAQ,GAAG,GAAG;gBAC1C,MAAM,OAAO,GAAG,WAAW,MAAM,MAAM,EAAE,OAAO,QAAQ;YAC1D;QACF,GAAG,6FAA6F;SAC/F,KAAK,CAAC,CAAA,QAAS,MAAM,KAAK,GAAG;IAChC,GAAG,qBAAqB;IAExB,YAAY,IAAI,CAAC,QAAQ,2DAA2D;IAEpF,IAAI,CAAC,SAAS,MAAM,MAAM,OAAO;IACjC,OAAO;AACT;AAEA,MAAM,UAAU,CAAC,IAAI,MAAM,SAAW,MAAM,IAAI,MAAM,OAAO;AAE7D,MAAM,UAAU,CAAC,IAAI,MAAM,SAAW,KAAK,MAAM,IAAI,MAAM,MAAM;AAEjE,MAAM,OAAO,CAAA;IACX,IAAI;IAEJ,OAAO,CAAC,oBAAoB,YAAY,IAAI,CAAC,CAAA,QAAS,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK,EAAE,KAAK,OAAO,KAAK,IAAI,kBAAkB,QAAQ;AACzJ;AAEA,MAAM,QAAQ,CAAA;IACZ,IAAI,SAAS,aAAa,KAAK,MAAM,KAAK,GAAG,YAAY,MAAM,CAAC,GAAG,YAAY,MAAM;SAAO;QAC1F,MAAM,QAAQ,YAAY,IAAI,CAAC,CAAA,QAAS,mBAAmB,MAAM,MAAM,IAAI,EAAE,MAAM,KAAK;QACxF,IAAI,OAAO,MAAM,MAAM;IACzB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1111, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/its-fine/src/index.tsx"], "sourcesContent": ["import * as React from 'react'\r\nimport type <PERSON>actR<PERSON>onciler from 'react-reconciler'\r\n\r\n/**\r\n * An SSR-friendly useLayoutEffect.\r\n *\r\n * React currently throws a warning when using useLayoutEffect on the server.\r\n * To get around it, we can conditionally useEffect on the server (no-op) and\r\n * useLayoutEffect elsewhere.\r\n *\r\n * @see https://github.com/facebook/react/issues/14927\r\n */\r\nconst useIsomorphicLayoutEffect = /* @__PURE__ */ (() =>\r\n  typeof window !== 'undefined' && (window.document?.createElement || window.navigator?.product === 'ReactNative'))()\r\n  ? React.useLayoutEffect\r\n  : React.useEffect\r\n\r\n/**\r\n * Represents a react-internal Fiber node.\r\n */\r\nexport type Fiber<T = any> = Omit<ReactReconciler.Fiber, 'stateNode'> & { stateNode: T }\r\n\r\n/**\r\n * Represents a {@link Fiber} node selector for traversal.\r\n */\r\nexport type FiberSelector<T = any> = (\r\n  /** The current {@link Fiber} node. */\r\n  node: Fiber<T | null>,\r\n) => boolean | void\r\n\r\n/**\r\n * Traverses up or down a {@link Fiber}, return `true` to stop and select a node.\r\n */\r\nexport function traverseFiber<T = any>(\r\n  /** Input {@link Fiber} to traverse. */\r\n  fiber: Fiber | undefined,\r\n  /** Whether to ascend and walk up the tree. Will walk down if `false`. */\r\n  ascending: boolean,\r\n  /** A {@link Fiber} node selector, returns the first match when `true` is passed. */\r\n  selector: FiberSelector<T>,\r\n): Fiber<T> | undefined {\r\n  if (!fiber) return\r\n  if (selector(fiber) === true) return fiber\r\n\r\n  let child = ascending ? fiber.return : fiber.child\r\n  while (child) {\r\n    const match = traverseFiber(child, ascending, selector)\r\n    if (match) return match\r\n\r\n    child = ascending ? null : child.sibling\r\n  }\r\n}\r\n\r\n// In development, React will warn about using contexts between renderers.\r\n// Hide the warning because its-fine fixes this issue\r\n// https://github.com/facebook/react/pull/12779\r\nfunction wrapContext<T>(context: React.Context<T>): React.Context<T> {\r\n  try {\r\n    return Object.defineProperties(context, {\r\n      _currentRenderer: {\r\n        get() {\r\n          return null\r\n        },\r\n        set() {},\r\n      },\r\n      _currentRenderer2: {\r\n        get() {\r\n          return null\r\n        },\r\n        set() {},\r\n      },\r\n    })\r\n  } catch (_) {\r\n    return context\r\n  }\r\n}\r\n\r\nconst FiberContext = /* @__PURE__ */ wrapContext(/* @__PURE__ */ React.createContext<Fiber>(null!))\r\n\r\n/**\r\n * A react-internal {@link Fiber} provider. This component binds React children to the React Fiber tree. Call its-fine hooks within this.\r\n */\r\nexport class FiberProvider extends React.Component<{ children?: React.ReactNode }> {\r\n  private _reactInternals!: Fiber\r\n\r\n  render() {\r\n    return <FiberContext.Provider value={this._reactInternals}>{this.props.children}</FiberContext.Provider>\r\n  }\r\n}\r\n\r\n/**\r\n * Returns the current react-internal {@link Fiber}. This is an implementation detail of [react-reconciler](https://github.com/facebook/react/tree/main/packages/react-reconciler).\r\n */\r\nexport function useFiber(): Fiber<null> | undefined {\r\n  const root = React.useContext(FiberContext)\r\n  if (root === null) throw new Error('its-fine: useFiber must be called within a <FiberProvider />!')\r\n\r\n  const id = React.useId()\r\n  const fiber = React.useMemo(() => {\r\n    for (const maybeFiber of [root, root?.alternate]) {\r\n      if (!maybeFiber) continue\r\n      const fiber = traverseFiber<null>(maybeFiber, false, (node) => {\r\n        let state = node.memoizedState\r\n        while (state) {\r\n          if (state.memoizedState === id) return true\r\n          state = state.next\r\n        }\r\n      })\r\n      if (fiber) return fiber\r\n    }\r\n  }, [root, id])\r\n\r\n  return fiber\r\n}\r\n\r\n/**\r\n * Represents a react-reconciler container instance.\r\n */\r\nexport interface ContainerInstance<T = any> {\r\n  containerInfo: T\r\n}\r\n\r\n/**\r\n * Returns the current react-reconciler container info passed to {@link ReactReconciler.Reconciler.createContainer}.\r\n *\r\n * In react-dom, a container will point to the root DOM element; in react-three-fiber, it will point to the root Zustand store.\r\n */\r\nexport function useContainer<T = any>(): T | undefined {\r\n  const fiber = useFiber()\r\n  const root = React.useMemo(\r\n    () => traverseFiber<ContainerInstance<T>>(fiber, true, (node) => node.stateNode?.containerInfo != null),\r\n    [fiber],\r\n  )\r\n\r\n  return root?.stateNode.containerInfo\r\n}\r\n\r\n/**\r\n * Returns the nearest react-reconciler child instance or the node created from {@link ReactReconciler.HostConfig.createInstance}.\r\n *\r\n * In react-dom, this would be a DOM element; in react-three-fiber this would be an instance descriptor.\r\n */\r\nexport function useNearestChild<T = any>(\r\n  /** An optional element type to filter to. */\r\n  type?: keyof React.JSX.IntrinsicElements,\r\n): React.RefObject<T | undefined> {\r\n  const fiber = useFiber()\r\n  const childRef = React.useRef<T>(undefined)\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    childRef.current = traverseFiber<T>(\r\n      fiber,\r\n      false,\r\n      (node) => typeof node.type === 'string' && (type === undefined || node.type === type),\r\n    )?.stateNode\r\n  }, [fiber])\r\n\r\n  return childRef\r\n}\r\n\r\n/**\r\n * Returns the nearest react-reconciler parent instance or the node created from {@link ReactReconciler.HostConfig.createInstance}.\r\n *\r\n * In react-dom, this would be a DOM element; in react-three-fiber this would be an instance descriptor.\r\n */\r\nexport function useNearestParent<T = any>(\r\n  /** An optional element type to filter to. */\r\n  type?: keyof React.JSX.IntrinsicElements,\r\n): React.RefObject<T | undefined> {\r\n  const fiber = useFiber()\r\n  const parentRef = React.useRef<T>(undefined)\r\n\r\n  useIsomorphicLayoutEffect(() => {\r\n    parentRef.current = traverseFiber<T>(\r\n      fiber,\r\n      true,\r\n      (node) => typeof node.type === 'string' && (type === undefined || node.type === type),\r\n    )?.stateNode\r\n  }, [fiber])\r\n\r\n  return parentRef\r\n}\r\n\r\nexport type ContextMap = Map<React.Context<any>, any> & {\r\n  get<T>(context: React.Context<T>): T | undefined\r\n}\r\n\r\nconst REACT_CONTEXT_TYPE = Symbol.for('react.context')\r\n\r\nconst isContext = <T,>(type: unknown): type is React.Context<T> =>\r\n  type !== null && typeof type === 'object' && '$$typeof' in type && type.$$typeof === REACT_CONTEXT_TYPE\r\n\r\n/**\r\n * Returns a map of all contexts and their values.\r\n */\r\nexport function useContextMap(): ContextMap {\r\n  const fiber = useFiber()\r\n  const [contextMap] = React.useState(() => new Map<React.Context<any>, any>())\r\n\r\n  // Collect live context\r\n  contextMap.clear()\r\n  let node = fiber\r\n  while (node) {\r\n    const context = node.type\r\n    if (isContext(context) && context !== FiberContext && !contextMap.has(context)) {\r\n      contextMap.set(context, React.use(wrapContext(context)))\r\n    }\r\n\r\n    node = node.return!\r\n  }\r\n\r\n  return contextMap\r\n}\r\n\r\n/**\r\n * Represents a react-context bridge provider component.\r\n */\r\nexport type ContextBridge = React.FC<React.PropsWithChildren<{}>>\r\n\r\n/**\r\n * React Context currently cannot be shared across [React renderers](https://reactjs.org/docs/codebase-overview.html#renderers) but explicitly forwarded between providers (see [react#17275](https://github.com/facebook/react/issues/17275)). This hook returns a {@link ContextBridge} of live context providers to pierce Context across renderers.\r\n *\r\n * Pass {@link ContextBridge} as a component to a secondary renderer to enable context-sharing within its children.\r\n */\r\nexport function useContextBridge(): ContextBridge {\r\n  const contextMap = useContextMap()\r\n\r\n  // Flatten context and their memoized values into a `ContextBridge` provider\r\n  return React.useMemo(\r\n    () =>\r\n      Array.from(contextMap.keys()).reduce(\r\n        (Prev, context) => (props) =>\r\n          (\r\n            <Prev>\r\n              <context.Provider {...props} value={contextMap.get(context)} />\r\n            </Prev>\r\n          ),\r\n        (props) => <FiberProvider {...props} />,\r\n      ),\r\n    [contextMap],\r\n  )\r\n}\r\n"], "names": ["useIsomorphicLayoutEffect", "_a", "_b", "React", "traverseFiber", "fiber", "ascending", "selector", "child", "match", "wrapContext", "context", "_", "FiberContext", "FiberProvider", "useFiber", "root", "id", "maybeFiber", "node", "state", "useContainer", "useNearestChild", "type", "childRef", "useNearestParent", "parentRef", "REACT_CONTEXT_TYPE", "isContext", "useContextMap", "contextMap", "useContextBridge", "Prev", "props"], "mappings": ";;;;;;;;;;;;AAYA,MAAMA,IAA6C,aAAA,GAAA,CAAA,MAAA;;IACjD,OAAA,OAAO,UAAW,eAAA,CAAA,CAAA,CAAgBC,IAAA,OAAO,QAAA,KAAP,OAAA,KAAA,IAAAA,EAAiB,aAAA,KAAA,CAAA,CAAiBC,IAAA,OAAO,SAAA,KAAP,OAAA,KAAA,IAAAA,EAAkB,OAAA,MAAY,aAAA;AAAA,CAAA,EAAA,0MAChGC,EAAM,gBAAA,yMACNA,EAAM,UAAA;AAkBM,SAAAC,EAEdC,CAAAA,EAEAC,CAAAA,EAEAC,CAAAA,EACsB;IACtB,IAAI,CAACF,EAAO,CAAA;IACZ,IAAIE,EAASF,CAAK,MAAM,CAAA,EAAa,CAAA,OAAAA;IAErC,IAAIG,IAAQF,IAAYD,EAAM,MAAA,GAASA,EAAM,KAAA;IAC7C,MAAOG,GAAO;QACZ,MAAMC,IAAQL,EAAcI,GAAOF,GAAWC,CAAQ;QACtD,IAAIE,EAAc,CAAA,OAAAA;QAEVD,IAAAF,IAAY,OAAOE,EAAM,OAAA;IAAA;AAErC;AAKA,SAASE,EAAeC,CAAAA,EAA6C;IAC/D,IAAA;QACK,OAAA,OAAO,gBAAA,CAAiBA,GAAS;YACtC,kBAAkB;gBAChB,MAAM;oBACG,OAAA;gBACT;gBACA,MAAM,EAAA;YACR;YACA,mBAAmB;gBACjB,MAAM;oBACG,OAAA;gBACT;gBACA,MAAM,EAAA;YAAC;QACT,CACD;IAAA,EAAA,OACMC,GAAG;QACH,OAAAD;IAAA;AAEX;AAEA,MAAME,IAA+B,aAAA,GAAAH,EAAkC,aAAA,6MAAAP,EAAA,cAAA,EAAqB,IAAK,CAAC;AAKrF,MAAAW,gNAAsBX,EAAM,UAAA,CAA0C;IAGjF,SAAS;QACA,OAAA,aAAA,6MAAAA,EAAA,cAAA,EAACU,EAAa,QAAA,EAAb;YAAsB,OAAO,IAAA,CAAK,eAAA;QAAA,GAAkB,IAAA,CAAK,KAAA,CAAM,QAAS;IAAA;AAEpF;AAKO,SAASE,IAAoC;IAC5C,MAAAC,KAAOb,EAAM,oNAAA,EAAWU,CAAY;IAC1C,IAAIG,MAAS,KAAY,CAAA,MAAA,IAAI,MAAM,+DAA+D;IAE5F,MAAAC,8MAAKd,EAAM,MAAA,CAAM;IAehB,WAdOA,EAAM,8MAAA,EAAQ,MAAM;QAChC,KAAA,MAAWe,KAAc;YAACF;YAAMA,KAAA,OAAA,KAAA,IAAAA,EAAM,SAAS;SAAA,CAAG;YAChD,IAAI,CAACE,EAAY,CAAA;YACjB,MAAMb,IAAQD,EAAoBc,GAAY,CAAA,GAAO,CAACC,MAAS;gBAC7D,IAAIC,IAAQD,EAAK,aAAA;gBACjB,MAAOC,GAAO;oBACR,IAAAA,EAAM,aAAA,KAAkBH,EAAW,CAAA,OAAA,CAAA;oBACvCG,IAAQA,EAAM,IAAA;gBAAA;YAChB,CACD;YACD,IAAIf,EAAcA,CAAAA,OAAAA;QAAA;IACpB,GACC;QAACW;QAAMC,CAAE;KAAC;AAGf;AAcO,SAASI,IAAuC;IACrD,MAAMhB,IAAQU,EAAS,GACjBC,8MAAOb,EAAM,QAAA,EACjB,IAAMC,EAAoCC,GAAO,CAAA,GAAM,CAACc,MAAS;;YAAA,OAAA,CAAA,CAAAlB,IAAAkB,EAAK,SAAA,KAAL,OAAA,KAAA,IAAAlB,EAAgB,aAAA,KAAiB;QAAA,CAAI,GACtG;QAACI,CAAK;KAAA;IAGR,OAAOW,KAAA,OAAA,KAAA,IAAAA,EAAM,SAAA,CAAU,aAAA;AACzB;AAOO,SAASM,EAEdC,CAAAA,EACgC;IAChC,MAAMlB,IAAQU,EAAS,GACjBS,8MAAWrB,EAAM,OAAA,EAAU,KAAA,CAAS;IAE1C,OAAAH,EAA0B,MAAM;;QAC9BwB,EAAS,OAAA,GAAA,CAAUvB,IAAAG,EACjBC,GACA,CAAA,GACA,CAACc,IAAS,OAAOA,EAAK,IAAA,IAAS,YAAA,CAAaI,MAAS,KAAA,KAAaJ,EAAK,IAAA,KAASI,CAAAA,EAAA,KAH/D,OAAA,KAAA,IAAAtB,EAIhB,SAAA;IAAA,GACF;QAACI,CAAK;KAAC,GAEHmB;AACT;AAOO,SAASC,EAEdF,CAAAA,EACgC;IAChC,MAAMlB,IAAQU,EAAS,GACjBW,8MAAYvB,EAAM,OAAA,EAAU,KAAA,CAAS;IAE3C,OAAAH,EAA0B,MAAM;;QAC9B0B,EAAU,OAAA,GAAA,CAAUzB,IAAAG,EAClBC,GACA,CAAA,GACA,CAACc,IAAS,OAAOA,EAAK,IAAA,IAAS,YAAA,CAAaI,MAAS,KAAA,KAAaJ,EAAK,IAAA,KAASI,CAAAA,EAAA,KAH9D,OAAA,KAAA,IAAAtB,EAIjB,SAAA;IAAA,GACF;QAACI,CAAK;KAAC,GAEHqB;AACT;AAMA,MAAMC,IAAqB,OAAO,GAAA,CAAI,eAAe,GAE/CC,IAAY,CAAKL,IACrBA,MAAS,QAAQ,OAAOA,KAAS,YAAY,cAAcA,KAAQA,EAAK,QAAA,KAAaI;AAKhF,SAASE,IAA4B;IAC1C,MAAMxB,IAAQU,EAAS,GACjB,CAACe,CAAU,CAAA,6MAAI3B,EAAM,SAAA,EAAS,IAAM,aAAA,GAAA,IAAI,KAA8B;IAG5E2B,EAAW,KAAA,CAAM;IACjB,IAAIX,IAAOd;IACX,MAAOc,GAAM;QACX,MAAMR,IAAUQ,EAAK,IAAA;QACjBS,EAAUjB,CAAO,KAAKA,MAAYE,KAAgB,CAACiB,EAAW,GAAA,CAAInB,CAAO,KAC3EmB,EAAW,GAAA,CAAInB,6MAASR,EAAM,IAAA,EAAIO,EAAYC,CAAO,CAAC,CAAC,GAGzDQ,IAAOA,EAAK,MAAA;IAAA;IAGP,OAAAW;AACT;AAYO,SAASC,IAAkC;IAChD,MAAMD,IAAaD,EAAc;IAGjC,iNAAO1B,EAAM,QAAA,EACX,IACE,MAAM,IAAA,CAAK2B,EAAW,IAAA,CAAA,CAAM,EAAE,MAAA,CAC5B,CAACE,GAAMrB,IAAY,CAACsB,IAEhB,aAAA,6MAAA9B,EAAA,cAAA,EAAC6B,GAAAA,MACE,aAAA,6MAAA7B,EAAA,cAAA,EAAAQ,EAAQ,QAAA,EAAR;oBAAkB,GAAGsB,CAAAA;oBAAO,OAAOH,EAAW,GAAA,CAAInB,CAAO;gBAAA,CAAG,CAC/D,GAEJ,CAACsB,IAAW,aAAA,6MAAA9B,EAAA,cAAA,EAAAW,GAAA;gBAAe,GAAGmB,CAAAA;YAAO,CAAA,IAEzC;QAACH,CAAU;KAAA;AAEf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1246, "column": 0}, "map": {"version": 3, "file": "index.js", "sources": ["../src/index.ts"], "names": ["createDebounce", "callback", "ms", "timeoutId", "args", "useMeasure", "debounce", "scroll", "polyfill", "offsetSize", "ResizeObserver", "bounds", "set", "useState", "state", "useRef", "scrollDebounce", "resizeDebounce", "mounted", "useEffect", "forceRefresh", "resizeChange", "scrollChange", "useMemo", "left", "top", "width", "height", "bottom", "right", "x", "y", "size", "areBoundsEqual", "removeListeners", "element", "addListeners", "scrollContainer", "ref", "node", "findScrollContainers", "useOnWindowScroll", "useOnWindowResize", "onWindowResize", "cb", "onScroll", "enabled", "result", "overflow", "overflowX", "overflowY", "prop", "keys", "a", "b", "key"], "mappings": ";;;;;AAEA,SAASA,EAAmDC,CAAAA,EAAaC,CAAAA,CAAY;IAC/EC,IAAAA;IAEJ,OAAO,CAAA,GAAIC,IAA8B;QAChC,OAAA,YAAA,CAAaD,CAAS,GAC7BA,IAAY,OAAO,UAAA,CAAW,IAAMF,EAAS,GAAGG,CAAI,GAAGF,CAAE;IAC3D;AACF;AA0CA,SAASG,EACP,EAAE,UAAAC,CAAAA,EAAU,QAAAC,CAAAA,EAAQ,UAAAC,CAAAA,EAAU,YAAAC,CAAW,EAAA,GAAa;IAAE,UAAU;IAAG,QAAQ,CAAA;IAAO,YAAY,CAAA;AAAA,CAAA,CACxF;IACR,MAAMC,IACJF,KAAAA,CAAa,OAAO,UAAW,cAAc,KAAqB;IAAA,IAAM,OAAe,cAAA;IAEzF,IAAI,CAACE,GACH,MAAM,IAAI,MACR,gJACF;IAGF,MAAM,CAACC,GAAQC,CAAG,CAAA,GAAIC,qNAAAA,EAAuB;QAC3C,MAAM;QACN,KAAK;QACL,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,OAAO;QACP,GAAG;QACH,GAAG;IAAA,CACJ,GAGKC,QAAQC,+MAAAA,EAAc;QAC1B,SAAS;QACT,kBAAkB;QAClB,gBAAgB;QAChB,YAAYJ;QACZ,oBAAoB;IAAA,CACrB,GAGKK,IAAiBV,IAAY,OAAOA,KAAa,WAAWA,IAAWA,EAAS,MAAA,GAAU,MAC1FW,IAAiBX,IAAY,OAAOA,KAAa,WAAWA,IAAWA,EAAS,MAAA,GAAU,MAG1FY,uNAAUH,EAAO,CAAA,CAAK;0NAC5BI,EAAU,IAAA,CACRD,EAAQ,OAAA,GAAU,CAAA,GACX,IAAM,KAAA,CAAMA,EAAQ,OAAA,GAAU,CAAA,CAAA,CAAA,CACtC;IAGD,MAAM,CAACE,GAAcC,GAAcC,CAAY,CAAA,GAAIC,oNAAAA,EAAQ,IAAM;QAC/D,MAAMtB,IAAW,IAAM;YACjB,IAAA,CAACa,EAAM,OAAA,CAAQ,OAAA,EAAS;YACtB,MAAA,EAAE,MAAAU,CAAAA,EAAM,KAAAC,CAAAA,EAAK,OAAAC,CAAAA,EAAO,QAAAC,CAAAA,EAAQ,QAAAC,CAAAA,EAAQ,OAAAC,CAAAA,EAAO,GAAAC,CAAAA,EAAG,GAAAC,CAAE,EAAA,GACpDjB,EAAM,OAAA,CAAQ,OAAA,CAAQ,qBAAA,CAAsB,GAExCkB,IAAO;gBACX,MAAAR;gBACA,KAAAC;gBACA,OAAAC;gBACA,QAAAC;gBACA,QAAAC;gBACA,OAAAC;gBACA,GAAAC;gBACA,GAAAC;YACF;YAEIjB,EAAM,OAAA,CAAQ,OAAA,YAAmB,eAAeL,KAAAA,CAC7CuB,EAAA,MAAA,GAASlB,EAAM,OAAA,CAAQ,OAAA,CAAQ,YAAA,EAC/BkB,EAAA,KAAA,GAAQlB,EAAM,OAAA,CAAQ,OAAA,CAAQ,WAAA,GAGrC,OAAO,MAAA,CAAOkB,CAAI,GACdd,EAAQ,OAAA,IAAW,CAACe,EAAenB,EAAM,OAAA,CAAQ,UAAA,EAAYkB,CAAI,KAAGpB,EAAKE,EAAM,OAAA,CAAQ,UAAA,GAAakB,CAAK;QAC/G;QACO,OAAA;YACL/B;YACAgB,IAAiBjB,EAAeC,GAAUgB,CAAc,IAAIhB;YAC5De,IAAiBhB,EAAeC,GAAUe,CAAc,IAAIf,CAC9D;SAAA;IAAA,GACC;QAACW;QAAKH;QAAYO;QAAgBC,CAAc;KAAC;IAGpD,SAASiB,GAAkB;QACrBpB,EAAM,OAAA,CAAQ,gBAAA,IAAA,CACVA,EAAA,OAAA,CAAQ,gBAAA,CAAiB,OAAA,CAASqB,KAAYA,EAAQ,mBAAA,CAAoB,UAAUb,GAAc,CAAA,CAAI,CAAC,GAC7GR,EAAM,OAAA,CAAQ,gBAAA,GAAmB,IAAA,GAG/BA,EAAM,OAAA,CAAQ,cAAA,IAAA,CACVA,EAAA,OAAA,CAAQ,cAAA,CAAe,UAAA,CAAW,GACxCA,EAAM,OAAA,CAAQ,cAAA,GAAiB,IAAA,GAG7BA,EAAM,OAAA,CAAQ,kBAAA,IAAA,CACZ,iBAAiB,UAAU,yBAAyB,OAAO,WAAA,GAC7D,OAAO,WAAA,CAAY,mBAAA,CAAoB,UAAUA,EAAM,OAAA,CAAQ,kBAAkB,IACxE,yBAAyB,UAClC,OAAO,mBAAA,CAAoB,qBAAqBA,EAAM,OAAA,CAAQ,kBAAkB,CAAA;IAEpF;IAIF,SAASsB,GAAe;QACjBtB,EAAM,OAAA,CAAQ,OAAA,IAAA,CACnBA,EAAM,OAAA,CAAQ,cAAA,GAAiB,IAAIJ,EAAeY,CAAY,GAC9DR,EAAM,OAAA,CAAQ,cAAA,CAAgB,OAAA,CAAQA,EAAM,OAAA,CAAQ,OAAO,GACvDP,KAAUO,EAAM,OAAA,CAAQ,gBAAA,IAC1BA,EAAM,OAAA,CAAQ,gBAAA,CAAiB,OAAA,CAASuB,KACtCA,EAAgB,gBAAA,CAAiB,UAAUf,GAAc;gBAAE,SAAS,CAAA;gBAAM,SAAS,CAAA;YAAM,CAAA,CAC3F,GAIIR,EAAA,OAAA,CAAQ,kBAAA,GAAqB,IAAM;YAC1BQ,EAAA;QACf,GAGI,iBAAiB,UAAU,sBAAsB,OAAO,WAAA,GAC1D,OAAO,WAAA,CAAY,gBAAA,CAAiB,UAAUR,EAAM,OAAA,CAAQ,kBAAkB,IACrE,yBAAyB,UAElC,OAAO,gBAAA,CAAiB,qBAAqBA,EAAM,OAAA,CAAQ,kBAAkB,CAAA;IAC/E;IAIIwB,MAAAA,KAAOC,GAAkC;QACzC,CAACA,KAAQA,MAASzB,EAAM,OAAA,CAAQ,OAAA,IAAA,CACpBoB,EAAA,GAChBpB,EAAM,OAAA,CAAQ,OAAA,GAAUyB,GAClBzB,EAAA,OAAA,CAAQ,gBAAA,GAAmB0B,EAAqBD,CAAI,GAC7CH,EAAA,CAAA;IACf;IAGkBK,OAAAA,EAAAnB,GAAc,CAAQf,CAAAA,CAAO,GAC/CmC,EAAkBrB,CAAY,yNAG9BF,EAAU,IAAM;QACEe,EAAA,GACHE,EAAA;IACZ,GAAA;QAAC7B;QAAQe;QAAcD,CAAY;KAAC,IAG7BF,qNAAAA,EAAA,IAAMe,GAAiB,EAAE,GAC5B;QAACI;QAAK3B;QAAQS,CAAY;;AACnC;AAGA,SAASsB,EAAkBC,CAAAA,CAAwC;IACjExB,sNAAAA,EAAU,IAAM;QACd,MAAMyB,IAAKD;QACJ,OAAA,OAAA,gBAAA,CAAiB,UAAUC,CAAE,GAC7B,IAAM,KAAK,OAAO,mBAAA,CAAoB,UAAUA,CAAE;IAAA,GACxD;QAACD,CAAc;KAAC;AACrB;AACA,SAASF,EAAkBI,CAAAA,EAAsBC,CAAAA,CAAkB;0NACjE3B,EAAU,IAAM;QACd,IAAI2B,GAAS;YACX,MAAMF,IAAKC;YACJ,OAAA,OAAA,gBAAA,CAAiB,UAAUD,GAAI;gBAAE,SAAS,CAAA;gBAAM,SAAS,CAAA;YAAA,CAAM,GAC/D,IAAM,KAAK,OAAO,mBAAA,CAAoB,UAAUA,GAAI,CAAA,CAAI;QAAA;IACjE,GACC;QAACC;QAAUC,CAAO;KAAC;AACxB;AAGA,SAASN,EAAqBL,CAAAA,CAAsD;IAClF,MAAMY,IAA6B,CAAC,CAAA;IACpC,IAAI,CAACZ,KAAWA,MAAY,SAAS,IAAA,EAAaY,OAAAA;IAC5C,MAAA,EAAE,UAAAC,CAAAA,EAAU,WAAAC,CAAAA,EAAW,WAAAC,CAAc,EAAA,GAAA,OAAO,gBAAA,CAAiBf,CAAO;IACtE,OAAA;QAACa;QAAUC;QAAWC,CAAS;KAAA,CAAE,IAAA,CAAMC,KAASA,MAAS,UAAUA,MAAS,QAAQ,KAAGJ,EAAO,IAAA,CAAKZ,CAAO,GACvG,CAAC;WAAGY,EAAQ;WAAGP,EAAqBL,EAAQ,aAAa,CAAC;;AACnE;AAGA,MAAMiB,IAA+B;IAAC;IAAK;IAAK;IAAO;IAAU;IAAQ;IAAS;IAAS,QAAQ;CAAA,EAC7FnB,IAAiB,CAACoB,GAAiBC,IAA6BF,EAAK,KAAA,EAAOG,IAAQF,CAAAA,CAAEE,CAAG,CAAA,KAAMD,CAAAA,CAAEC,CAAG,CAAC", "debugId": null}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/%40babel/runtime/helpers/esm/extends.js"], "sourcesContent": ["function _extends() {\n  return _extends = Object.assign ? Object.assign.bind() : function (n) {\n    for (var e = 1; e < arguments.length; e++) {\n      var t = arguments[e];\n      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);\n    }\n    return n;\n  }, _extends.apply(null, arguments);\n}\nexport { _extends as default };"], "names": [], "mappings": ";;;AAAA,SAAS;IACP,OAAO,WAAW,uCAAgB,OAAO,MAAM,CAAC,IAAI,2CAMjD,SAAS,KAAK,CAAC,MAAM;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1400, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/%40react-three/drei/core/OrbitControls.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport { useThree, useFrame } from '@react-three/fiber';\nimport * as React from 'react';\nimport { OrbitControls as OrbitControls$1 } from 'three-stdlib';\n\nconst OrbitControls = /* @__PURE__ */React.forwardRef(({\n  makeDefault,\n  camera,\n  regress,\n  domElement,\n  enableDamping = true,\n  keyEvents = false,\n  onChange,\n  onStart,\n  onEnd,\n  ...restProps\n}, ref) => {\n  const invalidate = useThree(state => state.invalidate);\n  const defaultCamera = useThree(state => state.camera);\n  const gl = useThree(state => state.gl);\n  const events = useThree(state => state.events);\n  const setEvents = useThree(state => state.setEvents);\n  const set = useThree(state => state.set);\n  const get = useThree(state => state.get);\n  const performance = useThree(state => state.performance);\n  const explCamera = camera || defaultCamera;\n  const explDomElement = domElement || events.connected || gl.domElement;\n  const controls = React.useMemo(() => new OrbitControls$1(explCamera), [explCamera]);\n  useFrame(() => {\n    if (controls.enabled) controls.update();\n  }, -1);\n  React.useEffect(() => {\n    if (keyEvents) {\n      controls.connect(keyEvents === true ? explDomElement : keyEvents);\n    }\n    controls.connect(explDomElement);\n    return () => void controls.dispose();\n  }, [keyEvents, explDomElement, regress, controls, invalidate]);\n  React.useEffect(() => {\n    const callback = e => {\n      invalidate();\n      if (regress) performance.regress();\n      if (onChange) onChange(e);\n    };\n    const onStartCb = e => {\n      if (onStart) onStart(e);\n    };\n    const onEndCb = e => {\n      if (onEnd) onEnd(e);\n    };\n    controls.addEventListener('change', callback);\n    controls.addEventListener('start', onStartCb);\n    controls.addEventListener('end', onEndCb);\n    return () => {\n      controls.removeEventListener('start', onStartCb);\n      controls.removeEventListener('end', onEndCb);\n      controls.removeEventListener('change', callback);\n    };\n  }, [onChange, onStart, onEnd, controls, invalidate, setEvents]);\n  React.useEffect(() => {\n    if (makeDefault) {\n      const old = get().controls;\n      // @ts-ignore https://github.com/three-types/three-ts-types/pull/1398\n      set({\n        controls\n      });\n      return () => set({\n        controls: old\n      });\n    }\n  }, [makeDefault, controls]);\n  return /*#__PURE__*/React.createElement(\"primitive\", _extends({\n    ref: ref,\n    object: controls,\n    enableDamping: enableDamping\n  }, restProps));\n});\n\nexport { OrbitControls };\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AACA;;;;;AAEA,MAAM,gBAAgB,aAAa,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EACrD,WAAW,EACX,MAAM,EACN,OAAO,EACP,UAAU,EACV,gBAAgB,IAAI,EACpB,YAAY,KAAK,EACjB,QAAQ,EACR,OAAO,EACP,KAAK,EACL,GAAG,WACJ,EAAE;IACD,MAAM,aAAa,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,UAAU;IACrD,MAAM,gBAAgB,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,MAAM;IACpD,MAAM,KAAK,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,EAAE;IACrC,MAAM,SAAS,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,MAAM;IAC7C,MAAM,YAAY,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,SAAS;IACnD,MAAM,MAAM,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,GAAG;IACvC,MAAM,MAAM,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,GAAG;IACvC,MAAM,cAAc,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,WAAW;IACvD,MAAM,aAAa,UAAU;IAC7B,MAAM,iBAAiB,cAAc,OAAO,SAAS,IAAI,GAAG,UAAU;IACtE,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM,IAAI,4JAAA,CAAA,gBAAe,CAAC,aAAa;QAAC;KAAW;IAClF,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;QACP,IAAI,SAAS,OAAO,EAAE,SAAS,MAAM;IACvC,GAAG,CAAC;IACJ,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,WAAW;YACb,SAAS,OAAO,CAAC,cAAc,OAAO,iBAAiB;QACzD;QACA,SAAS,OAAO,CAAC;QACjB,OAAO,IAAM,KAAK,SAAS,OAAO;IACpC,GAAG;QAAC;QAAW;QAAgB;QAAS;QAAU;KAAW;IAC7D,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,WAAW,CAAA;YACf;YACA,IAAI,SAAS,YAAY,OAAO;YAChC,IAAI,UAAU,SAAS;QACzB;QACA,MAAM,YAAY,CAAA;YAChB,IAAI,SAAS,QAAQ;QACvB;QACA,MAAM,UAAU,CAAA;YACd,IAAI,OAAO,MAAM;QACnB;QACA,SAAS,gBAAgB,CAAC,UAAU;QACpC,SAAS,gBAAgB,CAAC,SAAS;QACnC,SAAS,gBAAgB,CAAC,OAAO;QACjC,OAAO;YACL,SAAS,mBAAmB,CAAC,SAAS;YACtC,SAAS,mBAAmB,CAAC,OAAO;YACpC,SAAS,mBAAmB,CAAC,UAAU;QACzC;IACF,GAAG;QAAC;QAAU;QAAS;QAAO;QAAU;QAAY;KAAU;IAC9D,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,aAAa;YACf,MAAM,MAAM,MAAM,QAAQ;YAC1B,qEAAqE;YACrE,IAAI;gBACF;YACF;YACA,OAAO,IAAM,IAAI;oBACf,UAAU;gBACZ;QACF;IACF,GAAG;QAAC;QAAa;KAAS;IAC1B,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,aAAa,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;QAC5D,KAAK;QACL,QAAQ;QACR,eAAe;IACjB,GAAG;AACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1498, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/%40react-three/drei/helpers/environment-assets.js"], "sourcesContent": ["const presetsObj = {\n  apartment: 'lebombo_1k.hdr',\n  city: 'potsdamer_platz_1k.hdr',\n  dawn: 'kiara_1_dawn_1k.hdr',\n  forest: 'forest_slope_1k.hdr',\n  lobby: 'st_fagans_interior_1k.hdr',\n  night: 'dikhololo_night_1k.hdr',\n  park: 'rooitou_park_1k.hdr',\n  studio: 'studio_small_03_1k.hdr',\n  sunset: 'venice_sunset_1k.hdr',\n  warehouse: 'empty_warehouse_01_1k.hdr'\n};\n\nexport { presetsObj };\n"], "names": [], "mappings": ";;;AAAA,MAAM,aAAa;IACjB,WAAW;IACX,MAAM;IACN,MAAM;IACN,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,WAAW;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1520, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/%40react-three/drei/core/useEnvironment.js"], "sourcesContent": ["import { useThree, useLoader } from '@react-three/fiber';\nimport { CubeReflectionMapping, EquirectangularReflectionMapping, CubeTextureLoader } from 'three';\nimport { RGBELoader, EXRLoader } from 'three-stdlib';\nimport { HDRJPGLoader, GainMapLoader } from '@monogrid/gainmap-js';\nimport { presetsObj } from '../helpers/environment-assets.js';\nimport { useLayoutEffect } from 'react';\n\nconst CUBEMAP_ROOT = 'https://raw.githack.com/pmndrs/drei-assets/456060a26bbeb8fdf79326f224b6d99b8bcce736/hdri/';\nconst isArray = arr => Array.isArray(arr);\nconst defaultFiles = ['/px.png', '/nx.png', '/py.png', '/ny.png', '/pz.png', '/nz.png'];\nfunction useEnvironment({\n  files = defaultFiles,\n  path = '',\n  preset = undefined,\n  colorSpace = undefined,\n  extensions\n} = {}) {\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n\n  // Everything else\n  const multiFile = isArray(files);\n  const {\n    extension,\n    isCubemap\n  } = getExtension(files);\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  const gl = useThree(state => state.gl);\n  useLayoutEffect(() => {\n    // Only required for gainmap\n    if (extension !== 'webp' && extension !== 'jpg' && extension !== 'jpeg') return;\n    function clearGainmapTexture() {\n      useLoader.clear(loader, multiFile ? [files] : files);\n    }\n    gl.domElement.addEventListener('webglcontextlost', clearGainmapTexture, {\n      once: true\n    });\n  }, [files, gl.domElement]);\n  const loaderResult = useLoader(loader, multiFile ? [files] : files, loader => {\n    // Gainmap requires a renderer\n    if (extension === 'webp' || extension === 'jpg' || extension === 'jpeg') {\n      // @ts-expect-error\n      loader.setRenderer(gl);\n    }\n    loader.setPath == null || loader.setPath(path);\n    // @ts-expect-error\n    if (extensions) extensions(loader);\n  });\n  let texture = multiFile ?\n  // @ts-ignore\n  loaderResult[0] : loaderResult;\n  if (extension === 'jpg' || extension === 'jpeg' || extension === 'webp') {\n    var _renderTarget;\n    texture = (_renderTarget = texture.renderTarget) == null ? void 0 : _renderTarget.texture;\n  }\n  texture.mapping = isCubemap ? CubeReflectionMapping : EquirectangularReflectionMapping;\n  texture.colorSpace = colorSpace !== null && colorSpace !== void 0 ? colorSpace : isCubemap ? 'srgb' : 'srgb-linear';\n  return texture;\n}\nconst preloadDefaultOptions = {\n  files: defaultFiles,\n  path: '',\n  preset: undefined,\n  extensions: undefined\n};\nuseEnvironment.preload = preloadOptions => {\n  const options = {\n    ...preloadDefaultOptions,\n    ...preloadOptions\n  };\n  let {\n    files,\n    path = ''\n  } = options;\n  const {\n    preset,\n    extensions\n  } = options;\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n    path = CUBEMAP_ROOT;\n  }\n  const {\n    extension\n  } = getExtension(files);\n  if (extension === 'webp' || extension === 'jpg' || extension === 'jpeg') {\n    throw new Error('useEnvironment: Preloading gainmaps is not supported');\n  }\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  useLoader.preload(loader, isArray(files) ? [files] : files, loader => {\n    loader.setPath == null || loader.setPath(path);\n    // @ts-expect-error\n    if (extensions) extensions(loader);\n  });\n};\nconst clearDefaultOptins = {\n  files: defaultFiles,\n  preset: undefined\n};\nuseEnvironment.clear = clearOptions => {\n  const options = {\n    ...clearDefaultOptins,\n    ...clearOptions\n  };\n  let {\n    files\n  } = options;\n  const {\n    preset\n  } = options;\n  if (preset) {\n    validatePreset(preset);\n    files = presetsObj[preset];\n  }\n  const {\n    extension\n  } = getExtension(files);\n  const loader = getLoader(extension);\n  if (!loader) throw new Error('useEnvironment: Unrecognized file extension: ' + files);\n  useLoader.clear(loader, isArray(files) ? [files] : files);\n};\nfunction validatePreset(preset) {\n  if (!(preset in presetsObj)) throw new Error('Preset must be one of: ' + Object.keys(presetsObj).join(', '));\n}\nfunction getExtension(files) {\n  var _firstEntry$split$pop;\n  const isCubemap = isArray(files) && files.length === 6;\n  const isGainmap = isArray(files) && files.length === 3 && files.some(file => file.endsWith('json'));\n  const firstEntry = isArray(files) ? files[0] : files;\n\n  // Everything else\n  const extension = isCubemap ? 'cube' : isGainmap ? 'webp' : firstEntry.startsWith('data:application/exr') ? 'exr' : firstEntry.startsWith('data:application/hdr') ? 'hdr' : firstEntry.startsWith('data:image/jpeg') ? 'jpg' : (_firstEntry$split$pop = firstEntry.split('.').pop()) == null || (_firstEntry$split$pop = _firstEntry$split$pop.split('?')) == null || (_firstEntry$split$pop = _firstEntry$split$pop.shift()) == null ? void 0 : _firstEntry$split$pop.toLowerCase();\n  return {\n    extension,\n    isCubemap,\n    isGainmap\n  };\n}\nfunction getLoader(extension) {\n  const loader = extension === 'cube' ? CubeTextureLoader : extension === 'hdr' ? RGBELoader : extension === 'exr' ? EXRLoader : extension === 'jpg' || extension === 'jpeg' ? HDRJPGLoader : extension === 'webp' ? GainMapLoader : null;\n  return loader;\n}\n\nexport { useEnvironment };\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,eAAe;AACrB,MAAM,UAAU,CAAA,MAAO,MAAM,OAAO,CAAC;AACrC,MAAM,eAAe;IAAC;IAAW;IAAW;IAAW;IAAW;IAAW;CAAU;AACvF,SAAS,eAAe,EACtB,QAAQ,YAAY,EACpB,OAAO,EAAE,EACT,SAAS,SAAS,EAClB,aAAa,SAAS,EACtB,UAAU,EACX,GAAG,CAAC,CAAC;IACJ,IAAI,QAAQ;QACV,eAAe;QACf,QAAQ,4KAAA,CAAA,aAAU,CAAC,OAAO;QAC1B,OAAO;IACT;IAEA,kBAAkB;IAClB,MAAM,YAAY,QAAQ;IAC1B,MAAM,EACJ,SAAS,EACT,SAAS,EACV,GAAG,aAAa;IACjB,MAAM,SAAS,UAAU;IACzB,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM,kDAAkD;IAC/E,MAAM,KAAK,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,EAAE;IACrC,CAAA,GAAA,qMAAA,CAAA,kBAAe,AAAD,EAAE;QACd,4BAA4B;QAC5B,IAAI,cAAc,UAAU,cAAc,SAAS,cAAc,QAAQ;QACzE,SAAS;YACP,gNAAA,CAAA,YAAS,CAAC,KAAK,CAAC,QAAQ,YAAY;gBAAC;aAAM,GAAG;QAChD;QACA,GAAG,UAAU,CAAC,gBAAgB,CAAC,oBAAoB,qBAAqB;YACtE,MAAM;QACR;IACF,GAAG;QAAC;QAAO,GAAG,UAAU;KAAC;IACzB,MAAM,eAAe,CAAA,GAAA,gNAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,YAAY;QAAC;KAAM,GAAG,OAAO,CAAA;QAClE,8BAA8B;QAC9B,IAAI,cAAc,UAAU,cAAc,SAAS,cAAc,QAAQ;YACvE,mBAAmB;YACnB,OAAO,WAAW,CAAC;QACrB;QACA,OAAO,OAAO,IAAI,QAAQ,OAAO,OAAO,CAAC;QACzC,mBAAmB;QACnB,IAAI,YAAY,WAAW;IAC7B;IACA,IAAI,UAAU,YACd,aAAa;IACb,YAAY,CAAC,EAAE,GAAG;IAClB,IAAI,cAAc,SAAS,cAAc,UAAU,cAAc,QAAQ;QACvE,IAAI;QACJ,UAAU,CAAC,gBAAgB,QAAQ,YAAY,KAAK,OAAO,KAAK,IAAI,cAAc,OAAO;IAC3F;IACA,QAAQ,OAAO,GAAG,YAAY,+IAAA,CAAA,wBAAqB,GAAG,+IAAA,CAAA,mCAAgC;IACtF,QAAQ,UAAU,GAAG,eAAe,QAAQ,eAAe,KAAK,IAAI,aAAa,YAAY,SAAS;IACtG,OAAO;AACT;AACA,MAAM,wBAAwB;IAC5B,OAAO;IACP,MAAM;IACN,QAAQ;IACR,YAAY;AACd;AACA,eAAe,OAAO,GAAG,CAAA;IACvB,MAAM,UAAU;QACd,GAAG,qBAAqB;QACxB,GAAG,cAAc;IACnB;IACA,IAAI,EACF,KAAK,EACL,OAAO,EAAE,EACV,GAAG;IACJ,MAAM,EACJ,MAAM,EACN,UAAU,EACX,GAAG;IACJ,IAAI,QAAQ;QACV,eAAe;QACf,QAAQ,4KAAA,CAAA,aAAU,CAAC,OAAO;QAC1B,OAAO;IACT;IACA,MAAM,EACJ,SAAS,EACV,GAAG,aAAa;IACjB,IAAI,cAAc,UAAU,cAAc,SAAS,cAAc,QAAQ;QACvE,MAAM,IAAI,MAAM;IAClB;IACA,MAAM,SAAS,UAAU;IACzB,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM,kDAAkD;IAC/E,gNAAA,CAAA,YAAS,CAAC,OAAO,CAAC,QAAQ,QAAQ,SAAS;QAAC;KAAM,GAAG,OAAO,CAAA;QAC1D,OAAO,OAAO,IAAI,QAAQ,OAAO,OAAO,CAAC;QACzC,mBAAmB;QACnB,IAAI,YAAY,WAAW;IAC7B;AACF;AACA,MAAM,qBAAqB;IACzB,OAAO;IACP,QAAQ;AACV;AACA,eAAe,KAAK,GAAG,CAAA;IACrB,MAAM,UAAU;QACd,GAAG,kBAAkB;QACrB,GAAG,YAAY;IACjB;IACA,IAAI,EACF,KAAK,EACN,GAAG;IACJ,MAAM,EACJ,MAAM,EACP,GAAG;IACJ,IAAI,QAAQ;QACV,eAAe;QACf,QAAQ,4KAAA,CAAA,aAAU,CAAC,OAAO;IAC5B;IACA,MAAM,EACJ,SAAS,EACV,GAAG,aAAa;IACjB,MAAM,SAAS,UAAU;IACzB,IAAI,CAAC,QAAQ,MAAM,IAAI,MAAM,kDAAkD;IAC/E,gNAAA,CAAA,YAAS,CAAC,KAAK,CAAC,QAAQ,QAAQ,SAAS;QAAC;KAAM,GAAG;AACrD;AACA,SAAS,eAAe,MAAM;IAC5B,IAAI,CAAC,CAAC,UAAU,4KAAA,CAAA,aAAU,GAAG,MAAM,IAAI,MAAM,4BAA4B,OAAO,IAAI,CAAC,4KAAA,CAAA,aAAU,EAAE,IAAI,CAAC;AACxG;AACA,SAAS,aAAa,KAAK;IACzB,IAAI;IACJ,MAAM,YAAY,QAAQ,UAAU,MAAM,MAAM,KAAK;IACrD,MAAM,YAAY,QAAQ,UAAU,MAAM,MAAM,KAAK,KAAK,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC;IAC3F,MAAM,aAAa,QAAQ,SAAS,KAAK,CAAC,EAAE,GAAG;IAE/C,kBAAkB;IAClB,MAAM,YAAY,YAAY,SAAS,YAAY,SAAS,WAAW,UAAU,CAAC,0BAA0B,QAAQ,WAAW,UAAU,CAAC,0BAA0B,QAAQ,WAAW,UAAU,CAAC,qBAAqB,QAAQ,CAAC,wBAAwB,WAAW,KAAK,CAAC,KAAK,GAAG,EAAE,KAAK,QAAQ,CAAC,wBAAwB,sBAAsB,KAAK,CAAC,IAAI,KAAK,QAAQ,CAAC,wBAAwB,sBAAsB,KAAK,EAAE,KAAK,OAAO,KAAK,IAAI,sBAAsB,WAAW;IACld,OAAO;QACL;QACA;QACA;IACF;AACF;AACA,SAAS,UAAU,SAAS;IAC1B,MAAM,SAAS,cAAc,SAAS,+IAAA,CAAA,oBAAiB,GAAG,cAAc,QAAQ,wJAAA,CAAA,aAAU,GAAG,cAAc,QAAQ,uJAAA,CAAA,YAAS,GAAG,cAAc,SAAS,cAAc,SAAS,6KAAA,CAAA,eAAY,GAAG,cAAc,SAAS,6KAAA,CAAA,gBAAa,GAAG;IACnO,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1677, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/%40react-three/drei/core/Environment.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport { useThree, use<PERSON>rame, createPortal, applyProps, extend } from '@react-three/fiber';\nimport { Scene, WebGLCubeRenderTarget, HalfFloatType } from 'three';\nimport { GroundProjectedEnv } from 'three-stdlib';\nimport { useEnvironment } from './useEnvironment.js';\n\nconst isRef = obj => obj.current && obj.current.isScene;\nconst resolveScene = scene => isRef(scene) ? scene.current : scene;\nfunction setEnvProps(background, scene, defaultScene, texture, sceneProps = {}) {\n  var _target$backgroundRot, _target$backgroundRot2, _target$environmentRo, _target$environmentRo2;\n  // defaults\n  sceneProps = {\n    backgroundBlurriness: 0,\n    backgroundIntensity: 1,\n    backgroundRotation: [0, 0, 0],\n    environmentIntensity: 1,\n    environmentRotation: [0, 0, 0],\n    ...sceneProps\n  };\n  const target = resolveScene(scene || defaultScene);\n  const oldbg = target.background;\n  const oldenv = target.environment;\n  const oldSceneProps = {\n    // @ts-ignore\n    backgroundBlurriness: target.backgroundBlurriness,\n    // @ts-ignore\n    backgroundIntensity: target.backgroundIntensity,\n    // @ts-ignore\n    backgroundRotation: (_target$backgroundRot = (_target$backgroundRot2 = target.backgroundRotation) == null || _target$backgroundRot2.clone == null ? void 0 : _target$backgroundRot2.clone()) !== null && _target$backgroundRot !== void 0 ? _target$backgroundRot : [0, 0, 0],\n    // @ts-ignore\n    environmentIntensity: target.environmentIntensity,\n    // @ts-ignore\n    environmentRotation: (_target$environmentRo = (_target$environmentRo2 = target.environmentRotation) == null || _target$environmentRo2.clone == null ? void 0 : _target$environmentRo2.clone()) !== null && _target$environmentRo !== void 0 ? _target$environmentRo : [0, 0, 0]\n  };\n  if (background !== 'only') target.environment = texture;\n  if (background) target.background = texture;\n  applyProps(target, sceneProps);\n  return () => {\n    if (background !== 'only') target.environment = oldenv;\n    if (background) target.background = oldbg;\n    applyProps(target, oldSceneProps);\n  };\n}\nfunction EnvironmentMap({\n  scene,\n  background = false,\n  map,\n  ...config\n}) {\n  const defaultScene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    if (map) return setEnvProps(background, scene, defaultScene, map, config);\n  });\n  return null;\n}\nfunction EnvironmentCube({\n  background = false,\n  scene,\n  blur,\n  backgroundBlurriness,\n  backgroundIntensity,\n  backgroundRotation,\n  environmentIntensity,\n  environmentRotation,\n  ...rest\n}) {\n  const texture = useEnvironment(rest);\n  const defaultScene = useThree(state => state.scene);\n  React.useLayoutEffect(() => {\n    return setEnvProps(background, scene, defaultScene, texture, {\n      backgroundBlurriness: blur !== null && blur !== void 0 ? blur : backgroundBlurriness,\n      backgroundIntensity,\n      backgroundRotation,\n      environmentIntensity,\n      environmentRotation\n    });\n  });\n  React.useEffect(() => {\n    return () => {\n      texture.dispose();\n    };\n  }, [texture]);\n  return null;\n}\nfunction EnvironmentPortal({\n  children,\n  near = 0.1,\n  far = 1000,\n  resolution = 256,\n  frames = 1,\n  map,\n  background = false,\n  blur,\n  backgroundBlurriness,\n  backgroundIntensity,\n  backgroundRotation,\n  environmentIntensity,\n  environmentRotation,\n  scene,\n  files,\n  path,\n  preset = undefined,\n  extensions\n}) {\n  const gl = useThree(state => state.gl);\n  const defaultScene = useThree(state => state.scene);\n  const camera = React.useRef(null);\n  const [virtualScene] = React.useState(() => new Scene());\n  const fbo = React.useMemo(() => {\n    const fbo = new WebGLCubeRenderTarget(resolution);\n    fbo.texture.type = HalfFloatType;\n    return fbo;\n  }, [resolution]);\n  React.useEffect(() => {\n    return () => {\n      fbo.dispose();\n    };\n  }, [fbo]);\n  React.useLayoutEffect(() => {\n    if (frames === 1) {\n      const autoClear = gl.autoClear;\n      gl.autoClear = true;\n      camera.current.update(gl, virtualScene);\n      gl.autoClear = autoClear;\n    }\n    return setEnvProps(background, scene, defaultScene, fbo.texture, {\n      backgroundBlurriness: blur !== null && blur !== void 0 ? blur : backgroundBlurriness,\n      backgroundIntensity,\n      backgroundRotation,\n      environmentIntensity,\n      environmentRotation\n    });\n  }, [children, virtualScene, fbo.texture, scene, defaultScene, background, frames, gl]);\n  let count = 1;\n  useFrame(() => {\n    if (frames === Infinity || count < frames) {\n      const autoClear = gl.autoClear;\n      gl.autoClear = true;\n      camera.current.update(gl, virtualScene);\n      gl.autoClear = autoClear;\n      count++;\n    }\n  });\n  return /*#__PURE__*/React.createElement(React.Fragment, null, createPortal(/*#__PURE__*/React.createElement(React.Fragment, null, children, /*#__PURE__*/React.createElement(\"cubeCamera\", {\n    ref: camera,\n    args: [near, far, fbo]\n  }), files || preset ? /*#__PURE__*/React.createElement(EnvironmentCube, {\n    background: true,\n    files: files,\n    preset: preset,\n    path: path,\n    extensions: extensions\n  }) : map ? /*#__PURE__*/React.createElement(EnvironmentMap, {\n    background: true,\n    map: map,\n    extensions: extensions\n  }) : null), virtualScene));\n}\nfunction EnvironmentGround(props) {\n  var _props$ground, _props$ground2, _scale, _props$ground3;\n  const textureDefault = useEnvironment(props);\n  const texture = props.map || textureDefault;\n  React.useMemo(() => extend({\n    GroundProjectedEnvImpl: GroundProjectedEnv\n  }), []);\n  React.useEffect(() => {\n    return () => {\n      textureDefault.dispose();\n    };\n  }, [textureDefault]);\n  const args = React.useMemo(() => [texture], [texture]);\n  const height = (_props$ground = props.ground) == null ? void 0 : _props$ground.height;\n  const radius = (_props$ground2 = props.ground) == null ? void 0 : _props$ground2.radius;\n  const scale = (_scale = (_props$ground3 = props.ground) == null ? void 0 : _props$ground3.scale) !== null && _scale !== void 0 ? _scale : 1000;\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(EnvironmentMap, _extends({}, props, {\n    map: texture\n  })), /*#__PURE__*/React.createElement(\"groundProjectedEnvImpl\", {\n    args: args,\n    scale: scale,\n    height: height,\n    radius: radius\n  }));\n}\nfunction Environment(props) {\n  return props.ground ? /*#__PURE__*/React.createElement(EnvironmentGround, props) : props.map ? /*#__PURE__*/React.createElement(EnvironmentMap, props) : props.children ? /*#__PURE__*/React.createElement(EnvironmentPortal, props) : /*#__PURE__*/React.createElement(EnvironmentCube, props);\n}\n\nexport { Environment, EnvironmentCube, EnvironmentMap, EnvironmentPortal };\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,QAAQ,CAAA,MAAO,IAAI,OAAO,IAAI,IAAI,OAAO,CAAC,OAAO;AACvD,MAAM,eAAe,CAAA,QAAS,MAAM,SAAS,MAAM,OAAO,GAAG;AAC7D,SAAS,YAAY,UAAU,EAAE,KAAK,EAAE,YAAY,EAAE,OAAO,EAAE,aAAa,CAAC,CAAC;IAC5E,IAAI,uBAAuB,wBAAwB,uBAAuB;IAC1E,WAAW;IACX,aAAa;QACX,sBAAsB;QACtB,qBAAqB;QACrB,oBAAoB;YAAC;YAAG;YAAG;SAAE;QAC7B,sBAAsB;QACtB,qBAAqB;YAAC;YAAG;YAAG;SAAE;QAC9B,GAAG,UAAU;IACf;IACA,MAAM,SAAS,aAAa,SAAS;IACrC,MAAM,QAAQ,OAAO,UAAU;IAC/B,MAAM,SAAS,OAAO,WAAW;IACjC,MAAM,gBAAgB;QACpB,aAAa;QACb,sBAAsB,OAAO,oBAAoB;QACjD,aAAa;QACb,qBAAqB,OAAO,mBAAmB;QAC/C,aAAa;QACb,oBAAoB,CAAC,wBAAwB,CAAC,yBAAyB,OAAO,kBAAkB,KAAK,QAAQ,uBAAuB,KAAK,IAAI,OAAO,KAAK,IAAI,uBAAuB,KAAK,EAAE,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB;YAAC;YAAG;YAAG;SAAE;QAC7Q,aAAa;QACb,sBAAsB,OAAO,oBAAoB;QACjD,aAAa;QACb,qBAAqB,CAAC,wBAAwB,CAAC,yBAAyB,OAAO,mBAAmB,KAAK,QAAQ,uBAAuB,KAAK,IAAI,OAAO,KAAK,IAAI,uBAAuB,KAAK,EAAE,MAAM,QAAQ,0BAA0B,KAAK,IAAI,wBAAwB;YAAC;YAAG;YAAG;SAAE;IACjR;IACA,IAAI,eAAe,QAAQ,OAAO,WAAW,GAAG;IAChD,IAAI,YAAY,OAAO,UAAU,GAAG;IACpC,CAAA,GAAA,iNAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;IACnB,OAAO;QACL,IAAI,eAAe,QAAQ,OAAO,WAAW,GAAG;QAChD,IAAI,YAAY,OAAO,UAAU,GAAG;QACpC,CAAA,GAAA,iNAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;IACrB;AACF;AACA,SAAS,eAAe,EACtB,KAAK,EACL,aAAa,KAAK,EAClB,GAAG,EACH,GAAG,QACJ;IACC,MAAM,eAAe,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,KAAK;IAClD,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,IAAI,KAAK,OAAO,YAAY,YAAY,OAAO,cAAc,KAAK;IACpE;IACA,OAAO;AACT;AACA,SAAS,gBAAgB,EACvB,aAAa,KAAK,EAClB,KAAK,EACL,IAAI,EACJ,oBAAoB,EACpB,mBAAmB,EACnB,kBAAkB,EAClB,oBAAoB,EACpB,mBAAmB,EACnB,GAAG,MACJ;IACC,MAAM,UAAU,CAAA,GAAA,kKAAA,CAAA,iBAAc,AAAD,EAAE;IAC/B,MAAM,eAAe,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,KAAK;IAClD,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,OAAO,YAAY,YAAY,OAAO,cAAc,SAAS;YAC3D,sBAAsB,SAAS,QAAQ,SAAS,KAAK,IAAI,OAAO;YAChE;YACA;YACA;YACA;QACF;IACF;IACA,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,OAAO;YACL,QAAQ,OAAO;QACjB;IACF,GAAG;QAAC;KAAQ;IACZ,OAAO;AACT;AACA,SAAS,kBAAkB,EACzB,QAAQ,EACR,OAAO,GAAG,EACV,MAAM,IAAI,EACV,aAAa,GAAG,EAChB,SAAS,CAAC,EACV,GAAG,EACH,aAAa,KAAK,EAClB,IAAI,EACJ,oBAAoB,EACpB,mBAAmB,EACnB,kBAAkB,EAClB,oBAAoB,EACpB,mBAAmB,EACnB,KAAK,EACL,KAAK,EACL,IAAI,EACJ,SAAS,SAAS,EAClB,UAAU,EACX;IACC,MAAM,KAAK,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,EAAE;IACrC,MAAM,eAAe,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,QAAS,MAAM,KAAK;IAClD,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC5B,MAAM,CAAC,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,IAAM,IAAI,+IAAA,CAAA,QAAK;IACrD,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACxB,MAAM,MAAM,IAAI,+IAAA,CAAA,wBAAqB,CAAC;QACtC,IAAI,OAAO,CAAC,IAAI,GAAG,+IAAA,CAAA,gBAAa;QAChC,OAAO;IACT,GAAG;QAAC;KAAW;IACf,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,OAAO;YACL,IAAI,OAAO;QACb;IACF,GAAG;QAAC;KAAI;IACR,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,IAAI,WAAW,GAAG;YAChB,MAAM,YAAY,GAAG,SAAS;YAC9B,GAAG,SAAS,GAAG;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI;YAC1B,GAAG,SAAS,GAAG;QACjB;QACA,OAAO,YAAY,YAAY,OAAO,cAAc,IAAI,OAAO,EAAE;YAC/D,sBAAsB,SAAS,QAAQ,SAAS,KAAK,IAAI,OAAO;YAChE;YACA;YACA;YACA;QACF;IACF,GAAG;QAAC;QAAU;QAAc,IAAI,OAAO;QAAE;QAAO;QAAc;QAAY;QAAQ;KAAG;IACrF,IAAI,QAAQ;IACZ,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE;QACP,IAAI,WAAW,YAAY,QAAQ,QAAQ;YACzC,MAAM,YAAY,GAAG,SAAS;YAC9B,GAAG,SAAS,GAAG;YACf,OAAO,OAAO,CAAC,MAAM,CAAC,IAAI;YAC1B,GAAG,SAAS,GAAG;YACf;QACF;IACF;IACA,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,qMAAA,CAAA,WAAc,EAAE,MAAM,CAAA,GAAA,mNAAA,CAAA,eAAY,AAAD,EAAE,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,qMAAA,CAAA,WAAc,EAAE,MAAM,UAAU,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,cAAc;QACzL,KAAK;QACL,MAAM;YAAC;YAAM;YAAK;SAAI;IACxB,IAAI,SAAS,SAAS,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,iBAAiB;QACtE,YAAY;QACZ,OAAO;QACP,QAAQ;QACR,MAAM;QACN,YAAY;IACd,KAAK,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,gBAAgB;QAC1D,YAAY;QACZ,KAAK;QACL,YAAY;IACd,KAAK,OAAO;AACd;AACA,SAAS,kBAAkB,KAAK;IAC9B,IAAI,eAAe,gBAAgB,QAAQ;IAC3C,MAAM,iBAAiB,CAAA,GAAA,kKAAA,CAAA,iBAAc,AAAD,EAAE;IACtC,MAAM,UAAU,MAAM,GAAG,IAAI;IAC7B,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM,CAAA,GAAA,6MAAA,CAAA,SAAM,AAAD,EAAE;YACzB,wBAAwB,gKAAA,CAAA,qBAAkB;QAC5C,IAAI,EAAE;IACN,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,OAAO;YACL,eAAe,OAAO;QACxB;IACF,GAAG;QAAC;KAAe;IACnB,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM;YAAC;SAAQ,EAAE;QAAC;KAAQ;IACrD,MAAM,SAAS,CAAC,gBAAgB,MAAM,MAAM,KAAK,OAAO,KAAK,IAAI,cAAc,MAAM;IACrF,MAAM,SAAS,CAAC,iBAAiB,MAAM,MAAM,KAAK,OAAO,KAAK,IAAI,eAAe,MAAM;IACvF,MAAM,QAAQ,CAAC,SAAS,CAAC,iBAAiB,MAAM,MAAM,KAAK,OAAO,KAAK,IAAI,eAAe,KAAK,MAAM,QAAQ,WAAW,KAAK,IAAI,SAAS;IAC1I,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,qMAAA,CAAA,WAAc,EAAE,MAAM,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,gBAAgB,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACjI,KAAK;IACP,KAAK,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,0BAA0B;QAC9D,MAAM;QACN,OAAO;QACP,QAAQ;QACR,QAAQ;IACV;AACF;AACA,SAAS,YAAY,KAAK;IACxB,OAAO,MAAM,MAAM,GAAG,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,mBAAmB,SAAS,MAAM,GAAG,GAAG,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,gBAAgB,SAAS,MAAM,QAAQ,GAAG,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,mBAAmB,SAAS,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,iBAAiB;AAC3R", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1893, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/%40react-three/drei/core/shapes.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as THREE from 'three';\n\nfunction create(type, effect) {\n  const El = type + 'Geometry';\n  return /*#__PURE__*/React.forwardRef(({\n    args,\n    children,\n    ...props\n  }, fref) => {\n    const ref = React.useRef(null);\n    React.useImperativeHandle(fref, () => ref.current);\n    React.useLayoutEffect(() => void (effect == null ? void 0 : effect(ref.current)));\n    return /*#__PURE__*/React.createElement(\"mesh\", _extends({\n      ref: ref\n    }, props), /*#__PURE__*/React.createElement(El, {\n      attach: \"geometry\",\n      args: args\n    }), children);\n  });\n}\nconst Box = /* @__PURE__ */create('box');\nconst Circle = /* @__PURE__ */create('circle');\nconst Cone = /* @__PURE__ */create('cone');\nconst Cylinder = /* @__PURE__ */create('cylinder');\nconst Sphere = /* @__PURE__ */create('sphere');\nconst Plane = /* @__PURE__ */create('plane');\nconst Tube = /* @__PURE__ */create('tube');\nconst Torus = /* @__PURE__ */create('torus');\nconst TorusKnot = /* @__PURE__ */create('torusKnot');\nconst Tetrahedron = /* @__PURE__ */create('tetrahedron');\nconst Ring = /* @__PURE__ */create('ring');\nconst Polyhedron = /* @__PURE__ */create('polyhedron');\nconst Icosahedron = /* @__PURE__ */create('icosahedron');\nconst Octahedron = /* @__PURE__ */create('octahedron');\nconst Dodecahedron = /* @__PURE__ */create('dodecahedron');\nconst Extrude = /* @__PURE__ */create('extrude');\nconst Lathe = /* @__PURE__ */create('lathe');\nconst Capsule = /* @__PURE__ */create('capsule');\nconst Shape = /* @__PURE__ */create('shape', ({\n  geometry\n}) => {\n  // Calculate UVs (by https://discourse.threejs.org/u/prisoner849)\n  // https://discourse.threejs.org/t/custom-shape-in-image-not-working/49348/10\n  const pos = geometry.attributes.position;\n  const b3 = new THREE.Box3().setFromBufferAttribute(pos);\n  const b3size = new THREE.Vector3();\n  b3.getSize(b3size);\n  const uv = [];\n  let x = 0,\n    y = 0,\n    u = 0,\n    v = 0;\n  for (let i = 0; i < pos.count; i++) {\n    x = pos.getX(i);\n    y = pos.getY(i);\n    u = (x - b3.min.x) / b3size.x;\n    v = (y - b3.min.y) / b3size.y;\n    uv.push(u, v);\n  }\n  geometry.setAttribute('uv', new THREE.Float32BufferAttribute(uv, 2));\n});\n\nexport { Box, Capsule, Circle, Cone, Cylinder, Dodecahedron, Extrude, Icosahedron, Lathe, Octahedron, Plane, Polyhedron, Ring, Shape, Sphere, Tetrahedron, Torus, TorusKnot, Tube };\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;;;;AAEA,SAAS,OAAO,IAAI,EAAE,MAAM;IAC1B,MAAM,KAAK,OAAO;IAClB,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EACpC,IAAI,EACJ,QAAQ,EACR,GAAG,OACJ,EAAE;QACD,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;QACzB,CAAA,GAAA,qMAAA,CAAA,sBAAyB,AAAD,EAAE,MAAM,IAAM,IAAI,OAAO;QACjD,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE,IAAM,KAAK,CAAC,UAAU,OAAO,KAAK,IAAI,OAAO,IAAI,OAAO,CAAC;QAC/E,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;YACvD,KAAK;QACP,GAAG,QAAQ,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,IAAI;YAC9C,QAAQ;YACR,MAAM;QACR,IAAI;IACN;AACF;AACA,MAAM,MAAM,aAAa,GAAE,OAAO;AAClC,MAAM,SAAS,aAAa,GAAE,OAAO;AACrC,MAAM,OAAO,aAAa,GAAE,OAAO;AACnC,MAAM,WAAW,aAAa,GAAE,OAAO;AACvC,MAAM,SAAS,aAAa,GAAE,OAAO;AACrC,MAAM,QAAQ,aAAa,GAAE,OAAO;AACpC,MAAM,OAAO,aAAa,GAAE,OAAO;AACnC,MAAM,QAAQ,aAAa,GAAE,OAAO;AACpC,MAAM,YAAY,aAAa,GAAE,OAAO;AACxC,MAAM,cAAc,aAAa,GAAE,OAAO;AAC1C,MAAM,OAAO,aAAa,GAAE,OAAO;AACnC,MAAM,aAAa,aAAa,GAAE,OAAO;AACzC,MAAM,cAAc,aAAa,GAAE,OAAO;AAC1C,MAAM,aAAa,aAAa,GAAE,OAAO;AACzC,MAAM,eAAe,aAAa,GAAE,OAAO;AAC3C,MAAM,UAAU,aAAa,GAAE,OAAO;AACtC,MAAM,QAAQ,aAAa,GAAE,OAAO;AACpC,MAAM,UAAU,aAAa,GAAE,OAAO;AACtC,MAAM,QAAQ,aAAa,GAAE,OAAO,SAAS,CAAC,EAC5C,QAAQ,EACT;IACC,iEAAiE;IACjE,6EAA6E;IAC7E,MAAM,MAAM,SAAS,UAAU,CAAC,QAAQ;IACxC,MAAM,KAAK,IAAI,+IAAA,CAAA,OAAU,GAAG,sBAAsB,CAAC;IACnD,MAAM,SAAS,IAAI,+IAAA,CAAA,UAAa;IAChC,GAAG,OAAO,CAAC;IACX,MAAM,KAAK,EAAE;IACb,IAAI,IAAI,GACN,IAAI,GACJ,IAAI,GACJ,IAAI;IACN,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,KAAK,EAAE,IAAK;QAClC,IAAI,IAAI,IAAI,CAAC;QACb,IAAI,IAAI,IAAI,CAAC;QACb,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC;QAC7B,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,CAAC,IAAI,OAAO,CAAC;QAC7B,GAAG,IAAI,CAAC,GAAG;IACb;IACA,SAAS,YAAY,CAAC,MAAM,IAAI,+IAAA,CAAA,yBAA4B,CAAC,IAAI;AACnE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1977, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/%40react-three/drei/web/Html.js"], "sourcesContent": ["import _extends from '@babel/runtime/helpers/esm/extends';\nimport * as React from 'react';\nimport * as ReactDOM from 'react-dom/client';\nimport { Vector3, DoubleSide, OrthographicCamera, PerspectiveCamera, Vector2 } from 'three';\nimport { useThree, useFrame } from '@react-three/fiber';\n\nconst v1 = /* @__PURE__ */new Vector3();\nconst v2 = /* @__PURE__ */new Vector3();\nconst v3 = /* @__PURE__ */new Vector3();\nconst v4 = /* @__PURE__ */new Vector2();\nfunction defaultCalculatePosition(el, camera, size) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  objectPos.project(camera);\n  const widthHalf = size.width / 2;\n  const heightHalf = size.height / 2;\n  return [objectPos.x * widthHalf + widthHalf, -(objectPos.y * heightHalf) + heightHalf];\n}\nfunction isObjectBehindCamera(el, camera) {\n  const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n  const deltaCamObj = objectPos.sub(cameraPos);\n  const camDir = camera.getWorldDirection(v3);\n  return deltaCamObj.angleTo(camDir) > Math.PI / 2;\n}\nfunction isObjectVisible(el, camera, raycaster, occlude) {\n  const elPos = v1.setFromMatrixPosition(el.matrixWorld);\n  const screenPos = elPos.clone();\n  screenPos.project(camera);\n  v4.set(screenPos.x, screenPos.y);\n  raycaster.setFromCamera(v4, camera);\n  const intersects = raycaster.intersectObjects(occlude, true);\n  if (intersects.length) {\n    const intersectionDistance = intersects[0].distance;\n    const pointDistance = elPos.distanceTo(raycaster.ray.origin);\n    return pointDistance < intersectionDistance;\n  }\n  return true;\n}\nfunction objectScale(el, camera) {\n  if (camera instanceof OrthographicCamera) {\n    return camera.zoom;\n  } else if (camera instanceof PerspectiveCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const vFOV = camera.fov * Math.PI / 180;\n    const dist = objectPos.distanceTo(cameraPos);\n    const scaleFOV = 2 * Math.tan(vFOV / 2) * dist;\n    return 1 / scaleFOV;\n  } else {\n    return 1;\n  }\n}\nfunction objectZIndex(el, camera, zIndexRange) {\n  if (camera instanceof PerspectiveCamera || camera instanceof OrthographicCamera) {\n    const objectPos = v1.setFromMatrixPosition(el.matrixWorld);\n    const cameraPos = v2.setFromMatrixPosition(camera.matrixWorld);\n    const dist = objectPos.distanceTo(cameraPos);\n    const A = (zIndexRange[1] - zIndexRange[0]) / (camera.far - camera.near);\n    const B = zIndexRange[1] - A * camera.far;\n    return Math.round(A * dist + B);\n  }\n  return undefined;\n}\nconst epsilon = value => Math.abs(value) < 1e-10 ? 0 : value;\nfunction getCSSMatrix(matrix, multipliers, prepend = '') {\n  let matrix3d = 'matrix3d(';\n  for (let i = 0; i !== 16; i++) {\n    matrix3d += epsilon(multipliers[i] * matrix.elements[i]) + (i !== 15 ? ',' : ')');\n  }\n  return prepend + matrix3d;\n}\nconst getCameraCSSMatrix = (multipliers => {\n  return matrix => getCSSMatrix(matrix, multipliers);\n})([1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1, 1, -1, 1, 1]);\nconst getObjectCSSMatrix = (scaleMultipliers => {\n  return (matrix, factor) => getCSSMatrix(matrix, scaleMultipliers(factor), 'translate(-50%,-50%)');\n})(f => [1 / f, 1 / f, 1 / f, 1, -1 / f, -1 / f, -1 / f, -1, 1 / f, 1 / f, 1 / f, 1, 1, 1, 1, 1]);\nfunction isRefObject(ref) {\n  return ref && typeof ref === 'object' && 'current' in ref;\n}\nconst Html = /* @__PURE__ */React.forwardRef(({\n  children,\n  eps = 0.001,\n  style,\n  className,\n  prepend,\n  center,\n  fullscreen,\n  portal,\n  distanceFactor,\n  sprite = false,\n  transform = false,\n  occlude,\n  onOcclude,\n  castShadow,\n  receiveShadow,\n  material,\n  geometry,\n  zIndexRange = [16777271, 0],\n  calculatePosition = defaultCalculatePosition,\n  as = 'div',\n  wrapperClass,\n  pointerEvents = 'auto',\n  ...props\n}, ref) => {\n  const {\n    gl,\n    camera,\n    scene,\n    size,\n    raycaster,\n    events,\n    viewport\n  } = useThree();\n  const [el] = React.useState(() => document.createElement(as));\n  const root = React.useRef(null);\n  const group = React.useRef(null);\n  const oldZoom = React.useRef(0);\n  const oldPosition = React.useRef([0, 0]);\n  const transformOuterRef = React.useRef(null);\n  const transformInnerRef = React.useRef(null);\n  // Append to the connected element, which makes HTML work with views\n  const target = (portal == null ? void 0 : portal.current) || events.connected || gl.domElement.parentNode;\n  const occlusionMeshRef = React.useRef(null);\n  const isMeshSizeSet = React.useRef(false);\n  const isRayCastOcclusion = React.useMemo(() => {\n    return occlude && occlude !== 'blending' || Array.isArray(occlude) && occlude.length && isRefObject(occlude[0]);\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    const el = gl.domElement;\n    if (occlude && occlude === 'blending') {\n      el.style.zIndex = `${Math.floor(zIndexRange[0] / 2)}`;\n      el.style.position = 'absolute';\n      el.style.pointerEvents = 'none';\n    } else {\n      el.style.zIndex = null;\n      el.style.position = null;\n      el.style.pointerEvents = null;\n    }\n  }, [occlude]);\n  React.useLayoutEffect(() => {\n    if (group.current) {\n      const currentRoot = root.current = ReactDOM.createRoot(el);\n      scene.updateMatrixWorld();\n      if (transform) {\n        el.style.cssText = `position:absolute;top:0;left:0;pointer-events:none;overflow:hidden;`;\n      } else {\n        const vec = calculatePosition(group.current, camera, size);\n        el.style.cssText = `position:absolute;top:0;left:0;transform:translate3d(${vec[0]}px,${vec[1]}px,0);transform-origin:0 0;`;\n      }\n      if (target) {\n        if (prepend) target.prepend(el);else target.appendChild(el);\n      }\n      return () => {\n        if (target) target.removeChild(el);\n        currentRoot.unmount();\n      };\n    }\n  }, [target, transform]);\n  React.useLayoutEffect(() => {\n    if (wrapperClass) el.className = wrapperClass;\n  }, [wrapperClass]);\n  const styles = React.useMemo(() => {\n    if (transform) {\n      return {\n        position: 'absolute',\n        top: 0,\n        left: 0,\n        width: size.width,\n        height: size.height,\n        transformStyle: 'preserve-3d',\n        pointerEvents: 'none'\n      };\n    } else {\n      return {\n        position: 'absolute',\n        transform: center ? 'translate3d(-50%,-50%,0)' : 'none',\n        ...(fullscreen && {\n          top: -size.height / 2,\n          left: -size.width / 2,\n          width: size.width,\n          height: size.height\n        }),\n        ...style\n      };\n    }\n  }, [style, center, fullscreen, size, transform]);\n  const transformInnerStyles = React.useMemo(() => ({\n    position: 'absolute',\n    pointerEvents\n  }), [pointerEvents]);\n  React.useLayoutEffect(() => {\n    isMeshSizeSet.current = false;\n    if (transform) {\n      var _root$current;\n      (_root$current = root.current) == null || _root$current.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: transformOuterRef,\n        style: styles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: transformInnerRef,\n        style: transformInnerStyles\n      }, /*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        className: className,\n        style: style,\n        children: children\n      }))));\n    } else {\n      var _root$current2;\n      (_root$current2 = root.current) == null || _root$current2.render(/*#__PURE__*/React.createElement(\"div\", {\n        ref: ref,\n        style: styles,\n        className: className,\n        children: children\n      }));\n    }\n  });\n  const visible = React.useRef(true);\n  useFrame(gl => {\n    if (group.current) {\n      camera.updateMatrixWorld();\n      group.current.updateWorldMatrix(true, false);\n      const vec = transform ? oldPosition.current : calculatePosition(group.current, camera, size);\n      if (transform || Math.abs(oldZoom.current - camera.zoom) > eps || Math.abs(oldPosition.current[0] - vec[0]) > eps || Math.abs(oldPosition.current[1] - vec[1]) > eps) {\n        const isBehindCamera = isObjectBehindCamera(group.current, camera);\n        let raytraceTarget = false;\n        if (isRayCastOcclusion) {\n          if (Array.isArray(occlude)) {\n            raytraceTarget = occlude.map(item => item.current);\n          } else if (occlude !== 'blending') {\n            raytraceTarget = [scene];\n          }\n        }\n        const previouslyVisible = visible.current;\n        if (raytraceTarget) {\n          const isvisible = isObjectVisible(group.current, camera, raycaster, raytraceTarget);\n          visible.current = isvisible && !isBehindCamera;\n        } else {\n          visible.current = !isBehindCamera;\n        }\n        if (previouslyVisible !== visible.current) {\n          if (onOcclude) onOcclude(!visible.current);else el.style.display = visible.current ? 'block' : 'none';\n        }\n        const halfRange = Math.floor(zIndexRange[0] / 2);\n        const zRange = occlude ? isRayCastOcclusion //\n        ? [zIndexRange[0], halfRange] : [halfRange - 1, 0] : zIndexRange;\n        el.style.zIndex = `${objectZIndex(group.current, camera, zRange)}`;\n        if (transform) {\n          const [widthHalf, heightHalf] = [size.width / 2, size.height / 2];\n          const fov = camera.projectionMatrix.elements[5] * heightHalf;\n          const {\n            isOrthographicCamera,\n            top,\n            left,\n            bottom,\n            right\n          } = camera;\n          const cameraMatrix = getCameraCSSMatrix(camera.matrixWorldInverse);\n          const cameraTransform = isOrthographicCamera ? `scale(${fov})translate(${epsilon(-(right + left) / 2)}px,${epsilon((top + bottom) / 2)}px)` : `translateZ(${fov}px)`;\n          let matrix = group.current.matrixWorld;\n          if (sprite) {\n            matrix = camera.matrixWorldInverse.clone().transpose().copyPosition(matrix).scale(group.current.scale);\n            matrix.elements[3] = matrix.elements[7] = matrix.elements[11] = 0;\n            matrix.elements[15] = 1;\n          }\n          el.style.width = size.width + 'px';\n          el.style.height = size.height + 'px';\n          el.style.perspective = isOrthographicCamera ? '' : `${fov}px`;\n          if (transformOuterRef.current && transformInnerRef.current) {\n            transformOuterRef.current.style.transform = `${cameraTransform}${cameraMatrix}translate(${widthHalf}px,${heightHalf}px)`;\n            transformInnerRef.current.style.transform = getObjectCSSMatrix(matrix, 1 / ((distanceFactor || 10) / 400));\n          }\n        } else {\n          const scale = distanceFactor === undefined ? 1 : objectScale(group.current, camera) * distanceFactor;\n          el.style.transform = `translate3d(${vec[0]}px,${vec[1]}px,0) scale(${scale})`;\n        }\n        oldPosition.current = vec;\n        oldZoom.current = camera.zoom;\n      }\n    }\n    if (!isRayCastOcclusion && occlusionMeshRef.current && !isMeshSizeSet.current) {\n      if (transform) {\n        if (transformOuterRef.current) {\n          const el = transformOuterRef.current.children[0];\n          if (el != null && el.clientWidth && el != null && el.clientHeight) {\n            const {\n              isOrthographicCamera\n            } = camera;\n            if (isOrthographicCamera || geometry) {\n              if (props.scale) {\n                if (!Array.isArray(props.scale)) {\n                  occlusionMeshRef.current.scale.setScalar(1 / props.scale);\n                } else if (props.scale instanceof Vector3) {\n                  occlusionMeshRef.current.scale.copy(props.scale.clone().divideScalar(1));\n                } else {\n                  occlusionMeshRef.current.scale.set(1 / props.scale[0], 1 / props.scale[1], 1 / props.scale[2]);\n                }\n              }\n            } else {\n              const ratio = (distanceFactor || 10) / 400;\n              const w = el.clientWidth * ratio;\n              const h = el.clientHeight * ratio;\n              occlusionMeshRef.current.scale.set(w, h, 1);\n            }\n            isMeshSizeSet.current = true;\n          }\n        }\n      } else {\n        const ele = el.children[0];\n        if (ele != null && ele.clientWidth && ele != null && ele.clientHeight) {\n          const ratio = 1 / viewport.factor;\n          const w = ele.clientWidth * ratio;\n          const h = ele.clientHeight * ratio;\n          occlusionMeshRef.current.scale.set(w, h, 1);\n          isMeshSizeSet.current = true;\n        }\n        occlusionMeshRef.current.lookAt(gl.camera.position);\n      }\n    }\n  });\n  const shaders = React.useMemo(() => ({\n    vertexShader: !transform ? /* glsl */`\n          /*\n            This shader is from the THREE's SpriteMaterial.\n            We need to turn the backing plane into a Sprite\n            (make it always face the camera) if \"transfrom\"\n            is false.\n          */\n          #include <common>\n\n          void main() {\n            vec2 center = vec2(0., 1.);\n            float rotation = 0.0;\n\n            // This is somewhat arbitrary, but it seems to work well\n            // Need to figure out how to derive this dynamically if it even matters\n            float size = 0.03;\n\n            vec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n            vec2 scale;\n            scale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n            scale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n\n            bool isPerspective = isPerspectiveMatrix( projectionMatrix );\n            if ( isPerspective ) scale *= - mvPosition.z;\n\n            vec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale * size;\n            vec2 rotatedPosition;\n            rotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n            rotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n            mvPosition.xy += rotatedPosition;\n\n            gl_Position = projectionMatrix * mvPosition;\n          }\n      ` : undefined,\n    fragmentShader: /* glsl */`\n        void main() {\n          gl_FragColor = vec4(0.0, 0.0, 0.0, 0.0);\n        }\n      `\n  }), [transform]);\n  return /*#__PURE__*/React.createElement(\"group\", _extends({}, props, {\n    ref: group\n  }), occlude && !isRayCastOcclusion && /*#__PURE__*/React.createElement(\"mesh\", {\n    castShadow: castShadow,\n    receiveShadow: receiveShadow,\n    ref: occlusionMeshRef\n  }, geometry || /*#__PURE__*/React.createElement(\"planeGeometry\", null), material || /*#__PURE__*/React.createElement(\"shaderMaterial\", {\n    side: DoubleSide,\n    vertexShader: shaders.vertexShader,\n    fragmentShader: shaders.fragmentShader\n  })));\n});\n\nexport { Html };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AAAA;;;;;;AAEA,MAAM,KAAK,aAAa,GAAE,IAAI,+IAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,+IAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,+IAAA,CAAA,UAAO;AACrC,MAAM,KAAK,aAAa,GAAE,IAAI,+IAAA,CAAA,UAAO;AACrC,SAAS,yBAAyB,EAAE,EAAE,MAAM,EAAE,IAAI;IAChD,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACzD,UAAU,OAAO,CAAC;IAClB,MAAM,YAAY,KAAK,KAAK,GAAG;IAC/B,MAAM,aAAa,KAAK,MAAM,GAAG;IACjC,OAAO;QAAC,UAAU,CAAC,GAAG,YAAY;QAAW,CAAC,CAAC,UAAU,CAAC,GAAG,UAAU,IAAI;KAAW;AACxF;AACA,SAAS,qBAAqB,EAAE,EAAE,MAAM;IACtC,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;IAC7D,MAAM,cAAc,UAAU,GAAG,CAAC;IAClC,MAAM,SAAS,OAAO,iBAAiB,CAAC;IACxC,OAAO,YAAY,OAAO,CAAC,UAAU,KAAK,EAAE,GAAG;AACjD;AACA,SAAS,gBAAgB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO;IACrD,MAAM,QAAQ,GAAG,qBAAqB,CAAC,GAAG,WAAW;IACrD,MAAM,YAAY,MAAM,KAAK;IAC7B,UAAU,OAAO,CAAC;IAClB,GAAG,GAAG,CAAC,UAAU,CAAC,EAAE,UAAU,CAAC;IAC/B,UAAU,aAAa,CAAC,IAAI;IAC5B,MAAM,aAAa,UAAU,gBAAgB,CAAC,SAAS;IACvD,IAAI,WAAW,MAAM,EAAE;QACrB,MAAM,uBAAuB,UAAU,CAAC,EAAE,CAAC,QAAQ;QACnD,MAAM,gBAAgB,MAAM,UAAU,CAAC,UAAU,GAAG,CAAC,MAAM;QAC3D,OAAO,gBAAgB;IACzB;IACA,OAAO;AACT;AACA,SAAS,YAAY,EAAE,EAAE,MAAM;IAC7B,IAAI,kBAAkB,+IAAA,CAAA,qBAAkB,EAAE;QACxC,OAAO,OAAO,IAAI;IACpB,OAAO,IAAI,kBAAkB,+IAAA,CAAA,oBAAiB,EAAE;QAC9C,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;QACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;QAC7D,MAAM,OAAO,OAAO,GAAG,GAAG,KAAK,EAAE,GAAG;QACpC,MAAM,OAAO,UAAU,UAAU,CAAC;QAClC,MAAM,WAAW,IAAI,KAAK,GAAG,CAAC,OAAO,KAAK;QAC1C,OAAO,IAAI;IACb,OAAO;QACL,OAAO;IACT;AACF;AACA,SAAS,aAAa,EAAE,EAAE,MAAM,EAAE,WAAW;IAC3C,IAAI,kBAAkB,+IAAA,CAAA,oBAAiB,IAAI,kBAAkB,+IAAA,CAAA,qBAAkB,EAAE;QAC/E,MAAM,YAAY,GAAG,qBAAqB,CAAC,GAAG,WAAW;QACzD,MAAM,YAAY,GAAG,qBAAqB,CAAC,OAAO,WAAW;QAC7D,MAAM,OAAO,UAAU,UAAU,CAAC;QAClC,MAAM,IAAI,CAAC,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,IAAI,CAAC,OAAO,GAAG,GAAG,OAAO,IAAI;QACvE,MAAM,IAAI,WAAW,CAAC,EAAE,GAAG,IAAI,OAAO,GAAG;QACzC,OAAO,KAAK,KAAK,CAAC,IAAI,OAAO;IAC/B;IACA,OAAO;AACT;AACA,MAAM,UAAU,CAAA,QAAS,KAAK,GAAG,CAAC,SAAS,QAAQ,IAAI;AACvD,SAAS,aAAa,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE;IACrD,IAAI,WAAW;IACf,IAAK,IAAI,IAAI,GAAG,MAAM,IAAI,IAAK;QAC7B,YAAY,QAAQ,WAAW,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,EAAE,IAAI,CAAC,MAAM,KAAK,MAAM,GAAG;IAClF;IACA,OAAO,UAAU;AACnB;AACA,MAAM,qBAAqB,CAAC,CAAA;IAC1B,OAAO,CAAA,SAAU,aAAa,QAAQ;AACxC,CAAC,EAAE;IAAC;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;IAAG;IAAG,CAAC;IAAG;IAAG;CAAE;AACvD,MAAM,qBAAqB,CAAC,CAAA;IAC1B,OAAO,CAAC,QAAQ,SAAW,aAAa,QAAQ,iBAAiB,SAAS;AAC5E,CAAC,EAAE,CAAA,IAAK;QAAC,IAAI;QAAG,IAAI;QAAG,IAAI;QAAG;QAAG,CAAC,IAAI;QAAG,CAAC,IAAI;QAAG,CAAC,IAAI;QAAG,CAAC;QAAG,IAAI;QAAG,IAAI;QAAG,IAAI;QAAG;QAAG;QAAG;QAAG;QAAG;KAAE;AAChG,SAAS,YAAY,GAAG;IACtB,OAAO,OAAO,OAAO,QAAQ,YAAY,aAAa;AACxD;AACA,MAAM,OAAO,aAAa,GAAE,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE,CAAC,EAC5C,QAAQ,EACR,MAAM,KAAK,EACX,KAAK,EACL,SAAS,EACT,OAAO,EACP,MAAM,EACN,UAAU,EACV,MAAM,EACN,cAAc,EACd,SAAS,KAAK,EACd,YAAY,KAAK,EACjB,OAAO,EACP,SAAS,EACT,UAAU,EACV,aAAa,EACb,QAAQ,EACR,QAAQ,EACR,cAAc;IAAC;IAAU;CAAE,EAC3B,oBAAoB,wBAAwB,EAC5C,KAAK,KAAK,EACV,YAAY,EACZ,gBAAgB,MAAM,EACtB,GAAG,OACJ,EAAE;IACD,MAAM,EACJ,EAAE,EACF,MAAM,EACN,KAAK,EACL,IAAI,EACJ,SAAS,EACT,MAAM,EACN,QAAQ,EACT,GAAG,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD;IACX,MAAM,CAAC,GAAG,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAE,IAAM,SAAS,aAAa,CAAC;IACzD,MAAM,OAAO,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC1B,MAAM,QAAQ,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC3B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;QAAC;QAAG;KAAE;IACvC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACvC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACvC,oEAAoE;IACpE,MAAM,SAAS,CAAC,UAAU,OAAO,KAAK,IAAI,OAAO,OAAO,KAAK,OAAO,SAAS,IAAI,GAAG,UAAU,CAAC,UAAU;IACzG,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACtC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IACnC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QACvC,OAAO,WAAW,YAAY,cAAc,MAAM,OAAO,CAAC,YAAY,QAAQ,MAAM,IAAI,YAAY,OAAO,CAAC,EAAE;IAChH,GAAG;QAAC;KAAQ;IACZ,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,MAAM,KAAK,GAAG,UAAU;QACxB,IAAI,WAAW,YAAY,YAAY;YACrC,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG,KAAK,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG,IAAI;YACrD,GAAG,KAAK,CAAC,QAAQ,GAAG;YACpB,GAAG,KAAK,CAAC,aAAa,GAAG;QAC3B,OAAO;YACL,GAAG,KAAK,CAAC,MAAM,GAAG;YAClB,GAAG,KAAK,CAAC,QAAQ,GAAG;YACpB,GAAG,KAAK,CAAC,aAAa,GAAG;QAC3B;IACF,GAAG;QAAC;KAAQ;IACZ,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,cAAc,KAAK,OAAO,GAAG,CAAA,GAAA,kKAAA,CAAA,aAAmB,AAAD,EAAE;YACvD,MAAM,iBAAiB;YACvB,IAAI,WAAW;gBACb,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,mEAAmE,CAAC;YAC1F,OAAO;gBACL,MAAM,MAAM,kBAAkB,MAAM,OAAO,EAAE,QAAQ;gBACrD,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC,qDAAqD,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,2BAA2B,CAAC;YAC5H;YACA,IAAI,QAAQ;gBACV,IAAI,SAAS,OAAO,OAAO,CAAC;qBAAS,OAAO,WAAW,CAAC;YAC1D;YACA,OAAO;gBACL,IAAI,QAAQ,OAAO,WAAW,CAAC;gBAC/B,YAAY,OAAO;YACrB;QACF;IACF,GAAG;QAAC;QAAQ;KAAU;IACtB,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,IAAI,cAAc,GAAG,SAAS,GAAG;IACnC,GAAG;QAAC;KAAa;IACjB,MAAM,SAAS,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE;QAC3B,IAAI,WAAW;YACb,OAAO;gBACL,UAAU;gBACV,KAAK;gBACL,MAAM;gBACN,OAAO,KAAK,KAAK;gBACjB,QAAQ,KAAK,MAAM;gBACnB,gBAAgB;gBAChB,eAAe;YACjB;QACF,OAAO;YACL,OAAO;gBACL,UAAU;gBACV,WAAW,SAAS,6BAA6B;gBACjD,GAAI,cAAc;oBAChB,KAAK,CAAC,KAAK,MAAM,GAAG;oBACpB,MAAM,CAAC,KAAK,KAAK,GAAG;oBACpB,OAAO,KAAK,KAAK;oBACjB,QAAQ,KAAK,MAAM;gBACrB,CAAC;gBACD,GAAG,KAAK;YACV;QACF;IACF,GAAG;QAAC;QAAO;QAAQ;QAAY;QAAM;KAAU;IAC/C,MAAM,uBAAuB,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM,CAAC;YAChD,UAAU;YACV;QACF,CAAC,GAAG;QAAC;KAAc;IACnB,CAAA,GAAA,qMAAA,CAAA,kBAAqB,AAAD,EAAE;QACpB,cAAc,OAAO,GAAG;QACxB,IAAI,WAAW;YACb,IAAI;YACJ,CAAC,gBAAgB,KAAK,OAAO,KAAK,QAAQ,cAAc,MAAM,CAAC,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;gBACrG,KAAK;gBACL,OAAO;YACT,GAAG,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;gBACzC,KAAK;gBACL,OAAO;YACT,GAAG,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;gBACzC,KAAK;gBACL,WAAW;gBACX,OAAO;gBACP,UAAU;YACZ;QACF,OAAO;YACL,IAAI;YACJ,CAAC,iBAAiB,KAAK,OAAO,KAAK,QAAQ,eAAe,MAAM,CAAC,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;gBACvG,KAAK;gBACL,OAAO;gBACP,WAAW;gBACX,UAAU;YACZ;QACF;IACF;IACA,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAA;QACP,IAAI,MAAM,OAAO,EAAE;YACjB,OAAO,iBAAiB;YACxB,MAAM,OAAO,CAAC,iBAAiB,CAAC,MAAM;YACtC,MAAM,MAAM,YAAY,YAAY,OAAO,GAAG,kBAAkB,MAAM,OAAO,EAAE,QAAQ;YACvF,IAAI,aAAa,KAAK,GAAG,CAAC,QAAQ,OAAO,GAAG,OAAO,IAAI,IAAI,OAAO,KAAK,GAAG,CAAC,YAAY,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,OAAO,KAAK,GAAG,CAAC,YAAY,OAAO,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,KAAK;gBACpK,MAAM,iBAAiB,qBAAqB,MAAM,OAAO,EAAE;gBAC3D,IAAI,iBAAiB;gBACrB,IAAI,oBAAoB;oBACtB,IAAI,MAAM,OAAO,CAAC,UAAU;wBAC1B,iBAAiB,QAAQ,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO;oBACnD,OAAO,IAAI,YAAY,YAAY;wBACjC,iBAAiB;4BAAC;yBAAM;oBAC1B;gBACF;gBACA,MAAM,oBAAoB,QAAQ,OAAO;gBACzC,IAAI,gBAAgB;oBAClB,MAAM,YAAY,gBAAgB,MAAM,OAAO,EAAE,QAAQ,WAAW;oBACpE,QAAQ,OAAO,GAAG,aAAa,CAAC;gBAClC,OAAO;oBACL,QAAQ,OAAO,GAAG,CAAC;gBACrB;gBACA,IAAI,sBAAsB,QAAQ,OAAO,EAAE;oBACzC,IAAI,WAAW,UAAU,CAAC,QAAQ,OAAO;yBAAO,GAAG,KAAK,CAAC,OAAO,GAAG,QAAQ,OAAO,GAAG,UAAU;gBACjG;gBACA,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW,CAAC,EAAE,GAAG;gBAC9C,MAAM,SAAS,UAAU,mBAAmB,EAAE;mBAC5C;oBAAC,WAAW,CAAC,EAAE;oBAAE;iBAAU,GAAG;oBAAC,YAAY;oBAAG;iBAAE,GAAG;gBACrD,GAAG,KAAK,CAAC,MAAM,GAAG,GAAG,aAAa,MAAM,OAAO,EAAE,QAAQ,SAAS;gBAClE,IAAI,WAAW;oBACb,MAAM,CAAC,WAAW,WAAW,GAAG;wBAAC,KAAK,KAAK,GAAG;wBAAG,KAAK,MAAM,GAAG;qBAAE;oBACjE,MAAM,MAAM,OAAO,gBAAgB,CAAC,QAAQ,CAAC,EAAE,GAAG;oBAClD,MAAM,EACJ,oBAAoB,EACpB,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACN,GAAG;oBACJ,MAAM,eAAe,mBAAmB,OAAO,kBAAkB;oBACjE,MAAM,kBAAkB,uBAAuB,CAAC,MAAM,EAAE,IAAI,WAAW,EAAE,QAAQ,CAAC,CAAC,QAAQ,IAAI,IAAI,GAAG,GAAG,EAAE,QAAQ,CAAC,MAAM,MAAM,IAAI,GAAG,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,GAAG,CAAC;oBACpK,IAAI,SAAS,MAAM,OAAO,CAAC,WAAW;oBACtC,IAAI,QAAQ;wBACV,SAAS,OAAO,kBAAkB,CAAC,KAAK,GAAG,SAAS,GAAG,YAAY,CAAC,QAAQ,KAAK,CAAC,MAAM,OAAO,CAAC,KAAK;wBACrG,OAAO,QAAQ,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,EAAE,GAAG,OAAO,QAAQ,CAAC,GAAG,GAAG;wBAChE,OAAO,QAAQ,CAAC,GAAG,GAAG;oBACxB;oBACA,GAAG,KAAK,CAAC,KAAK,GAAG,KAAK,KAAK,GAAG;oBAC9B,GAAG,KAAK,CAAC,MAAM,GAAG,KAAK,MAAM,GAAG;oBAChC,GAAG,KAAK,CAAC,WAAW,GAAG,uBAAuB,KAAK,GAAG,IAAI,EAAE,CAAC;oBAC7D,IAAI,kBAAkB,OAAO,IAAI,kBAAkB,OAAO,EAAE;wBAC1D,kBAAkB,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,GAAG,kBAAkB,aAAa,UAAU,EAAE,UAAU,GAAG,EAAE,WAAW,GAAG,CAAC;wBACxH,kBAAkB,OAAO,CAAC,KAAK,CAAC,SAAS,GAAG,mBAAmB,QAAQ,IAAI,CAAC,CAAC,kBAAkB,EAAE,IAAI,GAAG;oBAC1G;gBACF,OAAO;oBACL,MAAM,QAAQ,mBAAmB,YAAY,IAAI,YAAY,MAAM,OAAO,EAAE,UAAU;oBACtF,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;gBAC/E;gBACA,YAAY,OAAO,GAAG;gBACtB,QAAQ,OAAO,GAAG,OAAO,IAAI;YAC/B;QACF;QACA,IAAI,CAAC,sBAAsB,iBAAiB,OAAO,IAAI,CAAC,cAAc,OAAO,EAAE;YAC7E,IAAI,WAAW;gBACb,IAAI,kBAAkB,OAAO,EAAE;oBAC7B,MAAM,KAAK,kBAAkB,OAAO,CAAC,QAAQ,CAAC,EAAE;oBAChD,IAAI,MAAM,QAAQ,GAAG,WAAW,IAAI,MAAM,QAAQ,GAAG,YAAY,EAAE;wBACjE,MAAM,EACJ,oBAAoB,EACrB,GAAG;wBACJ,IAAI,wBAAwB,UAAU;4BACpC,IAAI,MAAM,KAAK,EAAE;gCACf,IAAI,CAAC,MAAM,OAAO,CAAC,MAAM,KAAK,GAAG;oCAC/B,iBAAiB,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,MAAM,KAAK;gCAC1D,OAAO,IAAI,MAAM,KAAK,YAAY,+IAAA,CAAA,UAAO,EAAE;oCACzC,iBAAiB,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,KAAK,GAAG,YAAY,CAAC;gCACvE,OAAO;oCACL,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE,IAAI,MAAM,KAAK,CAAC,EAAE;gCAC/F;4BACF;wBACF,OAAO;4BACL,MAAM,QAAQ,CAAC,kBAAkB,EAAE,IAAI;4BACvC,MAAM,IAAI,GAAG,WAAW,GAAG;4BAC3B,MAAM,IAAI,GAAG,YAAY,GAAG;4BAC5B,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG;wBAC3C;wBACA,cAAc,OAAO,GAAG;oBAC1B;gBACF;YACF,OAAO;gBACL,MAAM,MAAM,GAAG,QAAQ,CAAC,EAAE;gBAC1B,IAAI,OAAO,QAAQ,IAAI,WAAW,IAAI,OAAO,QAAQ,IAAI,YAAY,EAAE;oBACrE,MAAM,QAAQ,IAAI,SAAS,MAAM;oBACjC,MAAM,IAAI,IAAI,WAAW,GAAG;oBAC5B,MAAM,IAAI,IAAI,YAAY,GAAG;oBAC7B,iBAAiB,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,GAAG;oBACzC,cAAc,OAAO,GAAG;gBAC1B;gBACA,iBAAiB,OAAO,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,QAAQ;YACpD;QACF;IACF;IACA,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,UAAa,AAAD,EAAE,IAAM,CAAC;YACnC,cAAc,CAAC,YAAY,QAAQ,GAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAiCpC,CAAC,GAAG;YACN,gBAAgB,QAAQ,GAAE,CAAC;;;;MAIzB,CAAC;QACL,CAAC,GAAG;QAAC;KAAU;IACf,OAAO,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QACnE,KAAK;IACP,IAAI,WAAW,CAAC,sBAAsB,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ;QAC7E,YAAY;QACZ,eAAe;QACf,KAAK;IACP,GAAG,YAAY,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,iBAAiB,OAAO,YAAY,WAAW,GAAE,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAE,kBAAkB;QACrI,MAAM,+IAAA,CAAA,aAAU;QAChB,cAAc,QAAQ,YAAY;QAClC,gBAAgB,QAAQ,cAAc;IACxC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2400, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/three-stdlib/node_modules/fflate/esm/index.mjs"], "sourcesContent": ["import { createRequire } from 'module';\nvar require = createRequire('/');\n// DEFLATE is a complex format; to read this code, you should probably check the RFC first:\n// https://tools.ietf.org/html/rfc1951\n// You may also wish to take a look at the guide I made about this program:\n// https://gist.github.com/101arrowz/253f31eb5abc3d9275ab943003ffecad\n// Some of the following code is similar to that of UZIP.js:\n// https://github.com/photopea/UZIP.js\n// However, the vast majority of the codebase has diverged from UZIP.js to increase performance and reduce bundle size.\n// Sometimes 0 will appear where -1 would be more appropriate. This is because using a uint\n// is better for memory in most engines (I *think*).\n// Mediocre shim\nvar Worker;\nvar workerAdd = \";var __w=require('worker_threads');__w.parentPort.on('message',function(m){onmessage({data:m})}),postMessage=function(m,t){__w.parentPort.postMessage(m,t)},close=process.exit;self=global\";\ntry {\n    Worker = require('worker_threads').Worker;\n}\ncatch (e) {\n}\nvar wk = Worker ? function (c, _, msg, transfer, cb) {\n    var done = false;\n    var w = new Worker(c + workerAdd, { eval: true })\n        .on('error', function (e) { return cb(e, null); })\n        .on('message', function (m) { return cb(null, m); })\n        .on('exit', function (c) {\n        if (c && !done)\n            cb(new Error('exited with code ' + c), null);\n    });\n    w.postMessage(msg, transfer);\n    w.terminate = function () {\n        done = true;\n        return Worker.prototype.terminate.call(w);\n    };\n    return w;\n} : function (_, __, ___, ____, cb) {\n    setImmediate(function () { return cb(new Error('async operations unsupported - update to Node 12+ (or Node 10-11 with the --experimental-worker CLI flag)'), null); });\n    var NOP = function () { };\n    return {\n        terminate: NOP,\n        postMessage: NOP\n    };\n};\n\n// aliases for shorter compressed code (most minifers don't do this)\nvar u8 = Uint8Array, u16 = Uint16Array, u32 = Uint32Array;\n// fixed length extra bits\nvar fleb = new u8([0, 0, 0, 0, 0, 0, 0, 0, 1, 1, 1, 1, 2, 2, 2, 2, 3, 3, 3, 3, 4, 4, 4, 4, 5, 5, 5, 5, 0, /* unused */ 0, 0, /* impossible */ 0]);\n// fixed distance extra bits\n// see fleb note\nvar fdeb = new u8([0, 0, 0, 0, 1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 8, 8, 9, 9, 10, 10, 11, 11, 12, 12, 13, 13, /* unused */ 0, 0]);\n// code length index map\nvar clim = new u8([16, 17, 18, 0, 8, 7, 9, 6, 10, 5, 11, 4, 12, 3, 13, 2, 14, 1, 15]);\n// get base, reverse index map from extra bits\nvar freb = function (eb, start) {\n    var b = new u16(31);\n    for (var i = 0; i < 31; ++i) {\n        b[i] = start += 1 << eb[i - 1];\n    }\n    // numbers here are at max 18 bits\n    var r = new u32(b[30]);\n    for (var i = 1; i < 30; ++i) {\n        for (var j = b[i]; j < b[i + 1]; ++j) {\n            r[j] = ((j - b[i]) << 5) | i;\n        }\n    }\n    return [b, r];\n};\nvar _a = freb(fleb, 2), fl = _a[0], revfl = _a[1];\n// we can ignore the fact that the other numbers are wrong; they never happen anyway\nfl[28] = 258, revfl[258] = 28;\nvar _b = freb(fdeb, 0), fd = _b[0], revfd = _b[1];\n// map of value to reverse (assuming 16 bits)\nvar rev = new u16(32768);\nfor (var i = 0; i < 32768; ++i) {\n    // reverse table algorithm from SO\n    var x = ((i & 0xAAAA) >>> 1) | ((i & 0x5555) << 1);\n    x = ((x & 0xCCCC) >>> 2) | ((x & 0x3333) << 2);\n    x = ((x & 0xF0F0) >>> 4) | ((x & 0x0F0F) << 4);\n    rev[i] = (((x & 0xFF00) >>> 8) | ((x & 0x00FF) << 8)) >>> 1;\n}\n// create huffman tree from u8 \"map\": index -> code length for code index\n// mb (max bits) must be at most 15\n// TODO: optimize/split up?\nvar hMap = (function (cd, mb, r) {\n    var s = cd.length;\n    // index\n    var i = 0;\n    // u16 \"map\": index -> # of codes with bit length = index\n    var l = new u16(mb);\n    // length of cd must be 288 (total # of codes)\n    for (; i < s; ++i)\n        ++l[cd[i] - 1];\n    // u16 \"map\": index -> minimum code for bit length = index\n    var le = new u16(mb);\n    for (i = 0; i < mb; ++i) {\n        le[i] = (le[i - 1] + l[i - 1]) << 1;\n    }\n    var co;\n    if (r) {\n        // u16 \"map\": index -> number of actual bits, symbol for code\n        co = new u16(1 << mb);\n        // bits to remove for reverser\n        var rvb = 15 - mb;\n        for (i = 0; i < s; ++i) {\n            // ignore 0 lengths\n            if (cd[i]) {\n                // num encoding both symbol and bits read\n                var sv = (i << 4) | cd[i];\n                // free bits\n                var r_1 = mb - cd[i];\n                // start value\n                var v = le[cd[i] - 1]++ << r_1;\n                // m is end value\n                for (var m = v | ((1 << r_1) - 1); v <= m; ++v) {\n                    // every 16 bit value starting with the code yields the same result\n                    co[rev[v] >>> rvb] = sv;\n                }\n            }\n        }\n    }\n    else {\n        co = new u16(s);\n        for (i = 0; i < s; ++i) {\n            if (cd[i]) {\n                co[i] = rev[le[cd[i] - 1]++] >>> (15 - cd[i]);\n            }\n        }\n    }\n    return co;\n});\n// fixed length tree\nvar flt = new u8(288);\nfor (var i = 0; i < 144; ++i)\n    flt[i] = 8;\nfor (var i = 144; i < 256; ++i)\n    flt[i] = 9;\nfor (var i = 256; i < 280; ++i)\n    flt[i] = 7;\nfor (var i = 280; i < 288; ++i)\n    flt[i] = 8;\n// fixed distance tree\nvar fdt = new u8(32);\nfor (var i = 0; i < 32; ++i)\n    fdt[i] = 5;\n// fixed length map\nvar flm = /*#__PURE__*/ hMap(flt, 9, 0), flrm = /*#__PURE__*/ hMap(flt, 9, 1);\n// fixed distance map\nvar fdm = /*#__PURE__*/ hMap(fdt, 5, 0), fdrm = /*#__PURE__*/ hMap(fdt, 5, 1);\n// find max of array\nvar max = function (a) {\n    var m = a[0];\n    for (var i = 1; i < a.length; ++i) {\n        if (a[i] > m)\n            m = a[i];\n    }\n    return m;\n};\n// read d, starting at bit p and mask with m\nvar bits = function (d, p, m) {\n    var o = (p / 8) | 0;\n    return ((d[o] | (d[o + 1] << 8)) >> (p & 7)) & m;\n};\n// read d, starting at bit p continuing for at least 16 bits\nvar bits16 = function (d, p) {\n    var o = (p / 8) | 0;\n    return ((d[o] | (d[o + 1] << 8) | (d[o + 2] << 16)) >> (p & 7));\n};\n// get end of byte\nvar shft = function (p) { return ((p / 8) | 0) + (p & 7 && 1); };\n// typed array slice - allows garbage collector to free original reference,\n// while being more compatible than .slice\nvar slc = function (v, s, e) {\n    if (s == null || s < 0)\n        s = 0;\n    if (e == null || e > v.length)\n        e = v.length;\n    // can't use .constructor in case user-supplied\n    var n = new (v instanceof u16 ? u16 : v instanceof u32 ? u32 : u8)(e - s);\n    n.set(v.subarray(s, e));\n    return n;\n};\n// expands raw DEFLATE data\nvar inflt = function (dat, buf, st) {\n    // source length\n    var sl = dat.length;\n    if (!sl || (st && !st.l && sl < 5))\n        return buf || new u8(0);\n    // have to estimate size\n    var noBuf = !buf || st;\n    // no state\n    var noSt = !st || st.i;\n    if (!st)\n        st = {};\n    // Assumes roughly 33% compression ratio average\n    if (!buf)\n        buf = new u8(sl * 3);\n    // ensure buffer can fit at least l elements\n    var cbuf = function (l) {\n        var bl = buf.length;\n        // need to increase size to fit\n        if (l > bl) {\n            // Double or set to necessary, whichever is greater\n            var nbuf = new u8(Math.max(bl * 2, l));\n            nbuf.set(buf);\n            buf = nbuf;\n        }\n    };\n    //  last chunk         bitpos           bytes\n    var final = st.f || 0, pos = st.p || 0, bt = st.b || 0, lm = st.l, dm = st.d, lbt = st.m, dbt = st.n;\n    // total bits\n    var tbts = sl * 8;\n    do {\n        if (!lm) {\n            // BFINAL - this is only 1 when last chunk is next\n            st.f = final = bits(dat, pos, 1);\n            // type: 0 = no compression, 1 = fixed huffman, 2 = dynamic huffman\n            var type = bits(dat, pos + 1, 3);\n            pos += 3;\n            if (!type) {\n                // go to end of byte boundary\n                var s = shft(pos) + 4, l = dat[s - 4] | (dat[s - 3] << 8), t = s + l;\n                if (t > sl) {\n                    if (noSt)\n                        throw 'unexpected EOF';\n                    break;\n                }\n                // ensure size\n                if (noBuf)\n                    cbuf(bt + l);\n                // Copy over uncompressed data\n                buf.set(dat.subarray(s, t), bt);\n                // Get new bitpos, update byte count\n                st.b = bt += l, st.p = pos = t * 8;\n                continue;\n            }\n            else if (type == 1)\n                lm = flrm, dm = fdrm, lbt = 9, dbt = 5;\n            else if (type == 2) {\n                //  literal                            lengths\n                var hLit = bits(dat, pos, 31) + 257, hcLen = bits(dat, pos + 10, 15) + 4;\n                var tl = hLit + bits(dat, pos + 5, 31) + 1;\n                pos += 14;\n                // length+distance tree\n                var ldt = new u8(tl);\n                // code length tree\n                var clt = new u8(19);\n                for (var i = 0; i < hcLen; ++i) {\n                    // use index map to get real code\n                    clt[clim[i]] = bits(dat, pos + i * 3, 7);\n                }\n                pos += hcLen * 3;\n                // code lengths bits\n                var clb = max(clt), clbmsk = (1 << clb) - 1;\n                // code lengths map\n                var clm = hMap(clt, clb, 1);\n                for (var i = 0; i < tl;) {\n                    var r = clm[bits(dat, pos, clbmsk)];\n                    // bits read\n                    pos += r & 15;\n                    // symbol\n                    var s = r >>> 4;\n                    // code length to copy\n                    if (s < 16) {\n                        ldt[i++] = s;\n                    }\n                    else {\n                        //  copy   count\n                        var c = 0, n = 0;\n                        if (s == 16)\n                            n = 3 + bits(dat, pos, 3), pos += 2, c = ldt[i - 1];\n                        else if (s == 17)\n                            n = 3 + bits(dat, pos, 7), pos += 3;\n                        else if (s == 18)\n                            n = 11 + bits(dat, pos, 127), pos += 7;\n                        while (n--)\n                            ldt[i++] = c;\n                    }\n                }\n                //    length tree                 distance tree\n                var lt = ldt.subarray(0, hLit), dt = ldt.subarray(hLit);\n                // max length bits\n                lbt = max(lt);\n                // max dist bits\n                dbt = max(dt);\n                lm = hMap(lt, lbt, 1);\n                dm = hMap(dt, dbt, 1);\n            }\n            else\n                throw 'invalid block type';\n            if (pos > tbts) {\n                if (noSt)\n                    throw 'unexpected EOF';\n                break;\n            }\n        }\n        // Make sure the buffer can hold this + the largest possible addition\n        // Maximum chunk size (practically, theoretically infinite) is 2^17;\n        if (noBuf)\n            cbuf(bt + 131072);\n        var lms = (1 << lbt) - 1, dms = (1 << dbt) - 1;\n        var lpos = pos;\n        for (;; lpos = pos) {\n            // bits read, code\n            var c = lm[bits16(dat, pos) & lms], sym = c >>> 4;\n            pos += c & 15;\n            if (pos > tbts) {\n                if (noSt)\n                    throw 'unexpected EOF';\n                break;\n            }\n            if (!c)\n                throw 'invalid length/literal';\n            if (sym < 256)\n                buf[bt++] = sym;\n            else if (sym == 256) {\n                lpos = pos, lm = null;\n                break;\n            }\n            else {\n                var add = sym - 254;\n                // no extra bits needed if less\n                if (sym > 264) {\n                    // index\n                    var i = sym - 257, b = fleb[i];\n                    add = bits(dat, pos, (1 << b) - 1) + fl[i];\n                    pos += b;\n                }\n                // dist\n                var d = dm[bits16(dat, pos) & dms], dsym = d >>> 4;\n                if (!d)\n                    throw 'invalid distance';\n                pos += d & 15;\n                var dt = fd[dsym];\n                if (dsym > 3) {\n                    var b = fdeb[dsym];\n                    dt += bits16(dat, pos) & ((1 << b) - 1), pos += b;\n                }\n                if (pos > tbts) {\n                    if (noSt)\n                        throw 'unexpected EOF';\n                    break;\n                }\n                if (noBuf)\n                    cbuf(bt + 131072);\n                var end = bt + add;\n                for (; bt < end; bt += 4) {\n                    buf[bt] = buf[bt - dt];\n                    buf[bt + 1] = buf[bt + 1 - dt];\n                    buf[bt + 2] = buf[bt + 2 - dt];\n                    buf[bt + 3] = buf[bt + 3 - dt];\n                }\n                bt = end;\n            }\n        }\n        st.l = lm, st.p = lpos, st.b = bt;\n        if (lm)\n            final = 1, st.m = lbt, st.d = dm, st.n = dbt;\n    } while (!final);\n    return bt == buf.length ? buf : slc(buf, 0, bt);\n};\n// starting at p, write the minimum number of bits that can hold v to d\nvar wbits = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) | 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n};\n// starting at p, write the minimum number of bits (>8) that can hold v to d\nvar wbits16 = function (d, p, v) {\n    v <<= p & 7;\n    var o = (p / 8) | 0;\n    d[o] |= v;\n    d[o + 1] |= v >>> 8;\n    d[o + 2] |= v >>> 16;\n};\n// creates code lengths from a frequency table\nvar hTree = function (d, mb) {\n    // Need extra info to make a tree\n    var t = [];\n    for (var i = 0; i < d.length; ++i) {\n        if (d[i])\n            t.push({ s: i, f: d[i] });\n    }\n    var s = t.length;\n    var t2 = t.slice();\n    if (!s)\n        return [et, 0];\n    if (s == 1) {\n        var v = new u8(t[0].s + 1);\n        v[t[0].s] = 1;\n        return [v, 1];\n    }\n    t.sort(function (a, b) { return a.f - b.f; });\n    // after i2 reaches last ind, will be stopped\n    // freq must be greater than largest possible number of symbols\n    t.push({ s: -1, f: 25001 });\n    var l = t[0], r = t[1], i0 = 0, i1 = 1, i2 = 2;\n    t[0] = { s: -1, f: l.f + r.f, l: l, r: r };\n    // efficient algorithm from UZIP.js\n    // i0 is lookbehind, i2 is lookahead - after processing two low-freq\n    // symbols that combined have high freq, will start processing i2 (high-freq,\n    // non-composite) symbols instead\n    // see https://reddit.com/r/photopea/comments/ikekht/uzipjs_questions/\n    while (i1 != s - 1) {\n        l = t[t[i0].f < t[i2].f ? i0++ : i2++];\n        r = t[i0 != i1 && t[i0].f < t[i2].f ? i0++ : i2++];\n        t[i1++] = { s: -1, f: l.f + r.f, l: l, r: r };\n    }\n    var maxSym = t2[0].s;\n    for (var i = 1; i < s; ++i) {\n        if (t2[i].s > maxSym)\n            maxSym = t2[i].s;\n    }\n    // code lengths\n    var tr = new u16(maxSym + 1);\n    // max bits in tree\n    var mbt = ln(t[i1 - 1], tr, 0);\n    if (mbt > mb) {\n        // more algorithms from UZIP.js\n        // TODO: find out how this code works (debt)\n        //  ind    debt\n        var i = 0, dt = 0;\n        //    left            cost\n        var lft = mbt - mb, cst = 1 << lft;\n        t2.sort(function (a, b) { return tr[b.s] - tr[a.s] || a.f - b.f; });\n        for (; i < s; ++i) {\n            var i2_1 = t2[i].s;\n            if (tr[i2_1] > mb) {\n                dt += cst - (1 << (mbt - tr[i2_1]));\n                tr[i2_1] = mb;\n            }\n            else\n                break;\n        }\n        dt >>>= lft;\n        while (dt > 0) {\n            var i2_2 = t2[i].s;\n            if (tr[i2_2] < mb)\n                dt -= 1 << (mb - tr[i2_2]++ - 1);\n            else\n                ++i;\n        }\n        for (; i >= 0 && dt; --i) {\n            var i2_3 = t2[i].s;\n            if (tr[i2_3] == mb) {\n                --tr[i2_3];\n                ++dt;\n            }\n        }\n        mbt = mb;\n    }\n    return [new u8(tr), mbt];\n};\n// get the max length and assign length codes\nvar ln = function (n, l, d) {\n    return n.s == -1\n        ? Math.max(ln(n.l, l, d + 1), ln(n.r, l, d + 1))\n        : (l[n.s] = d);\n};\n// length codes generation\nvar lc = function (c) {\n    var s = c.length;\n    // Note that the semicolon was intentional\n    while (s && !c[--s])\n        ;\n    var cl = new u16(++s);\n    //  ind      num         streak\n    var cli = 0, cln = c[0], cls = 1;\n    var w = function (v) { cl[cli++] = v; };\n    for (var i = 1; i <= s; ++i) {\n        if (c[i] == cln && i != s)\n            ++cls;\n        else {\n            if (!cln && cls > 2) {\n                for (; cls > 138; cls -= 138)\n                    w(32754);\n                if (cls > 2) {\n                    w(cls > 10 ? ((cls - 11) << 5) | 28690 : ((cls - 3) << 5) | 12305);\n                    cls = 0;\n                }\n            }\n            else if (cls > 3) {\n                w(cln), --cls;\n                for (; cls > 6; cls -= 6)\n                    w(8304);\n                if (cls > 2)\n                    w(((cls - 3) << 5) | 8208), cls = 0;\n            }\n            while (cls--)\n                w(cln);\n            cls = 1;\n            cln = c[i];\n        }\n    }\n    return [cl.subarray(0, cli), s];\n};\n// calculate the length of output from tree, code lengths\nvar clen = function (cf, cl) {\n    var l = 0;\n    for (var i = 0; i < cl.length; ++i)\n        l += cf[i] * cl[i];\n    return l;\n};\n// writes a fixed block\n// returns the new bit pos\nvar wfblk = function (out, pos, dat) {\n    // no need to write 00 as type: TypedArray defaults to 0\n    var s = dat.length;\n    var o = shft(pos + 2);\n    out[o] = s & 255;\n    out[o + 1] = s >>> 8;\n    out[o + 2] = out[o] ^ 255;\n    out[o + 3] = out[o + 1] ^ 255;\n    for (var i = 0; i < s; ++i)\n        out[o + i + 4] = dat[i];\n    return (o + 4 + s) * 8;\n};\n// writes a block\nvar wblk = function (dat, out, final, syms, lf, df, eb, li, bs, bl, p) {\n    wbits(out, p++, final);\n    ++lf[256];\n    var _a = hTree(lf, 15), dlt = _a[0], mlb = _a[1];\n    var _b = hTree(df, 15), ddt = _b[0], mdb = _b[1];\n    var _c = lc(dlt), lclt = _c[0], nlc = _c[1];\n    var _d = lc(ddt), lcdt = _d[0], ndc = _d[1];\n    var lcfreq = new u16(19);\n    for (var i = 0; i < lclt.length; ++i)\n        lcfreq[lclt[i] & 31]++;\n    for (var i = 0; i < lcdt.length; ++i)\n        lcfreq[lcdt[i] & 31]++;\n    var _e = hTree(lcfreq, 7), lct = _e[0], mlcb = _e[1];\n    var nlcc = 19;\n    for (; nlcc > 4 && !lct[clim[nlcc - 1]]; --nlcc)\n        ;\n    var flen = (bl + 5) << 3;\n    var ftlen = clen(lf, flt) + clen(df, fdt) + eb;\n    var dtlen = clen(lf, dlt) + clen(df, ddt) + eb + 14 + 3 * nlcc + clen(lcfreq, lct) + (2 * lcfreq[16] + 3 * lcfreq[17] + 7 * lcfreq[18]);\n    if (flen <= ftlen && flen <= dtlen)\n        return wfblk(out, p, dat.subarray(bs, bs + bl));\n    var lm, ll, dm, dl;\n    wbits(out, p, 1 + (dtlen < ftlen)), p += 2;\n    if (dtlen < ftlen) {\n        lm = hMap(dlt, mlb, 0), ll = dlt, dm = hMap(ddt, mdb, 0), dl = ddt;\n        var llm = hMap(lct, mlcb, 0);\n        wbits(out, p, nlc - 257);\n        wbits(out, p + 5, ndc - 1);\n        wbits(out, p + 10, nlcc - 4);\n        p += 14;\n        for (var i = 0; i < nlcc; ++i)\n            wbits(out, p + 3 * i, lct[clim[i]]);\n        p += 3 * nlcc;\n        var lcts = [lclt, lcdt];\n        for (var it = 0; it < 2; ++it) {\n            var clct = lcts[it];\n            for (var i = 0; i < clct.length; ++i) {\n                var len = clct[i] & 31;\n                wbits(out, p, llm[len]), p += lct[len];\n                if (len > 15)\n                    wbits(out, p, (clct[i] >>> 5) & 127), p += clct[i] >>> 12;\n            }\n        }\n    }\n    else {\n        lm = flm, ll = flt, dm = fdm, dl = fdt;\n    }\n    for (var i = 0; i < li; ++i) {\n        if (syms[i] > 255) {\n            var len = (syms[i] >>> 18) & 31;\n            wbits16(out, p, lm[len + 257]), p += ll[len + 257];\n            if (len > 7)\n                wbits(out, p, (syms[i] >>> 23) & 31), p += fleb[len];\n            var dst = syms[i] & 31;\n            wbits16(out, p, dm[dst]), p += dl[dst];\n            if (dst > 3)\n                wbits16(out, p, (syms[i] >>> 5) & 8191), p += fdeb[dst];\n        }\n        else {\n            wbits16(out, p, lm[syms[i]]), p += ll[syms[i]];\n        }\n    }\n    wbits16(out, p, lm[256]);\n    return p + ll[256];\n};\n// deflate options (nice << 13) | chain\nvar deo = /*#__PURE__*/ new u32([65540, 131080, 131088, 131104, 262176, 1048704, 1048832, 2114560, 2117632]);\n// empty\nvar et = /*#__PURE__*/ new u8(0);\n// compresses data into a raw DEFLATE buffer\nvar dflt = function (dat, lvl, plvl, pre, post, lst) {\n    var s = dat.length;\n    var o = new u8(pre + s + 5 * (1 + Math.ceil(s / 7000)) + post);\n    // writing to this writes to the output buffer\n    var w = o.subarray(pre, o.length - post);\n    var pos = 0;\n    if (!lvl || s < 8) {\n        for (var i = 0; i <= s; i += 65535) {\n            // end\n            var e = i + 65535;\n            if (e < s) {\n                // write full block\n                pos = wfblk(w, pos, dat.subarray(i, e));\n            }\n            else {\n                // write final block\n                w[i] = lst;\n                pos = wfblk(w, pos, dat.subarray(i, s));\n            }\n        }\n    }\n    else {\n        var opt = deo[lvl - 1];\n        var n = opt >>> 13, c = opt & 8191;\n        var msk_1 = (1 << plvl) - 1;\n        //    prev 2-byte val map    curr 2-byte val map\n        var prev = new u16(32768), head = new u16(msk_1 + 1);\n        var bs1_1 = Math.ceil(plvl / 3), bs2_1 = 2 * bs1_1;\n        var hsh = function (i) { return (dat[i] ^ (dat[i + 1] << bs1_1) ^ (dat[i + 2] << bs2_1)) & msk_1; };\n        // 24576 is an arbitrary number of maximum symbols per block\n        // 424 buffer for last block\n        var syms = new u32(25000);\n        // length/literal freq   distance freq\n        var lf = new u16(288), df = new u16(32);\n        //  l/lcnt  exbits  index  l/lind  waitdx  bitpos\n        var lc_1 = 0, eb = 0, i = 0, li = 0, wi = 0, bs = 0;\n        for (; i < s; ++i) {\n            // hash value\n            // deopt when i > s - 3 - at end, deopt acceptable\n            var hv = hsh(i);\n            // index mod 32768    previous index mod\n            var imod = i & 32767, pimod = head[hv];\n            prev[imod] = pimod;\n            head[hv] = imod;\n            // We always should modify head and prev, but only add symbols if\n            // this data is not yet processed (\"wait\" for wait index)\n            if (wi <= i) {\n                // bytes remaining\n                var rem = s - i;\n                if ((lc_1 > 7000 || li > 24576) && rem > 423) {\n                    pos = wblk(dat, w, 0, syms, lf, df, eb, li, bs, i - bs, pos);\n                    li = lc_1 = eb = 0, bs = i;\n                    for (var j = 0; j < 286; ++j)\n                        lf[j] = 0;\n                    for (var j = 0; j < 30; ++j)\n                        df[j] = 0;\n                }\n                //  len    dist   chain\n                var l = 2, d = 0, ch_1 = c, dif = (imod - pimod) & 32767;\n                if (rem > 2 && hv == hsh(i - dif)) {\n                    var maxn = Math.min(n, rem) - 1;\n                    var maxd = Math.min(32767, i);\n                    // max possible length\n                    // not capped at dif because decompressors implement \"rolling\" index population\n                    var ml = Math.min(258, rem);\n                    while (dif <= maxd && --ch_1 && imod != pimod) {\n                        if (dat[i + l] == dat[i + l - dif]) {\n                            var nl = 0;\n                            for (; nl < ml && dat[i + nl] == dat[i + nl - dif]; ++nl)\n                                ;\n                            if (nl > l) {\n                                l = nl, d = dif;\n                                // break out early when we reach \"nice\" (we are satisfied enough)\n                                if (nl > maxn)\n                                    break;\n                                // now, find the rarest 2-byte sequence within this\n                                // length of literals and search for that instead.\n                                // Much faster than just using the start\n                                var mmd = Math.min(dif, nl - 2);\n                                var md = 0;\n                                for (var j = 0; j < mmd; ++j) {\n                                    var ti = (i - dif + j + 32768) & 32767;\n                                    var pti = prev[ti];\n                                    var cd = (ti - pti + 32768) & 32767;\n                                    if (cd > md)\n                                        md = cd, pimod = ti;\n                                }\n                            }\n                        }\n                        // check the previous match\n                        imod = pimod, pimod = prev[imod];\n                        dif += (imod - pimod + 32768) & 32767;\n                    }\n                }\n                // d will be nonzero only when a match was found\n                if (d) {\n                    // store both dist and len data in one Uint32\n                    // Make sure this is recognized as a len/dist with 28th bit (2^28)\n                    syms[li++] = 268435456 | (revfl[l] << 18) | revfd[d];\n                    var lin = revfl[l] & 31, din = revfd[d] & 31;\n                    eb += fleb[lin] + fdeb[din];\n                    ++lf[257 + lin];\n                    ++df[din];\n                    wi = i + l;\n                    ++lc_1;\n                }\n                else {\n                    syms[li++] = dat[i];\n                    ++lf[dat[i]];\n                }\n            }\n        }\n        pos = wblk(dat, w, lst, syms, lf, df, eb, li, bs, i - bs, pos);\n        // this is the easiest way to avoid needing to maintain state\n        if (!lst && pos & 7)\n            pos = wfblk(w, pos + 1, et);\n    }\n    return slc(o, 0, pre + shft(pos) + post);\n};\n// CRC32 table\nvar crct = /*#__PURE__*/ (function () {\n    var t = new Int32Array(256);\n    for (var i = 0; i < 256; ++i) {\n        var c = i, k = 9;\n        while (--k)\n            c = ((c & 1) && -306674912) ^ (c >>> 1);\n        t[i] = c;\n    }\n    return t;\n})();\n// CRC32\nvar crc = function () {\n    var c = -1;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var cr = c;\n            for (var i = 0; i < d.length; ++i)\n                cr = crct[(cr & 255) ^ d[i]] ^ (cr >>> 8);\n            c = cr;\n        },\n        d: function () { return ~c; }\n    };\n};\n// Alder32\nvar adler = function () {\n    var a = 1, b = 0;\n    return {\n        p: function (d) {\n            // closures have awful performance\n            var n = a, m = b;\n            var l = d.length;\n            for (var i = 0; i != l;) {\n                var e = Math.min(i + 2655, l);\n                for (; i < e; ++i)\n                    m += n += d[i];\n                n = (n & 65535) + 15 * (n >> 16), m = (m & 65535) + 15 * (m >> 16);\n            }\n            a = n, b = m;\n        },\n        d: function () {\n            a %= 65521, b %= 65521;\n            return (a & 255) << 24 | (a >>> 8) << 16 | (b & 255) << 8 | (b >>> 8);\n        }\n    };\n};\n;\n// deflate with opts\nvar dopt = function (dat, opt, pre, post, st) {\n    return dflt(dat, opt.level == null ? 6 : opt.level, opt.mem == null ? Math.ceil(Math.max(8, Math.min(13, Math.log(dat.length))) * 1.5) : (12 + opt.mem), pre, post, !st);\n};\n// Walmart object spread\nvar mrg = function (a, b) {\n    var o = {};\n    for (var k in a)\n        o[k] = a[k];\n    for (var k in b)\n        o[k] = b[k];\n    return o;\n};\n// worker clone\n// This is possibly the craziest part of the entire codebase, despite how simple it may seem.\n// The only parameter to this function is a closure that returns an array of variables outside of the function scope.\n// We're going to try to figure out the variable names used in the closure as strings because that is crucial for workerization.\n// We will return an object mapping of true variable name to value (basically, the current scope as a JS object).\n// The reason we can't just use the original variable names is minifiers mangling the toplevel scope.\n// This took me three weeks to figure out how to do.\nvar wcln = function (fn, fnStr, td) {\n    var dt = fn();\n    var st = fn.toString();\n    var ks = st.slice(st.indexOf('[') + 1, st.lastIndexOf(']')).replace(/ /g, '').split(',');\n    for (var i = 0; i < dt.length; ++i) {\n        var v = dt[i], k = ks[i];\n        if (typeof v == 'function') {\n            fnStr += ';' + k + '=';\n            var st_1 = v.toString();\n            if (v.prototype) {\n                // for global objects\n                if (st_1.indexOf('[native code]') != -1) {\n                    var spInd = st_1.indexOf(' ', 8) + 1;\n                    fnStr += st_1.slice(spInd, st_1.indexOf('(', spInd));\n                }\n                else {\n                    fnStr += st_1;\n                    for (var t in v.prototype)\n                        fnStr += ';' + k + '.prototype.' + t + '=' + v.prototype[t].toString();\n                }\n            }\n            else\n                fnStr += st_1;\n        }\n        else\n            td[k] = v;\n    }\n    return [fnStr, td];\n};\nvar ch = [];\n// clone bufs\nvar cbfs = function (v) {\n    var tl = [];\n    for (var k in v) {\n        if (v[k] instanceof u8 || v[k] instanceof u16 || v[k] instanceof u32)\n            tl.push((v[k] = new v[k].constructor(v[k])).buffer);\n    }\n    return tl;\n};\n// use a worker to execute code\nvar wrkr = function (fns, init, id, cb) {\n    var _a;\n    if (!ch[id]) {\n        var fnStr = '', td_1 = {}, m = fns.length - 1;\n        for (var i = 0; i < m; ++i)\n            _a = wcln(fns[i], fnStr, td_1), fnStr = _a[0], td_1 = _a[1];\n        ch[id] = wcln(fns[m], fnStr, td_1);\n    }\n    var td = mrg({}, ch[id][1]);\n    return wk(ch[id][0] + ';onmessage=function(e){for(var k in e.data)self[k]=e.data[k];onmessage=' + init.toString() + '}', id, td, cbfs(td), cb);\n};\n// base async inflate fn\nvar bInflt = function () { return [u8, u16, u32, fleb, fdeb, clim, fl, fd, flrm, fdrm, rev, hMap, max, bits, bits16, shft, slc, inflt, inflateSync, pbf, gu8]; };\nvar bDflt = function () { return [u8, u16, u32, fleb, fdeb, clim, revfl, revfd, flm, flt, fdm, fdt, rev, deo, et, hMap, wbits, wbits16, hTree, ln, lc, clen, wfblk, wblk, shft, slc, dflt, dopt, deflateSync, pbf]; };\n// gzip extra\nvar gze = function () { return [gzh, gzhl, wbytes, crc, crct]; };\n// gunzip extra\nvar guze = function () { return [gzs, gzl]; };\n// zlib extra\nvar zle = function () { return [zlh, wbytes, adler]; };\n// unzlib extra\nvar zule = function () { return [zlv]; };\n// post buf\nvar pbf = function (msg) { return postMessage(msg, [msg.buffer]); };\n// get u8\nvar gu8 = function (o) { return o && o.size && new u8(o.size); };\n// async helper\nvar cbify = function (dat, opts, fns, init, id, cb) {\n    var w = wrkr(fns, init, id, function (err, dat) {\n        w.terminate();\n        cb(err, dat);\n    });\n    w.postMessage([dat, opts], opts.consume ? [dat.buffer] : []);\n    return function () { w.terminate(); };\n};\n// auto stream\nvar astrm = function (strm) {\n    strm.ondata = function (dat, final) { return postMessage([dat, final], [dat.buffer]); };\n    return function (ev) { return strm.push(ev.data[0], ev.data[1]); };\n};\n// async stream attach\nvar astrmify = function (fns, strm, opts, init, id) {\n    var t;\n    var w = wrkr(fns, init, id, function (err, dat) {\n        if (err)\n            w.terminate(), strm.ondata.call(strm, err);\n        else {\n            if (dat[1])\n                w.terminate();\n            strm.ondata.call(strm, err, dat[0], dat[1]);\n        }\n    });\n    w.postMessage(opts);\n    strm.push = function (d, f) {\n        if (t)\n            throw 'stream finished';\n        if (!strm.ondata)\n            throw 'no stream handler';\n        w.postMessage([d, t = f], [d.buffer]);\n    };\n    strm.terminate = function () { w.terminate(); };\n};\n// read 2 bytes\nvar b2 = function (d, b) { return d[b] | (d[b + 1] << 8); };\n// read 4 bytes\nvar b4 = function (d, b) { return (d[b] | (d[b + 1] << 8) | (d[b + 2] << 16) | (d[b + 3] << 24)) >>> 0; };\nvar b8 = function (d, b) { return b4(d, b) + (b4(d, b + 4) * 4294967296); };\n// write bytes\nvar wbytes = function (d, b, v) {\n    for (; v; ++b)\n        d[b] = v, v >>>= 8;\n};\n// gzip header\nvar gzh = function (c, o) {\n    var fn = o.filename;\n    c[0] = 31, c[1] = 139, c[2] = 8, c[8] = o.level < 2 ? 4 : o.level == 9 ? 2 : 0, c[9] = 3; // assume Unix\n    if (o.mtime != 0)\n        wbytes(c, 4, Math.floor(new Date(o.mtime || Date.now()) / 1000));\n    if (fn) {\n        c[3] = 8;\n        for (var i = 0; i <= fn.length; ++i)\n            c[i + 10] = fn.charCodeAt(i);\n    }\n};\n// gzip footer: -8 to -4 = CRC, -4 to -0 is length\n// gzip start\nvar gzs = function (d) {\n    if (d[0] != 31 || d[1] != 139 || d[2] != 8)\n        throw 'invalid gzip data';\n    var flg = d[3];\n    var st = 10;\n    if (flg & 4)\n        st += d[10] | (d[11] << 8) + 2;\n    for (var zs = (flg >> 3 & 1) + (flg >> 4 & 1); zs > 0; zs -= !d[st++])\n        ;\n    return st + (flg & 2);\n};\n// gzip length\nvar gzl = function (d) {\n    var l = d.length;\n    return ((d[l - 4] | d[l - 3] << 8 | d[l - 2] << 16) | (d[l - 1] << 24)) >>> 0;\n};\n// gzip header length\nvar gzhl = function (o) { return 10 + ((o.filename && (o.filename.length + 1)) || 0); };\n// zlib header\nvar zlh = function (c, o) {\n    var lv = o.level, fl = lv == 0 ? 0 : lv < 6 ? 1 : lv == 9 ? 3 : 2;\n    c[0] = 120, c[1] = (fl << 6) | (fl ? (32 - 2 * fl) : 1);\n};\n// zlib valid\nvar zlv = function (d) {\n    if ((d[0] & 15) != 8 || (d[0] >>> 4) > 7 || ((d[0] << 8 | d[1]) % 31))\n        throw 'invalid zlib data';\n    if (d[1] & 32)\n        throw 'invalid zlib data: preset dictionaries not supported';\n};\nfunction AsyncCmpStrm(opts, cb) {\n    if (!cb && typeof opts == 'function')\n        cb = opts, opts = {};\n    this.ondata = cb;\n    return opts;\n}\n// zlib footer: -4 to -0 is Adler32\n/**\n * Streaming DEFLATE compression\n */\nvar Deflate = /*#__PURE__*/ (function () {\n    function Deflate(opts, cb) {\n        if (!cb && typeof opts == 'function')\n            cb = opts, opts = {};\n        this.ondata = cb;\n        this.o = opts || {};\n    }\n    Deflate.prototype.p = function (c, f) {\n        this.ondata(dopt(c, this.o, 0, 0, !f), f);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Deflate.prototype.push = function (chunk, final) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        this.d = final;\n        this.p(chunk, final || false);\n    };\n    return Deflate;\n}());\nexport { Deflate };\n/**\n * Asynchronous streaming DEFLATE compression\n */\nvar AsyncDeflate = /*#__PURE__*/ (function () {\n    function AsyncDeflate(opts, cb) {\n        astrmify([\n            bDflt,\n            function () { return [astrm, Deflate]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Deflate(ev.data);\n            onmessage = astrm(strm);\n        }, 6);\n    }\n    return AsyncDeflate;\n}());\nexport { AsyncDeflate };\nexport function deflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n    ], function (ev) { return pbf(deflateSync(ev.data[0], ev.data[1])); }, 0, cb);\n}\n/**\n * Compresses data with DEFLATE without any wrapper\n * @param data The data to compress\n * @param opts The compression options\n * @returns The deflated version of the data\n */\nexport function deflateSync(data, opts) {\n    return dopt(data, opts || {}, 0, 0);\n}\n/**\n * Streaming DEFLATE decompression\n */\nvar Inflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an inflation stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Inflate(cb) {\n        this.s = {};\n        this.p = new u8(0);\n        this.ondata = cb;\n    }\n    Inflate.prototype.e = function (c) {\n        if (this.d)\n            throw 'stream finished';\n        if (!this.ondata)\n            throw 'no stream handler';\n        var l = this.p.length;\n        var n = new u8(l + c.length);\n        n.set(this.p), n.set(c, l), this.p = n;\n    };\n    Inflate.prototype.c = function (final) {\n        this.d = this.s.i = final || false;\n        var bts = this.s.b;\n        var dt = inflt(this.p, this.o, this.s);\n        this.ondata(slc(dt, bts, this.s.b), this.d);\n        this.o = slc(dt, this.s.b - 32768), this.s.b = this.o.length;\n        this.p = slc(this.p, (this.s.p / 8) | 0), this.s.p &= 7;\n    };\n    /**\n     * Pushes a chunk to be inflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the final chunk\n     */\n    Inflate.prototype.push = function (chunk, final) {\n        this.e(chunk), this.c(final);\n    };\n    return Inflate;\n}());\nexport { Inflate };\n/**\n * Asynchronous streaming DEFLATE decompression\n */\nvar AsyncInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous inflation stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncInflate(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            function () { return [astrm, Inflate]; }\n        ], this, 0, function () {\n            var strm = new Inflate();\n            onmessage = astrm(strm);\n        }, 7);\n    }\n    return AsyncInflate;\n}());\nexport { AsyncInflate };\nexport function inflate(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt\n    ], function (ev) { return pbf(inflateSync(ev.data[0], gu8(ev.data[1]))); }, 1, cb);\n}\n/**\n * Expands DEFLATE data with no wrapper\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function inflateSync(data, out) {\n    return inflt(data, out);\n}\n// before you yell at me for not just using extends, my reason is that TS inheritance is hard to workerize.\n/**\n * Streaming GZIP compression\n */\nvar Gzip = /*#__PURE__*/ (function () {\n    function Gzip(opts, cb) {\n        this.c = crc();\n        this.l = 0;\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be GZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gzip.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Gzip.prototype.p = function (c, f) {\n        this.c.p(c);\n        this.l += c.length;\n        var raw = dopt(c, this.o, this.v && gzhl(this.o), f && 8, !f);\n        if (this.v)\n            gzh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 8, this.c.d()), wbytes(raw, raw.length - 4, this.l);\n        this.ondata(raw, f);\n    };\n    return Gzip;\n}());\nexport { Gzip };\n/**\n * Asynchronous streaming GZIP compression\n */\nvar AsyncGzip = /*#__PURE__*/ (function () {\n    function AsyncGzip(opts, cb) {\n        astrmify([\n            bDflt,\n            gze,\n            function () { return [astrm, Deflate, Gzip]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Gzip(ev.data);\n            onmessage = astrm(strm);\n        }, 8);\n    }\n    return AsyncGzip;\n}());\nexport { AsyncGzip };\nexport function gzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        gze,\n        function () { return [gzipSync]; }\n    ], function (ev) { return pbf(gzipSync(ev.data[0], ev.data[1])); }, 2, cb);\n}\n/**\n * Compresses data with GZIP\n * @param data The data to compress\n * @param opts The compression options\n * @returns The gzipped version of the data\n */\nexport function gzipSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var c = crc(), l = data.length;\n    c.p(data);\n    var d = dopt(data, opts, gzhl(opts), 8), s = d.length;\n    return gzh(d, opts), wbytes(d, s - 8, c.d()), wbytes(d, s - 4, l), d;\n}\n/**\n * Streaming GZIP decompression\n */\nvar Gunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a GUNZIP stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Gunzip(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be GUNZIPped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Gunzip.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            var s = this.p.length > 3 ? gzs(this.p) : 4;\n            if (s >= this.p.length && !final)\n                return;\n            this.p = this.p.subarray(s), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 8)\n                throw 'invalid gzip stream';\n            this.p = this.p.subarray(0, -8);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Gunzip;\n}());\nexport { Gunzip };\n/**\n * Asynchronous streaming GZIP decompression\n */\nvar AsyncGunzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous GUNZIP stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncGunzip(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            guze,\n            function () { return [astrm, Inflate, Gunzip]; }\n        ], this, 0, function () {\n            var strm = new Gunzip();\n            onmessage = astrm(strm);\n        }, 9);\n    }\n    return AsyncGunzip;\n}());\nexport { AsyncGunzip };\nexport function gunzip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        guze,\n        function () { return [gunzipSync]; }\n    ], function (ev) { return pbf(gunzipSync(ev.data[0])); }, 3, cb);\n}\n/**\n * Expands GZIP data\n * @param data The data to decompress\n * @param out Where to write the data. GZIP already encodes the output size, so providing this doesn't save memory.\n * @returns The decompressed version of the data\n */\nexport function gunzipSync(data, out) {\n    return inflt(data.subarray(gzs(data), -8), out || new u8(gzl(data)));\n}\n/**\n * Streaming Zlib compression\n */\nvar Zlib = /*#__PURE__*/ (function () {\n    function Zlib(opts, cb) {\n        this.c = adler();\n        this.v = 1;\n        Deflate.call(this, opts, cb);\n    }\n    /**\n     * Pushes a chunk to be zlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Zlib.prototype.push = function (chunk, final) {\n        Deflate.prototype.push.call(this, chunk, final);\n    };\n    Zlib.prototype.p = function (c, f) {\n        this.c.p(c);\n        var raw = dopt(c, this.o, this.v && 2, f && 4, !f);\n        if (this.v)\n            zlh(raw, this.o), this.v = 0;\n        if (f)\n            wbytes(raw, raw.length - 4, this.c.d());\n        this.ondata(raw, f);\n    };\n    return Zlib;\n}());\nexport { Zlib };\n/**\n * Asynchronous streaming Zlib compression\n */\nvar AsyncZlib = /*#__PURE__*/ (function () {\n    function AsyncZlib(opts, cb) {\n        astrmify([\n            bDflt,\n            zle,\n            function () { return [astrm, Deflate, Zlib]; }\n        ], this, AsyncCmpStrm.call(this, opts, cb), function (ev) {\n            var strm = new Zlib(ev.data);\n            onmessage = astrm(strm);\n        }, 10);\n    }\n    return AsyncZlib;\n}());\nexport { AsyncZlib };\nexport function zlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bDflt,\n        zle,\n        function () { return [zlibSync]; }\n    ], function (ev) { return pbf(zlibSync(ev.data[0], ev.data[1])); }, 4, cb);\n}\n/**\n * Compress data with Zlib\n * @param data The data to compress\n * @param opts The compression options\n * @returns The zlib-compressed version of the data\n */\nexport function zlibSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var a = adler();\n    a.p(data);\n    var d = dopt(data, opts, 2, 4);\n    return zlh(d, opts), wbytes(d, d.length - 4, a.d()), d;\n}\n/**\n * Streaming Zlib decompression\n */\nvar Unzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates a Zlib decompression stream\n     * @param cb The callback to call whenever data is inflated\n     */\n    function Unzlib(cb) {\n        this.v = 1;\n        Inflate.call(this, cb);\n    }\n    /**\n     * Pushes a chunk to be unzlibbed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzlib.prototype.push = function (chunk, final) {\n        Inflate.prototype.e.call(this, chunk);\n        if (this.v) {\n            if (this.p.length < 2 && !final)\n                return;\n            this.p = this.p.subarray(2), this.v = 0;\n        }\n        if (final) {\n            if (this.p.length < 4)\n                throw 'invalid zlib stream';\n            this.p = this.p.subarray(0, -4);\n        }\n        // necessary to prevent TS from using the closure value\n        // This allows for workerization to function correctly\n        Inflate.prototype.c.call(this, final);\n    };\n    return Unzlib;\n}());\nexport { Unzlib };\n/**\n * Asynchronous streaming Zlib decompression\n */\nvar AsyncUnzlib = /*#__PURE__*/ (function () {\n    /**\n     * Creates an asynchronous Zlib decompression stream\n     * @param cb The callback to call whenever data is deflated\n     */\n    function AsyncUnzlib(cb) {\n        this.ondata = cb;\n        astrmify([\n            bInflt,\n            zule,\n            function () { return [astrm, Inflate, Unzlib]; }\n        ], this, 0, function () {\n            var strm = new Unzlib();\n            onmessage = astrm(strm);\n        }, 11);\n    }\n    return AsyncUnzlib;\n}());\nexport { AsyncUnzlib };\nexport function unzlib(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return cbify(data, opts, [\n        bInflt,\n        zule,\n        function () { return [unzlibSync]; }\n    ], function (ev) { return pbf(unzlibSync(ev.data[0], gu8(ev.data[1]))); }, 5, cb);\n}\n/**\n * Expands Zlib data\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function unzlibSync(data, out) {\n    return inflt((zlv(data), data.subarray(2, -4)), out);\n}\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzip as compress, AsyncGzip as AsyncCompress };\n// Default algorithm for compression (used because having a known output size allows faster decompression)\nexport { gzipSync as compressSync, Gzip as Compress };\n/**\n * Streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar Decompress = /*#__PURE__*/ (function () {\n    /**\n     * Creates a decompression stream\n     * @param cb The callback to call whenever data is decompressed\n     */\n    function Decompress(cb) {\n        this.G = Gunzip;\n        this.I = Inflate;\n        this.Z = Unzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Decompress.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no stream handler';\n        if (!this.s) {\n            if (this.p && this.p.length) {\n                var n = new u8(this.p.length + chunk.length);\n                n.set(this.p), n.set(chunk, this.p.length);\n            }\n            else\n                this.p = chunk;\n            if (this.p.length > 2) {\n                var _this_1 = this;\n                var cb = function () { _this_1.ondata.apply(_this_1, arguments); };\n                this.s = (this.p[0] == 31 && this.p[1] == 139 && this.p[2] == 8)\n                    ? new this.G(cb)\n                    : ((this.p[0] & 15) != 8 || (this.p[0] >> 4) > 7 || ((this.p[0] << 8 | this.p[1]) % 31))\n                        ? new this.I(cb)\n                        : new this.Z(cb);\n                this.s.push(this.p, final);\n                this.p = null;\n            }\n        }\n        else\n            this.s.push(chunk, final);\n    };\n    return Decompress;\n}());\nexport { Decompress };\n/**\n * Asynchronous streaming GZIP, Zlib, or raw DEFLATE decompression\n */\nvar AsyncDecompress = /*#__PURE__*/ (function () {\n    /**\n   * Creates an asynchronous decompression stream\n   * @param cb The callback to call whenever data is decompressed\n   */\n    function AsyncDecompress(cb) {\n        this.G = AsyncGunzip;\n        this.I = AsyncInflate;\n        this.Z = AsyncUnzlib;\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be decompressed\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncDecompress.prototype.push = function (chunk, final) {\n        Decompress.prototype.push.call(this, chunk, final);\n    };\n    return AsyncDecompress;\n}());\nexport { AsyncDecompress };\nexport function decompress(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzip(data, opts, cb)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflate(data, opts, cb)\n            : unzlib(data, opts, cb);\n}\n/**\n * Expands compressed GZIP, Zlib, or raw DEFLATE data, automatically detecting the format\n * @param data The data to decompress\n * @param out Where to write the data. Saves memory if you know the decompressed size and provide an output buffer of that length.\n * @returns The decompressed version of the data\n */\nexport function decompressSync(data, out) {\n    return (data[0] == 31 && data[1] == 139 && data[2] == 8)\n        ? gunzipSync(data, out)\n        : ((data[0] & 15) != 8 || (data[0] >> 4) > 7 || ((data[0] << 8 | data[1]) % 31))\n            ? inflateSync(data, out)\n            : unzlibSync(data, out);\n}\n// flatten a directory structure\nvar fltn = function (d, p, t, o) {\n    for (var k in d) {\n        var val = d[k], n = p + k;\n        if (val instanceof u8)\n            t[n] = [val, o];\n        else if (Array.isArray(val))\n            t[n] = [val[0], mrg(o, val[1])];\n        else\n            fltn(val, n + '/', t, o);\n    }\n};\n// text encoder\nvar te = typeof TextEncoder != 'undefined' && /*#__PURE__*/ new TextEncoder();\n// text decoder\nvar td = typeof TextDecoder != 'undefined' && /*#__PURE__*/ new TextDecoder();\n// text decoder stream\nvar tds = 0;\ntry {\n    td.decode(et, { stream: true });\n    tds = 1;\n}\ncatch (e) { }\n// decode UTF8\nvar dutf8 = function (d) {\n    for (var r = '', i = 0;;) {\n        var c = d[i++];\n        var eb = (c > 127) + (c > 223) + (c > 239);\n        if (i + eb > d.length)\n            return [r, slc(d, i - 1)];\n        if (!eb)\n            r += String.fromCharCode(c);\n        else if (eb == 3) {\n            c = ((c & 15) << 18 | (d[i++] & 63) << 12 | (d[i++] & 63) << 6 | (d[i++] & 63)) - 65536,\n                r += String.fromCharCode(55296 | (c >> 10), 56320 | (c & 1023));\n        }\n        else if (eb & 1)\n            r += String.fromCharCode((c & 31) << 6 | (d[i++] & 63));\n        else\n            r += String.fromCharCode((c & 15) << 12 | (d[i++] & 63) << 6 | (d[i++] & 63));\n    }\n};\n/**\n * Streaming UTF-8 decoding\n */\nvar DecodeUTF8 = /*#__PURE__*/ (function () {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is decoded\n     */\n    function DecodeUTF8(cb) {\n        this.ondata = cb;\n        if (tds)\n            this.t = new TextDecoder();\n        else\n            this.p = et;\n    }\n    /**\n     * Pushes a chunk to be decoded from UTF-8 binary\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    DecodeUTF8.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback';\n        final = !!final;\n        if (this.t) {\n            this.ondata(this.t.decode(chunk, { stream: true }), final);\n            if (final) {\n                if (this.t.decode().length)\n                    throw 'invalid utf-8 data';\n                this.t = null;\n            }\n            return;\n        }\n        if (!this.p)\n            throw 'stream finished';\n        var dat = new u8(this.p.length + chunk.length);\n        dat.set(this.p);\n        dat.set(chunk, this.p.length);\n        var _a = dutf8(dat), ch = _a[0], np = _a[1];\n        if (final) {\n            if (np.length)\n                throw 'invalid utf-8 data';\n            this.p = null;\n        }\n        else\n            this.p = np;\n        this.ondata(ch, final);\n    };\n    return DecodeUTF8;\n}());\nexport { DecodeUTF8 };\n/**\n * Streaming UTF-8 encoding\n */\nvar EncodeUTF8 = /*#__PURE__*/ (function () {\n    /**\n     * Creates a UTF-8 decoding stream\n     * @param cb The callback to call whenever data is encoded\n     */\n    function EncodeUTF8(cb) {\n        this.ondata = cb;\n    }\n    /**\n     * Pushes a chunk to be encoded to UTF-8\n     * @param chunk The string data to push\n     * @param final Whether this is the last chunk\n     */\n    EncodeUTF8.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback';\n        if (this.d)\n            throw 'stream finished';\n        this.ondata(strToU8(chunk), this.d = final || false);\n    };\n    return EncodeUTF8;\n}());\nexport { EncodeUTF8 };\n/**\n * Converts a string into a Uint8Array for use with compression/decompression methods\n * @param str The string to encode\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless decoding a binary string.\n * @returns The string encoded in UTF-8/Latin-1 binary\n */\nexport function strToU8(str, latin1) {\n    if (latin1) {\n        var ar_1 = new u8(str.length);\n        for (var i = 0; i < str.length; ++i)\n            ar_1[i] = str.charCodeAt(i);\n        return ar_1;\n    }\n    if (te)\n        return te.encode(str);\n    var l = str.length;\n    var ar = new u8(str.length + (str.length >> 1));\n    var ai = 0;\n    var w = function (v) { ar[ai++] = v; };\n    for (var i = 0; i < l; ++i) {\n        if (ai + 5 > ar.length) {\n            var n = new u8(ai + 8 + ((l - i) << 1));\n            n.set(ar);\n            ar = n;\n        }\n        var c = str.charCodeAt(i);\n        if (c < 128 || latin1)\n            w(c);\n        else if (c < 2048)\n            w(192 | (c >> 6)), w(128 | (c & 63));\n        else if (c > 55295 && c < 57344)\n            c = 65536 + (c & 1023 << 10) | (str.charCodeAt(++i) & 1023),\n                w(240 | (c >> 18)), w(128 | ((c >> 12) & 63)), w(128 | ((c >> 6) & 63)), w(128 | (c & 63));\n        else\n            w(224 | (c >> 12)), w(128 | ((c >> 6) & 63)), w(128 | (c & 63));\n    }\n    return slc(ar, 0, ai);\n}\n/**\n * Converts a Uint8Array to a string\n * @param dat The data to decode to string\n * @param latin1 Whether or not to interpret the data as Latin-1. This should\n *               not need to be true unless encoding to binary string.\n * @returns The original UTF-8/Latin-1 string\n */\nexport function strFromU8(dat, latin1) {\n    if (latin1) {\n        var r = '';\n        for (var i = 0; i < dat.length; i += 16384)\n            r += String.fromCharCode.apply(null, dat.subarray(i, i + 16384));\n        return r;\n    }\n    else if (td)\n        return td.decode(dat);\n    else {\n        var _a = dutf8(dat), out = _a[0], ext = _a[1];\n        if (ext.length)\n            throw 'invalid utf-8 data';\n        return out;\n    }\n}\n;\n// deflate bit flag\nvar dbf = function (l) { return l == 1 ? 3 : l < 6 ? 2 : l == 9 ? 1 : 0; };\n// skip local zip header\nvar slzh = function (d, b) { return b + 30 + b2(d, b + 26) + b2(d, b + 28); };\n// read zip header\nvar zh = function (d, b, z) {\n    var fnl = b2(d, b + 28), fn = strFromU8(d.subarray(b + 46, b + 46 + fnl), !(b2(d, b + 8) & 2048)), es = b + 46 + fnl, bs = b4(d, b + 20);\n    var _a = z && bs == 4294967295 ? z64e(d, es) : [bs, b4(d, b + 24), b4(d, b + 42)], sc = _a[0], su = _a[1], off = _a[2];\n    return [b2(d, b + 10), sc, su, fn, es + b2(d, b + 30) + b2(d, b + 32), off];\n};\n// read zip64 extra field\nvar z64e = function (d, b) {\n    for (; b2(d, b) != 1; b += 4 + b2(d, b + 2))\n        ;\n    return [b8(d, b + 12), b8(d, b + 4), b8(d, b + 20)];\n};\n// extra field length\nvar exfl = function (ex) {\n    var le = 0;\n    if (ex) {\n        for (var k in ex) {\n            var l = ex[k].length;\n            if (l > 65535)\n                throw 'extra field too long';\n            le += l + 4;\n        }\n    }\n    return le;\n};\n// write zip header\nvar wzh = function (d, b, f, fn, u, c, ce, co) {\n    var fl = fn.length, ex = f.extra, col = co && co.length;\n    var exl = exfl(ex);\n    wbytes(d, b, ce != null ? 0x2014B50 : 0x4034B50), b += 4;\n    if (ce != null)\n        d[b++] = 20, d[b++] = f.os;\n    d[b] = 20, b += 2; // spec compliance? what's that?\n    d[b++] = (f.flag << 1) | (c == null && 8), d[b++] = u && 8;\n    d[b++] = f.compression & 255, d[b++] = f.compression >> 8;\n    var dt = new Date(f.mtime == null ? Date.now() : f.mtime), y = dt.getFullYear() - 1980;\n    if (y < 0 || y > 119)\n        throw 'date not in range 1980-2099';\n    wbytes(d, b, (y << 25) | ((dt.getMonth() + 1) << 21) | (dt.getDate() << 16) | (dt.getHours() << 11) | (dt.getMinutes() << 5) | (dt.getSeconds() >>> 1)), b += 4;\n    if (c != null) {\n        wbytes(d, b, f.crc);\n        wbytes(d, b + 4, c);\n        wbytes(d, b + 8, f.size);\n    }\n    wbytes(d, b + 12, fl);\n    wbytes(d, b + 14, exl), b += 16;\n    if (ce != null) {\n        wbytes(d, b, col);\n        wbytes(d, b + 6, f.attrs);\n        wbytes(d, b + 10, ce), b += 14;\n    }\n    d.set(fn, b);\n    b += fl;\n    if (exl) {\n        for (var k in ex) {\n            var exf = ex[k], l = exf.length;\n            wbytes(d, b, +k);\n            wbytes(d, b + 2, l);\n            d.set(exf, b + 4), b += 4 + l;\n        }\n    }\n    if (col)\n        d.set(co, b), b += col;\n    return b;\n};\n// write zip footer (end of central directory)\nvar wzf = function (o, b, c, d, e) {\n    wbytes(o, b, 0x6054B50); // skip disk\n    wbytes(o, b + 8, c);\n    wbytes(o, b + 10, c);\n    wbytes(o, b + 12, d);\n    wbytes(o, b + 16, e);\n};\n/**\n * A pass-through stream to keep data uncompressed in a ZIP archive.\n */\nvar ZipPassThrough = /*#__PURE__*/ (function () {\n    /**\n     * Creates a pass-through stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     */\n    function ZipPassThrough(filename) {\n        this.filename = filename;\n        this.c = crc();\n        this.size = 0;\n        this.compression = 0;\n    }\n    /**\n     * Processes a chunk and pushes to the output stream. You can override this\n     * method in a subclass for custom behavior, but by default this passes\n     * the data through. You must call this.ondata(err, chunk, final) at some\n     * point in this method.\n     * @param chunk The chunk to process\n     * @param final Whether this is the last chunk\n     */\n    ZipPassThrough.prototype.process = function (chunk, final) {\n        this.ondata(null, chunk, final);\n    };\n    /**\n     * Pushes a chunk to be added. If you are subclassing this with a custom\n     * compression algorithm, note that you must push data from the source\n     * file only, pre-compression.\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    ZipPassThrough.prototype.push = function (chunk, final) {\n        if (!this.ondata)\n            throw 'no callback - add to ZIP archive before pushing';\n        this.c.p(chunk);\n        this.size += chunk.length;\n        if (final)\n            this.crc = this.c.d();\n        this.process(chunk, final || false);\n    };\n    return ZipPassThrough;\n}());\nexport { ZipPassThrough };\n// I don't extend because TypeScript extension adds 1kB of runtime bloat\n/**\n * Streaming DEFLATE compression for ZIP archives. Prefer using AsyncZipDeflate\n * for better performance\n */\nvar ZipDeflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */\n    function ZipDeflate(filename, opts) {\n        var _this_1 = this;\n        if (!opts)\n            opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new Deflate(opts, function (dat, final) {\n            _this_1.ondata(null, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n    }\n    ZipDeflate.prototype.process = function (chunk, final) {\n        try {\n            this.d.push(chunk, final);\n        }\n        catch (e) {\n            this.ondata(e, null, final);\n        }\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    ZipDeflate.prototype.push = function (chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return ZipDeflate;\n}());\nexport { ZipDeflate };\n/**\n * Asynchronous streaming DEFLATE compression for ZIP archives\n */\nvar AsyncZipDeflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE stream that can be added to ZIP archives\n     * @param filename The filename to associate with this data stream\n     * @param opts The compression options\n     */\n    function AsyncZipDeflate(filename, opts) {\n        var _this_1 = this;\n        if (!opts)\n            opts = {};\n        ZipPassThrough.call(this, filename);\n        this.d = new AsyncDeflate(opts, function (err, dat, final) {\n            _this_1.ondata(err, dat, final);\n        });\n        this.compression = 8;\n        this.flag = dbf(opts.level);\n        this.terminate = this.d.terminate;\n    }\n    AsyncZipDeflate.prototype.process = function (chunk, final) {\n        this.d.push(chunk, final);\n    };\n    /**\n     * Pushes a chunk to be deflated\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    AsyncZipDeflate.prototype.push = function (chunk, final) {\n        ZipPassThrough.prototype.push.call(this, chunk, final);\n    };\n    return AsyncZipDeflate;\n}());\nexport { AsyncZipDeflate };\n// TODO: Better tree shaking\n/**\n * A zippable archive to which files can incrementally be added\n */\nvar Zip = /*#__PURE__*/ (function () {\n    /**\n     * Creates an empty ZIP archive to which files can be added\n     * @param cb The callback to call whenever data for the generated ZIP archive\n     *           is available\n     */\n    function Zip(cb) {\n        this.ondata = cb;\n        this.u = [];\n        this.d = 1;\n    }\n    /**\n     * Adds a file to the ZIP archive\n     * @param file The file stream to add\n     */\n    Zip.prototype.add = function (file) {\n        var _this_1 = this;\n        if (this.d & 2)\n            throw 'stream finished';\n        var f = strToU8(file.filename), fl = f.length;\n        var com = file.comment, o = com && strToU8(com);\n        var u = fl != file.filename.length || (o && (com.length != o.length));\n        var hl = fl + exfl(file.extra) + 30;\n        if (fl > 65535)\n            throw 'filename too long';\n        var header = new u8(hl);\n        wzh(header, 0, file, f, u);\n        var chks = [header];\n        var pAll = function () {\n            for (var _i = 0, chks_1 = chks; _i < chks_1.length; _i++) {\n                var chk = chks_1[_i];\n                _this_1.ondata(null, chk, false);\n            }\n            chks = [];\n        };\n        var tr = this.d;\n        this.d = 0;\n        var ind = this.u.length;\n        var uf = mrg(file, {\n            f: f,\n            u: u,\n            o: o,\n            t: function () {\n                if (file.terminate)\n                    file.terminate();\n            },\n            r: function () {\n                pAll();\n                if (tr) {\n                    var nxt = _this_1.u[ind + 1];\n                    if (nxt)\n                        nxt.r();\n                    else\n                        _this_1.d = 1;\n                }\n                tr = 1;\n            }\n        });\n        var cl = 0;\n        file.ondata = function (err, dat, final) {\n            if (err) {\n                _this_1.ondata(err, dat, final);\n                _this_1.terminate();\n            }\n            else {\n                cl += dat.length;\n                chks.push(dat);\n                if (final) {\n                    var dd = new u8(16);\n                    wbytes(dd, 0, 0x8074B50);\n                    wbytes(dd, 4, file.crc);\n                    wbytes(dd, 8, cl);\n                    wbytes(dd, 12, file.size);\n                    chks.push(dd);\n                    uf.c = cl, uf.b = hl + cl + 16, uf.crc = file.crc, uf.size = file.size;\n                    if (tr)\n                        uf.r();\n                    tr = 1;\n                }\n                else if (tr)\n                    pAll();\n            }\n        };\n        this.u.push(uf);\n    };\n    /**\n     * Ends the process of adding files and prepares to emit the final chunks.\n     * This *must* be called after adding all desired files for the resulting\n     * ZIP file to work properly.\n     */\n    Zip.prototype.end = function () {\n        var _this_1 = this;\n        if (this.d & 2) {\n            if (this.d & 1)\n                throw 'stream finishing';\n            throw 'stream finished';\n        }\n        if (this.d)\n            this.e();\n        else\n            this.u.push({\n                r: function () {\n                    if (!(_this_1.d & 1))\n                        return;\n                    _this_1.u.splice(-1, 1);\n                    _this_1.e();\n                },\n                t: function () { }\n            });\n        this.d = 3;\n    };\n    Zip.prototype.e = function () {\n        var bt = 0, l = 0, tl = 0;\n        for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n            var f = _a[_i];\n            tl += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0);\n        }\n        var out = new u8(tl + 22);\n        for (var _b = 0, _c = this.u; _b < _c.length; _b++) {\n            var f = _c[_b];\n            wzh(out, bt, f, f.f, f.u, f.c, l, f.o);\n            bt += 46 + f.f.length + exfl(f.extra) + (f.o ? f.o.length : 0), l += f.b;\n        }\n        wzf(out, bt, this.u.length, tl, l);\n        this.ondata(null, out, true);\n        this.d = 2;\n    };\n    /**\n     * A method to terminate any internal workers used by the stream. Subsequent\n     * calls to add() will fail.\n     */\n    Zip.prototype.terminate = function () {\n        for (var _i = 0, _a = this.u; _i < _a.length; _i++) {\n            var f = _a[_i];\n            f.t();\n        }\n        this.d = 2;\n    };\n    return Zip;\n}());\nexport { Zip };\nexport function zip(data, opts, cb) {\n    if (!cb)\n        cb = opts, opts = {};\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var r = {};\n    fltn(data, '', r, opts);\n    var k = Object.keys(r);\n    var lft = k.length, o = 0, tot = 0;\n    var slft = lft, files = new Array(lft);\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var cbf = function () {\n        var out = new u8(tot + 22), oe = o, cdl = tot - o;\n        tot = 0;\n        for (var i = 0; i < slft; ++i) {\n            var f = files[i];\n            try {\n                var l = f.c.length;\n                wzh(out, tot, f, f.f, f.u, l);\n                var badd = 30 + f.f.length + exfl(f.extra);\n                var loc = tot + badd;\n                out.set(f.c, loc);\n                wzh(out, o, f, f.f, f.u, l, tot, f.m), o += 16 + badd + (f.m ? f.m.length : 0), tot = loc + l;\n            }\n            catch (e) {\n                return cb(e, null);\n            }\n        }\n        wzf(out, o, files.length, cdl, oe);\n        cb(null, out);\n    };\n    if (!lft)\n        cbf();\n    var _loop_1 = function (i) {\n        var fn = k[i];\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var c = crc(), size = file.length;\n        c.p(file);\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        var compression = p.level == 0 ? 0 : 8;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                var l = d.length;\n                files[i] = mrg(p, {\n                    size: size,\n                    crc: c.d(),\n                    c: d,\n                    f: f,\n                    m: m,\n                    u: s != fn.length || (m && (com.length != ms)),\n                    compression: compression\n                });\n                o += 30 + s + exl + l;\n                tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n                if (!--lft)\n                    cbf();\n            }\n        };\n        if (s > 65535)\n            cbl('filename too long', null);\n        if (!compression)\n            cbl(null, file);\n        else if (size < 160000) {\n            try {\n                cbl(null, deflateSync(file, p));\n            }\n            catch (e) {\n                cbl(e, null);\n            }\n        }\n        else\n            term.push(deflate(file, p, cbl));\n    };\n    // Cannot use lft because it can decrease\n    for (var i = 0; i < slft; ++i) {\n        _loop_1(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously creates a ZIP file. Prefer using `zip` for better performance\n * with more than one file.\n * @param data The directory structure for the ZIP archive\n * @param opts The main options, merged with per-file options\n * @returns The generated ZIP archive\n */\nexport function zipSync(data, opts) {\n    if (!opts)\n        opts = {};\n    var r = {};\n    var files = [];\n    fltn(data, '', r, opts);\n    var o = 0;\n    var tot = 0;\n    for (var fn in r) {\n        var _a = r[fn], file = _a[0], p = _a[1];\n        var compression = p.level == 0 ? 0 : 8;\n        var f = strToU8(fn), s = f.length;\n        var com = p.comment, m = com && strToU8(com), ms = m && m.length;\n        var exl = exfl(p.extra);\n        if (s > 65535)\n            throw 'filename too long';\n        var d = compression ? deflateSync(file, p) : file, l = d.length;\n        var c = crc();\n        c.p(file);\n        files.push(mrg(p, {\n            size: file.length,\n            crc: c.d(),\n            c: d,\n            f: f,\n            m: m,\n            u: s != fn.length || (m && (com.length != ms)),\n            o: o,\n            compression: compression\n        }));\n        o += 30 + s + exl + l;\n        tot += 76 + 2 * (s + exl) + (ms || 0) + l;\n    }\n    var out = new u8(tot + 22), oe = o, cdl = tot - o;\n    for (var i = 0; i < files.length; ++i) {\n        var f = files[i];\n        wzh(out, f.o, f, f.f, f.u, f.c.length);\n        var badd = 30 + f.f.length + exfl(f.extra);\n        out.set(f.c, f.o + badd);\n        wzh(out, o, f, f.f, f.u, f.c.length, f.o, f.m), o += 16 + badd + (f.m ? f.m.length : 0);\n    }\n    wzf(out, o, files.length, cdl, oe);\n    return out;\n}\n/**\n * Streaming pass-through decompression for ZIP archives\n */\nvar UnzipPassThrough = /*#__PURE__*/ (function () {\n    function UnzipPassThrough() {\n    }\n    UnzipPassThrough.prototype.push = function (data, final) {\n        this.ondata(null, data, final);\n    };\n    UnzipPassThrough.compression = 0;\n    return UnzipPassThrough;\n}());\nexport { UnzipPassThrough };\n/**\n * Streaming DEFLATE decompression for ZIP archives. Prefer AsyncZipInflate for\n * better performance.\n */\nvar UnzipInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */\n    function UnzipInflate() {\n        var _this_1 = this;\n        this.i = new Inflate(function (dat, final) {\n            _this_1.ondata(null, dat, final);\n        });\n    }\n    UnzipInflate.prototype.push = function (data, final) {\n        try {\n            this.i.push(data, final);\n        }\n        catch (e) {\n            this.ondata(e, data, final);\n        }\n    };\n    UnzipInflate.compression = 8;\n    return UnzipInflate;\n}());\nexport { UnzipInflate };\n/**\n * Asynchronous streaming DEFLATE decompression for ZIP archives\n */\nvar AsyncUnzipInflate = /*#__PURE__*/ (function () {\n    /**\n     * Creates a DEFLATE decompression that can be used in ZIP archives\n     */\n    function AsyncUnzipInflate(_, sz) {\n        var _this_1 = this;\n        if (sz < 320000) {\n            this.i = new Inflate(function (dat, final) {\n                _this_1.ondata(null, dat, final);\n            });\n        }\n        else {\n            this.i = new AsyncInflate(function (err, dat, final) {\n                _this_1.ondata(err, dat, final);\n            });\n            this.terminate = this.i.terminate;\n        }\n    }\n    AsyncUnzipInflate.prototype.push = function (data, final) {\n        if (this.i.terminate)\n            data = slc(data, 0);\n        this.i.push(data, final);\n    };\n    AsyncUnzipInflate.compression = 8;\n    return AsyncUnzipInflate;\n}());\nexport { AsyncUnzipInflate };\n/**\n * A ZIP archive decompression stream that emits files as they are discovered\n */\nvar Unzip = /*#__PURE__*/ (function () {\n    /**\n     * Creates a ZIP decompression stream\n     * @param cb The callback to call whenever a file in the ZIP archive is found\n     */\n    function Unzip(cb) {\n        this.onfile = cb;\n        this.k = [];\n        this.o = {\n            0: UnzipPassThrough\n        };\n        this.p = et;\n    }\n    /**\n     * Pushes a chunk to be unzipped\n     * @param chunk The chunk to push\n     * @param final Whether this is the last chunk\n     */\n    Unzip.prototype.push = function (chunk, final) {\n        var _this_1 = this;\n        if (!this.onfile)\n            throw 'no callback';\n        if (!this.p)\n            throw 'stream finished';\n        if (this.c > 0) {\n            var len = Math.min(this.c, chunk.length);\n            var toAdd = chunk.subarray(0, len);\n            this.c -= len;\n            if (this.d)\n                this.d.push(toAdd, !this.c);\n            else\n                this.k[0].push(toAdd);\n            chunk = chunk.subarray(len);\n            if (chunk.length)\n                return this.push(chunk, final);\n        }\n        else {\n            var f = 0, i = 0, is = void 0, buf = void 0;\n            if (!this.p.length)\n                buf = chunk;\n            else if (!chunk.length)\n                buf = this.p;\n            else {\n                buf = new u8(this.p.length + chunk.length);\n                buf.set(this.p), buf.set(chunk, this.p.length);\n            }\n            var l = buf.length, oc = this.c, add = oc && this.d;\n            var _loop_2 = function () {\n                var _a;\n                var sig = b4(buf, i);\n                if (sig == 0x4034B50) {\n                    f = 1, is = i;\n                    this_1.d = null;\n                    this_1.c = 0;\n                    var bf = b2(buf, i + 6), cmp_1 = b2(buf, i + 8), u = bf & 2048, dd = bf & 8, fnl = b2(buf, i + 26), es = b2(buf, i + 28);\n                    if (l > i + 30 + fnl + es) {\n                        var chks_2 = [];\n                        this_1.k.unshift(chks_2);\n                        f = 2;\n                        var sc_1 = b4(buf, i + 18), su_1 = b4(buf, i + 22);\n                        var fn_1 = strFromU8(buf.subarray(i + 30, i += 30 + fnl), !u);\n                        if (sc_1 == 4294967295) {\n                            _a = dd ? [-2] : z64e(buf, i), sc_1 = _a[0], su_1 = _a[1];\n                        }\n                        else if (dd)\n                            sc_1 = -1;\n                        i += es;\n                        this_1.c = sc_1;\n                        var d_1;\n                        var file_1 = {\n                            name: fn_1,\n                            compression: cmp_1,\n                            start: function () {\n                                if (!file_1.ondata)\n                                    throw 'no callback';\n                                if (!sc_1)\n                                    file_1.ondata(null, et, true);\n                                else {\n                                    var ctr = _this_1.o[cmp_1];\n                                    if (!ctr)\n                                        throw 'unknown compression type ' + cmp_1;\n                                    d_1 = sc_1 < 0 ? new ctr(fn_1) : new ctr(fn_1, sc_1, su_1);\n                                    d_1.ondata = function (err, dat, final) { file_1.ondata(err, dat, final); };\n                                    for (var _i = 0, chks_3 = chks_2; _i < chks_3.length; _i++) {\n                                        var dat = chks_3[_i];\n                                        d_1.push(dat, false);\n                                    }\n                                    if (_this_1.k[0] == chks_2 && _this_1.c)\n                                        _this_1.d = d_1;\n                                    else\n                                        d_1.push(et, true);\n                                }\n                            },\n                            terminate: function () {\n                                if (d_1 && d_1.terminate)\n                                    d_1.terminate();\n                            }\n                        };\n                        if (sc_1 >= 0)\n                            file_1.size = sc_1, file_1.originalSize = su_1;\n                        this_1.onfile(file_1);\n                    }\n                    return \"break\";\n                }\n                else if (oc) {\n                    if (sig == 0x8074B50) {\n                        is = i += 12 + (oc == -2 && 8), f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                    else if (sig == 0x2014B50) {\n                        is = i -= 4, f = 3, this_1.c = 0;\n                        return \"break\";\n                    }\n                }\n            };\n            var this_1 = this;\n            for (; i < l - 4; ++i) {\n                var state_1 = _loop_2();\n                if (state_1 === \"break\")\n                    break;\n            }\n            this.p = et;\n            if (oc < 0) {\n                var dat = f ? buf.subarray(0, is - 12 - (oc == -2 && 8) - (b4(buf, is - 16) == 0x8074B50 && 4)) : buf.subarray(0, i);\n                if (add)\n                    add.push(dat, !!f);\n                else\n                    this.k[+(f == 2)].push(dat);\n            }\n            if (f & 2)\n                return this.push(buf.subarray(i), final);\n            this.p = buf.subarray(i);\n        }\n        if (final) {\n            if (this.c)\n                throw 'invalid zip file';\n            this.p = null;\n        }\n    };\n    /**\n     * Registers a decoder with the stream, allowing for files compressed with\n     * the compression type provided to be expanded correctly\n     * @param decoder The decoder constructor\n     */\n    Unzip.prototype.register = function (decoder) {\n        this.o[decoder.compression] = decoder;\n    };\n    return Unzip;\n}());\nexport { Unzip };\n/**\n * Asynchronously decompresses a ZIP archive\n * @param data The raw compressed ZIP file\n * @param cb The callback to call with the decompressed files\n * @returns A function that can be used to immediately terminate the unzipping\n */\nexport function unzip(data, cb) {\n    if (typeof cb != 'function')\n        throw 'no callback';\n    var term = [];\n    var tAll = function () {\n        for (var i = 0; i < term.length; ++i)\n            term[i]();\n    };\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558) {\n            cb('invalid zip file', null);\n            return;\n        }\n    }\n    ;\n    var lft = b2(data, e + 8);\n    if (!lft)\n        cb(null, {});\n    var c = lft;\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50) {\n            cb('invalid zip file', null);\n            return;\n        }\n        c = lft = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    var _loop_3 = function (i) {\n        var _a = zh(data, o, z), c_1 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        var cbl = function (e, d) {\n            if (e) {\n                tAll();\n                cb(e, null);\n            }\n            else {\n                files[fn] = d;\n                if (!--lft)\n                    cb(null, files);\n            }\n        };\n        if (!c_1)\n            cbl(null, slc(data, b, b + sc));\n        else if (c_1 == 8) {\n            var infl = data.subarray(b, b + sc);\n            if (sc < 320000) {\n                try {\n                    cbl(null, inflateSync(infl, new u8(su)));\n                }\n                catch (e) {\n                    cbl(e, null);\n                }\n            }\n            else\n                term.push(inflate(infl, { size: su }, cbl));\n        }\n        else\n            cbl('unknown compression type ' + c_1, null);\n    };\n    for (var i = 0; i < c; ++i) {\n        _loop_3(i);\n    }\n    return tAll;\n}\n/**\n * Synchronously decompresses a ZIP archive. Prefer using `unzip` for better\n * performance with more than one file.\n * @param data The raw compressed ZIP file\n * @returns The decompressed files\n */\nexport function unzipSync(data) {\n    var files = {};\n    var e = data.length - 22;\n    for (; b4(data, e) != 0x6054B50; --e) {\n        if (!e || data.length - e > 65558)\n            throw 'invalid zip file';\n    }\n    ;\n    var c = b2(data, e + 8);\n    if (!c)\n        return {};\n    var o = b4(data, e + 16);\n    var z = o == 4294967295;\n    if (z) {\n        e = b4(data, e - 12);\n        if (b4(data, e) != 0x6064B50)\n            throw 'invalid zip file';\n        c = b4(data, e + 32);\n        o = b4(data, e + 48);\n    }\n    for (var i = 0; i < c; ++i) {\n        var _a = zh(data, o, z), c_2 = _a[0], sc = _a[1], su = _a[2], fn = _a[3], no = _a[4], off = _a[5], b = slzh(data, off);\n        o = no;\n        if (!c_2)\n            files[fn] = slc(data, b, b + sc);\n        else if (c_2 == 8)\n            files[fn] = inflateSync(data.subarray(b, b + sc), new u8(su));\n        else\n            throw 'unknown compression type ' + c_2;\n    }\n    return files;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACA,IAAI,UAAU,CAAA,GAAA,qGAAA,CAAA,gBAAa,AAAD,EAAE;AAC5B,2FAA2F;AAC3F,sCAAsC;AACtC,2EAA2E;AAC3E,qEAAqE;AACrE,4DAA4D;AAC5D,sCAAsC;AACtC,uHAAuH;AACvH,2FAA2F;AAC3F,oDAAoD;AACpD,gBAAgB;AAChB,IAAI;AACJ,IAAI,YAAY;AAChB,IAAI;IACA,SAAS,QAAQ,kBAAkB,MAAM;AAC7C,EACA,OAAO,GAAG,CACV;AACA,IAAI,KAAK,SAAS,SAAU,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,QAAQ,EAAE,EAAE;IAC/C,IAAI,OAAO;IACX,IAAI,IAAI,IAAI,OAAO,IAAI,WAAW;QAAE,MAAM;IAAK,GAC1C,EAAE,CAAC,SAAS,SAAU,CAAC;QAAI,OAAO,GAAG,GAAG;IAAO,GAC/C,EAAE,CAAC,WAAW,SAAU,CAAC;QAAI,OAAO,GAAG,MAAM;IAAI,GACjD,EAAE,CAAC,QAAQ,SAAU,CAAC;QACvB,IAAI,KAAK,CAAC,MACN,GAAG,IAAI,MAAM,sBAAsB,IAAI;IAC/C;IACA,EAAE,WAAW,CAAC,KAAK;IACnB,EAAE,SAAS,GAAG;QACV,OAAO;QACP,OAAO,OAAO,SAAS,CAAC,SAAS,CAAC,IAAI,CAAC;IAC3C;IACA,OAAO;AACX,IAAI,SAAU,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;IAC9B,aAAa;QAAc,OAAO,GAAG,IAAI,MAAM,8GAA8G;IAAO;IACpK,IAAI,MAAM,YAAc;IACxB,OAAO;QACH,WAAW;QACX,aAAa;IACjB;AACJ;AAEA,oEAAoE;AACpE,IAAI,KAAK,YAAY,MAAM,aAAa,MAAM;AAC9C,0BAA0B;AAC1B,IAAI,OAAO,IAAI,GAAG;IAAC;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG,UAAU,GAAG;IAAG;IAAG,cAAc,GAAG;CAAE;AAChJ,4BAA4B;AAC5B,gBAAgB;AAChB,IAAI,OAAO,IAAI,GAAG;IAAC;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI;IAAI,UAAU,GAAG;IAAG;CAAE;AACvI,wBAAwB;AACxB,IAAI,OAAO,IAAI,GAAG;IAAC;IAAI;IAAI;IAAI;IAAG;IAAG;IAAG;IAAG;IAAG;IAAI;IAAG;IAAI;IAAG;IAAI;IAAG;IAAI;IAAG;IAAI;IAAG;CAAG;AACpF,8CAA8C;AAC9C,IAAI,OAAO,SAAU,EAAE,EAAE,KAAK;IAC1B,IAAI,IAAI,IAAI,IAAI;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACzB,CAAC,CAAC,EAAE,GAAG,SAAS,KAAK,EAAE,CAAC,IAAI,EAAE;IAClC;IACA,kCAAkC;IAClC,IAAI,IAAI,IAAI,IAAI,CAAC,CAAC,GAAG;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACzB,IAAK,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,EAAE,EAAG;YAClC,CAAC,CAAC,EAAE,GAAG,AAAE,IAAI,CAAC,CAAC,EAAE,IAAK,IAAK;QAC/B;IACJ;IACA,OAAO;QAAC;QAAG;KAAE;AACjB;AACA,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE;AACjD,oFAAoF;AACpF,EAAE,CAAC,GAAG,GAAG,KAAK,KAAK,CAAC,IAAI,GAAG;AAC3B,IAAI,KAAK,KAAK,MAAM,IAAI,KAAK,EAAE,CAAC,EAAE,EAAE,QAAQ,EAAE,CAAC,EAAE;AACjD,6CAA6C;AAC7C,IAAI,MAAM,IAAI,IAAI;AAClB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,EAAE,EAAG;IAC5B,kCAAkC;IAClC,IAAI,IAAI,AAAC,CAAC,IAAI,MAAM,MAAM,IAAM,CAAC,IAAI,MAAM,KAAK;IAChD,IAAI,AAAC,CAAC,IAAI,MAAM,MAAM,IAAM,CAAC,IAAI,MAAM,KAAK;IAC5C,IAAI,AAAC,CAAC,IAAI,MAAM,MAAM,IAAM,CAAC,IAAI,MAAM,KAAK;IAC5C,GAAG,CAAC,EAAE,GAAG,CAAC,AAAC,CAAC,IAAI,MAAM,MAAM,IAAM,CAAC,IAAI,MAAM,KAAK,CAAE,MAAM;AAC9D;AACA,yEAAyE;AACzE,mCAAmC;AACnC,2BAA2B;AAC3B,IAAI,OAAQ,SAAU,EAAE,EAAE,EAAE,EAAE,CAAC;IAC3B,IAAI,IAAI,GAAG,MAAM;IACjB,QAAQ;IACR,IAAI,IAAI;IACR,yDAAyD;IACzD,IAAI,IAAI,IAAI,IAAI;IAChB,8CAA8C;IAC9C,MAAO,IAAI,GAAG,EAAE,EACZ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;IAClB,0DAA0D;IAC1D,IAAI,KAAK,IAAI,IAAI;IACjB,IAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACrB,EAAE,CAAC,EAAE,GAAG,AAAC,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,IAAK;IACtC;IACA,IAAI;IACJ,IAAI,GAAG;QACH,6DAA6D;QAC7D,KAAK,IAAI,IAAI,KAAK;QAClB,8BAA8B;QAC9B,IAAI,MAAM,KAAK;QACf,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACpB,mBAAmB;YACnB,IAAI,EAAE,CAAC,EAAE,EAAE;gBACP,yCAAyC;gBACzC,IAAI,KAAK,AAAC,KAAK,IAAK,EAAE,CAAC,EAAE;gBACzB,YAAY;gBACZ,IAAI,MAAM,KAAK,EAAE,CAAC,EAAE;gBACpB,cAAc;gBACd,IAAI,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,MAAM;gBAC3B,iBAAiB;gBACjB,IAAK,IAAI,IAAI,IAAK,CAAC,KAAK,GAAG,IAAI,GAAI,KAAK,GAAG,EAAE,EAAG;oBAC5C,mEAAmE;oBACnE,EAAE,CAAC,GAAG,CAAC,EAAE,KAAK,IAAI,GAAG;gBACzB;YACJ;QACJ;IACJ,OACK;QACD,KAAK,IAAI,IAAI;QACb,IAAK,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;YACpB,IAAI,EAAE,CAAC,EAAE,EAAE;gBACP,EAAE,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,KAAM,KAAK,EAAE,CAAC,EAAE;YAChD;QACJ;IACJ;IACA,OAAO;AACX;AACA,oBAAoB;AACpB,IAAI,MAAM,IAAI,GAAG;AACjB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EACvB,GAAG,CAAC,EAAE,GAAG;AACb,IAAK,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE,EACzB,GAAG,CAAC,EAAE,GAAG;AACb,IAAK,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE,EACzB,GAAG,CAAC,EAAE,GAAG;AACb,IAAK,IAAI,IAAI,KAAK,IAAI,KAAK,EAAE,EACzB,GAAG,CAAC,EAAE,GAAG;AACb,sBAAsB;AACtB,IAAI,MAAM,IAAI,GAAG;AACjB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EACtB,GAAG,CAAC,EAAE,GAAG;AACb,mBAAmB;AACnB,IAAI,MAAM,WAAW,GAAG,KAAK,KAAK,GAAG,IAAI,OAAO,WAAW,GAAG,KAAK,KAAK,GAAG;AAC3E,qBAAqB;AACrB,IAAI,MAAM,WAAW,GAAG,KAAK,KAAK,GAAG,IAAI,OAAO,WAAW,GAAG,KAAK,KAAK,GAAG;AAC3E,oBAAoB;AACpB,IAAI,MAAM,SAAU,CAAC;IACjB,IAAI,IAAI,CAAC,CAAC,EAAE;IACZ,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,EAAE,EAAG;QAC/B,IAAI,CAAC,CAAC,EAAE,GAAG,GACP,IAAI,CAAC,CAAC,EAAE;IAChB;IACA,OAAO;AACX;AACA,4CAA4C;AAC5C,IAAI,OAAO,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IACxB,IAAI,IAAI,AAAC,IAAI,IAAK;IAClB,OAAO,AAAC,CAAC,CAAC,CAAC,EAAE,GAAI,CAAC,CAAC,IAAI,EAAE,IAAI,CAAE,KAAK,CAAC,IAAI,CAAC,IAAK;AACnD;AACA,4DAA4D;AAC5D,IAAI,SAAS,SAAU,CAAC,EAAE,CAAC;IACvB,IAAI,IAAI,AAAC,IAAI,IAAK;IAClB,OAAQ,CAAC,CAAC,CAAC,EAAE,GAAI,CAAC,CAAC,IAAI,EAAE,IAAI,IAAM,CAAC,CAAC,IAAI,EAAE,IAAI,EAAG,KAAK,CAAC,IAAI,CAAC;AACjE;AACA,kBAAkB;AAClB,IAAI,OAAO,SAAU,CAAC;IAAI,OAAO,CAAC,AAAC,IAAI,IAAK,CAAC,IAAI,CAAC,IAAI,KAAK,CAAC;AAAG;AAC/D,2EAA2E;AAC3E,0CAA0C;AAC1C,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IACvB,IAAI,KAAK,QAAQ,IAAI,GACjB,IAAI;IACR,IAAI,KAAK,QAAQ,IAAI,EAAE,MAAM,EACzB,IAAI,EAAE,MAAM;IAChB,+CAA+C;IAC/C,IAAI,IAAI,IAAI,CAAC,aAAa,MAAM,MAAM,aAAa,MAAM,MAAM,EAAE,EAAE,IAAI;IACvE,EAAE,GAAG,CAAC,EAAE,QAAQ,CAAC,GAAG;IACpB,OAAO;AACX;AACA,2BAA2B;AAC3B,IAAI,QAAQ,SAAU,GAAG,EAAE,GAAG,EAAE,EAAE;IAC9B,gBAAgB;IAChB,IAAI,KAAK,IAAI,MAAM;IACnB,IAAI,CAAC,MAAO,MAAM,CAAC,GAAG,CAAC,IAAI,KAAK,GAC5B,OAAO,OAAO,IAAI,GAAG;IACzB,wBAAwB;IACxB,IAAI,QAAQ,CAAC,OAAO;IACpB,WAAW;IACX,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC;IACtB,IAAI,CAAC,IACD,KAAK,CAAC;IACV,gDAAgD;IAChD,IAAI,CAAC,KACD,MAAM,IAAI,GAAG,KAAK;IACtB,4CAA4C;IAC5C,IAAI,OAAO,SAAU,CAAC;QAClB,IAAI,KAAK,IAAI,MAAM;QACnB,+BAA+B;QAC/B,IAAI,IAAI,IAAI;YACR,mDAAmD;YACnD,IAAI,OAAO,IAAI,GAAG,KAAK,GAAG,CAAC,KAAK,GAAG;YACnC,KAAK,GAAG,CAAC;YACT,MAAM;QACV;IACJ;IACA,6CAA6C;IAC7C,IAAI,QAAQ,GAAG,CAAC,IAAI,GAAG,MAAM,GAAG,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC,EAAE,MAAM,GAAG,CAAC;IACpG,aAAa;IACb,IAAI,OAAO,KAAK;IAChB,GAAG;QACC,IAAI,CAAC,IAAI;YACL,kDAAkD;YAClD,GAAG,CAAC,GAAG,QAAQ,KAAK,KAAK,KAAK;YAC9B,mEAAmE;YACnE,IAAI,OAAO,KAAK,KAAK,MAAM,GAAG;YAC9B,OAAO;YACP,IAAI,CAAC,MAAM;gBACP,6BAA6B;gBAC7B,IAAI,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,GAAI,GAAG,CAAC,IAAI,EAAE,IAAI,GAAI,IAAI,IAAI;gBACnE,IAAI,IAAI,IAAI;oBACR,IAAI,MACA,MAAM;oBACV;gBACJ;gBACA,cAAc;gBACd,IAAI,OACA,KAAK,KAAK;gBACd,8BAA8B;gBAC9B,IAAI,GAAG,CAAC,IAAI,QAAQ,CAAC,GAAG,IAAI;gBAC5B,oCAAoC;gBACpC,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG,CAAC,GAAG,MAAM,IAAI;gBACjC;YACJ,OACK,IAAI,QAAQ,GACb,KAAK,MAAM,KAAK,MAAM,MAAM,GAAG,MAAM;iBACpC,IAAI,QAAQ,GAAG;gBAChB,8CAA8C;gBAC9C,IAAI,OAAO,KAAK,KAAK,KAAK,MAAM,KAAK,QAAQ,KAAK,KAAK,MAAM,IAAI,MAAM;gBACvE,IAAI,KAAK,OAAO,KAAK,KAAK,MAAM,GAAG,MAAM;gBACzC,OAAO;gBACP,uBAAuB;gBACvB,IAAI,MAAM,IAAI,GAAG;gBACjB,mBAAmB;gBACnB,IAAI,MAAM,IAAI,GAAG;gBACjB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,EAAE,EAAG;oBAC5B,iCAAiC;oBACjC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,KAAK,MAAM,IAAI,GAAG;gBAC1C;gBACA,OAAO,QAAQ;gBACf,oBAAoB;gBACpB,IAAI,MAAM,IAAI,MAAM,SAAS,CAAC,KAAK,GAAG,IAAI;gBAC1C,mBAAmB;gBACnB,IAAI,MAAM,KAAK,KAAK,KAAK;gBACzB,IAAK,IAAI,IAAI,GAAG,IAAI,IAAK;oBACrB,IAAI,IAAI,GAAG,CAAC,KAAK,KAAK,KAAK,QAAQ;oBACnC,YAAY;oBACZ,OAAO,IAAI;oBACX,SAAS;oBACT,IAAI,IAAI,MAAM;oBACd,sBAAsB;oBACtB,IAAI,IAAI,IAAI;wBACR,GAAG,CAAC,IAAI,GAAG;oBACf,OACK;wBACD,gBAAgB;wBAChB,IAAI,IAAI,GAAG,IAAI;wBACf,IAAI,KAAK,IACL,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,OAAO,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE;6BAClD,IAAI,KAAK,IACV,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,OAAO;6BACjC,IAAI,KAAK,IACV,IAAI,KAAK,KAAK,KAAK,KAAK,MAAM,OAAO;wBACzC,MAAO,IACH,GAAG,CAAC,IAAI,GAAG;oBACnB;gBACJ;gBACA,+CAA+C;gBAC/C,IAAI,KAAK,IAAI,QAAQ,CAAC,GAAG,OAAO,KAAK,IAAI,QAAQ,CAAC;gBAClD,kBAAkB;gBAClB,MAAM,IAAI;gBACV,gBAAgB;gBAChB,MAAM,IAAI;gBACV,KAAK,KAAK,IAAI,KAAK;gBACnB,KAAK,KAAK,IAAI,KAAK;YACvB,OAEI,MAAM;YACV,IAAI,MAAM,MAAM;gBACZ,IAAI,MACA,MAAM;gBACV;YACJ;QACJ;QACA,qEAAqE;QACrE,oEAAoE;QACpE,IAAI,OACA,KAAK,KAAK;QACd,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,GAAG,MAAM,CAAC,KAAK,GAAG,IAAI;QAC7C,IAAI,OAAO;QACX,OAAQ,OAAO,IAAK;YAChB,kBAAkB;YAClB,IAAI,IAAI,EAAE,CAAC,OAAO,KAAK,OAAO,IAAI,EAAE,MAAM,MAAM;YAChD,OAAO,IAAI;YACX,IAAI,MAAM,MAAM;gBACZ,IAAI,MACA,MAAM;gBACV;YACJ;YACA,IAAI,CAAC,GACD,MAAM;YACV,IAAI,MAAM,KACN,GAAG,CAAC,KAAK,GAAG;iBACX,IAAI,OAAO,KAAK;gBACjB,OAAO,KAAK,KAAK;gBACjB;YACJ,OACK;gBACD,IAAI,MAAM,MAAM;gBAChB,+BAA+B;gBAC/B,IAAI,MAAM,KAAK;oBACX,QAAQ;oBACR,IAAI,IAAI,MAAM,KAAK,IAAI,IAAI,CAAC,EAAE;oBAC9B,MAAM,KAAK,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,KAAK,EAAE,CAAC,EAAE;oBAC1C,OAAO;gBACX;gBACA,OAAO;gBACP,IAAI,IAAI,EAAE,CAAC,OAAO,KAAK,OAAO,IAAI,EAAE,OAAO,MAAM;gBACjD,IAAI,CAAC,GACD,MAAM;gBACV,OAAO,IAAI;gBACX,IAAI,KAAK,EAAE,CAAC,KAAK;gBACjB,IAAI,OAAO,GAAG;oBACV,IAAI,IAAI,IAAI,CAAC,KAAK;oBAClB,MAAM,OAAO,KAAK,OAAQ,CAAC,KAAK,CAAC,IAAI,GAAI,OAAO;gBACpD;gBACA,IAAI,MAAM,MAAM;oBACZ,IAAI,MACA,MAAM;oBACV;gBACJ;gBACA,IAAI,OACA,KAAK,KAAK;gBACd,IAAI,MAAM,KAAK;gBACf,MAAO,KAAK,KAAK,MAAM,EAAG;oBACtB,GAAG,CAAC,GAAG,GAAG,GAAG,CAAC,KAAK,GAAG;oBACtB,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,KAAK,IAAI,GAAG;oBAC9B,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,KAAK,IAAI,GAAG;oBAC9B,GAAG,CAAC,KAAK,EAAE,GAAG,GAAG,CAAC,KAAK,IAAI,GAAG;gBAClC;gBACA,KAAK;YACT;QACJ;QACA,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,CAAC,GAAG;QAC/B,IAAI,IACA,QAAQ,GAAG,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG;IACjD,QAAS,CAAC,MAAO;IACjB,OAAO,MAAM,IAAI,MAAM,GAAG,MAAM,IAAI,KAAK,GAAG;AAChD;AACA,uEAAuE;AACvE,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IACzB,MAAM,IAAI;IACV,IAAI,IAAI,AAAC,IAAI,IAAK;IAClB,CAAC,CAAC,EAAE,IAAI;IACR,CAAC,CAAC,IAAI,EAAE,IAAI,MAAM;AACtB;AACA,4EAA4E;AAC5E,IAAI,UAAU,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3B,MAAM,IAAI;IACV,IAAI,IAAI,AAAC,IAAI,IAAK;IAClB,CAAC,CAAC,EAAE,IAAI;IACR,CAAC,CAAC,IAAI,EAAE,IAAI,MAAM;IAClB,CAAC,CAAC,IAAI,EAAE,IAAI,MAAM;AACtB;AACA,8CAA8C;AAC9C,IAAI,QAAQ,SAAU,CAAC,EAAE,EAAE;IACvB,iCAAiC;IACjC,IAAI,IAAI,EAAE;IACV,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,EAAE,EAAG;QAC/B,IAAI,CAAC,CAAC,EAAE,EACJ,EAAE,IAAI,CAAC;YAAE,GAAG;YAAG,GAAG,CAAC,CAAC,EAAE;QAAC;IAC/B;IACA,IAAI,IAAI,EAAE,MAAM;IAChB,IAAI,KAAK,EAAE,KAAK;IAChB,IAAI,CAAC,GACD,OAAO;QAAC;QAAI;KAAE;IAClB,IAAI,KAAK,GAAG;QACR,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG;QACxB,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG;QACZ,OAAO;YAAC;YAAG;SAAE;IACjB;IACA,EAAE,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;QAAI,OAAO,EAAE,CAAC,GAAG,EAAE,CAAC;IAAE;IAC3C,6CAA6C;IAC7C,+DAA+D;IAC/D,EAAE,IAAI,CAAC;QAAE,GAAG,CAAC;QAAG,GAAG;IAAM;IACzB,IAAI,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,KAAK,GAAG,KAAK,GAAG,KAAK;IAC7C,CAAC,CAAC,EAAE,GAAG;QAAE,GAAG,CAAC;QAAG,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;QAAE,GAAG;QAAG,GAAG;IAAE;IACzC,mCAAmC;IACnC,oEAAoE;IACpE,6EAA6E;IAC7E,iCAAiC;IACjC,sEAAsE;IACtE,MAAO,MAAM,IAAI,EAAG;QAChB,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,KAAK;QACtC,IAAI,CAAC,CAAC,MAAM,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,OAAO,KAAK;QAClD,CAAC,CAAC,KAAK,GAAG;YAAE,GAAG,CAAC;YAAG,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;YAAE,GAAG;YAAG,GAAG;QAAE;IAChD;IACA,IAAI,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;IACpB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACxB,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,QACV,SAAS,EAAE,CAAC,EAAE,CAAC,CAAC;IACxB;IACA,eAAe;IACf,IAAI,KAAK,IAAI,IAAI,SAAS;IAC1B,mBAAmB;IACnB,IAAI,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,IAAI;IAC5B,IAAI,MAAM,IAAI;QACV,+BAA+B;QAC/B,4CAA4C;QAC5C,eAAe;QACf,IAAI,IAAI,GAAG,KAAK;QAChB,0BAA0B;QAC1B,IAAI,MAAM,MAAM,IAAI,MAAM,KAAK;QAC/B,GAAG,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;YAAI,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC;QAAE;QACjE,MAAO,IAAI,GAAG,EAAE,EAAG;YACf,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YAClB,IAAI,EAAE,CAAC,KAAK,GAAG,IAAI;gBACf,MAAM,MAAM,CAAC,KAAM,MAAM,EAAE,CAAC,KAAK,AAAC;gBAClC,EAAE,CAAC,KAAK,GAAG;YACf,OAEI;QACR;QACA,QAAQ;QACR,MAAO,KAAK,EAAG;YACX,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YAClB,IAAI,EAAE,CAAC,KAAK,GAAG,IACX,MAAM,KAAM,KAAK,EAAE,CAAC,KAAK,KAAK;iBAE9B,EAAE;QACV;QACA,MAAO,KAAK,KAAK,IAAI,EAAE,EAAG;YACtB,IAAI,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC;YAClB,IAAI,EAAE,CAAC,KAAK,IAAI,IAAI;gBAChB,EAAE,EAAE,CAAC,KAAK;gBACV,EAAE;YACN;QACJ;QACA,MAAM;IACV;IACA,OAAO;QAAC,IAAI,GAAG;QAAK;KAAI;AAC5B;AACA,6CAA6C;AAC7C,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IACtB,OAAO,EAAE,CAAC,IAAI,CAAC,IACT,KAAK,GAAG,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,IAAI,IAAI,GAAG,EAAE,CAAC,EAAE,GAAG,IAAI,MAC1C,CAAC,CAAC,EAAE,CAAC,CAAC,GAAG;AACpB;AACA,0BAA0B;AAC1B,IAAI,KAAK,SAAU,CAAC;IAChB,IAAI,IAAI,EAAE,MAAM;IAChB,0CAA0C;IAC1C,MAAO,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE;IAEnB,IAAI,KAAK,IAAI,IAAI,EAAE;IACnB,+BAA+B;IAC/B,IAAI,MAAM,GAAG,MAAM,CAAC,CAAC,EAAE,EAAE,MAAM;IAC/B,IAAI,IAAI,SAAU,CAAC;QAAI,EAAE,CAAC,MAAM,GAAG;IAAG;IACtC,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,EAAE,EAAG;QACzB,IAAI,CAAC,CAAC,EAAE,IAAI,OAAO,KAAK,GACpB,EAAE;aACD;YACD,IAAI,CAAC,OAAO,MAAM,GAAG;gBACjB,MAAO,MAAM,KAAK,OAAO,IACrB,EAAE;gBACN,IAAI,MAAM,GAAG;oBACT,EAAE,MAAM,KAAK,AAAE,MAAM,MAAO,IAAK,QAAQ,AAAE,MAAM,KAAM,IAAK;oBAC5D,MAAM;gBACV;YACJ,OACK,IAAI,MAAM,GAAG;gBACd,EAAE,MAAM,EAAE;gBACV,MAAO,MAAM,GAAG,OAAO,EACnB,EAAE;gBACN,IAAI,MAAM,GACN,EAAE,AAAE,MAAM,KAAM,IAAK,OAAO,MAAM;YAC1C;YACA,MAAO,MACH,EAAE;YACN,MAAM;YACN,MAAM,CAAC,CAAC,EAAE;QACd;IACJ;IACA,OAAO;QAAC,GAAG,QAAQ,CAAC,GAAG;QAAM;KAAE;AACnC;AACA,yDAAyD;AACzD,IAAI,OAAO,SAAU,EAAE,EAAE,EAAE;IACvB,IAAI,IAAI;IACR,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,EAAE,EAC7B,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE;IACtB,OAAO;AACX;AACA,uBAAuB;AACvB,0BAA0B;AAC1B,IAAI,QAAQ,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG;IAC/B,wDAAwD;IACxD,IAAI,IAAI,IAAI,MAAM;IAClB,IAAI,IAAI,KAAK,MAAM;IACnB,GAAG,CAAC,EAAE,GAAG,IAAI;IACb,GAAG,CAAC,IAAI,EAAE,GAAG,MAAM;IACnB,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG;IACtB,GAAG,CAAC,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,EAAE,GAAG;IAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EACrB,GAAG,CAAC,IAAI,IAAI,EAAE,GAAG,GAAG,CAAC,EAAE;IAC3B,OAAO,CAAC,IAAI,IAAI,CAAC,IAAI;AACzB;AACA,iBAAiB;AACjB,IAAI,OAAO,SAAU,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC;IACjE,MAAM,KAAK,KAAK;IAChB,EAAE,EAAE,CAAC,IAAI;IACT,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;IAChD,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;IAChD,IAAI,KAAK,GAAG,MAAM,OAAO,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;IAC3C,IAAI,KAAK,GAAG,MAAM,OAAO,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;IAC3C,IAAI,SAAS,IAAI,IAAI;IACrB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAC/B,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG;IACxB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAC/B,MAAM,CAAC,IAAI,CAAC,EAAE,GAAG,GAAG;IACxB,IAAI,KAAK,MAAM,QAAQ,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;IACpD,IAAI,OAAO;IACX,MAAO,OAAO,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,EAAE,EAAE;IAE3C,IAAI,OAAO,AAAC,KAAK,KAAM;IACvB,IAAI,QAAQ,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO;IAC5C,IAAI,QAAQ,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,KAAK,IAAI,OAAO,KAAK,QAAQ,OAAO,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG,GAAG,IAAI,MAAM,CAAC,GAAG;IACtI,IAAI,QAAQ,SAAS,QAAQ,OACzB,OAAO,MAAM,KAAK,GAAG,IAAI,QAAQ,CAAC,IAAI,KAAK;IAC/C,IAAI,IAAI,IAAI,IAAI;IAChB,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK;IACzC,IAAI,QAAQ,OAAO;QACf,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI,KAAK;QAC/D,IAAI,MAAM,KAAK,KAAK,MAAM;QAC1B,MAAM,KAAK,GAAG,MAAM;QACpB,MAAM,KAAK,IAAI,GAAG,MAAM;QACxB,MAAM,KAAK,IAAI,IAAI,OAAO;QAC1B,KAAK;QACL,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,EAAE,EACxB,MAAM,KAAK,IAAI,IAAI,GAAG,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;QACtC,KAAK,IAAI;QACT,IAAI,OAAO;YAAC;YAAM;SAAK;QACvB,IAAK,IAAI,KAAK,GAAG,KAAK,GAAG,EAAE,GAAI;YAC3B,IAAI,OAAO,IAAI,CAAC,GAAG;YACnB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAAG;gBAClC,IAAI,MAAM,IAAI,CAAC,EAAE,GAAG;gBACpB,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,IAAI;gBACtC,IAAI,MAAM,IACN,MAAM,KAAK,GAAG,AAAC,IAAI,CAAC,EAAE,KAAK,IAAK,MAAM,KAAK,IAAI,CAAC,EAAE,KAAK;YAC/D;QACJ;IACJ,OACK;QACD,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK;IACvC;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACzB,IAAI,IAAI,CAAC,EAAE,GAAG,KAAK;YACf,IAAI,MAAM,AAAC,IAAI,CAAC,EAAE,KAAK,KAAM;YAC7B,QAAQ,KAAK,GAAG,EAAE,CAAC,MAAM,IAAI,GAAG,KAAK,EAAE,CAAC,MAAM,IAAI;YAClD,IAAI,MAAM,GACN,MAAM,KAAK,GAAG,AAAC,IAAI,CAAC,EAAE,KAAK,KAAM,KAAK,KAAK,IAAI,CAAC,IAAI;YACxD,IAAI,MAAM,IAAI,CAAC,EAAE,GAAG;YACpB,QAAQ,KAAK,GAAG,EAAE,CAAC,IAAI,GAAG,KAAK,EAAE,CAAC,IAAI;YACtC,IAAI,MAAM,GACN,QAAQ,KAAK,GAAG,AAAC,IAAI,CAAC,EAAE,KAAK,IAAK,OAAO,KAAK,IAAI,CAAC,IAAI;QAC/D,OACK;YACD,QAAQ,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,KAAK,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAClD;IACJ;IACA,QAAQ,KAAK,GAAG,EAAE,CAAC,IAAI;IACvB,OAAO,IAAI,EAAE,CAAC,IAAI;AACtB;AACA,uCAAuC;AACvC,IAAI,MAAM,WAAW,GAAG,IAAI,IAAI;IAAC;IAAO;IAAQ;IAAQ;IAAQ;IAAQ;IAAS;IAAS;IAAS;CAAQ;AAC3G,QAAQ;AACR,IAAI,KAAK,WAAW,GAAG,IAAI,GAAG;AAC9B,4CAA4C;AAC5C,IAAI,OAAO,SAAU,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;IAC/C,IAAI,IAAI,IAAI,MAAM;IAClB,IAAI,IAAI,IAAI,GAAG,MAAM,IAAI,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI;IACzD,8CAA8C;IAC9C,IAAI,IAAI,EAAE,QAAQ,CAAC,KAAK,EAAE,MAAM,GAAG;IACnC,IAAI,MAAM;IACV,IAAI,CAAC,OAAO,IAAI,GAAG;QACf,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK,MAAO;YAChC,MAAM;YACN,IAAI,IAAI,IAAI;YACZ,IAAI,IAAI,GAAG;gBACP,mBAAmB;gBACnB,MAAM,MAAM,GAAG,KAAK,IAAI,QAAQ,CAAC,GAAG;YACxC,OACK;gBACD,oBAAoB;gBACpB,CAAC,CAAC,EAAE,GAAG;gBACP,MAAM,MAAM,GAAG,KAAK,IAAI,QAAQ,CAAC,GAAG;YACxC;QACJ;IACJ,OACK;QACD,IAAI,MAAM,GAAG,CAAC,MAAM,EAAE;QACtB,IAAI,IAAI,QAAQ,IAAI,IAAI,MAAM;QAC9B,IAAI,QAAQ,CAAC,KAAK,IAAI,IAAI;QAC1B,gDAAgD;QAChD,IAAI,OAAO,IAAI,IAAI,QAAQ,OAAO,IAAI,IAAI,QAAQ;QAClD,IAAI,QAAQ,KAAK,IAAI,CAAC,OAAO,IAAI,QAAQ,IAAI;QAC7C,IAAI,MAAM,SAAU,CAAC;YAAI,OAAO,CAAC,GAAG,CAAC,EAAE,GAAI,GAAG,CAAC,IAAI,EAAE,IAAI,QAAU,GAAG,CAAC,IAAI,EAAE,IAAI,KAAM,IAAI;QAAO;QAClG,4DAA4D;QAC5D,4BAA4B;QAC5B,IAAI,OAAO,IAAI,IAAI;QACnB,sCAAsC;QACtC,IAAI,KAAK,IAAI,IAAI,MAAM,KAAK,IAAI,IAAI;QACpC,iDAAiD;QACjD,IAAI,OAAO,GAAG,KAAK,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG,KAAK;QAClD,MAAO,IAAI,GAAG,EAAE,EAAG;YACf,aAAa;YACb,kDAAkD;YAClD,IAAI,KAAK,IAAI;YACb,wCAAwC;YACxC,IAAI,OAAO,IAAI,OAAO,QAAQ,IAAI,CAAC,GAAG;YACtC,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,GAAG,GAAG;YACX,iEAAiE;YACjE,yDAAyD;YACzD,IAAI,MAAM,GAAG;gBACT,kBAAkB;gBAClB,IAAI,MAAM,IAAI;gBACd,IAAI,CAAC,OAAO,QAAQ,KAAK,KAAK,KAAK,MAAM,KAAK;oBAC1C,MAAM,KAAK,KAAK,GAAG,GAAG,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;oBACxD,KAAK,OAAO,KAAK,GAAG,KAAK;oBACzB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EACvB,EAAE,CAAC,EAAE,GAAG;oBACZ,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EACtB,EAAE,CAAC,EAAE,GAAG;gBAChB;gBACA,uBAAuB;gBACvB,IAAI,IAAI,GAAG,IAAI,GAAG,OAAO,GAAG,MAAM,AAAC,OAAO,QAAS;gBACnD,IAAI,MAAM,KAAK,MAAM,IAAI,IAAI,MAAM;oBAC/B,IAAI,OAAO,KAAK,GAAG,CAAC,GAAG,OAAO;oBAC9B,IAAI,OAAO,KAAK,GAAG,CAAC,OAAO;oBAC3B,sBAAsB;oBACtB,+EAA+E;oBAC/E,IAAI,KAAK,KAAK,GAAG,CAAC,KAAK;oBACvB,MAAO,OAAO,QAAQ,EAAE,QAAQ,QAAQ,MAAO;wBAC3C,IAAI,GAAG,CAAC,IAAI,EAAE,IAAI,GAAG,CAAC,IAAI,IAAI,IAAI,EAAE;4BAChC,IAAI,KAAK;4BACT,MAAO,KAAK,MAAM,GAAG,CAAC,IAAI,GAAG,IAAI,GAAG,CAAC,IAAI,KAAK,IAAI,EAAE,EAAE;4BAEtD,IAAI,KAAK,GAAG;gCACR,IAAI,IAAI,IAAI;gCACZ,iEAAiE;gCACjE,IAAI,KAAK,MACL;gCACJ,mDAAmD;gCACnD,kDAAkD;gCAClD,wCAAwC;gCACxC,IAAI,MAAM,KAAK,GAAG,CAAC,KAAK,KAAK;gCAC7B,IAAI,KAAK;gCACT,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;oCAC1B,IAAI,KAAK,AAAC,IAAI,MAAM,IAAI,QAAS;oCACjC,IAAI,MAAM,IAAI,CAAC,GAAG;oCAClB,IAAI,KAAK,AAAC,KAAK,MAAM,QAAS;oCAC9B,IAAI,KAAK,IACL,KAAK,IAAI,QAAQ;gCACzB;4BACJ;wBACJ;wBACA,2BAA2B;wBAC3B,OAAO,OAAO,QAAQ,IAAI,CAAC,KAAK;wBAChC,OAAO,AAAC,OAAO,QAAQ,QAAS;oBACpC;gBACJ;gBACA,gDAAgD;gBAChD,IAAI,GAAG;oBACH,6CAA6C;oBAC7C,kEAAkE;oBAClE,IAAI,CAAC,KAAK,GAAG,YAAa,KAAK,CAAC,EAAE,IAAI,KAAM,KAAK,CAAC,EAAE;oBACpD,IAAI,MAAM,KAAK,CAAC,EAAE,GAAG,IAAI,MAAM,KAAK,CAAC,EAAE,GAAG;oBAC1C,MAAM,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI;oBAC3B,EAAE,EAAE,CAAC,MAAM,IAAI;oBACf,EAAE,EAAE,CAAC,IAAI;oBACT,KAAK,IAAI;oBACT,EAAE;gBACN,OACK;oBACD,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,EAAE;oBACnB,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC;gBAChB;YACJ;QACJ;QACA,MAAM,KAAK,KAAK,GAAG,KAAK,MAAM,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;QAC1D,6DAA6D;QAC7D,IAAI,CAAC,OAAO,MAAM,GACd,MAAM,MAAM,GAAG,MAAM,GAAG;IAChC;IACA,OAAO,IAAI,GAAG,GAAG,MAAM,KAAK,OAAO;AACvC;AACA,cAAc;AACd,IAAI,OAAO,WAAW,GAAG,AAAC;IACtB,IAAI,IAAI,IAAI,WAAW;IACvB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;QAC1B,IAAI,IAAI,GAAG,IAAI;QACf,MAAO,EAAE,EACL,IAAI,CAAC,AAAC,IAAI,KAAM,CAAC,SAAS,IAAK,MAAM;QACzC,CAAC,CAAC,EAAE,GAAG;IACX;IACA,OAAO;AACX;AACA,QAAQ;AACR,IAAI,MAAM;IACN,IAAI,IAAI,CAAC;IACT,OAAO;QACH,GAAG,SAAU,CAAC;YACV,kCAAkC;YAClC,IAAI,KAAK;YACT,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,EAAE,EAC5B,KAAK,IAAI,CAAC,AAAC,KAAK,MAAO,CAAC,CAAC,EAAE,CAAC,GAAI,OAAO;YAC3C,IAAI;QACR;QACA,GAAG;YAAc,OAAO,CAAC;QAAG;IAChC;AACJ;AACA,UAAU;AACV,IAAI,QAAQ;IACR,IAAI,IAAI,GAAG,IAAI;IACf,OAAO;QACH,GAAG,SAAU,CAAC;YACV,kCAAkC;YAClC,IAAI,IAAI,GAAG,IAAI;YACf,IAAI,IAAI,EAAE,MAAM;YAChB,IAAK,IAAI,IAAI,GAAG,KAAK,GAAI;gBACrB,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,MAAM;gBAC3B,MAAO,IAAI,GAAG,EAAE,EACZ,KAAK,KAAK,CAAC,CAAC,EAAE;gBAClB,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,IAAI,CAAC,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE;YACrE;YACA,IAAI,GAAG,IAAI;QACf;QACA,GAAG;YACC,KAAK,OAAO,KAAK;YACjB,OAAO,CAAC,IAAI,GAAG,KAAK,KAAK,AAAC,MAAM,KAAM,KAAK,CAAC,IAAI,GAAG,KAAK,IAAK,MAAM;QACvE;IACJ;AACJ;;AAEA,oBAAoB;AACpB,IAAI,OAAO,SAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE;IACxC,OAAO,KAAK,KAAK,IAAI,KAAK,IAAI,OAAO,IAAI,IAAI,KAAK,EAAE,IAAI,GAAG,IAAI,OAAO,KAAK,IAAI,CAAC,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,MAAM,MAAM,OAAQ,KAAK,IAAI,GAAG,EAAG,KAAK,MAAM,CAAC;AACzK;AACA,wBAAwB;AACxB,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC;IACpB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EACV,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACf,IAAK,IAAI,KAAK,EACV,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACf,OAAO;AACX;AACA,eAAe;AACf,6FAA6F;AAC7F,qHAAqH;AACrH,gIAAgI;AAChI,iHAAiH;AACjH,qGAAqG;AACrG,oDAAoD;AACpD,IAAI,OAAO,SAAU,EAAE,EAAE,KAAK,EAAE,EAAE;IAC9B,IAAI,KAAK;IACT,IAAI,KAAK,GAAG,QAAQ;IACpB,IAAI,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,OAAO,GAAG,GAAG,WAAW,CAAC,MAAM,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC;IACpF,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM,EAAE,EAAE,EAAG;QAChC,IAAI,IAAI,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;QACxB,IAAI,OAAO,KAAK,YAAY;YACxB,SAAS,MAAM,IAAI;YACnB,IAAI,OAAO,EAAE,QAAQ;YACrB,IAAI,EAAE,SAAS,EAAE;gBACb,qBAAqB;gBACrB,IAAI,KAAK,OAAO,CAAC,oBAAoB,CAAC,GAAG;oBACrC,IAAI,QAAQ,KAAK,OAAO,CAAC,KAAK,KAAK;oBACnC,SAAS,KAAK,KAAK,CAAC,OAAO,KAAK,OAAO,CAAC,KAAK;gBACjD,OACK;oBACD,SAAS;oBACT,IAAK,IAAI,KAAK,EAAE,SAAS,CACrB,SAAS,MAAM,IAAI,gBAAgB,IAAI,MAAM,EAAE,SAAS,CAAC,EAAE,CAAC,QAAQ;gBAC5E;YACJ,OAEI,SAAS;QACjB,OAEI,EAAE,CAAC,EAAE,GAAG;IAChB;IACA,OAAO;QAAC;QAAO;KAAG;AACtB;AACA,IAAI,KAAK,EAAE;AACX,aAAa;AACb,IAAI,OAAO,SAAU,CAAC;IAClB,IAAI,KAAK,EAAE;IACX,IAAK,IAAI,KAAK,EAAG;QACb,IAAI,CAAC,CAAC,EAAE,YAAY,MAAM,CAAC,CAAC,EAAE,YAAY,OAAO,CAAC,CAAC,EAAE,YAAY,KAC7D,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,CAAC,WAAW,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM;IAC1D;IACA,OAAO;AACX;AACA,+BAA+B;AAC/B,IAAI,OAAO,SAAU,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;IAClC,IAAI;IACJ,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE;QACT,IAAI,QAAQ,IAAI,OAAO,CAAC,GAAG,IAAI,IAAI,MAAM,GAAG;QAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EACrB,KAAK,KAAK,GAAG,CAAC,EAAE,EAAE,OAAO,OAAO,QAAQ,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;QAC/D,EAAE,CAAC,GAAG,GAAG,KAAK,GAAG,CAAC,EAAE,EAAE,OAAO;IACjC;IACA,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE;IAC1B,OAAO,GAAG,EAAE,CAAC,GAAG,CAAC,EAAE,GAAG,4EAA4E,KAAK,QAAQ,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK;AAC/I;AACA,wBAAwB;AACxB,IAAI,SAAS;IAAc,OAAO;QAAC;QAAI;QAAK;QAAK;QAAM;QAAM;QAAM;QAAI;QAAI;QAAM;QAAM;QAAK;QAAM;QAAK;QAAM;QAAQ;QAAM;QAAK;QAAO;QAAa;QAAK;KAAI;AAAE;AAC/J,IAAI,QAAQ;IAAc,OAAO;QAAC;QAAI;QAAK;QAAK;QAAM;QAAM;QAAM;QAAO;QAAO;QAAK;QAAK;QAAK;QAAK;QAAK;QAAK;QAAI;QAAM;QAAO;QAAS;QAAO;QAAI;QAAI;QAAM;QAAO;QAAM;QAAM;QAAK;QAAM;QAAM;QAAa;KAAI;AAAE;AACpN,aAAa;AACb,IAAI,MAAM;IAAc,OAAO;QAAC;QAAK;QAAM;QAAQ;QAAK;KAAK;AAAE;AAC/D,eAAe;AACf,IAAI,OAAO;IAAc,OAAO;QAAC;QAAK;KAAI;AAAE;AAC5C,aAAa;AACb,IAAI,MAAM;IAAc,OAAO;QAAC;QAAK;QAAQ;KAAM;AAAE;AACrD,eAAe;AACf,IAAI,OAAO;IAAc,OAAO;QAAC;KAAI;AAAE;AACvC,WAAW;AACX,IAAI,MAAM,SAAU,GAAG;IAAI,OAAO,YAAY,KAAK;QAAC,IAAI,MAAM;KAAC;AAAG;AAClE,SAAS;AACT,IAAI,MAAM,SAAU,CAAC;IAAI,OAAO,KAAK,EAAE,IAAI,IAAI,IAAI,GAAG,EAAE,IAAI;AAAG;AAC/D,eAAe;AACf,IAAI,QAAQ,SAAU,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,EAAE,EAAE,EAAE;IAC9C,IAAI,IAAI,KAAK,KAAK,MAAM,IAAI,SAAU,GAAG,EAAE,GAAG;QAC1C,EAAE,SAAS;QACX,GAAG,KAAK;IACZ;IACA,EAAE,WAAW,CAAC;QAAC;QAAK;KAAK,EAAE,KAAK,OAAO,GAAG;QAAC,IAAI,MAAM;KAAC,GAAG,EAAE;IAC3D,OAAO;QAAc,EAAE,SAAS;IAAI;AACxC;AACA,cAAc;AACd,IAAI,QAAQ,SAAU,IAAI;IACtB,KAAK,MAAM,GAAG,SAAU,GAAG,EAAE,KAAK;QAAI,OAAO,YAAY;YAAC;YAAK;SAAM,EAAE;YAAC,IAAI,MAAM;SAAC;IAAG;IACtF,OAAO,SAAU,EAAE;QAAI,OAAO,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE;IAAG;AACrE;AACA,sBAAsB;AACtB,IAAI,WAAW,SAAU,GAAG,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,EAAE;IAC9C,IAAI;IACJ,IAAI,IAAI,KAAK,KAAK,MAAM,IAAI,SAAU,GAAG,EAAE,GAAG;QAC1C,IAAI,KACA,EAAE,SAAS,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM;aACrC;YACD,IAAI,GAAG,CAAC,EAAE,EACN,EAAE,SAAS;YACf,KAAK,MAAM,CAAC,IAAI,CAAC,MAAM,KAAK,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE;QAC9C;IACJ;IACA,EAAE,WAAW,CAAC;IACd,KAAK,IAAI,GAAG,SAAU,CAAC,EAAE,CAAC;QACtB,IAAI,GACA,MAAM;QACV,IAAI,CAAC,KAAK,MAAM,EACZ,MAAM;QACV,EAAE,WAAW,CAAC;YAAC;YAAG,IAAI;SAAE,EAAE;YAAC,EAAE,MAAM;SAAC;IACxC;IACA,KAAK,SAAS,GAAG;QAAc,EAAE,SAAS;IAAI;AAClD;AACA,eAAe;AACf,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,CAAC,EAAE,GAAI,CAAC,CAAC,IAAI,EAAE,IAAI;AAAI;AAC1D,eAAe;AACf,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,CAAC,CAAC,EAAE,GAAI,CAAC,CAAC,IAAI,EAAE,IAAI,IAAM,CAAC,CAAC,IAAI,EAAE,IAAI,KAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAG,MAAM;AAAG;AACxG,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO,GAAG,GAAG,KAAM,GAAG,GAAG,IAAI,KAAK;AAAa;AAC1E,cAAc;AACd,IAAI,SAAS,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IAC1B,MAAO,GAAG,EAAE,EACR,CAAC,CAAC,EAAE,GAAG,GAAG,OAAO;AACzB;AACA,cAAc;AACd,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC;IACpB,IAAI,KAAK,EAAE,QAAQ;IACnB,CAAC,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,GAAG,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,GAAG,cAAc;IACxG,IAAI,EAAE,KAAK,IAAI,GACX,OAAO,GAAG,GAAG,KAAK,KAAK,CAAC,IAAI,KAAK,EAAE,KAAK,IAAI,KAAK,GAAG,MAAM;IAC9D,IAAI,IAAI;QACJ,CAAC,CAAC,EAAE,GAAG;QACP,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,MAAM,EAAE,EAAE,EAC9B,CAAC,CAAC,IAAI,GAAG,GAAG,GAAG,UAAU,CAAC;IAClC;AACJ;AACA,kDAAkD;AAClD,aAAa;AACb,IAAI,MAAM,SAAU,CAAC;IACjB,IAAI,CAAC,CAAC,EAAE,IAAI,MAAM,CAAC,CAAC,EAAE,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,GACrC,MAAM;IACV,IAAI,MAAM,CAAC,CAAC,EAAE;IACd,IAAI,KAAK;IACT,IAAI,MAAM,GACN,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,IAAI;IACjC,IAAK,IAAI,KAAK,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,KAAK,GAAG,MAAM,CAAC,CAAC,CAAC,KAAK;IAErE,OAAO,KAAK,CAAC,MAAM,CAAC;AACxB;AACA,cAAc;AACd,IAAI,MAAM,SAAU,CAAC;IACjB,IAAI,IAAI,EAAE,MAAM;IAChB,OAAO,CAAC,AAAC,CAAC,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,KAAO,CAAC,CAAC,IAAI,EAAE,IAAI,EAAG,MAAM;AAChF;AACA,qBAAqB;AACrB,IAAI,OAAO,SAAU,CAAC;IAAI,OAAO,KAAK,CAAC,AAAC,EAAE,QAAQ,IAAK,EAAE,QAAQ,CAAC,MAAM,GAAG,KAAO,CAAC;AAAG;AACtF,cAAc;AACd,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC;IACpB,IAAI,KAAK,EAAE,KAAK,EAAE,KAAK,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,MAAM,IAAI,IAAI;IAChE,CAAC,CAAC,EAAE,GAAG,KAAK,CAAC,CAAC,EAAE,GAAG,AAAC,MAAM,IAAK,CAAC,KAAM,KAAK,IAAI,KAAM,CAAC;AAC1D;AACA,aAAa;AACb,IAAI,MAAM,SAAU,CAAC;IACjB,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,KAAK,AAAC,CAAC,CAAC,EAAE,KAAK,IAAK,KAAM,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC,EAAE,IAAI,IAC9D,MAAM;IACV,IAAI,CAAC,CAAC,EAAE,GAAG,IACP,MAAM;AACd;AACA,SAAS,aAAa,IAAI,EAAE,EAAE;IAC1B,IAAI,CAAC,MAAM,OAAO,QAAQ,YACtB,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,CAAC,MAAM,GAAG;IACd,OAAO;AACX;AACA,mCAAmC;AACnC;;CAEC,GACD,IAAI,UAAyB;IACzB,SAAS,QAAQ,IAAI,EAAE,EAAE;QACrB,IAAI,CAAC,MAAM,OAAO,QAAQ,YACtB,KAAK,MAAM,OAAO,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,CAAC,GAAG,QAAQ,CAAC;IACtB;IACA,QAAQ,SAAS,CAAC,CAAC,GAAG,SAAU,CAAC,EAAE,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,CAAC,IAAI;IAC3C;IACA;;;;KAIC,GACD,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC3C,IAAI,IAAI,CAAC,CAAC,EACN,MAAM;QACV,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,MAAM;QACV,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,CAAC,OAAO,SAAS;IAC3B;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,eAA8B;IAC9B,SAAS,aAAa,IAAI,EAAE,EAAE;QAC1B,SAAS;YACL;YACA;gBAAc,OAAO;oBAAC;oBAAO;iBAAQ;YAAE;SAC1C,EAAE,IAAI,EAAE,aAAa,IAAI,CAAC,IAAI,EAAE,MAAM,KAAK,SAAU,EAAE;YACpD,IAAI,OAAO,IAAI,QAAQ,GAAG,IAAI;YAC9B,YAAY,MAAM;QACtB,GAAG;IACP;IACA,OAAO;AACX;;AAEO,SAAS,QAAQ,IAAI,EAAE,IAAI,EAAE,EAAE;IAClC,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,OAAO,MAAM,MAAM,MAAM;QACrB;KACH,EAAE,SAAU,EAAE;QAAI,OAAO,IAAI,YAAY,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE;IAAI,GAAG,GAAG;AAC9E;AAOO,SAAS,YAAY,IAAI,EAAE,IAAI;IAClC,OAAO,KAAK,MAAM,QAAQ,CAAC,GAAG,GAAG;AACrC;AACA;;CAEC,GACD,IAAI,UAAyB;IACzB;;;KAGC,GACD,SAAS,QAAQ,EAAE;QACf,IAAI,CAAC,CAAC,GAAG,CAAC;QACV,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,QAAQ,SAAS,CAAC,CAAC,GAAG,SAAU,CAAC;QAC7B,IAAI,IAAI,CAAC,CAAC,EACN,MAAM;QACV,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,MAAM;QACV,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM;QACrB,IAAI,IAAI,IAAI,GAAG,IAAI,EAAE,MAAM;QAC3B,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,GAAG;IACzC;IACA,QAAQ,SAAS,CAAC,CAAC,GAAG,SAAU,KAAK;QACjC,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,SAAS;QAC7B,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC,CAAC;QAClB,IAAI,KAAK,MAAM,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC;QAC1C,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,QAAQ,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM;QAC5D,IAAI,CAAC,CAAC,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,AAAC,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI;IAC1D;IACA;;;;KAIC,GACD,QAAQ,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC3C,IAAI,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,CAAC;IAC1B;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,eAA8B;IAC9B;;;KAGC,GACD,SAAS,aAAa,EAAE;QACpB,IAAI,CAAC,MAAM,GAAG;QACd,SAAS;YACL;YACA;gBAAc,OAAO;oBAAC;oBAAO;iBAAQ;YAAE;SAC1C,EAAE,IAAI,EAAE,GAAG;YACR,IAAI,OAAO,IAAI;YACf,YAAY,MAAM;QACtB,GAAG;IACP;IACA,OAAO;AACX;;AAEO,SAAS,QAAQ,IAAI,EAAE,IAAI,EAAE,EAAE;IAClC,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,OAAO,MAAM,MAAM,MAAM;QACrB;KACH,EAAE,SAAU,EAAE;QAAI,OAAO,IAAI,YAAY,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI,CAAC,EAAE;IAAK,GAAG,GAAG;AACnF;AAOO,SAAS,YAAY,IAAI,EAAE,GAAG;IACjC,OAAO,MAAM,MAAM;AACvB;AACA,2GAA2G;AAC3G;;CAEC,GACD,IAAI,OAAsB;IACtB,SAAS,KAAK,IAAI,EAAE,EAAE;QAClB,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,QAAQ,IAAI,CAAC,IAAI,EAAE,MAAM;IAC7B;IACA;;;;KAIC,GACD,KAAK,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QACxC,QAAQ,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;IAC7C;IACA,KAAK,SAAS,CAAC,CAAC,GAAG,SAAU,CAAC,EAAE,CAAC;QAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,IAAI,CAAC,CAAC,IAAI,EAAE,MAAM;QAClB,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC;QAC3D,IAAI,IAAI,CAAC,CAAC,EACN,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG;QAC/B,IAAI,GACA,OAAO,KAAK,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,OAAO,KAAK,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC;QAC/E,IAAI,CAAC,MAAM,CAAC,KAAK;IACrB;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,YAA2B;IAC3B,SAAS,UAAU,IAAI,EAAE,EAAE;QACvB,SAAS;YACL;YACA;YACA;gBAAc,OAAO;oBAAC;oBAAO;oBAAS;iBAAK;YAAE;SAChD,EAAE,IAAI,EAAE,aAAa,IAAI,CAAC,IAAI,EAAE,MAAM,KAAK,SAAU,EAAE;YACpD,IAAI,OAAO,IAAI,KAAK,GAAG,IAAI;YAC3B,YAAY,MAAM;QACtB,GAAG;IACP;IACA,OAAO;AACX;;AAEO,SAAS,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE;IAC/B,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,OAAO,MAAM,MAAM,MAAM;QACrB;QACA;QACA;YAAc,OAAO;gBAAC;aAAS;QAAE;KACpC,EAAE,SAAU,EAAE;QAAI,OAAO,IAAI,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE;IAAI,GAAG,GAAG;AAC3E;AAOO,SAAS,SAAS,IAAI,EAAE,IAAI;IAC/B,IAAI,CAAC,MACD,OAAO,CAAC;IACZ,IAAI,IAAI,OAAO,IAAI,KAAK,MAAM;IAC9B,EAAE,CAAC,CAAC;IACJ,IAAI,IAAI,KAAK,MAAM,MAAM,KAAK,OAAO,IAAI,IAAI,EAAE,MAAM;IACrD,OAAO,IAAI,GAAG,OAAO,OAAO,GAAG,IAAI,GAAG,EAAE,CAAC,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI;AACvE;AACA;;CAEC,GACD,IAAI,SAAwB;IACxB;;;KAGC,GACD,SAAS,OAAO,EAAE;QACd,IAAI,CAAC,CAAC,GAAG;QACT,QAAQ,IAAI,CAAC,IAAI,EAAE;IACvB;IACA;;;;KAIC,GACD,OAAO,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC1C,QAAQ,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;QAC/B,IAAI,IAAI,CAAC,CAAC,EAAE;YACR,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,IAAI,IAAI,IAAI,CAAC,CAAC,IAAI;YAC1C,IAAI,KAAK,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC,OACvB;YACJ,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG;QAC1C;QACA,IAAI,OAAO;YACP,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,GAChB,MAAM;YACV,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;QACjC;QACA,uDAAuD;QACvD,sDAAsD;QACtD,QAAQ,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;IACnC;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,cAA6B;IAC7B;;;KAGC,GACD,SAAS,YAAY,EAAE;QACnB,IAAI,CAAC,MAAM,GAAG;QACd,SAAS;YACL;YACA;YACA;gBAAc,OAAO;oBAAC;oBAAO;oBAAS;iBAAO;YAAE;SAClD,EAAE,IAAI,EAAE,GAAG;YACR,IAAI,OAAO,IAAI;YACf,YAAY,MAAM;QACtB,GAAG;IACP;IACA,OAAO;AACX;;AAEO,SAAS,OAAO,IAAI,EAAE,IAAI,EAAE,EAAE;IACjC,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,OAAO,MAAM,MAAM,MAAM;QACrB;QACA;QACA;YAAc,OAAO;gBAAC;aAAW;QAAE;KACtC,EAAE,SAAU,EAAE;QAAI,OAAO,IAAI,WAAW,GAAG,IAAI,CAAC,EAAE;IAAI,GAAG,GAAG;AACjE;AAOO,SAAS,WAAW,IAAI,EAAE,GAAG;IAChC,OAAO,MAAM,KAAK,QAAQ,CAAC,IAAI,OAAO,CAAC,IAAI,OAAO,IAAI,GAAG,IAAI;AACjE;AACA;;CAEC,GACD,IAAI,OAAsB;IACtB,SAAS,KAAK,IAAI,EAAE,EAAE;QAClB,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,QAAQ,IAAI,CAAC,IAAI,EAAE,MAAM;IAC7B;IACA;;;;KAIC,GACD,KAAK,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QACxC,QAAQ,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;IAC7C;IACA,KAAK,SAAS,CAAC,CAAC,GAAG,SAAU,CAAC,EAAE,CAAC;QAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,IAAI,MAAM,KAAK,GAAG,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC;QAChD,IAAI,IAAI,CAAC,CAAC,EACN,IAAI,KAAK,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,GAAG;QAC/B,IAAI,GACA,OAAO,KAAK,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,CAAC,KAAK;IACrB;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,YAA2B;IAC3B,SAAS,UAAU,IAAI,EAAE,EAAE;QACvB,SAAS;YACL;YACA;YACA;gBAAc,OAAO;oBAAC;oBAAO;oBAAS;iBAAK;YAAE;SAChD,EAAE,IAAI,EAAE,aAAa,IAAI,CAAC,IAAI,EAAE,MAAM,KAAK,SAAU,EAAE;YACpD,IAAI,OAAO,IAAI,KAAK,GAAG,IAAI;YAC3B,YAAY,MAAM;QACtB,GAAG;IACP;IACA,OAAO;AACX;;AAEO,SAAS,KAAK,IAAI,EAAE,IAAI,EAAE,EAAE;IAC/B,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,OAAO,MAAM,MAAM,MAAM;QACrB;QACA;QACA;YAAc,OAAO;gBAAC;aAAS;QAAE;KACpC,EAAE,SAAU,EAAE;QAAI,OAAO,IAAI,SAAS,GAAG,IAAI,CAAC,EAAE,EAAE,GAAG,IAAI,CAAC,EAAE;IAAI,GAAG,GAAG;AAC3E;AAOO,SAAS,SAAS,IAAI,EAAE,IAAI;IAC/B,IAAI,CAAC,MACD,OAAO,CAAC;IACZ,IAAI,IAAI;IACR,EAAE,CAAC,CAAC;IACJ,IAAI,IAAI,KAAK,MAAM,MAAM,GAAG;IAC5B,OAAO,IAAI,GAAG,OAAO,OAAO,GAAG,EAAE,MAAM,GAAG,GAAG,EAAE,CAAC,KAAK;AACzD;AACA;;CAEC,GACD,IAAI,SAAwB;IACxB;;;KAGC,GACD,SAAS,OAAO,EAAE;QACd,IAAI,CAAC,CAAC,GAAG;QACT,QAAQ,IAAI,CAAC,IAAI,EAAE;IACvB;IACA;;;;KAIC,GACD,OAAO,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC1C,QAAQ,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;QAC/B,IAAI,IAAI,CAAC,CAAC,EAAE;YACR,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,KAAK,CAAC,OACtB;YACJ,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,GAAG;QAC1C;QACA,IAAI,OAAO;YACP,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,GAChB,MAAM;YACV,IAAI,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC;QACjC;QACA,uDAAuD;QACvD,sDAAsD;QACtD,QAAQ,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE;IACnC;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,cAA6B;IAC7B;;;KAGC,GACD,SAAS,YAAY,EAAE;QACnB,IAAI,CAAC,MAAM,GAAG;QACd,SAAS;YACL;YACA;YACA;gBAAc,OAAO;oBAAC;oBAAO;oBAAS;iBAAO;YAAE;SAClD,EAAE,IAAI,EAAE,GAAG;YACR,IAAI,OAAO,IAAI;YACf,YAAY,MAAM;QACtB,GAAG;IACP;IACA,OAAO;AACX;;AAEO,SAAS,OAAO,IAAI,EAAE,IAAI,EAAE,EAAE;IACjC,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,OAAO,MAAM,MAAM,MAAM;QACrB;QACA;QACA;YAAc,OAAO;gBAAC;aAAW;QAAE;KACtC,EAAE,SAAU,EAAE;QAAI,OAAO,IAAI,WAAW,GAAG,IAAI,CAAC,EAAE,EAAE,IAAI,GAAG,IAAI,CAAC,EAAE;IAAK,GAAG,GAAG;AAClF;AAOO,SAAS,WAAW,IAAI,EAAE,GAAG;IAChC,OAAO,MAAM,CAAC,IAAI,OAAO,KAAK,QAAQ,CAAC,GAAG,CAAC,EAAE,GAAG;AACpD;;;AAKA;;CAEC,GACD,IAAI,aAA4B;IAC5B;;;KAGC,GACD,SAAS,WAAW,EAAE;QAClB,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;;;KAIC,GACD,WAAW,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,MAAM;QACV,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;YACT,IAAI,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE;gBACzB,IAAI,IAAI,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,MAAM;gBAC3C,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM;YAC7C,OAEI,IAAI,CAAC,CAAC,GAAG;YACb,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG;gBACnB,IAAI,UAAU,IAAI;gBAClB,IAAI,KAAK;oBAAc,QAAQ,MAAM,CAAC,KAAK,CAAC,SAAS;gBAAY;gBACjE,IAAI,CAAC,CAAC,GAAG,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,IACxD,IAAI,IAAI,CAAC,CAAC,CAAC,MACX,AAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,KAAK,AAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,IAAK,KAAM,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,KAC9E,IAAI,IAAI,CAAC,CAAC,CAAC,MACX,IAAI,IAAI,CAAC,CAAC,CAAC;gBACrB,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;gBACpB,IAAI,CAAC,CAAC,GAAG;YACb;QACJ,OAEI,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;IAC3B;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,kBAAiC;IACjC;;;GAGD,GACC,SAAS,gBAAgB,EAAE;QACvB,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;;;KAIC,GACD,gBAAgB,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QACnD,WAAW,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;IAChD;IACA,OAAO;AACX;;AAEO,SAAS,WAAW,IAAI,EAAE,IAAI,EAAE,EAAE;IACrC,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,OAAO,AAAC,IAAI,CAAC,EAAE,IAAI,MAAM,IAAI,CAAC,EAAE,IAAI,OAAO,IAAI,CAAC,EAAE,IAAI,IAChD,OAAO,MAAM,MAAM,MACnB,AAAC,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,KAAK,AAAC,IAAI,CAAC,EAAE,IAAI,IAAK,KAAM,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC,EAAE,IAAI,KACtE,QAAQ,MAAM,MAAM,MACpB,OAAO,MAAM,MAAM;AACjC;AAOO,SAAS,eAAe,IAAI,EAAE,GAAG;IACpC,OAAO,AAAC,IAAI,CAAC,EAAE,IAAI,MAAM,IAAI,CAAC,EAAE,IAAI,OAAO,IAAI,CAAC,EAAE,IAAI,IAChD,WAAW,MAAM,OACjB,AAAC,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,KAAK,KAAK,AAAC,IAAI,CAAC,EAAE,IAAI,IAAK,KAAM,CAAC,IAAI,CAAC,EAAE,IAAI,IAAI,IAAI,CAAC,EAAE,IAAI,KACtE,YAAY,MAAM,OAClB,WAAW,MAAM;AAC/B;AACA,gCAAgC;AAChC,IAAI,OAAO,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC3B,IAAK,IAAI,KAAK,EAAG;QACb,IAAI,MAAM,CAAC,CAAC,EAAE,EAAE,IAAI,IAAI;QACxB,IAAI,eAAe,IACf,CAAC,CAAC,EAAE,GAAG;YAAC;YAAK;SAAE;aACd,IAAI,MAAM,OAAO,CAAC,MACnB,CAAC,CAAC,EAAE,GAAG;YAAC,GAAG,CAAC,EAAE;YAAE,IAAI,GAAG,GAAG,CAAC,EAAE;SAAE;aAE/B,KAAK,KAAK,IAAI,KAAK,GAAG;IAC9B;AACJ;AACA,eAAe;AACf,IAAI,KAAK,OAAO,eAAe,eAAe,WAAW,GAAG,IAAI;AAChE,eAAe;AACf,IAAI,KAAK,OAAO,eAAe,eAAe,WAAW,GAAG,IAAI;AAChE,sBAAsB;AACtB,IAAI,MAAM;AACV,IAAI;IACA,GAAG,MAAM,CAAC,IAAI;QAAE,QAAQ;IAAK;IAC7B,MAAM;AACV,EACA,OAAO,GAAG,CAAE;AACZ,cAAc;AACd,IAAI,QAAQ,SAAU,CAAC;IACnB,IAAK,IAAI,IAAI,IAAI,IAAI,IAAK;QACtB,IAAI,IAAI,CAAC,CAAC,IAAI;QACd,IAAI,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG;QACzC,IAAI,IAAI,KAAK,EAAE,MAAM,EACjB,OAAO;YAAC;YAAG,IAAI,GAAG,IAAI;SAAG;QAC7B,IAAI,CAAC,IACD,KAAK,OAAO,YAAY,CAAC;aACxB,IAAI,MAAM,GAAG;YACd,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,KAAK,IAAK,CAAC,CAAC,IAAI,GAAG,EAAG,IAAI,OAC9E,KAAK,OAAO,YAAY,CAAC,QAAS,KAAK,IAAK,QAAS,IAAI;QACjE,OACK,IAAI,KAAK,GACV,KAAK,OAAO,YAAY,CAAC,CAAC,IAAI,EAAE,KAAK,IAAK,CAAC,CAAC,IAAI,GAAG;aAEnD,KAAK,OAAO,YAAY,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE,KAAK,IAAK,CAAC,CAAC,IAAI,GAAG;IACjF;AACJ;AACA;;CAEC,GACD,IAAI,aAA4B;IAC5B;;;KAGC,GACD,SAAS,WAAW,EAAE;QAClB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,KACA,IAAI,CAAC,CAAC,GAAG,IAAI;aAEb,IAAI,CAAC,CAAC,GAAG;IACjB;IACA;;;;KAIC,GACD,WAAW,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,MAAM;QACV,QAAQ,CAAC,CAAC;QACV,IAAI,IAAI,CAAC,CAAC,EAAE;YACR,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO;gBAAE,QAAQ;YAAK,IAAI;YACpD,IAAI,OAAO;gBACP,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,EACtB,MAAM;gBACV,IAAI,CAAC,CAAC,GAAG;YACb;YACA;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,CAAC,EACP,MAAM;QACV,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,MAAM;QAC7C,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;QACd,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM;QAC5B,IAAI,KAAK,MAAM,MAAM,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE;QAC3C,IAAI,OAAO;YACP,IAAI,GAAG,MAAM,EACT,MAAM;YACV,IAAI,CAAC,CAAC,GAAG;QACb,OAEI,IAAI,CAAC,CAAC,GAAG;QACb,IAAI,CAAC,MAAM,CAAC,IAAI;IACpB;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,aAA4B;IAC5B;;;KAGC,GACD,SAAS,WAAW,EAAE;QAClB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;;;KAIC,GACD,WAAW,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC9C,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,MAAM;QACV,IAAI,IAAI,CAAC,CAAC,EACN,MAAM;QACV,IAAI,CAAC,MAAM,CAAC,QAAQ,QAAQ,IAAI,CAAC,CAAC,GAAG,SAAS;IAClD;IACA,OAAO;AACX;;AASO,SAAS,QAAQ,GAAG,EAAE,MAAM;IAC/B,IAAI,QAAQ;QACR,IAAI,OAAO,IAAI,GAAG,IAAI,MAAM;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,EAAE,EAC9B,IAAI,CAAC,EAAE,GAAG,IAAI,UAAU,CAAC;QAC7B,OAAO;IACX;IACA,IAAI,IACA,OAAO,GAAG,MAAM,CAAC;IACrB,IAAI,IAAI,IAAI,MAAM;IAClB,IAAI,KAAK,IAAI,GAAG,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,IAAI,CAAC;IAC7C,IAAI,KAAK;IACT,IAAI,IAAI,SAAU,CAAC;QAAI,EAAE,CAAC,KAAK,GAAG;IAAG;IACrC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACxB,IAAI,KAAK,IAAI,GAAG,MAAM,EAAE;YACpB,IAAI,IAAI,IAAI,GAAG,KAAK,IAAI,CAAC,AAAC,IAAI,KAAM,CAAC;YACrC,EAAE,GAAG,CAAC;YACN,KAAK;QACT;QACA,IAAI,IAAI,IAAI,UAAU,CAAC;QACvB,IAAI,IAAI,OAAO,QACX,EAAE;aACD,IAAI,IAAI,MACT,EAAE,MAAO,KAAK,IAAK,EAAE,MAAO,IAAI;aAC/B,IAAI,IAAI,SAAS,IAAI,OACtB,IAAI,QAAQ,CAAC,IAAI,QAAQ,EAAE,IAAK,IAAI,UAAU,CAAC,EAAE,KAAK,MAClD,EAAE,MAAO,KAAK,KAAM,EAAE,MAAO,AAAC,KAAK,KAAM,KAAM,EAAE,MAAO,AAAC,KAAK,IAAK,KAAM,EAAE,MAAO,IAAI;aAE1F,EAAE,MAAO,KAAK,KAAM,EAAE,MAAO,AAAC,KAAK,IAAK,KAAM,EAAE,MAAO,IAAI;IACnE;IACA,OAAO,IAAI,IAAI,GAAG;AACtB;AAQO,SAAS,UAAU,GAAG,EAAE,MAAM;IACjC,IAAI,QAAQ;QACR,IAAI,IAAI;QACR,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,KAAK,MACjC,KAAK,OAAO,YAAY,CAAC,KAAK,CAAC,MAAM,IAAI,QAAQ,CAAC,GAAG,IAAI;QAC7D,OAAO;IACX,OACK,IAAI,IACL,OAAO,GAAG,MAAM,CAAC;SAChB;QACD,IAAI,KAAK,MAAM,MAAM,MAAM,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;QAC7C,IAAI,IAAI,MAAM,EACV,MAAM;QACV,OAAO;IACX;AACJ;;AAEA,mBAAmB;AACnB,IAAI,MAAM,SAAU,CAAC;IAAI,OAAO,KAAK,IAAI,IAAI,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAAG;AACzE,wBAAwB;AACxB,IAAI,OAAO,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO,IAAI,KAAK,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,IAAI;AAAK;AAC5E,kBAAkB;AAClB,IAAI,KAAK,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;IACtB,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,KAAK,UAAU,EAAE,QAAQ,CAAC,IAAI,IAAI,IAAI,KAAK,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG,GAAG,IAAI;IACrI,IAAI,KAAK,KAAK,MAAM,aAAa,KAAK,GAAG,MAAM;QAAC;QAAI,GAAG,GAAG,IAAI;QAAK,GAAG,GAAG,IAAI;KAAI,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;IACtH,OAAO;QAAC,GAAG,GAAG,IAAI;QAAK;QAAI;QAAI;QAAI,KAAK,GAAG,GAAG,IAAI,MAAM,GAAG,GAAG,IAAI;QAAK;KAAI;AAC/E;AACA,yBAAyB;AACzB,IAAI,OAAO,SAAU,CAAC,EAAE,CAAC;IACrB,MAAO,GAAG,GAAG,MAAM,GAAG,KAAK,IAAI,GAAG,GAAG,IAAI;IAEzC,OAAO;QAAC,GAAG,GAAG,IAAI;QAAK,GAAG,GAAG,IAAI;QAAI,GAAG,GAAG,IAAI;KAAI;AACvD;AACA,qBAAqB;AACrB,IAAI,OAAO,SAAU,EAAE;IACnB,IAAI,KAAK;IACT,IAAI,IAAI;QACJ,IAAK,IAAI,KAAK,GAAI;YACd,IAAI,IAAI,EAAE,CAAC,EAAE,CAAC,MAAM;YACpB,IAAI,IAAI,OACJ,MAAM;YACV,MAAM,IAAI;QACd;IACJ;IACA,OAAO;AACX;AACA,mBAAmB;AACnB,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;IACzC,IAAI,KAAK,GAAG,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,MAAM,GAAG,MAAM;IACvD,IAAI,MAAM,KAAK;IACf,OAAO,GAAG,GAAG,MAAM,OAAO,YAAY,YAAY,KAAK;IACvD,IAAI,MAAM,MACN,CAAC,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC,IAAI,GAAG,EAAE,EAAE;IAC9B,CAAC,CAAC,EAAE,GAAG,IAAI,KAAK,GAAG,gCAAgC;IACnD,CAAC,CAAC,IAAI,GAAG,AAAC,EAAE,IAAI,IAAI,IAAK,CAAC,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,KAAK;IACzD,CAAC,CAAC,IAAI,GAAG,EAAE,WAAW,GAAG,KAAK,CAAC,CAAC,IAAI,GAAG,EAAE,WAAW,IAAI;IACxD,IAAI,KAAK,IAAI,KAAK,EAAE,KAAK,IAAI,OAAO,KAAK,GAAG,KAAK,EAAE,KAAK,GAAG,IAAI,GAAG,WAAW,KAAK;IAClF,IAAI,IAAI,KAAK,IAAI,KACb,MAAM;IACV,OAAO,GAAG,GAAG,AAAC,KAAK,KAAO,AAAC,GAAG,QAAQ,KAAK,KAAM,KAAO,GAAG,OAAO,MAAM,KAAO,GAAG,QAAQ,MAAM,KAAO,GAAG,UAAU,MAAM,IAAM,GAAG,UAAU,OAAO,IAAK,KAAK;IAC9J,IAAI,KAAK,MAAM;QACX,OAAO,GAAG,GAAG,EAAE,GAAG;QAClB,OAAO,GAAG,IAAI,GAAG;QACjB,OAAO,GAAG,IAAI,GAAG,EAAE,IAAI;IAC3B;IACA,OAAO,GAAG,IAAI,IAAI;IAClB,OAAO,GAAG,IAAI,IAAI,MAAM,KAAK;IAC7B,IAAI,MAAM,MAAM;QACZ,OAAO,GAAG,GAAG;QACb,OAAO,GAAG,IAAI,GAAG,EAAE,KAAK;QACxB,OAAO,GAAG,IAAI,IAAI,KAAK,KAAK;IAChC;IACA,EAAE,GAAG,CAAC,IAAI;IACV,KAAK;IACL,IAAI,KAAK;QACL,IAAK,IAAI,KAAK,GAAI;YACd,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,IAAI,MAAM;YAC/B,OAAO,GAAG,GAAG,CAAC;YACd,OAAO,GAAG,IAAI,GAAG;YACjB,EAAE,GAAG,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI;QAChC;IACJ;IACA,IAAI,KACA,EAAE,GAAG,CAAC,IAAI,IAAI,KAAK;IACvB,OAAO;AACX;AACA,8CAA8C;AAC9C,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;IAC7B,OAAO,GAAG,GAAG,YAAY,YAAY;IACrC,OAAO,GAAG,IAAI,GAAG;IACjB,OAAO,GAAG,IAAI,IAAI;IAClB,OAAO,GAAG,IAAI,IAAI;IAClB,OAAO,GAAG,IAAI,IAAI;AACtB;AACA;;CAEC,GACD,IAAI,iBAAgC;IAChC;;;KAGC,GACD,SAAS,eAAe,QAAQ;QAC5B,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,WAAW,GAAG;IACvB;IACA;;;;;;;KAOC,GACD,eAAe,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK,EAAE,KAAK;QACrD,IAAI,CAAC,MAAM,CAAC,MAAM,OAAO;IAC7B;IACA;;;;;;KAMC,GACD,eAAe,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAClD,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,MAAM;QACV,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;QACT,IAAI,CAAC,IAAI,IAAI,MAAM,MAAM;QACzB,IAAI,OACA,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,OAAO,SAAS;IACjC;IACA,OAAO;AACX;;AAEA,wEAAwE;AACxE;;;CAGC,GACD,IAAI,aAA4B;IAC5B;;;;KAIC,GACD,SAAS,WAAW,QAAQ,EAAE,IAAI;QAC9B,IAAI,UAAU,IAAI;QAClB,IAAI,CAAC,MACD,OAAO,CAAC;QACZ,eAAe,IAAI,CAAC,IAAI,EAAE;QAC1B,IAAI,CAAC,CAAC,GAAG,IAAI,QAAQ,MAAM,SAAU,GAAG,EAAE,KAAK;YAC3C,QAAQ,MAAM,CAAC,MAAM,KAAK;QAC9B;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,KAAK;IAC9B;IACA,WAAW,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK,EAAE,KAAK;QACjD,IAAI;YACA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;QACvB,EACA,OAAO,GAAG;YACN,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM;QACzB;IACJ;IACA;;;;KAIC,GACD,WAAW,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QAC9C,eAAe,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;IACpD;IACA,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,kBAAiC;IACjC;;;;KAIC,GACD,SAAS,gBAAgB,QAAQ,EAAE,IAAI;QACnC,IAAI,UAAU,IAAI;QAClB,IAAI,CAAC,MACD,OAAO,CAAC;QACZ,eAAe,IAAI,CAAC,IAAI,EAAE;QAC1B,IAAI,CAAC,CAAC,GAAG,IAAI,aAAa,MAAM,SAAU,GAAG,EAAE,GAAG,EAAE,KAAK;YACrD,QAAQ,MAAM,CAAC,KAAK,KAAK;QAC7B;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,KAAK;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS;IACrC;IACA,gBAAgB,SAAS,CAAC,OAAO,GAAG,SAAU,KAAK,EAAE,KAAK;QACtD,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO;IACvB;IACA;;;;KAIC,GACD,gBAAgB,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QACnD,eAAe,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO;IACpD;IACA,OAAO;AACX;;AAEA,4BAA4B;AAC5B;;CAEC,GACD,IAAI,MAAqB;IACrB;;;;KAIC,GACD,SAAS,IAAI,EAAE;QACX,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,CAAC,GAAG,EAAE;QACX,IAAI,CAAC,CAAC,GAAG;IACb;IACA;;;KAGC,GACD,IAAI,SAAS,CAAC,GAAG,GAAG,SAAU,IAAI;QAC9B,IAAI,UAAU,IAAI;QAClB,IAAI,IAAI,CAAC,CAAC,GAAG,GACT,MAAM;QACV,IAAI,IAAI,QAAQ,KAAK,QAAQ,GAAG,KAAK,EAAE,MAAM;QAC7C,IAAI,MAAM,KAAK,OAAO,EAAE,IAAI,OAAO,QAAQ;QAC3C,IAAI,IAAI,MAAM,KAAK,QAAQ,CAAC,MAAM,IAAK,KAAM,IAAI,MAAM,IAAI,EAAE,MAAM;QACnE,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,IAAI;QACjC,IAAI,KAAK,OACL,MAAM;QACV,IAAI,SAAS,IAAI,GAAG;QACpB,IAAI,QAAQ,GAAG,MAAM,GAAG;QACxB,IAAI,OAAO;YAAC;SAAO;QACnB,IAAI,OAAO;YACP,IAAK,IAAI,KAAK,GAAG,SAAS,MAAM,KAAK,OAAO,MAAM,EAAE,KAAM;gBACtD,IAAI,MAAM,MAAM,CAAC,GAAG;gBACpB,QAAQ,MAAM,CAAC,MAAM,KAAK;YAC9B;YACA,OAAO,EAAE;QACb;QACA,IAAI,KAAK,IAAI,CAAC,CAAC;QACf,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC,MAAM;QACvB,IAAI,KAAK,IAAI,MAAM;YACf,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG;gBACC,IAAI,KAAK,SAAS,EACd,KAAK,SAAS;YACtB;YACA,GAAG;gBACC;gBACA,IAAI,IAAI;oBACJ,IAAI,MAAM,QAAQ,CAAC,CAAC,MAAM,EAAE;oBAC5B,IAAI,KACA,IAAI,CAAC;yBAEL,QAAQ,CAAC,GAAG;gBACpB;gBACA,KAAK;YACT;QACJ;QACA,IAAI,KAAK;QACT,KAAK,MAAM,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,KAAK;YACnC,IAAI,KAAK;gBACL,QAAQ,MAAM,CAAC,KAAK,KAAK;gBACzB,QAAQ,SAAS;YACrB,OACK;gBACD,MAAM,IAAI,MAAM;gBAChB,KAAK,IAAI,CAAC;gBACV,IAAI,OAAO;oBACP,IAAI,KAAK,IAAI,GAAG;oBAChB,OAAO,IAAI,GAAG;oBACd,OAAO,IAAI,GAAG,KAAK,GAAG;oBACtB,OAAO,IAAI,GAAG;oBACd,OAAO,IAAI,IAAI,KAAK,IAAI;oBACxB,KAAK,IAAI,CAAC;oBACV,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,GAAG,KAAK,KAAK,IAAI,GAAG,GAAG,GAAG,KAAK,GAAG,EAAE,GAAG,IAAI,GAAG,KAAK,IAAI;oBACtE,IAAI,IACA,GAAG,CAAC;oBACR,KAAK;gBACT,OACK,IAAI,IACL;YACR;QACJ;QACA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;IAChB;IACA;;;;KAIC,GACD,IAAI,SAAS,CAAC,GAAG,GAAG;QAChB,IAAI,UAAU,IAAI;QAClB,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG;YACZ,IAAI,IAAI,CAAC,CAAC,GAAG,GACT,MAAM;YACV,MAAM;QACV;QACA,IAAI,IAAI,CAAC,CAAC,EACN,IAAI,CAAC,CAAC;aAEN,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;YACR,GAAG;gBACC,IAAI,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,GACf;gBACJ,QAAQ,CAAC,CAAC,MAAM,CAAC,CAAC,GAAG;gBACrB,QAAQ,CAAC;YACb;YACA,GAAG,YAAc;QACrB;QACJ,IAAI,CAAC,CAAC,GAAG;IACb;IACA,IAAI,SAAS,CAAC,CAAC,GAAG;QACd,IAAI,KAAK,GAAG,IAAI,GAAG,KAAK;QACxB,IAAK,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;YAChD,IAAI,IAAI,EAAE,CAAC,GAAG;YACd,MAAM,KAAK,EAAE,CAAC,CAAC,MAAM,GAAG,KAAK,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC;QACjE;QACA,IAAI,MAAM,IAAI,GAAG,KAAK;QACtB,IAAK,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;YAChD,IAAI,IAAI,EAAE,CAAC,GAAG;YACd,IAAI,KAAK,IAAI,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC;YACrC,MAAM,KAAK,EAAE,CAAC,CAAC,MAAM,GAAG,KAAK,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,KAAK,EAAE,CAAC;QAC5E;QACA,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,MAAM,EAAE,IAAI;QAChC,IAAI,CAAC,MAAM,CAAC,MAAM,KAAK;QACvB,IAAI,CAAC,CAAC,GAAG;IACb;IACA;;;KAGC,GACD,IAAI,SAAS,CAAC,SAAS,GAAG;QACtB,IAAK,IAAI,KAAK,GAAG,KAAK,IAAI,CAAC,CAAC,EAAE,KAAK,GAAG,MAAM,EAAE,KAAM;YAChD,IAAI,IAAI,EAAE,CAAC,GAAG;YACd,EAAE,CAAC;QACP;QACA,IAAI,CAAC,CAAC,GAAG;IACb;IACA,OAAO;AACX;;AAEO,SAAS,IAAI,IAAI,EAAE,IAAI,EAAE,EAAE;IAC9B,IAAI,CAAC,IACD,KAAK,MAAM,OAAO,CAAC;IACvB,IAAI,OAAO,MAAM,YACb,MAAM;IACV,IAAI,IAAI,CAAC;IACT,KAAK,MAAM,IAAI,GAAG;IAClB,IAAI,IAAI,OAAO,IAAI,CAAC;IACpB,IAAI,MAAM,EAAE,MAAM,EAAE,IAAI,GAAG,MAAM;IACjC,IAAI,OAAO,KAAK,QAAQ,IAAI,MAAM;IAClC,IAAI,OAAO,EAAE;IACb,IAAI,OAAO;QACP,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAC/B,IAAI,CAAC,EAAE;IACf;IACA,IAAI,MAAM;QACN,IAAI,MAAM,IAAI,GAAG,MAAM,KAAK,KAAK,GAAG,MAAM,MAAM;QAChD,MAAM;QACN,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,EAAE,EAAG;YAC3B,IAAI,IAAI,KAAK,CAAC,EAAE;YAChB,IAAI;gBACA,IAAI,IAAI,EAAE,CAAC,CAAC,MAAM;gBAClB,IAAI,KAAK,KAAK,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE;gBAC3B,IAAI,OAAO,KAAK,EAAE,CAAC,CAAC,MAAM,GAAG,KAAK,EAAE,KAAK;gBACzC,IAAI,MAAM,MAAM;gBAChB,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE;gBACb,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,GAAG,KAAK,EAAE,CAAC,GAAG,KAAK,KAAK,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC,GAAG,MAAM,MAAM;YAChG,EACA,OAAO,GAAG;gBACN,OAAO,GAAG,GAAG;YACjB;QACJ;QACA,IAAI,KAAK,GAAG,MAAM,MAAM,EAAE,KAAK;QAC/B,GAAG,MAAM;IACb;IACA,IAAI,CAAC,KACD;IACJ,IAAI,UAAU,SAAU,CAAC;QACrB,IAAI,KAAK,CAAC,CAAC,EAAE;QACb,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;QACvC,IAAI,IAAI,OAAO,OAAO,KAAK,MAAM;QACjC,EAAE,CAAC,CAAC;QACJ,IAAI,IAAI,QAAQ,KAAK,IAAI,EAAE,MAAM;QACjC,IAAI,MAAM,EAAE,OAAO,EAAE,IAAI,OAAO,QAAQ,MAAM,KAAK,KAAK,EAAE,MAAM;QAChE,IAAI,MAAM,KAAK,EAAE,KAAK;QACtB,IAAI,cAAc,EAAE,KAAK,IAAI,IAAI,IAAI;QACrC,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC;YACpB,IAAI,GAAG;gBACH;gBACA,GAAG,GAAG;YACV,OACK;gBACD,IAAI,IAAI,EAAE,MAAM;gBAChB,KAAK,CAAC,EAAE,GAAG,IAAI,GAAG;oBACd,MAAM;oBACN,KAAK,EAAE,CAAC;oBACR,GAAG;oBACH,GAAG;oBACH,GAAG;oBACH,GAAG,KAAK,GAAG,MAAM,IAAK,KAAM,IAAI,MAAM,IAAI;oBAC1C,aAAa;gBACjB;gBACA,KAAK,KAAK,IAAI,MAAM;gBACpB,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;gBACxC,IAAI,CAAC,EAAE,KACH;YACR;QACJ;QACA,IAAI,IAAI,OACJ,IAAI,qBAAqB;QAC7B,IAAI,CAAC,aACD,IAAI,MAAM;aACT,IAAI,OAAO,QAAQ;YACpB,IAAI;gBACA,IAAI,MAAM,YAAY,MAAM;YAChC,EACA,OAAO,GAAG;gBACN,IAAI,GAAG;YACX;QACJ,OAEI,KAAK,IAAI,CAAC,QAAQ,MAAM,GAAG;IACnC;IACA,yCAAyC;IACzC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,EAAE,EAAG;QAC3B,QAAQ;IACZ;IACA,OAAO;AACX;AAQO,SAAS,QAAQ,IAAI,EAAE,IAAI;IAC9B,IAAI,CAAC,MACD,OAAO,CAAC;IACZ,IAAI,IAAI,CAAC;IACT,IAAI,QAAQ,EAAE;IACd,KAAK,MAAM,IAAI,GAAG;IAClB,IAAI,IAAI;IACR,IAAI,MAAM;IACV,IAAK,IAAI,MAAM,EAAG;QACd,IAAI,KAAK,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE;QACvC,IAAI,cAAc,EAAE,KAAK,IAAI,IAAI,IAAI;QACrC,IAAI,IAAI,QAAQ,KAAK,IAAI,EAAE,MAAM;QACjC,IAAI,MAAM,EAAE,OAAO,EAAE,IAAI,OAAO,QAAQ,MAAM,KAAK,KAAK,EAAE,MAAM;QAChE,IAAI,MAAM,KAAK,EAAE,KAAK;QACtB,IAAI,IAAI,OACJ,MAAM;QACV,IAAI,IAAI,cAAc,YAAY,MAAM,KAAK,MAAM,IAAI,EAAE,MAAM;QAC/D,IAAI,IAAI;QACR,EAAE,CAAC,CAAC;QACJ,MAAM,IAAI,CAAC,IAAI,GAAG;YACd,MAAM,KAAK,MAAM;YACjB,KAAK,EAAE,CAAC;YACR,GAAG;YACH,GAAG;YACH,GAAG;YACH,GAAG,KAAK,GAAG,MAAM,IAAK,KAAM,IAAI,MAAM,IAAI;YAC1C,GAAG;YACH,aAAa;QACjB;QACA,KAAK,KAAK,IAAI,MAAM;QACpB,OAAO,KAAK,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;IAC5C;IACA,IAAI,MAAM,IAAI,GAAG,MAAM,KAAK,KAAK,GAAG,MAAM,MAAM;IAChD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;QACnC,IAAI,IAAI,KAAK,CAAC,EAAE;QAChB,IAAI,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM;QACrC,IAAI,OAAO,KAAK,EAAE,CAAC,CAAC,MAAM,GAAG,KAAK,EAAE,KAAK;QACzC,IAAI,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG;QACnB,IAAI,KAAK,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,KAAK,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,MAAM,GAAG,CAAC;IAC1F;IACA,IAAI,KAAK,GAAG,MAAM,MAAM,EAAE,KAAK;IAC/B,OAAO;AACX;AACA;;CAEC,GACD,IAAI,mBAAkC;IAClC,SAAS,oBACT;IACA,iBAAiB,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,KAAK;QACnD,IAAI,CAAC,MAAM,CAAC,MAAM,MAAM;IAC5B;IACA,iBAAiB,WAAW,GAAG;IAC/B,OAAO;AACX;;AAEA;;;CAGC,GACD,IAAI,eAA8B;IAC9B;;KAEC,GACD,SAAS;QACL,IAAI,UAAU,IAAI;QAClB,IAAI,CAAC,CAAC,GAAG,IAAI,QAAQ,SAAU,GAAG,EAAE,KAAK;YACrC,QAAQ,MAAM,CAAC,MAAM,KAAK;QAC9B;IACJ;IACA,aAAa,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,KAAK;QAC/C,IAAI;YACA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM;QACtB,EACA,OAAO,GAAG;YACN,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM;QACzB;IACJ;IACA,aAAa,WAAW,GAAG;IAC3B,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,oBAAmC;IACnC;;KAEC,GACD,SAAS,kBAAkB,CAAC,EAAE,EAAE;QAC5B,IAAI,UAAU,IAAI;QAClB,IAAI,KAAK,QAAQ;YACb,IAAI,CAAC,CAAC,GAAG,IAAI,QAAQ,SAAU,GAAG,EAAE,KAAK;gBACrC,QAAQ,MAAM,CAAC,MAAM,KAAK;YAC9B;QACJ,OACK;YACD,IAAI,CAAC,CAAC,GAAG,IAAI,aAAa,SAAU,GAAG,EAAE,GAAG,EAAE,KAAK;gBAC/C,QAAQ,MAAM,CAAC,KAAK,KAAK;YAC7B;YACA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,SAAS;QACrC;IACJ;IACA,kBAAkB,SAAS,CAAC,IAAI,GAAG,SAAU,IAAI,EAAE,KAAK;QACpD,IAAI,IAAI,CAAC,CAAC,CAAC,SAAS,EAChB,OAAO,IAAI,MAAM;QACrB,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM;IACtB;IACA,kBAAkB,WAAW,GAAG;IAChC,OAAO;AACX;;AAEA;;CAEC,GACD,IAAI,QAAuB;IACvB;;;KAGC,GACD,SAAS,MAAM,EAAE;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,CAAC,GAAG,EAAE;QACX,IAAI,CAAC,CAAC,GAAG;YACL,GAAG;QACP;QACA,IAAI,CAAC,CAAC,GAAG;IACb;IACA;;;;KAIC,GACD,MAAM,SAAS,CAAC,IAAI,GAAG,SAAU,KAAK,EAAE,KAAK;QACzC,IAAI,UAAU,IAAI;QAClB,IAAI,CAAC,IAAI,CAAC,MAAM,EACZ,MAAM;QACV,IAAI,CAAC,IAAI,CAAC,CAAC,EACP,MAAM;QACV,IAAI,IAAI,CAAC,CAAC,GAAG,GAAG;YACZ,IAAI,MAAM,KAAK,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM;YACvC,IAAI,QAAQ,MAAM,QAAQ,CAAC,GAAG;YAC9B,IAAI,CAAC,CAAC,IAAI;YACV,IAAI,IAAI,CAAC,CAAC,EACN,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;iBAE1B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC;YACnB,QAAQ,MAAM,QAAQ,CAAC;YACvB,IAAI,MAAM,MAAM,EACZ,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO;QAChC,OACK;YACD,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,KAAK,GAAG,MAAM,KAAK;YAC1C,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,EACd,MAAM;iBACL,IAAI,CAAC,MAAM,MAAM,EAClB,MAAM,IAAI,CAAC,CAAC;iBACX;gBACD,MAAM,IAAI,GAAG,IAAI,CAAC,CAAC,CAAC,MAAM,GAAG,MAAM,MAAM;gBACzC,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,MAAM;YACjD;YACA,IAAI,IAAI,IAAI,MAAM,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,MAAM,MAAM,IAAI,CAAC,CAAC;YACnD,IAAI,UAAU;gBACV,IAAI;gBACJ,IAAI,MAAM,GAAG,KAAK;gBAClB,IAAI,OAAO,WAAW;oBAClB,IAAI,GAAG,KAAK;oBACZ,OAAO,CAAC,GAAG;oBACX,OAAO,CAAC,GAAG;oBACX,IAAI,KAAK,GAAG,KAAK,IAAI,IAAI,QAAQ,GAAG,KAAK,IAAI,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK,GAAG,MAAM,GAAG,KAAK,IAAI,KAAK,KAAK,GAAG,KAAK,IAAI;oBACrH,IAAI,IAAI,IAAI,KAAK,MAAM,IAAI;wBACvB,IAAI,SAAS,EAAE;wBACf,OAAO,CAAC,CAAC,OAAO,CAAC;wBACjB,IAAI;wBACJ,IAAI,OAAO,GAAG,KAAK,IAAI,KAAK,OAAO,GAAG,KAAK,IAAI;wBAC/C,IAAI,OAAO,UAAU,IAAI,QAAQ,CAAC,IAAI,IAAI,KAAK,KAAK,MAAM,CAAC;wBAC3D,IAAI,QAAQ,YAAY;4BACpB,KAAK,KAAK;gCAAC,CAAC;6BAAE,GAAG,KAAK,KAAK,IAAI,OAAO,EAAE,CAAC,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE;wBAC7D,OACK,IAAI,IACL,OAAO,CAAC;wBACZ,KAAK;wBACL,OAAO,CAAC,GAAG;wBACX,IAAI;wBACJ,IAAI,SAAS;4BACT,MAAM;4BACN,aAAa;4BACb,OAAO;gCACH,IAAI,CAAC,OAAO,MAAM,EACd,MAAM;gCACV,IAAI,CAAC,MACD,OAAO,MAAM,CAAC,MAAM,IAAI;qCACvB;oCACD,IAAI,MAAM,QAAQ,CAAC,CAAC,MAAM;oCAC1B,IAAI,CAAC,KACD,MAAM,8BAA8B;oCACxC,MAAM,OAAO,IAAI,IAAI,IAAI,QAAQ,IAAI,IAAI,MAAM,MAAM;oCACrD,IAAI,MAAM,GAAG,SAAU,GAAG,EAAE,GAAG,EAAE,KAAK;wCAAI,OAAO,MAAM,CAAC,KAAK,KAAK;oCAAQ;oCAC1E,IAAK,IAAI,KAAK,GAAG,SAAS,QAAQ,KAAK,OAAO,MAAM,EAAE,KAAM;wCACxD,IAAI,MAAM,MAAM,CAAC,GAAG;wCACpB,IAAI,IAAI,CAAC,KAAK;oCAClB;oCACA,IAAI,QAAQ,CAAC,CAAC,EAAE,IAAI,UAAU,QAAQ,CAAC,EACnC,QAAQ,CAAC,GAAG;yCAEZ,IAAI,IAAI,CAAC,IAAI;gCACrB;4BACJ;4BACA,WAAW;gCACP,IAAI,OAAO,IAAI,SAAS,EACpB,IAAI,SAAS;4BACrB;wBACJ;wBACA,IAAI,QAAQ,GACR,OAAO,IAAI,GAAG,MAAM,OAAO,YAAY,GAAG;wBAC9C,OAAO,MAAM,CAAC;oBAClB;oBACA,OAAO;gBACX,OACK,IAAI,IAAI;oBACT,IAAI,OAAO,WAAW;wBAClB,KAAK,KAAK,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG;wBAClD,OAAO;oBACX,OACK,IAAI,OAAO,WAAW;wBACvB,KAAK,KAAK,GAAG,IAAI,GAAG,OAAO,CAAC,GAAG;wBAC/B,OAAO;oBACX;gBACJ;YACJ;YACA,IAAI,SAAS,IAAI;YACjB,MAAO,IAAI,IAAI,GAAG,EAAE,EAAG;gBACnB,IAAI,UAAU;gBACd,IAAI,YAAY,SACZ;YACR;YACA,IAAI,CAAC,CAAC,GAAG;YACT,IAAI,KAAK,GAAG;gBACR,IAAI,MAAM,IAAI,IAAI,QAAQ,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,KAAK,KAAK,OAAO,aAAa,CAAC,KAAK,IAAI,QAAQ,CAAC,GAAG;gBAClH,IAAI,KACA,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;qBAEhB,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC;YAC/B;YACA,IAAI,IAAI,GACJ,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI;YACtC,IAAI,CAAC,CAAC,GAAG,IAAI,QAAQ,CAAC;QAC1B;QACA,IAAI,OAAO;YACP,IAAI,IAAI,CAAC,CAAC,EACN,MAAM;YACV,IAAI,CAAC,CAAC,GAAG;QACb;IACJ;IACA;;;;KAIC,GACD,MAAM,SAAS,CAAC,QAAQ,GAAG,SAAU,OAAO;QACxC,IAAI,CAAC,CAAC,CAAC,QAAQ,WAAW,CAAC,GAAG;IAClC;IACA,OAAO;AACX;;AAQO,SAAS,MAAM,IAAI,EAAE,EAAE;IAC1B,IAAI,OAAO,MAAM,YACb,MAAM;IACV,IAAI,OAAO,EAAE;IACb,IAAI,OAAO;QACP,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,EAAE,EAC/B,IAAI,CAAC,EAAE;IACf;IACA,IAAI,QAAQ,CAAC;IACb,IAAI,IAAI,KAAK,MAAM,GAAG;IACtB,MAAO,GAAG,MAAM,MAAM,WAAW,EAAE,EAAG;QAClC,IAAI,CAAC,KAAK,KAAK,MAAM,GAAG,IAAI,OAAO;YAC/B,GAAG,oBAAoB;YACvB;QACJ;IACJ;;IAEA,IAAI,MAAM,GAAG,MAAM,IAAI;IACvB,IAAI,CAAC,KACD,GAAG,MAAM,CAAC;IACd,IAAI,IAAI;IACR,IAAI,IAAI,GAAG,MAAM,IAAI;IACrB,IAAI,IAAI,KAAK;IACb,IAAI,GAAG;QACH,IAAI,GAAG,MAAM,IAAI;QACjB,IAAI,GAAG,MAAM,MAAM,WAAW;YAC1B,GAAG,oBAAoB;YACvB;QACJ;QACA,IAAI,MAAM,GAAG,MAAM,IAAI;QACvB,IAAI,GAAG,MAAM,IAAI;IACrB;IACA,IAAI,UAAU,SAAU,CAAC;QACrB,IAAI,KAAK,GAAG,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM;QAClH,IAAI;QACJ,IAAI,MAAM,SAAU,CAAC,EAAE,CAAC;YACpB,IAAI,GAAG;gBACH;gBACA,GAAG,GAAG;YACV,OACK;gBACD,KAAK,CAAC,GAAG,GAAG;gBACZ,IAAI,CAAC,EAAE,KACH,GAAG,MAAM;YACjB;QACJ;QACA,IAAI,CAAC,KACD,IAAI,MAAM,IAAI,MAAM,GAAG,IAAI;aAC1B,IAAI,OAAO,GAAG;YACf,IAAI,OAAO,KAAK,QAAQ,CAAC,GAAG,IAAI;YAChC,IAAI,KAAK,QAAQ;gBACb,IAAI;oBACA,IAAI,MAAM,YAAY,MAAM,IAAI,GAAG;gBACvC,EACA,OAAO,GAAG;oBACN,IAAI,GAAG;gBACX;YACJ,OAEI,KAAK,IAAI,CAAC,QAAQ,MAAM;gBAAE,MAAM;YAAG,GAAG;QAC9C,OAEI,IAAI,8BAA8B,KAAK;IAC/C;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACxB,QAAQ;IACZ;IACA,OAAO;AACX;AAOO,SAAS,UAAU,IAAI;IAC1B,IAAI,QAAQ,CAAC;IACb,IAAI,IAAI,KAAK,MAAM,GAAG;IACtB,MAAO,GAAG,MAAM,MAAM,WAAW,EAAE,EAAG;QAClC,IAAI,CAAC,KAAK,KAAK,MAAM,GAAG,IAAI,OACxB,MAAM;IACd;;IAEA,IAAI,IAAI,GAAG,MAAM,IAAI;IACrB,IAAI,CAAC,GACD,OAAO,CAAC;IACZ,IAAI,IAAI,GAAG,MAAM,IAAI;IACrB,IAAI,IAAI,KAAK;IACb,IAAI,GAAG;QACH,IAAI,GAAG,MAAM,IAAI;QACjB,IAAI,GAAG,MAAM,MAAM,WACf,MAAM;QACV,IAAI,GAAG,MAAM,IAAI;QACjB,IAAI,GAAG,MAAM,IAAI;IACrB;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE,EAAG;QACxB,IAAI,KAAK,GAAG,MAAM,GAAG,IAAI,MAAM,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,IAAI,KAAK,MAAM;QAClH,IAAI;QACJ,IAAI,CAAC,KACD,KAAK,CAAC,GAAG,GAAG,IAAI,MAAM,GAAG,IAAI;aAC5B,IAAI,OAAO,GACZ,KAAK,CAAC,GAAG,GAAG,YAAY,KAAK,QAAQ,CAAC,GAAG,IAAI,KAAK,IAAI,GAAG;aAEzD,MAAM,8BAA8B;IAC5C;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4914, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/%40monogrid/gainmap-js/dist/QuadRenderer-DuOPRGA4.js"], "sourcesContent": ["/**\n * @monogrid/gainmap-js v3.1.0\n * With ❤️, by MONOGRID <<EMAIL>>\n */\n\nimport { RGBAFormat, LinearFilter, ClampToEdgeWrapping, Scene, OrthographicCamera, HalfFloatType, FloatType, Mesh, PlaneGeometry, WebGLRenderTarget, UVMapping, WebGLRenderer, DataTexture, LinearSRGBColorSpace, ShaderMaterial, Texture, IntType, ShortType, ByteType, UnsignedIntType, UnsignedByteType, MeshBasicMaterial } from 'three';\n\nconst getBufferForType = (type, width, height) => {\n    let out;\n    switch (type) {\n        case UnsignedByteType:\n            out = new Uint8ClampedArray(width * height * 4);\n            break;\n        case HalfFloatType:\n            out = new Uint16Array(width * height * 4);\n            break;\n        case UnsignedIntType:\n            out = new Uint32Array(width * height * 4);\n            break;\n        case ByteType:\n            out = new Int8Array(width * height * 4);\n            break;\n        case ShortType:\n            out = new Int16Array(width * height * 4);\n            break;\n        case IntType:\n            out = new Int32Array(width * height * 4);\n            break;\n        case FloatType:\n            out = new Float32Array(width * height * 4);\n            break;\n        default:\n            throw new Error('Unsupported data type');\n    }\n    return out;\n};\nlet _canReadPixelsResult;\n/**\n * Test if this browser implementation can correctly read pixels from the specified\n * Render target type.\n *\n * Runs only once\n *\n * @param type\n * @param renderer\n * @param camera\n * @param renderTargetOptions\n * @returns\n */\nconst canReadPixels = (type, renderer, camera, renderTargetOptions) => {\n    if (_canReadPixelsResult !== undefined)\n        return _canReadPixelsResult;\n    const testRT = new WebGLRenderTarget(1, 1, renderTargetOptions);\n    renderer.setRenderTarget(testRT);\n    const mesh = new Mesh(new PlaneGeometry(), new MeshBasicMaterial({ color: 0xffffff }));\n    renderer.render(mesh, camera);\n    renderer.setRenderTarget(null);\n    const out = getBufferForType(type, testRT.width, testRT.height);\n    renderer.readRenderTargetPixels(testRT, 0, 0, testRT.width, testRT.height, out);\n    testRT.dispose();\n    mesh.geometry.dispose();\n    mesh.material.dispose();\n    _canReadPixelsResult = out[0] !== 0;\n    return _canReadPixelsResult;\n};\n/**\n * Utility class used for rendering a texture with a material\n *\n * @category Core\n * @group Core\n */\nclass QuadRenderer {\n    /**\n     * Constructs a new QuadRenderer\n     *\n     * @param options Parameters for this QuadRenderer\n     */\n    constructor(options) {\n        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r;\n        this._rendererIsDisposable = false;\n        this._supportsReadPixels = true;\n        /**\n         * Renders the input texture using the specified material\n         */\n        this.render = () => {\n            this._renderer.setRenderTarget(this._renderTarget);\n            try {\n                this._renderer.render(this._scene, this._camera);\n            }\n            catch (e) {\n                this._renderer.setRenderTarget(null);\n                throw e;\n            }\n            this._renderer.setRenderTarget(null);\n        };\n        this._width = options.width;\n        this._height = options.height;\n        this._type = options.type;\n        this._colorSpace = options.colorSpace;\n        const rtOptions = {\n            // fixed options\n            format: RGBAFormat,\n            depthBuffer: false,\n            stencilBuffer: false,\n            // user options\n            type: this._type, // set in class property\n            colorSpace: this._colorSpace, // set in class property\n            anisotropy: ((_a = options.renderTargetOptions) === null || _a === void 0 ? void 0 : _a.anisotropy) !== undefined ? (_b = options.renderTargetOptions) === null || _b === void 0 ? void 0 : _b.anisotropy : 1,\n            generateMipmaps: ((_c = options.renderTargetOptions) === null || _c === void 0 ? void 0 : _c.generateMipmaps) !== undefined ? (_d = options.renderTargetOptions) === null || _d === void 0 ? void 0 : _d.generateMipmaps : false,\n            magFilter: ((_e = options.renderTargetOptions) === null || _e === void 0 ? void 0 : _e.magFilter) !== undefined ? (_f = options.renderTargetOptions) === null || _f === void 0 ? void 0 : _f.magFilter : LinearFilter,\n            minFilter: ((_g = options.renderTargetOptions) === null || _g === void 0 ? void 0 : _g.minFilter) !== undefined ? (_h = options.renderTargetOptions) === null || _h === void 0 ? void 0 : _h.minFilter : LinearFilter,\n            samples: ((_j = options.renderTargetOptions) === null || _j === void 0 ? void 0 : _j.samples) !== undefined ? (_k = options.renderTargetOptions) === null || _k === void 0 ? void 0 : _k.samples : undefined,\n            wrapS: ((_l = options.renderTargetOptions) === null || _l === void 0 ? void 0 : _l.wrapS) !== undefined ? (_m = options.renderTargetOptions) === null || _m === void 0 ? void 0 : _m.wrapS : ClampToEdgeWrapping,\n            wrapT: ((_o = options.renderTargetOptions) === null || _o === void 0 ? void 0 : _o.wrapT) !== undefined ? (_p = options.renderTargetOptions) === null || _p === void 0 ? void 0 : _p.wrapT : ClampToEdgeWrapping\n        };\n        this._material = options.material;\n        if (options.renderer) {\n            this._renderer = options.renderer;\n        }\n        else {\n            this._renderer = QuadRenderer.instantiateRenderer();\n            this._rendererIsDisposable = true;\n        }\n        this._scene = new Scene();\n        this._camera = new OrthographicCamera();\n        this._camera.position.set(0, 0, 10);\n        this._camera.left = -0.5;\n        this._camera.right = 0.5;\n        this._camera.top = 0.5;\n        this._camera.bottom = -0.5;\n        this._camera.updateProjectionMatrix();\n        if (!canReadPixels(this._type, this._renderer, this._camera, rtOptions)) {\n            let alternativeType;\n            switch (this._type) {\n                case HalfFloatType:\n                    alternativeType = this._renderer.extensions.has('EXT_color_buffer_float') ? FloatType : undefined;\n                    break;\n            }\n            if (alternativeType !== undefined) {\n                console.warn(`This browser does not support reading pixels from ${this._type} RenderTargets, switching to ${FloatType}`);\n                this._type = alternativeType;\n            }\n            else {\n                this._supportsReadPixels = false;\n                console.warn('This browser dos not support toArray or toDataTexture, calls to those methods will result in an error thrown');\n            }\n        }\n        this._quad = new Mesh(new PlaneGeometry(), this._material);\n        this._quad.geometry.computeBoundingBox();\n        this._scene.add(this._quad);\n        this._renderTarget = new WebGLRenderTarget(this.width, this.height, rtOptions);\n        this._renderTarget.texture.mapping = ((_q = options.renderTargetOptions) === null || _q === void 0 ? void 0 : _q.mapping) !== undefined ? (_r = options.renderTargetOptions) === null || _r === void 0 ? void 0 : _r.mapping : UVMapping;\n    }\n    /**\n     * Instantiates a temporary renderer\n     *\n     * @returns\n     */\n    static instantiateRenderer() {\n        const renderer = new WebGLRenderer();\n        renderer.setSize(128, 128);\n        // renderer.outputColorSpace = SRGBColorSpace\n        // renderer.toneMapping = LinearToneMapping\n        // renderer.debug.checkShaderErrors = false\n        // this._rendererIsDisposable = true\n        return renderer;\n    }\n    /**\n     * Obtains a Buffer containing the rendered texture.\n     *\n     * @throws Error if the browser cannot read pixels from this RenderTarget type.\n     * @returns a TypedArray containing RGBA values from this renderer\n     */\n    toArray() {\n        if (!this._supportsReadPixels)\n            throw new Error('Can\\'t read pixels in this browser');\n        const out = getBufferForType(this._type, this._width, this._height);\n        this._renderer.readRenderTargetPixels(this._renderTarget, 0, 0, this._width, this._height, out);\n        return out;\n    }\n    /**\n     * Performs a readPixel operation in the renderTarget\n     * and returns a DataTexture containing the read data\n     *\n     * @param options options\n     * @returns\n     */\n    toDataTexture(options) {\n        const returnValue = new DataTexture(\n        // fixed values\n        this.toArray(), this.width, this.height, RGBAFormat, this._type, \n        // user values\n        (options === null || options === void 0 ? void 0 : options.mapping) || UVMapping, (options === null || options === void 0 ? void 0 : options.wrapS) || ClampToEdgeWrapping, (options === null || options === void 0 ? void 0 : options.wrapT) || ClampToEdgeWrapping, (options === null || options === void 0 ? void 0 : options.magFilter) || LinearFilter, (options === null || options === void 0 ? void 0 : options.minFilter) || LinearFilter, (options === null || options === void 0 ? void 0 : options.anisotropy) || 1, \n        // fixed value\n        LinearSRGBColorSpace);\n        // set this afterwards, we can't set it in constructor\n        returnValue.generateMipmaps = (options === null || options === void 0 ? void 0 : options.generateMipmaps) !== undefined ? options === null || options === void 0 ? void 0 : options.generateMipmaps : false;\n        return returnValue;\n    }\n    /**\n     * If using a disposable renderer, it will dispose it.\n     */\n    disposeOnDemandRenderer() {\n        this._renderer.setRenderTarget(null);\n        if (this._rendererIsDisposable) {\n            this._renderer.dispose();\n            this._renderer.forceContextLoss();\n        }\n    }\n    /**\n     * Will dispose of **all** assets used by this renderer.\n     *\n     *\n     * @param disposeRenderTarget will dispose of the renderTarget which will not be usable later\n     * set this to true if you passed the `renderTarget.texture` to a `PMREMGenerator`\n     * or are otherwise done with it.\n     *\n     * @example\n     * ```js\n     * const loader = new HDRJPGLoader(renderer)\n     * const result = await loader.loadAsync('gainmap.jpeg')\n     * const mesh = new Mesh(geometry, new MeshBasicMaterial({ map: result.renderTarget.texture }) )\n     * // DO NOT dispose the renderTarget here,\n     * // it is used directly in the material\n     * result.dispose()\n     * ```\n     *\n     * @example\n     * ```js\n     * const loader = new HDRJPGLoader(renderer)\n     * const pmremGenerator = new PMREMGenerator( renderer );\n     * const result = await loader.loadAsync('gainmap.jpeg')\n     * const envMap = pmremGenerator.fromEquirectangular(result.renderTarget.texture)\n     * const mesh = new Mesh(geometry, new MeshStandardMaterial({ envMap }) )\n     * // renderTarget can be disposed here\n     * // because it was used to generate a PMREM texture\n     * result.dispose(true)\n     * ```\n     */\n    dispose(disposeRenderTarget) {\n        this.disposeOnDemandRenderer();\n        if (disposeRenderTarget) {\n            this.renderTarget.dispose();\n        }\n        // dispose shader material texture uniforms\n        if (this.material instanceof ShaderMaterial) {\n            Object.values(this.material.uniforms).forEach(v => {\n                if (v.value instanceof Texture)\n                    v.value.dispose();\n            });\n        }\n        // dispose other material properties\n        Object.values(this.material).forEach(value => {\n            if (value instanceof Texture)\n                value.dispose();\n        });\n        this.material.dispose();\n        this._quad.geometry.dispose();\n    }\n    /**\n     * Width of the texture\n     */\n    get width() { return this._width; }\n    set width(value) {\n        this._width = value;\n        this._renderTarget.setSize(this._width, this._height);\n    }\n    /**\n     * Height of the texture\n     */\n    get height() { return this._height; }\n    set height(value) {\n        this._height = value;\n        this._renderTarget.setSize(this._width, this._height);\n    }\n    /**\n     * The renderer used\n     */\n    get renderer() { return this._renderer; }\n    /**\n     * The `WebGLRenderTarget` used.\n     */\n    get renderTarget() { return this._renderTarget; }\n    set renderTarget(value) {\n        this._renderTarget = value;\n        this._width = value.width;\n        this._height = value.height;\n        // this._type = value.texture.type\n    }\n    /**\n     * The `Material` used.\n     */\n    get material() { return this._material; }\n    /**\n     *\n     */\n    get type() { return this._type; }\n    get colorSpace() { return this._colorSpace; }\n}\n\nexport { QuadRenderer as Q };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AAAA;;AAEA,MAAM,mBAAmB,CAAC,MAAM,OAAO;IACnC,IAAI;IACJ,OAAQ;QACJ,KAAK,+IAAA,CAAA,mBAAgB;YACjB,MAAM,IAAI,kBAAkB,QAAQ,SAAS;YAC7C;QACJ,KAAK,+IAAA,CAAA,gBAAa;YACd,MAAM,IAAI,YAAY,QAAQ,SAAS;YACvC;QACJ,KAAK,+IAAA,CAAA,kBAAe;YAChB,MAAM,IAAI,YAAY,QAAQ,SAAS;YACvC;QACJ,KAAK,+IAAA,CAAA,WAAQ;YACT,MAAM,IAAI,UAAU,QAAQ,SAAS;YACrC;QACJ,KAAK,+IAAA,CAAA,YAAS;YACV,MAAM,IAAI,WAAW,QAAQ,SAAS;YACtC;QACJ,KAAK,+IAAA,CAAA,UAAO;YACR,MAAM,IAAI,WAAW,QAAQ,SAAS;YACtC;QACJ,KAAK,+IAAA,CAAA,YAAS;YACV,MAAM,IAAI,aAAa,QAAQ,SAAS;YACxC;QACJ;YACI,MAAM,IAAI,MAAM;IACxB;IACA,OAAO;AACX;AACA,IAAI;AACJ;;;;;;;;;;;CAWC,GACD,MAAM,gBAAgB,CAAC,MAAM,UAAU,QAAQ;IAC3C,IAAI,yBAAyB,WACzB,OAAO;IACX,MAAM,SAAS,IAAI,+IAAA,CAAA,oBAAiB,CAAC,GAAG,GAAG;IAC3C,SAAS,eAAe,CAAC;IACzB,MAAM,OAAO,IAAI,+IAAA,CAAA,OAAI,CAAC,IAAI,+IAAA,CAAA,gBAAa,IAAI,IAAI,+IAAA,CAAA,oBAAiB,CAAC;QAAE,OAAO;IAAS;IACnF,SAAS,MAAM,CAAC,MAAM;IACtB,SAAS,eAAe,CAAC;IACzB,MAAM,MAAM,iBAAiB,MAAM,OAAO,KAAK,EAAE,OAAO,MAAM;IAC9D,SAAS,sBAAsB,CAAC,QAAQ,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM,EAAE;IAC3E,OAAO,OAAO;IACd,KAAK,QAAQ,CAAC,OAAO;IACrB,KAAK,QAAQ,CAAC,OAAO;IACrB,uBAAuB,GAAG,CAAC,EAAE,KAAK;IAClC,OAAO;AACX;AACA;;;;;CAKC,GACD,MAAM;IACF;;;;KAIC,GACD,YAAY,OAAO,CAAE;QACjB,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI;QAChE,IAAI,CAAC,qBAAqB,GAAG;QAC7B,IAAI,CAAC,mBAAmB,GAAG;QAC3B;;SAEC,GACD,IAAI,CAAC,MAAM,GAAG;YACV,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa;YACjD,IAAI;gBACA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO;YACnD,EACA,OAAO,GAAG;gBACN,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;gBAC/B,MAAM;YACV;YACA,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;QACnC;QACA,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;QAC3B,IAAI,CAAC,OAAO,GAAG,QAAQ,MAAM;QAC7B,IAAI,CAAC,KAAK,GAAG,QAAQ,IAAI;QACzB,IAAI,CAAC,WAAW,GAAG,QAAQ,UAAU;QACrC,MAAM,YAAY;YACd,gBAAgB;YAChB,QAAQ,+IAAA,CAAA,aAAU;YAClB,aAAa;YACb,eAAe;YACf,eAAe;YACf,MAAM,IAAI,CAAC,KAAK;YAChB,YAAY,IAAI,CAAC,WAAW;YAC5B,YAAY,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,UAAU,GAAG;YAC5M,iBAAiB,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,eAAe,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,eAAe,GAAG;YAC3N,WAAW,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,GAAG,+IAAA,CAAA,eAAY;YACrN,WAAW,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,SAAS,GAAG,+IAAA,CAAA,eAAY;YACrN,SAAS,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,GAAG;YACnM,OAAO,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,+IAAA,CAAA,sBAAmB;YAChN,OAAO,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,KAAK,GAAG,+IAAA,CAAA,sBAAmB;QACpN;QACA,IAAI,CAAC,SAAS,GAAG,QAAQ,QAAQ;QACjC,IAAI,QAAQ,QAAQ,EAAE;YAClB,IAAI,CAAC,SAAS,GAAG,QAAQ,QAAQ;QACrC,OACK;YACD,IAAI,CAAC,SAAS,GAAG,aAAa,mBAAmB;YACjD,IAAI,CAAC,qBAAqB,GAAG;QACjC;QACA,IAAI,CAAC,MAAM,GAAG,IAAI,+IAAA,CAAA,QAAK;QACvB,IAAI,CAAC,OAAO,GAAG,IAAI,+IAAA,CAAA,qBAAkB;QACrC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,GAAG;QAChC,IAAI,CAAC,OAAO,CAAC,IAAI,GAAG,CAAC;QACrB,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG;QACrB,IAAI,CAAC,OAAO,CAAC,GAAG,GAAG;QACnB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,sBAAsB;QACnC,IAAI,CAAC,cAAc,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE,YAAY;YACrE,IAAI;YACJ,OAAQ,IAAI,CAAC,KAAK;gBACd,KAAK,+IAAA,CAAA,gBAAa;oBACd,kBAAkB,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,GAAG,CAAC,4BAA4B,+IAAA,CAAA,YAAS,GAAG;oBACxF;YACR;YACA,IAAI,oBAAoB,WAAW;gBAC/B,QAAQ,IAAI,CAAC,CAAC,kDAAkD,EAAE,IAAI,CAAC,KAAK,CAAC,6BAA6B,EAAE,+IAAA,CAAA,YAAS,EAAE;gBACvH,IAAI,CAAC,KAAK,GAAG;YACjB,OACK;gBACD,IAAI,CAAC,mBAAmB,GAAG;gBAC3B,QAAQ,IAAI,CAAC;YACjB;QACJ;QACA,IAAI,CAAC,KAAK,GAAG,IAAI,+IAAA,CAAA,OAAI,CAAC,IAAI,+IAAA,CAAA,gBAAa,IAAI,IAAI,CAAC,SAAS;QACzD,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,kBAAkB;QACtC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,KAAK;QAC1B,IAAI,CAAC,aAAa,GAAG,IAAI,+IAAA,CAAA,oBAAiB,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE;QACpE,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,MAAM,YAAY,CAAC,KAAK,QAAQ,mBAAmB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,OAAO,GAAG,+IAAA,CAAA,YAAS;IAC5O;IACA;;;;KAIC,GACD,OAAO,sBAAsB;QACzB,MAAM,WAAW,IAAI,iKAAA,CAAA,gBAAa;QAClC,SAAS,OAAO,CAAC,KAAK;QACtB,6CAA6C;QAC7C,2CAA2C;QAC3C,2CAA2C;QAC3C,oCAAoC;QACpC,OAAO;IACX;IACA;;;;;KAKC,GACD,UAAU;QACN,IAAI,CAAC,IAAI,CAAC,mBAAmB,EACzB,MAAM,IAAI,MAAM;QACpB,MAAM,MAAM,iBAAiB,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO;QAClE,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,IAAI,CAAC,aAAa,EAAE,GAAG,GAAG,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE;QAC3F,OAAO;IACX;IACA;;;;;;KAMC,GACD,cAAc,OAAO,EAAE;QACnB,MAAM,cAAc,IAAI,+IAAA,CAAA,cAAW,CACnC,eAAe;QACf,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,+IAAA,CAAA,aAAU,EAAE,IAAI,CAAC,KAAK,EAC/D,cAAc;QACd,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,OAAO,KAAK,+IAAA,CAAA,YAAS,EAAE,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,+IAAA,CAAA,sBAAmB,EAAE,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,KAAK,KAAK,+IAAA,CAAA,sBAAmB,EAAE,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,KAAK,+IAAA,CAAA,eAAY,EAAE,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,SAAS,KAAK,+IAAA,CAAA,eAAY,EAAE,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,UAAU,KAAK,GAC9f,cAAc;QACd,+IAAA,CAAA,uBAAoB;QACpB,sDAAsD;QACtD,YAAY,eAAe,GAAG,CAAC,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,eAAe,MAAM,YAAY,YAAY,QAAQ,YAAY,KAAK,IAAI,KAAK,IAAI,QAAQ,eAAe,GAAG;QACtM,OAAO;IACX;IACA;;KAEC,GACD,0BAA0B;QACtB,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;QAC/B,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,SAAS,CAAC,OAAO;YACtB,IAAI,CAAC,SAAS,CAAC,gBAAgB;QACnC;IACJ;IACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6BC,GACD,QAAQ,mBAAmB,EAAE;QACzB,IAAI,CAAC,uBAAuB;QAC5B,IAAI,qBAAqB;YACrB,IAAI,CAAC,YAAY,CAAC,OAAO;QAC7B;QACA,2CAA2C;QAC3C,IAAI,IAAI,CAAC,QAAQ,YAAY,+IAAA,CAAA,iBAAc,EAAE;YACzC,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;gBAC1C,IAAI,EAAE,KAAK,YAAY,+IAAA,CAAA,UAAO,EAC1B,EAAE,KAAK,CAAC,OAAO;YACvB;QACJ;QACA,oCAAoC;QACpC,OAAO,MAAM,CAAC,IAAI,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;YACjC,IAAI,iBAAiB,+IAAA,CAAA,UAAO,EACxB,MAAM,OAAO;QACrB;QACA,IAAI,CAAC,QAAQ,CAAC,OAAO;QACrB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO;IAC/B;IACA;;KAEC,GACD,IAAI,QAAQ;QAAE,OAAO,IAAI,CAAC,MAAM;IAAE;IAClC,IAAI,MAAM,KAAK,EAAE;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO;IACxD;IACA;;KAEC,GACD,IAAI,SAAS;QAAE,OAAO,IAAI,CAAC,OAAO;IAAE;IACpC,IAAI,OAAO,KAAK,EAAE;QACd,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO;IACxD;IACA;;KAEC,GACD,IAAI,WAAW;QAAE,OAAO,IAAI,CAAC,SAAS;IAAE;IACxC;;KAEC,GACD,IAAI,eAAe;QAAE,OAAO,IAAI,CAAC,aAAa;IAAE;IAChD,IAAI,aAAa,KAAK,EAAE;QACpB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,MAAM,GAAG,MAAM,KAAK;QACzB,IAAI,CAAC,OAAO,GAAG,MAAM,MAAM;IAC3B,kCAAkC;IACtC;IACA;;KAEC,GACD,IAAI,WAAW;QAAE,OAAO,IAAI,CAAC,SAAS;IAAE;IACxC;;KAEC,GACD,IAAI,OAAO;QAAE,OAAO,IAAI,CAAC,KAAK;IAAE;IAChC,IAAI,aAAa;QAAE,OAAO,IAAI,CAAC,WAAW;IAAE;AAChD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5213, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/%40monogrid/gainmap-js/dist/decode.js"], "sourcesContent": ["/**\n * @monogrid/gainmap-js v3.1.0\n * With ❤️, by MONOGRID <<EMAIL>>\n */\n\nimport { Q as QuadRenderer } from './QuadRenderer-DuOPRGA4.js';\nimport { ShaderMaterial, Vector3, NoBlending, SRGBColorSpace, LinearSRGBColorSpace, HalfFloatType, Loader, LoadingManager, Texture, UVMapping, ClampToEdgeWrapping, LinearFilter, LinearMipMapLinearFilter, RGBAFormat, UnsignedByteType, FileLoader } from 'three';\n\nconst vertexShader = /* glsl */ `\nvarying vec2 vUv;\n\nvoid main() {\n  vUv = uv;\n  gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);\n}\n`;\nconst fragmentShader = /* glsl */ `\n// min half float value\n#define HALF_FLOAT_MIN vec3( -65504, -65504, -65504 )\n// max half float value\n#define HALF_FLOAT_MAX vec3( 65504, 65504, 65504 )\n\nuniform sampler2D sdr;\nuniform sampler2D gainMap;\nuniform vec3 gamma;\nuniform vec3 offsetHdr;\nuniform vec3 offsetSdr;\nuniform vec3 gainMapMin;\nuniform vec3 gainMapMax;\nuniform float weightFactor;\n\nvarying vec2 vUv;\n\nvoid main() {\n  vec3 rgb = texture2D( sdr, vUv ).rgb;\n  vec3 recovery = texture2D( gainMap, vUv ).rgb;\n  vec3 logRecovery = pow( recovery, gamma );\n  vec3 logBoost = gainMapMin * ( 1.0 - logRecovery ) + gainMapMax * logRecovery;\n  vec3 hdrColor = (rgb + offsetSdr) * exp2( logBoost * weightFactor ) - offsetHdr;\n  vec3 clampedHdrColor = max( HALF_FLOAT_MIN, min( HALF_FLOAT_MAX, hdrColor ));\n  gl_FragColor = vec4( clampedHdrColor , 1.0 );\n}\n`;\n/**\n * A Material which is able to decode the Gainmap into a full HDR Representation\n *\n * @category Materials\n * @group Materials\n */\nclass GainMapDecoderMaterial extends ShaderMaterial {\n    /**\n     *\n     * @param params\n     */\n    constructor({ gamma, offsetHdr, offsetSdr, gainMapMin, gainMapMax, maxDisplayBoost, hdrCapacityMin, hdrCapacityMax, sdr, gainMap }) {\n        super({\n            name: 'GainMapDecoderMaterial',\n            vertexShader,\n            fragmentShader,\n            uniforms: {\n                sdr: { value: sdr },\n                gainMap: { value: gainMap },\n                gamma: { value: new Vector3(1.0 / gamma[0], 1.0 / gamma[1], 1.0 / gamma[2]) },\n                offsetHdr: { value: new Vector3().fromArray(offsetHdr) },\n                offsetSdr: { value: new Vector3().fromArray(offsetSdr) },\n                gainMapMin: { value: new Vector3().fromArray(gainMapMin) },\n                gainMapMax: { value: new Vector3().fromArray(gainMapMax) },\n                weightFactor: {\n                    value: (Math.log2(maxDisplayBoost) - hdrCapacityMin) / (hdrCapacityMax - hdrCapacityMin)\n                }\n            },\n            blending: NoBlending,\n            depthTest: false,\n            depthWrite: false\n        });\n        this._maxDisplayBoost = maxDisplayBoost;\n        this._hdrCapacityMin = hdrCapacityMin;\n        this._hdrCapacityMax = hdrCapacityMax;\n        this.needsUpdate = true;\n        this.uniformsNeedUpdate = true;\n    }\n    get sdr() { return this.uniforms.sdr.value; }\n    set sdr(value) { this.uniforms.sdr.value = value; }\n    get gainMap() { return this.uniforms.gainMap.value; }\n    set gainMap(value) { this.uniforms.gainMap.value = value; }\n    /**\n     * @see {@link GainMapMetadata.offsetHdr}\n     */\n    get offsetHdr() { return this.uniforms.offsetHdr.value.toArray(); }\n    set offsetHdr(value) { this.uniforms.offsetHdr.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.offsetSdr}\n     */\n    get offsetSdr() { return this.uniforms.offsetSdr.value.toArray(); }\n    set offsetSdr(value) { this.uniforms.offsetSdr.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gainMapMin}\n     */\n    get gainMapMin() { return this.uniforms.gainMapMin.value.toArray(); }\n    set gainMapMin(value) { this.uniforms.gainMapMin.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gainMapMax}\n     */\n    get gainMapMax() { return this.uniforms.gainMapMax.value.toArray(); }\n    set gainMapMax(value) { this.uniforms.gainMapMax.value.fromArray(value); }\n    /**\n     * @see {@link GainMapMetadata.gamma}\n     */\n    get gamma() {\n        const g = this.uniforms.gamma.value;\n        return [1 / g.x, 1 / g.y, 1 / g.z];\n    }\n    set gamma(value) {\n        const g = this.uniforms.gamma.value;\n        g.x = 1.0 / value[0];\n        g.y = 1.0 / value[1];\n        g.z = 1.0 / value[2];\n    }\n    /**\n     * @see {@link GainMapMetadata.hdrCapacityMin}\n     * @remarks Logarithmic space\n     */\n    get hdrCapacityMin() { return this._hdrCapacityMin; }\n    set hdrCapacityMin(value) {\n        this._hdrCapacityMin = value;\n        this.calculateWeight();\n    }\n    /**\n     * @see {@link GainMapMetadata.hdrCapacityMin}\n     * @remarks Logarithmic space\n     */\n    get hdrCapacityMax() { return this._hdrCapacityMax; }\n    set hdrCapacityMax(value) {\n        this._hdrCapacityMax = value;\n        this.calculateWeight();\n    }\n    /**\n     * @see {@link GainmapDecodingParameters.maxDisplayBoost}\n     * @remarks Non Logarithmic space\n     */\n    get maxDisplayBoost() { return this._maxDisplayBoost; }\n    set maxDisplayBoost(value) {\n        this._maxDisplayBoost = Math.max(1, Math.min(65504, value));\n        this.calculateWeight();\n    }\n    calculateWeight() {\n        const val = (Math.log2(this._maxDisplayBoost) - this._hdrCapacityMin) / (this._hdrCapacityMax - this._hdrCapacityMin);\n        this.uniforms.weightFactor.value = Math.max(0, Math.min(1, val));\n    }\n}\n\n/**\n * Decodes a gain map using a WebGLRenderTarget\n *\n * @category Decoding Functions\n * @group Decoding Functions\n * @example\n * import { decode } from '@monogrid/gainmap-js'\n * import {\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   TextureLoader,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const textureLoader = new TextureLoader()\n *\n * // load SDR Representation\n * const sdr = await textureLoader.loadAsync('sdr.jpg')\n * // load Gain map recovery image\n * const gainMap = await textureLoader.loadAsync('gainmap.jpg')\n * // load metadata\n * const metadata = await (await fetch('metadata.json')).json()\n *\n * const result = await decode({\n *   sdr,\n *   gainMap,\n *   // this allows to use `result.renderTarget.texture` directly\n *   renderer,\n *   // this will restore the full HDR range\n *   maxDisplayBoost: Math.pow(2, metadata.hdrCapacityMax),\n *   ...metadata\n * })\n *\n * const scene = new Scene()\n * // `result` can be used to populate a Texture\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n * @param params\n * @returns\n * @throws {Error} if the WebGLRenderer fails to render the gain map\n */\nconst decode = (params) => {\n    const { sdr, gainMap, renderer } = params;\n    if (sdr.colorSpace !== SRGBColorSpace) {\n        console.warn('SDR Colorspace needs to be *SRGBColorSpace*, setting it automatically');\n        sdr.colorSpace = SRGBColorSpace;\n    }\n    sdr.needsUpdate = true;\n    if (gainMap.colorSpace !== LinearSRGBColorSpace) {\n        console.warn('Gainmap Colorspace needs to be *LinearSRGBColorSpace*, setting it automatically');\n        gainMap.colorSpace = LinearSRGBColorSpace;\n    }\n    gainMap.needsUpdate = true;\n    const material = new GainMapDecoderMaterial({\n        ...params,\n        sdr,\n        gainMap\n    });\n    const quadRenderer = new QuadRenderer({\n        // TODO: three types are generic, eslint complains here, see how we can solve\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n        width: sdr.image.width,\n        // TODO: three types are generic, eslint complains here, see how we can solve\n        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment, @typescript-eslint/no-unsafe-member-access\n        height: sdr.image.height,\n        type: HalfFloatType,\n        colorSpace: LinearSRGBColorSpace,\n        material,\n        renderer,\n        renderTargetOptions: params.renderTargetOptions\n    });\n    try {\n        quadRenderer.render();\n    }\n    catch (e) {\n        quadRenderer.disposeOnDemandRenderer();\n        throw e;\n    }\n    return quadRenderer;\n};\n\nclass GainMapNotFoundError extends Error {\n}\n\nclass XMPMetadataNotFoundError extends Error {\n}\n\nconst getXMLValue = (xml, tag, defaultValue) => {\n    // Check for attribute format first: tag=\"value\"\n    const attributeMatch = new RegExp(`${tag}=\"([^\"]*)\"`, 'i').exec(xml);\n    if (attributeMatch)\n        return attributeMatch[1];\n    // Check for tag format: <tag>value</tag> or <tag><rdf:li>value</rdf:li>...</tag>\n    const tagMatch = new RegExp(`<${tag}[^>]*>([\\\\s\\\\S]*?)</${tag}>`, 'i').exec(xml);\n    if (tagMatch) {\n        // Check if it contains rdf:li elements\n        const liValues = tagMatch[1].match(/<rdf:li>([^<]*)<\\/rdf:li>/g);\n        if (liValues && liValues.length === 3) {\n            return liValues.map(v => v.replace(/<\\/?rdf:li>/g, ''));\n        }\n        return tagMatch[1].trim();\n    }\n    if (defaultValue !== undefined)\n        return defaultValue;\n    throw new Error(`Can't find ${tag} in gainmap metadata`);\n};\nconst extractXMP = (input) => {\n    let str;\n    // support node test environment\n    if (typeof TextDecoder !== 'undefined')\n        str = new TextDecoder().decode(input);\n    else\n        str = input.toString();\n    let start = str.indexOf('<x:xmpmeta');\n    while (start !== -1) {\n        const end = str.indexOf('x:xmpmeta>', start);\n        const xmpBlock = str.slice(start, end + 10);\n        try {\n            const gainMapMin = getXMLValue(xmpBlock, 'hdrgm:GainMapMin', '0');\n            const gainMapMax = getXMLValue(xmpBlock, 'hdrgm:GainMapMax');\n            const gamma = getXMLValue(xmpBlock, 'hdrgm:Gamma', '1');\n            const offsetSDR = getXMLValue(xmpBlock, 'hdrgm:OffsetSDR', '0.015625');\n            const offsetHDR = getXMLValue(xmpBlock, 'hdrgm:OffsetHDR', '0.015625');\n            // These are always attributes, so we can use a simpler regex\n            const hdrCapacityMinMatch = /hdrgm:HDRCapacityMin=\"([^\"]*)\"/.exec(xmpBlock);\n            const hdrCapacityMin = hdrCapacityMinMatch ? hdrCapacityMinMatch[1] : '0';\n            const hdrCapacityMaxMatch = /hdrgm:HDRCapacityMax=\"([^\"]*)\"/.exec(xmpBlock);\n            if (!hdrCapacityMaxMatch)\n                throw new Error('Incomplete gainmap metadata');\n            const hdrCapacityMax = hdrCapacityMaxMatch[1];\n            return {\n                gainMapMin: Array.isArray(gainMapMin) ? gainMapMin.map(v => parseFloat(v)) : [parseFloat(gainMapMin), parseFloat(gainMapMin), parseFloat(gainMapMin)],\n                gainMapMax: Array.isArray(gainMapMax) ? gainMapMax.map(v => parseFloat(v)) : [parseFloat(gainMapMax), parseFloat(gainMapMax), parseFloat(gainMapMax)],\n                gamma: Array.isArray(gamma) ? gamma.map(v => parseFloat(v)) : [parseFloat(gamma), parseFloat(gamma), parseFloat(gamma)],\n                offsetSdr: Array.isArray(offsetSDR) ? offsetSDR.map(v => parseFloat(v)) : [parseFloat(offsetSDR), parseFloat(offsetSDR), parseFloat(offsetSDR)],\n                offsetHdr: Array.isArray(offsetHDR) ? offsetHDR.map(v => parseFloat(v)) : [parseFloat(offsetHDR), parseFloat(offsetHDR), parseFloat(offsetHDR)],\n                hdrCapacityMin: parseFloat(hdrCapacityMin),\n                hdrCapacityMax: parseFloat(hdrCapacityMax)\n            };\n        }\n        catch (e) {\n            // Continue searching for another xmpmeta block if this one fails\n        }\n        start = str.indexOf('<x:xmpmeta', end);\n    }\n};\n\n/**\n * MPF Extractor (Multi Picture Format Extractor)\n * By Henrik S Nilsson 2019\n *\n * Extracts images stored in images based on the MPF format (found here: https://www.cipa.jp/e/std/std-sec.html\n * under \"CIPA DC-007-Translation-2021 Multi-Picture Format\"\n *\n * Overly commented, and without intention of being complete or production ready.\n * Created to extract depth maps from iPhone images, and to learn about image metadata.\n * Kudos to: Phil Harvey (exiftool), Jaume Sanchez (android-lens-blur-depth-extractor)\n */\nclass MPFExtractor {\n    constructor(options) {\n        this.options = {\n            debug: options && options.debug !== undefined ? options.debug : false,\n            extractFII: options && options.extractFII !== undefined ? options.extractFII : true,\n            extractNonFII: options && options.extractNonFII !== undefined ? options.extractNonFII : true\n        };\n    }\n    extract(imageArrayBuffer) {\n        return new Promise((resolve, reject) => {\n            const debug = this.options.debug;\n            const dataView = new DataView(imageArrayBuffer.buffer);\n            // If you're executing this line on a big endian machine, it'll be reversed.\n            // bigEnd further down though, refers to the endianness of the image itself.\n            if (dataView.getUint16(0) !== 0xffd8) {\n                reject(new Error('Not a valid jpeg'));\n                return;\n            }\n            const length = dataView.byteLength;\n            let offset = 2;\n            let loops = 0;\n            let marker; // APP# marker\n            while (offset < length) {\n                if (++loops > 250) {\n                    reject(new Error(`Found no marker after ${loops} loops 😵`));\n                    return;\n                }\n                if (dataView.getUint8(offset) !== 0xff) {\n                    reject(new Error(`Not a valid marker at offset 0x${offset.toString(16)}, found: 0x${dataView.getUint8(offset).toString(16)}`));\n                    return;\n                }\n                marker = dataView.getUint8(offset + 1);\n                if (debug)\n                    console.log(`Marker: ${marker.toString(16)}`);\n                if (marker === 0xe2) {\n                    if (debug)\n                        console.log('Found APP2 marker (0xffe2)');\n                    // Works for iPhone 8 Plus, X, and XSMax. Or any photos of MPF format.\n                    // Great way to visualize image information in html is using Exiftool. E.g.:\n                    // ./exiftool.exe -htmldump -wantTrailer photo.jpg > photo.html\n                    const formatPt = offset + 4;\n                    /*\n                     *  Structure of the MP Format Identifier\n                     *\n                     *  Offset Addr.  | Code (Hex)  | Description\n                     *  +00             ff            Marker Prefix      <-- offset\n                     *  +01             e2            APP2\n                     *  +02             #n            APP2 Field Length\n                     *  +03             #n            APP2 Field Length\n                     *  +04             4d            'M'                <-- formatPt\n                     *  +05             50            'P'\n                     *  +06             46            'F'\n                     *  +07             00            NULL\n                     *                                                   <-- tiffOffset\n                     */\n                    if (dataView.getUint32(formatPt) === 0x4d504600) {\n                        // Found MPF tag, so we start dig out sub images\n                        const tiffOffset = formatPt + 4;\n                        let bigEnd; // Endianness from TIFF header\n                        // Test for TIFF validity and endianness\n                        // 0x4949 and 0x4D4D ('II' and 'MM') marks Little Endian and Big Endian\n                        if (dataView.getUint16(tiffOffset) === 0x4949) {\n                            bigEnd = false;\n                        }\n                        else if (dataView.getUint16(tiffOffset) === 0x4d4d) {\n                            bigEnd = true;\n                        }\n                        else {\n                            reject(new Error('No valid endianness marker found in TIFF header'));\n                            return;\n                        }\n                        if (dataView.getUint16(tiffOffset + 2, !bigEnd) !== 0x002a) {\n                            reject(new Error('Not valid TIFF data! (no 0x002A marker)'));\n                            return;\n                        }\n                        // 32 bit number stating the offset from the start of the 8 Byte MP Header\n                        // to MP Index IFD Least possible value is thus 8 (means 0 offset)\n                        const firstIFDOffset = dataView.getUint32(tiffOffset + 4, !bigEnd);\n                        if (firstIFDOffset < 0x00000008) {\n                            reject(new Error('Not valid TIFF data! (First offset less than 8)'));\n                            return;\n                        }\n                        // Move ahead to MP Index IFD\n                        // Assume we're at the first IFD, so firstIFDOffset points to\n                        // MP Index IFD and not MP Attributes IFD. (If we try extract from a sub image,\n                        // we fail silently here due to this assumption)\n                        // Count (2 Byte) | MP Index Fields a.k.a. MP Entries (count * 12 Byte) | Offset of Next IFD (4 Byte)\n                        const dirStart = tiffOffset + firstIFDOffset; // Start of IFD (Image File Directory)\n                        const count = dataView.getUint16(dirStart, !bigEnd); // Count of MPEntries (2 Byte)\n                        // Extract info from MPEntries (starting after Count)\n                        const entriesStart = dirStart + 2;\n                        let numberOfImages = 0;\n                        for (let i = entriesStart; i < entriesStart + 12 * count; i += 12) {\n                            // Each entry is 12 Bytes long\n                            // Check MP Index IFD tags, here we only take tag 0xb001 = Number of images\n                            if (dataView.getUint16(i, !bigEnd) === 0xb001) {\n                                // stored in Last 4 bytes of its 12 Byte entry.\n                                numberOfImages = dataView.getUint32(i + 8, !bigEnd);\n                            }\n                        }\n                        const nextIFDOffsetLen = 4; // 4 Byte offset field that appears after MP Index IFD tags\n                        const MPImageListValPt = dirStart + 2 + count * 12 + nextIFDOffsetLen;\n                        const images = [];\n                        for (let i = MPImageListValPt; i < MPImageListValPt + numberOfImages * 16; i += 16) {\n                            const image = {\n                                MPType: dataView.getUint32(i, !bigEnd),\n                                size: dataView.getUint32(i + 4, !bigEnd),\n                                // This offset is specified relative to the address of the MP Endian\n                                // field in the MP Header, unless the image is a First Individual Image,\n                                // in which case the value of the offset shall be NULL (0x00000000).\n                                dataOffset: dataView.getUint32(i + 8, !bigEnd),\n                                dependantImages: dataView.getUint32(i + 12, !bigEnd),\n                                start: -1,\n                                end: -1,\n                                isFII: false\n                            };\n                            if (!image.dataOffset) {\n                                // dataOffset is 0x00000000 for First Individual Image\n                                image.start = 0;\n                                image.isFII = true;\n                            }\n                            else {\n                                image.start = tiffOffset + image.dataOffset;\n                                image.isFII = false;\n                            }\n                            image.end = image.start + image.size;\n                            images.push(image);\n                        }\n                        if (this.options.extractNonFII && images.length) {\n                            const bufferBlob = new Blob([dataView]);\n                            const imgs = [];\n                            for (const image of images) {\n                                if (image.isFII && !this.options.extractFII) {\n                                    continue; // Skip FII\n                                }\n                                const imageBlob = bufferBlob.slice(image.start, image.end + 1, 'image/jpeg');\n                                // we don't need this\n                                // const imageUrl = URL.createObjectURL(imageBlob)\n                                // image.img = document.createElement('img')\n                                // image.img.src = imageUrl\n                                imgs.push(imageBlob);\n                            }\n                            resolve(imgs);\n                        }\n                    }\n                }\n                offset += 2 + dataView.getUint16(offset + 2);\n            }\n        });\n    }\n}\n\n/**\n * Extracts XMP Metadata and the gain map recovery image\n * from a single JPEG file.\n *\n * @category Decoding Functions\n * @group Decoding Functions\n * @param jpegFile an `Uint8Array` containing and encoded JPEG file\n * @returns an sdr `Uint8Array` compressed in JPEG, a gainMap `Uint8Array` compressed in JPEG and the XMP parsed XMP metadata\n * @throws Error if XMP Metadata is not found\n * @throws Error if Gain map image is not found\n * @example\n * import { FileLoader } from 'three'\n * import { extractGainmapFromJPEG } from '@monogrid/gainmap-js'\n *\n * const jpegFile = await new FileLoader()\n *  .setResponseType('arraybuffer')\n *  .loadAsync('image.jpg')\n *\n * const { sdr, gainMap, metadata } = extractGainmapFromJPEG(jpegFile)\n */\nconst extractGainmapFromJPEG = async (jpegFile) => {\n    const metadata = extractXMP(jpegFile);\n    if (!metadata)\n        throw new XMPMetadataNotFoundError('Gain map XMP metadata not found');\n    const mpfExtractor = new MPFExtractor({ extractFII: true, extractNonFII: true });\n    const images = await mpfExtractor.extract(jpegFile);\n    if (images.length !== 2)\n        throw new GainMapNotFoundError('Gain map recovery image not found');\n    return {\n        sdr: new Uint8Array(await images[0].arrayBuffer()),\n        gainMap: new Uint8Array(await images[1].arrayBuffer()),\n        metadata\n    };\n};\n\n/**\n * private function, async get image from blob\n *\n * @param blob\n * @returns\n */\nconst getHTMLImageFromBlob = (blob) => {\n    return new Promise((resolve, reject) => {\n        const img = document.createElement('img');\n        img.onload = () => { resolve(img); };\n        img.onerror = (e) => { reject(e); };\n        img.src = URL.createObjectURL(blob);\n    });\n};\n\nclass LoaderBase extends Loader {\n    /**\n     *\n     * @param renderer\n     * @param manager\n     */\n    constructor(renderer, manager) {\n        super(manager);\n        if (renderer)\n            this._renderer = renderer;\n        this._internalLoadingManager = new LoadingManager();\n    }\n    /**\n     * Specify the renderer to use when rendering the gain map\n     *\n     * @param renderer\n     * @returns\n     */\n    setRenderer(renderer) {\n        this._renderer = renderer;\n        return this;\n    }\n    /**\n     * Specify the renderTarget options to use when rendering the gain map\n     *\n     * @param options\n     * @returns\n     */\n    setRenderTargetOptions(options) {\n        this._renderTargetOptions = options;\n        return this;\n    }\n    /**\n     * @private\n     * @returns\n     */\n    prepareQuadRenderer() {\n        if (!this._renderer)\n            console.warn('WARNING: An existing WebGL Renderer was not passed to this Loader constructor or in setRenderer, the result of this Loader will need to be converted to a Data Texture with toDataTexture() before you can use it in your renderer.');\n        // temporary values\n        const material = new GainMapDecoderMaterial({\n            gainMapMax: [1, 1, 1],\n            gainMapMin: [0, 0, 0],\n            gamma: [1, 1, 1],\n            offsetHdr: [1, 1, 1],\n            offsetSdr: [1, 1, 1],\n            hdrCapacityMax: 1,\n            hdrCapacityMin: 0,\n            maxDisplayBoost: 1,\n            gainMap: new Texture(),\n            sdr: new Texture()\n        });\n        return new QuadRenderer({\n            width: 16,\n            height: 16,\n            type: HalfFloatType,\n            colorSpace: LinearSRGBColorSpace,\n            material,\n            renderer: this._renderer,\n            renderTargetOptions: this._renderTargetOptions\n        });\n    }\n    /**\n   * @private\n   * @param quadRenderer\n   * @param metadata\n   * @param sdrBuffer\n   * @param gainMapBuffer\n   */\n    async render(quadRenderer, metadata, sdrBuffer, gainMapBuffer) {\n        // this is optional, will render a black gain-map if not present\n        const gainMapBlob = gainMapBuffer ? new Blob([gainMapBuffer], { type: 'image/jpeg' }) : undefined;\n        const sdrBlob = new Blob([sdrBuffer], { type: 'image/jpeg' });\n        let sdrImage;\n        let gainMapImage;\n        let needsFlip = false;\n        if (typeof createImageBitmap === 'undefined') {\n            const res = await Promise.all([\n                gainMapBlob ? getHTMLImageFromBlob(gainMapBlob) : Promise.resolve(undefined),\n                getHTMLImageFromBlob(sdrBlob)\n            ]);\n            gainMapImage = res[0];\n            sdrImage = res[1];\n            needsFlip = true;\n        }\n        else {\n            const res = await Promise.all([\n                gainMapBlob ? createImageBitmap(gainMapBlob, { imageOrientation: 'flipY' }) : Promise.resolve(undefined),\n                createImageBitmap(sdrBlob, { imageOrientation: 'flipY' })\n            ]);\n            gainMapImage = res[0];\n            sdrImage = res[1];\n        }\n        const gainMap = new Texture(gainMapImage || new ImageData(2, 2), UVMapping, ClampToEdgeWrapping, ClampToEdgeWrapping, LinearFilter, LinearMipMapLinearFilter, RGBAFormat, UnsignedByteType, 1, LinearSRGBColorSpace);\n        gainMap.flipY = needsFlip;\n        gainMap.needsUpdate = true;\n        const sdr = new Texture(sdrImage, UVMapping, ClampToEdgeWrapping, ClampToEdgeWrapping, LinearFilter, LinearMipMapLinearFilter, RGBAFormat, UnsignedByteType, 1, SRGBColorSpace);\n        sdr.flipY = needsFlip;\n        sdr.needsUpdate = true;\n        quadRenderer.width = sdrImage.width;\n        quadRenderer.height = sdrImage.height;\n        quadRenderer.material.gainMap = gainMap;\n        quadRenderer.material.sdr = sdr;\n        quadRenderer.material.gainMapMin = metadata.gainMapMin;\n        quadRenderer.material.gainMapMax = metadata.gainMapMax;\n        quadRenderer.material.offsetHdr = metadata.offsetHdr;\n        quadRenderer.material.offsetSdr = metadata.offsetSdr;\n        quadRenderer.material.gamma = metadata.gamma;\n        quadRenderer.material.hdrCapacityMin = metadata.hdrCapacityMin;\n        quadRenderer.material.hdrCapacityMax = metadata.hdrCapacityMax;\n        quadRenderer.material.maxDisplayBoost = Math.pow(2, metadata.hdrCapacityMax);\n        quadRenderer.material.needsUpdate = true;\n        quadRenderer.render();\n    }\n}\n\n/**\n * A Three.js Loader for the gain map format.\n *\n * @category Loaders\n * @group Loaders\n *\n * @example\n * import { GainMapLoader } from '@monogrid/gainmap-js'\n * import {\n *   EquirectangularReflectionMapping,\n *   LinearFilter,\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const loader = new GainMapLoader(renderer)\n *\n * const result = await loader.loadAsync(['sdr.jpeg', 'gainmap.jpeg', 'metadata.json'])\n * // `result` can be used to populate a Texture\n *\n * const scene = new Scene()\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // Starting from three.js r159\n * // `result.renderTarget.texture` can\n * // also be used as Equirectangular scene background\n * //\n * // it was previously needed to convert it\n * // to a DataTexture with `result.toDataTexture()`\n * scene.background = result.renderTarget.texture\n * scene.background.mapping = EquirectangularReflectionMapping\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n */\nclass GainMapLoader extends LoaderBase {\n    /**\n     * Loads a gainmap using separate data\n     * * sdr image\n     * * gain map image\n     * * metadata json\n     *\n     * useful for webp gain maps\n     *\n     * @param urls An array in the form of [sdr.jpg, gainmap.jpg, metadata.json]\n     * @param onLoad Load complete callback, will receive the result\n     * @param onProgress Progress callback, will receive a {@link ProgressEvent}\n     * @param onError Error callback\n     * @returns\n     */\n    load([sdrUrl, gainMapUrl, metadataUrl], onLoad, onProgress, onError) {\n        const quadRenderer = this.prepareQuadRenderer();\n        let sdr;\n        let gainMap;\n        let metadata;\n        const loadCheck = async () => {\n            if (sdr && gainMap && metadata) {\n                // solves #16\n                try {\n                    await this.render(quadRenderer, metadata, sdr, gainMap);\n                }\n                catch (error) {\n                    this.manager.itemError(sdrUrl);\n                    this.manager.itemError(gainMapUrl);\n                    this.manager.itemError(metadataUrl);\n                    if (typeof onError === 'function')\n                        onError(error);\n                    quadRenderer.disposeOnDemandRenderer();\n                    return;\n                }\n                if (typeof onLoad === 'function')\n                    onLoad(quadRenderer);\n                this.manager.itemEnd(sdrUrl);\n                this.manager.itemEnd(gainMapUrl);\n                this.manager.itemEnd(metadataUrl);\n                quadRenderer.disposeOnDemandRenderer();\n            }\n        };\n        let sdrLengthComputable = true;\n        let sdrTotal = 0;\n        let sdrLoaded = 0;\n        let gainMapLengthComputable = true;\n        let gainMapTotal = 0;\n        let gainMapLoaded = 0;\n        let metadataLengthComputable = true;\n        let metadataTotal = 0;\n        let metadataLoaded = 0;\n        const progressHandler = () => {\n            if (typeof onProgress === 'function') {\n                const total = sdrTotal + gainMapTotal + metadataTotal;\n                const loaded = sdrLoaded + gainMapLoaded + metadataLoaded;\n                const lengthComputable = sdrLengthComputable && gainMapLengthComputable && metadataLengthComputable;\n                onProgress(new ProgressEvent('progress', { lengthComputable, loaded, total }));\n            }\n        };\n        this.manager.itemStart(sdrUrl);\n        this.manager.itemStart(gainMapUrl);\n        this.manager.itemStart(metadataUrl);\n        const sdrLoader = new FileLoader(this._internalLoadingManager);\n        sdrLoader.setResponseType('arraybuffer');\n        sdrLoader.setRequestHeader(this.requestHeader);\n        sdrLoader.setPath(this.path);\n        sdrLoader.setWithCredentials(this.withCredentials);\n        sdrLoader.load(sdrUrl, async (buffer) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof buffer === 'string')\n                throw new Error('Invalid sdr buffer');\n            sdr = buffer;\n            await loadCheck();\n        }, (e) => {\n            sdrLengthComputable = e.lengthComputable;\n            sdrLoaded = e.loaded;\n            sdrTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(sdrUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        const gainMapLoader = new FileLoader(this._internalLoadingManager);\n        gainMapLoader.setResponseType('arraybuffer');\n        gainMapLoader.setRequestHeader(this.requestHeader);\n        gainMapLoader.setPath(this.path);\n        gainMapLoader.setWithCredentials(this.withCredentials);\n        gainMapLoader.load(gainMapUrl, async (buffer) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof buffer === 'string')\n                throw new Error('Invalid gainmap buffer');\n            gainMap = buffer;\n            await loadCheck();\n        }, (e) => {\n            gainMapLengthComputable = e.lengthComputable;\n            gainMapLoaded = e.loaded;\n            gainMapTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(gainMapUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        const metadataLoader = new FileLoader(this._internalLoadingManager);\n        // metadataLoader.setResponseType('json')\n        metadataLoader.setRequestHeader(this.requestHeader);\n        metadataLoader.setPath(this.path);\n        metadataLoader.setWithCredentials(this.withCredentials);\n        metadataLoader.load(metadataUrl, async (json) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof json !== 'string')\n                throw new Error('Invalid metadata string');\n            // TODO: implement check on JSON file and remove this eslint disable\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            metadata = JSON.parse(json);\n            await loadCheck();\n        }, (e) => {\n            metadataLengthComputable = e.lengthComputable;\n            metadataLoaded = e.loaded;\n            metadataTotal = e.total;\n            progressHandler();\n        }, (error) => {\n            this.manager.itemError(metadataUrl);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        return quadRenderer;\n    }\n}\n\n/**\n * A Three.js Loader for a JPEG with embedded gainmap metadata.\n *\n * @category Loaders\n * @group Loaders\n *\n * @example\n * import { HDRJPGLoader } from '@monogrid/gainmap-js'\n * import {\n *   EquirectangularReflectionMapping,\n *   LinearFilter,\n *   Mesh,\n *   MeshBasicMaterial,\n *   PerspectiveCamera,\n *   PlaneGeometry,\n *   Scene,\n *   WebGLRenderer\n * } from 'three'\n *\n * const renderer = new WebGLRenderer()\n *\n * const loader = new HDRJPGLoader(renderer)\n *\n * const result = await loader.loadAsync('gainmap.jpeg')\n * // `result` can be used to populate a Texture\n *\n * const scene = new Scene()\n * const mesh = new Mesh(\n *   new PlaneGeometry(),\n *   new MeshBasicMaterial({ map: result.renderTarget.texture })\n * )\n * scene.add(mesh)\n * renderer.render(scene, new PerspectiveCamera())\n *\n * // Starting from three.js r159\n * // `result.renderTarget.texture` can\n * // also be used as Equirectangular scene background\n * //\n * // it was previously needed to convert it\n * // to a DataTexture with `result.toDataTexture()`\n * scene.background = result.renderTarget.texture\n * scene.background.mapping = EquirectangularReflectionMapping\n *\n * // result must be manually disposed\n * // when you are done using it\n * result.dispose()\n *\n */\nclass HDRJPGLoader extends LoaderBase {\n    /**\n     * Loads a JPEG containing gain map metadata\n     * Renders a normal SDR image if gainmap data is not found\n     *\n     * @param url An array in the form of [sdr.jpg, gainmap.jpg, metadata.json]\n     * @param onLoad Load complete callback, will receive the result\n     * @param onProgress Progress callback, will receive a {@link ProgressEvent}\n     * @param onError Error callback\n     * @returns\n     */\n    load(url, onLoad, onProgress, onError) {\n        const quadRenderer = this.prepareQuadRenderer();\n        const loader = new FileLoader(this._internalLoadingManager);\n        loader.setResponseType('arraybuffer');\n        loader.setRequestHeader(this.requestHeader);\n        loader.setPath(this.path);\n        loader.setWithCredentials(this.withCredentials);\n        this.manager.itemStart(url);\n        loader.load(url, async (jpeg) => {\n            /* istanbul ignore if\n             this condition exists only because of three.js types + strict mode\n            */\n            if (typeof jpeg === 'string')\n                throw new Error('Invalid buffer, received [string], was expecting [ArrayBuffer]');\n            const jpegBuffer = new Uint8Array(jpeg);\n            let sdrJPEG;\n            let gainMapJPEG;\n            let metadata;\n            try {\n                const extractionResult = await extractGainmapFromJPEG(jpegBuffer);\n                // gain map is successfully reconstructed\n                sdrJPEG = extractionResult.sdr;\n                gainMapJPEG = extractionResult.gainMap;\n                metadata = extractionResult.metadata;\n            }\n            catch (e) {\n                // render the SDR version if this is not a gainmap\n                if (e instanceof XMPMetadataNotFoundError || e instanceof GainMapNotFoundError) {\n                    console.warn(`Failure to reconstruct an HDR image from ${url}: Gain map metadata not found in the file, HDRJPGLoader will render the SDR jpeg`);\n                    metadata = {\n                        gainMapMin: [0, 0, 0],\n                        gainMapMax: [1, 1, 1],\n                        gamma: [1, 1, 1],\n                        hdrCapacityMin: 0,\n                        hdrCapacityMax: 1,\n                        offsetHdr: [0, 0, 0],\n                        offsetSdr: [0, 0, 0]\n                    };\n                    sdrJPEG = jpegBuffer;\n                }\n                else {\n                    throw e;\n                }\n            }\n            // solves #16\n            try {\n                await this.render(quadRenderer, metadata, sdrJPEG, gainMapJPEG);\n            }\n            catch (error) {\n                this.manager.itemError(url);\n                if (typeof onError === 'function')\n                    onError(error);\n                quadRenderer.disposeOnDemandRenderer();\n                return;\n            }\n            if (typeof onLoad === 'function')\n                onLoad(quadRenderer);\n            this.manager.itemEnd(url);\n            quadRenderer.disposeOnDemandRenderer();\n        }, onProgress, (error) => {\n            this.manager.itemError(url);\n            if (typeof onError === 'function')\n                onError(error);\n        });\n        return quadRenderer;\n    }\n}\n\nexport { GainMapDecoderMaterial, GainMapLoader, HDRJPGLoader, HDRJPGLoader as JPEGRLoader, MPFExtractor, QuadRenderer, decode, extractGainmapFromJPEG, extractXMP };\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;AAED;AACA;;;AAEA,MAAM,eAAe,QAAQ,GAAG,CAAC;;;;;;;AAOjC,CAAC;AACD,MAAM,iBAAiB,QAAQ,GAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;AA0BnC,CAAC;AACD;;;;;CAKC,GACD,MAAM,+BAA+B,+IAAA,CAAA,iBAAc;IAC/C;;;KAGC,GACD,YAAY,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,cAAc,EAAE,cAAc,EAAE,GAAG,EAAE,OAAO,EAAE,CAAE;QAChI,KAAK,CAAC;YACF,MAAM;YACN;YACA;YACA,UAAU;gBACN,KAAK;oBAAE,OAAO;gBAAI;gBAClB,SAAS;oBAAE,OAAO;gBAAQ;gBAC1B,OAAO;oBAAE,OAAO,IAAI,+IAAA,CAAA,UAAO,CAAC,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,KAAK,CAAC,EAAE,EAAE,MAAM,KAAK,CAAC,EAAE;gBAAE;gBAC5E,WAAW;oBAAE,OAAO,IAAI,+IAAA,CAAA,UAAO,GAAG,SAAS,CAAC;gBAAW;gBACvD,WAAW;oBAAE,OAAO,IAAI,+IAAA,CAAA,UAAO,GAAG,SAAS,CAAC;gBAAW;gBACvD,YAAY;oBAAE,OAAO,IAAI,+IAAA,CAAA,UAAO,GAAG,SAAS,CAAC;gBAAY;gBACzD,YAAY;oBAAE,OAAO,IAAI,+IAAA,CAAA,UAAO,GAAG,SAAS,CAAC;gBAAY;gBACzD,cAAc;oBACV,OAAO,CAAC,KAAK,IAAI,CAAC,mBAAmB,cAAc,IAAI,CAAC,iBAAiB,cAAc;gBAC3F;YACJ;YACA,UAAU,+IAAA,CAAA,aAAU;YACpB,WAAW;YACX,YAAY;QAChB;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,kBAAkB,GAAG;IAC9B;IACA,IAAI,MAAM;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK;IAAE;IAC5C,IAAI,IAAI,KAAK,EAAE;QAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,KAAK,GAAG;IAAO;IAClD,IAAI,UAAU;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK;IAAE;IACpD,IAAI,QAAQ,KAAK,EAAE;QAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,KAAK,GAAG;IAAO;IAC1D;;KAEC,GACD,IAAI,YAAY;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO;IAAI;IAClE,IAAI,UAAU,KAAK,EAAE;QAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC;IAAQ;IACvE;;KAEC,GACD,IAAI,YAAY;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,OAAO;IAAI;IAClE,IAAI,UAAU,KAAK,EAAE;QAAE,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC;IAAQ;IACvE;;KAEC,GACD,IAAI,aAAa;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO;IAAI;IACpE,IAAI,WAAW,KAAK,EAAE;QAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC;IAAQ;IACzE;;KAEC,GACD,IAAI,aAAa;QAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO;IAAI;IACpE,IAAI,WAAW,KAAK,EAAE;QAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,SAAS,CAAC;IAAQ;IACzE;;KAEC,GACD,IAAI,QAAQ;QACR,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK;QACnC,OAAO;YAAC,IAAI,EAAE,CAAC;YAAE,IAAI,EAAE,CAAC;YAAE,IAAI,EAAE,CAAC;SAAC;IACtC;IACA,IAAI,MAAM,KAAK,EAAE;QACb,MAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK;QACnC,EAAE,CAAC,GAAG,MAAM,KAAK,CAAC,EAAE;QACpB,EAAE,CAAC,GAAG,MAAM,KAAK,CAAC,EAAE;QACpB,EAAE,CAAC,GAAG,MAAM,KAAK,CAAC,EAAE;IACxB;IACA;;;KAGC,GACD,IAAI,iBAAiB;QAAE,OAAO,IAAI,CAAC,eAAe;IAAE;IACpD,IAAI,eAAe,KAAK,EAAE;QACtB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,eAAe;IACxB;IACA;;;KAGC,GACD,IAAI,iBAAiB;QAAE,OAAO,IAAI,CAAC,eAAe;IAAE;IACpD,IAAI,eAAe,KAAK,EAAE;QACtB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,eAAe;IACxB;IACA;;;KAGC,GACD,IAAI,kBAAkB;QAAE,OAAO,IAAI,CAAC,gBAAgB;IAAE;IACtD,IAAI,gBAAgB,KAAK,EAAE;QACvB,IAAI,CAAC,gBAAgB,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO;QACpD,IAAI,CAAC,eAAe;IACxB;IACA,kBAAkB;QACd,MAAM,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,eAAe,IAAI,CAAC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe;QACpH,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG;IAC/D;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CAsDC,GACD,MAAM,SAAS,CAAC;IACZ,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG;IACnC,IAAI,IAAI,UAAU,KAAK,+IAAA,CAAA,iBAAc,EAAE;QACnC,QAAQ,IAAI,CAAC;QACb,IAAI,UAAU,GAAG,+IAAA,CAAA,iBAAc;IACnC;IACA,IAAI,WAAW,GAAG;IAClB,IAAI,QAAQ,UAAU,KAAK,+IAAA,CAAA,uBAAoB,EAAE;QAC7C,QAAQ,IAAI,CAAC;QACb,QAAQ,UAAU,GAAG,+IAAA,CAAA,uBAAoB;IAC7C;IACA,QAAQ,WAAW,GAAG;IACtB,MAAM,WAAW,IAAI,uBAAuB;QACxC,GAAG,MAAM;QACT;QACA;IACJ;IACA,MAAM,eAAe,IAAI,+KAAA,CAAA,IAAY,CAAC;QAClC,6EAA6E;QAC7E,+GAA+G;QAC/G,OAAO,IAAI,KAAK,CAAC,KAAK;QACtB,6EAA6E;QAC7E,+GAA+G;QAC/G,QAAQ,IAAI,KAAK,CAAC,MAAM;QACxB,MAAM,+IAAA,CAAA,gBAAa;QACnB,YAAY,+IAAA,CAAA,uBAAoB;QAChC;QACA;QACA,qBAAqB,OAAO,mBAAmB;IACnD;IACA,IAAI;QACA,aAAa,MAAM;IACvB,EACA,OAAO,GAAG;QACN,aAAa,uBAAuB;QACpC,MAAM;IACV;IACA,OAAO;AACX;AAEA,MAAM,6BAA6B;AACnC;AAEA,MAAM,iCAAiC;AACvC;AAEA,MAAM,cAAc,CAAC,KAAK,KAAK;IAC3B,gDAAgD;IAChD,MAAM,iBAAiB,IAAI,OAAO,GAAG,IAAI,UAAU,CAAC,EAAE,KAAK,IAAI,CAAC;IAChE,IAAI,gBACA,OAAO,cAAc,CAAC,EAAE;IAC5B,iFAAiF;IACjF,MAAM,WAAW,IAAI,OAAO,CAAC,CAAC,EAAE,IAAI,oBAAoB,EAAE,IAAI,CAAC,CAAC,EAAE,KAAK,IAAI,CAAC;IAC5E,IAAI,UAAU;QACV,uCAAuC;QACvC,MAAM,WAAW,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC;QACnC,IAAI,YAAY,SAAS,MAAM,KAAK,GAAG;YACnC,OAAO,SAAS,GAAG,CAAC,CAAA,IAAK,EAAE,OAAO,CAAC,gBAAgB;QACvD;QACA,OAAO,QAAQ,CAAC,EAAE,CAAC,IAAI;IAC3B;IACA,IAAI,iBAAiB,WACjB,OAAO;IACX,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,oBAAoB,CAAC;AAC3D;AACA,MAAM,aAAa,CAAC;IAChB,IAAI;IACJ,gCAAgC;IAChC,IAAI,OAAO,gBAAgB,aACvB,MAAM,IAAI,cAAc,MAAM,CAAC;SAE/B,MAAM,MAAM,QAAQ;IACxB,IAAI,QAAQ,IAAI,OAAO,CAAC;IACxB,MAAO,UAAU,CAAC,EAAG;QACjB,MAAM,MAAM,IAAI,OAAO,CAAC,cAAc;QACtC,MAAM,WAAW,IAAI,KAAK,CAAC,OAAO,MAAM;QACxC,IAAI;YACA,MAAM,aAAa,YAAY,UAAU,oBAAoB;YAC7D,MAAM,aAAa,YAAY,UAAU;YACzC,MAAM,QAAQ,YAAY,UAAU,eAAe;YACnD,MAAM,YAAY,YAAY,UAAU,mBAAmB;YAC3D,MAAM,YAAY,YAAY,UAAU,mBAAmB;YAC3D,6DAA6D;YAC7D,MAAM,sBAAsB,iCAAiC,IAAI,CAAC;YAClE,MAAM,iBAAiB,sBAAsB,mBAAmB,CAAC,EAAE,GAAG;YACtE,MAAM,sBAAsB,iCAAiC,IAAI,CAAC;YAClE,IAAI,CAAC,qBACD,MAAM,IAAI,MAAM;YACpB,MAAM,iBAAiB,mBAAmB,CAAC,EAAE;YAC7C,OAAO;gBACH,YAAY,MAAM,OAAO,CAAC,cAAc,WAAW,GAAG,CAAC,CAAA,IAAK,WAAW,MAAM;oBAAC,WAAW;oBAAa,WAAW;oBAAa,WAAW;iBAAY;gBACrJ,YAAY,MAAM,OAAO,CAAC,cAAc,WAAW,GAAG,CAAC,CAAA,IAAK,WAAW,MAAM;oBAAC,WAAW;oBAAa,WAAW;oBAAa,WAAW;iBAAY;gBACrJ,OAAO,MAAM,OAAO,CAAC,SAAS,MAAM,GAAG,CAAC,CAAA,IAAK,WAAW,MAAM;oBAAC,WAAW;oBAAQ,WAAW;oBAAQ,WAAW;iBAAO;gBACvH,WAAW,MAAM,OAAO,CAAC,aAAa,UAAU,GAAG,CAAC,CAAA,IAAK,WAAW,MAAM;oBAAC,WAAW;oBAAY,WAAW;oBAAY,WAAW;iBAAW;gBAC/I,WAAW,MAAM,OAAO,CAAC,aAAa,UAAU,GAAG,CAAC,CAAA,IAAK,WAAW,MAAM;oBAAC,WAAW;oBAAY,WAAW;oBAAY,WAAW;iBAAW;gBAC/I,gBAAgB,WAAW;gBAC3B,gBAAgB,WAAW;YAC/B;QACJ,EACA,OAAO,GAAG;QACN,iEAAiE;QACrE;QACA,QAAQ,IAAI,OAAO,CAAC,cAAc;IACtC;AACJ;AAEA;;;;;;;;;;CAUC,GACD,MAAM;IACF,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,OAAO,GAAG;YACX,OAAO,WAAW,QAAQ,KAAK,KAAK,YAAY,QAAQ,KAAK,GAAG;YAChE,YAAY,WAAW,QAAQ,UAAU,KAAK,YAAY,QAAQ,UAAU,GAAG;YAC/E,eAAe,WAAW,QAAQ,aAAa,KAAK,YAAY,QAAQ,aAAa,GAAG;QAC5F;IACJ;IACA,QAAQ,gBAAgB,EAAE;QACtB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,KAAK;YAChC,MAAM,WAAW,IAAI,SAAS,iBAAiB,MAAM;YACrD,4EAA4E;YAC5E,4EAA4E;YAC5E,IAAI,SAAS,SAAS,CAAC,OAAO,QAAQ;gBAClC,OAAO,IAAI,MAAM;gBACjB;YACJ;YACA,MAAM,SAAS,SAAS,UAAU;YAClC,IAAI,SAAS;YACb,IAAI,QAAQ;YACZ,IAAI,QAAQ,cAAc;YAC1B,MAAO,SAAS,OAAQ;gBACpB,IAAI,EAAE,QAAQ,KAAK;oBACf,OAAO,IAAI,MAAM,CAAC,sBAAsB,EAAE,MAAM,SAAS,CAAC;oBAC1D;gBACJ;gBACA,IAAI,SAAS,QAAQ,CAAC,YAAY,MAAM;oBACpC,OAAO,IAAI,MAAM,CAAC,+BAA+B,EAAE,OAAO,QAAQ,CAAC,IAAI,WAAW,EAAE,SAAS,QAAQ,CAAC,QAAQ,QAAQ,CAAC,KAAK;oBAC5H;gBACJ;gBACA,SAAS,SAAS,QAAQ,CAAC,SAAS;gBACpC,IAAI,OACA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,OAAO,QAAQ,CAAC,KAAK;gBAChD,IAAI,WAAW,MAAM;oBACjB,IAAI,OACA,QAAQ,GAAG,CAAC;oBAChB,sEAAsE;oBACtE,4EAA4E;oBAC5E,+DAA+D;oBAC/D,MAAM,WAAW,SAAS;oBAC1B;;;;;;;;;;;;;qBAaC,GACD,IAAI,SAAS,SAAS,CAAC,cAAc,YAAY;wBAC7C,gDAAgD;wBAChD,MAAM,aAAa,WAAW;wBAC9B,IAAI,QAAQ,8BAA8B;wBAC1C,wCAAwC;wBACxC,uEAAuE;wBACvE,IAAI,SAAS,SAAS,CAAC,gBAAgB,QAAQ;4BAC3C,SAAS;wBACb,OACK,IAAI,SAAS,SAAS,CAAC,gBAAgB,QAAQ;4BAChD,SAAS;wBACb,OACK;4BACD,OAAO,IAAI,MAAM;4BACjB;wBACJ;wBACA,IAAI,SAAS,SAAS,CAAC,aAAa,GAAG,CAAC,YAAY,QAAQ;4BACxD,OAAO,IAAI,MAAM;4BACjB;wBACJ;wBACA,0EAA0E;wBAC1E,kEAAkE;wBAClE,MAAM,iBAAiB,SAAS,SAAS,CAAC,aAAa,GAAG,CAAC;wBAC3D,IAAI,iBAAiB,YAAY;4BAC7B,OAAO,IAAI,MAAM;4BACjB;wBACJ;wBACA,6BAA6B;wBAC7B,6DAA6D;wBAC7D,+EAA+E;wBAC/E,gDAAgD;wBAChD,qGAAqG;wBACrG,MAAM,WAAW,aAAa,gBAAgB,sCAAsC;wBACpF,MAAM,QAAQ,SAAS,SAAS,CAAC,UAAU,CAAC,SAAS,8BAA8B;wBACnF,qDAAqD;wBACrD,MAAM,eAAe,WAAW;wBAChC,IAAI,iBAAiB;wBACrB,IAAK,IAAI,IAAI,cAAc,IAAI,eAAe,KAAK,OAAO,KAAK,GAAI;4BAC/D,8BAA8B;4BAC9B,2EAA2E;4BAC3E,IAAI,SAAS,SAAS,CAAC,GAAG,CAAC,YAAY,QAAQ;gCAC3C,+CAA+C;gCAC/C,iBAAiB,SAAS,SAAS,CAAC,IAAI,GAAG,CAAC;4BAChD;wBACJ;wBACA,MAAM,mBAAmB,GAAG,2DAA2D;wBACvF,MAAM,mBAAmB,WAAW,IAAI,QAAQ,KAAK;wBACrD,MAAM,SAAS,EAAE;wBACjB,IAAK,IAAI,IAAI,kBAAkB,IAAI,mBAAmB,iBAAiB,IAAI,KAAK,GAAI;4BAChF,MAAM,QAAQ;gCACV,QAAQ,SAAS,SAAS,CAAC,GAAG,CAAC;gCAC/B,MAAM,SAAS,SAAS,CAAC,IAAI,GAAG,CAAC;gCACjC,oEAAoE;gCACpE,wEAAwE;gCACxE,oEAAoE;gCACpE,YAAY,SAAS,SAAS,CAAC,IAAI,GAAG,CAAC;gCACvC,iBAAiB,SAAS,SAAS,CAAC,IAAI,IAAI,CAAC;gCAC7C,OAAO,CAAC;gCACR,KAAK,CAAC;gCACN,OAAO;4BACX;4BACA,IAAI,CAAC,MAAM,UAAU,EAAE;gCACnB,sDAAsD;gCACtD,MAAM,KAAK,GAAG;gCACd,MAAM,KAAK,GAAG;4BAClB,OACK;gCACD,MAAM,KAAK,GAAG,aAAa,MAAM,UAAU;gCAC3C,MAAM,KAAK,GAAG;4BAClB;4BACA,MAAM,GAAG,GAAG,MAAM,KAAK,GAAG,MAAM,IAAI;4BACpC,OAAO,IAAI,CAAC;wBAChB;wBACA,IAAI,IAAI,CAAC,OAAO,CAAC,aAAa,IAAI,OAAO,MAAM,EAAE;4BAC7C,MAAM,aAAa,IAAI,KAAK;gCAAC;6BAAS;4BACtC,MAAM,OAAO,EAAE;4BACf,KAAK,MAAM,SAAS,OAAQ;gCACxB,IAAI,MAAM,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,EAAE;oCACzC,UAAU,WAAW;gCACzB;gCACA,MAAM,YAAY,WAAW,KAAK,CAAC,MAAM,KAAK,EAAE,MAAM,GAAG,GAAG,GAAG;gCAC/D,qBAAqB;gCACrB,kDAAkD;gCAClD,4CAA4C;gCAC5C,2BAA2B;gCAC3B,KAAK,IAAI,CAAC;4BACd;4BACA,QAAQ;wBACZ;oBACJ;gBACJ;gBACA,UAAU,IAAI,SAAS,SAAS,CAAC,SAAS;YAC9C;QACJ;IACJ;AACJ;AAEA;;;;;;;;;;;;;;;;;;;CAmBC,GACD,MAAM,yBAAyB,OAAO;IAClC,MAAM,WAAW,WAAW;IAC5B,IAAI,CAAC,UACD,MAAM,IAAI,yBAAyB;IACvC,MAAM,eAAe,IAAI,aAAa;QAAE,YAAY;QAAM,eAAe;IAAK;IAC9E,MAAM,SAAS,MAAM,aAAa,OAAO,CAAC;IAC1C,IAAI,OAAO,MAAM,KAAK,GAClB,MAAM,IAAI,qBAAqB;IACnC,OAAO;QACH,KAAK,IAAI,WAAW,MAAM,MAAM,CAAC,EAAE,CAAC,WAAW;QAC/C,SAAS,IAAI,WAAW,MAAM,MAAM,CAAC,EAAE,CAAC,WAAW;QACnD;IACJ;AACJ;AAEA;;;;;CAKC,GACD,MAAM,uBAAuB,CAAC;IAC1B,OAAO,IAAI,QAAQ,CAAC,SAAS;QACzB,MAAM,MAAM,SAAS,aAAa,CAAC;QACnC,IAAI,MAAM,GAAG;YAAQ,QAAQ;QAAM;QACnC,IAAI,OAAO,GAAG,CAAC;YAAQ,OAAO;QAAI;QAClC,IAAI,GAAG,GAAG,IAAI,eAAe,CAAC;IAClC;AACJ;AAEA,MAAM,mBAAmB,+IAAA,CAAA,SAAM;IAC3B;;;;KAIC,GACD,YAAY,QAAQ,EAAE,OAAO,CAAE;QAC3B,KAAK,CAAC;QACN,IAAI,UACA,IAAI,CAAC,SAAS,GAAG;QACrB,IAAI,CAAC,uBAAuB,GAAG,IAAI,+IAAA,CAAA,iBAAc;IACrD;IACA;;;;;KAKC,GACD,YAAY,QAAQ,EAAE;QAClB,IAAI,CAAC,SAAS,GAAG;QACjB,OAAO,IAAI;IACf;IACA;;;;;KAKC,GACD,uBAAuB,OAAO,EAAE;QAC5B,IAAI,CAAC,oBAAoB,GAAG;QAC5B,OAAO,IAAI;IACf;IACA;;;KAGC,GACD,sBAAsB;QAClB,IAAI,CAAC,IAAI,CAAC,SAAS,EACf,QAAQ,IAAI,CAAC;QACjB,mBAAmB;QACnB,MAAM,WAAW,IAAI,uBAAuB;YACxC,YAAY;gBAAC;gBAAG;gBAAG;aAAE;YACrB,YAAY;gBAAC;gBAAG;gBAAG;aAAE;YACrB,OAAO;gBAAC;gBAAG;gBAAG;aAAE;YAChB,WAAW;gBAAC;gBAAG;gBAAG;aAAE;YACpB,WAAW;gBAAC;gBAAG;gBAAG;aAAE;YACpB,gBAAgB;YAChB,gBAAgB;YAChB,iBAAiB;YACjB,SAAS,IAAI,+IAAA,CAAA,UAAO;YACpB,KAAK,IAAI,+IAAA,CAAA,UAAO;QACpB;QACA,OAAO,IAAI,+KAAA,CAAA,IAAY,CAAC;YACpB,OAAO;YACP,QAAQ;YACR,MAAM,+IAAA,CAAA,gBAAa;YACnB,YAAY,+IAAA,CAAA,uBAAoB;YAChC;YACA,UAAU,IAAI,CAAC,SAAS;YACxB,qBAAqB,IAAI,CAAC,oBAAoB;QAClD;IACJ;IACA;;;;;;GAMD,GACC,MAAM,OAAO,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAE,aAAa,EAAE;QAC3D,gEAAgE;QAChE,MAAM,cAAc,gBAAgB,IAAI,KAAK;YAAC;SAAc,EAAE;YAAE,MAAM;QAAa,KAAK;QACxF,MAAM,UAAU,IAAI,KAAK;YAAC;SAAU,EAAE;YAAE,MAAM;QAAa;QAC3D,IAAI;QACJ,IAAI;QACJ,IAAI,YAAY;QAChB,IAAI,OAAO,sBAAsB,aAAa;YAC1C,MAAM,MAAM,MAAM,QAAQ,GAAG,CAAC;gBAC1B,cAAc,qBAAqB,eAAe,QAAQ,OAAO,CAAC;gBAClE,qBAAqB;aACxB;YACD,eAAe,GAAG,CAAC,EAAE;YACrB,WAAW,GAAG,CAAC,EAAE;YACjB,YAAY;QAChB,OACK;YACD,MAAM,MAAM,MAAM,QAAQ,GAAG,CAAC;gBAC1B,cAAc,kBAAkB,aAAa;oBAAE,kBAAkB;gBAAQ,KAAK,QAAQ,OAAO,CAAC;gBAC9F,kBAAkB,SAAS;oBAAE,kBAAkB;gBAAQ;aAC1D;YACD,eAAe,GAAG,CAAC,EAAE;YACrB,WAAW,GAAG,CAAC,EAAE;QACrB;QACA,MAAM,UAAU,IAAI,+IAAA,CAAA,UAAO,CAAC,gBAAgB,IAAI,UAAU,GAAG,IAAI,+IAAA,CAAA,YAAS,EAAE,+IAAA,CAAA,sBAAmB,EAAE,+IAAA,CAAA,sBAAmB,EAAE,+IAAA,CAAA,eAAY,EAAE,+IAAA,CAAA,2BAAwB,EAAE,+IAAA,CAAA,aAAU,EAAE,+IAAA,CAAA,mBAAgB,EAAE,GAAG,+IAAA,CAAA,uBAAoB;QACnN,QAAQ,KAAK,GAAG;QAChB,QAAQ,WAAW,GAAG;QACtB,MAAM,MAAM,IAAI,+IAAA,CAAA,UAAO,CAAC,UAAU,+IAAA,CAAA,YAAS,EAAE,+IAAA,CAAA,sBAAmB,EAAE,+IAAA,CAAA,sBAAmB,EAAE,+IAAA,CAAA,eAAY,EAAE,+IAAA,CAAA,2BAAwB,EAAE,+IAAA,CAAA,aAAU,EAAE,+IAAA,CAAA,mBAAgB,EAAE,GAAG,+IAAA,CAAA,iBAAc;QAC9K,IAAI,KAAK,GAAG;QACZ,IAAI,WAAW,GAAG;QAClB,aAAa,KAAK,GAAG,SAAS,KAAK;QACnC,aAAa,MAAM,GAAG,SAAS,MAAM;QACrC,aAAa,QAAQ,CAAC,OAAO,GAAG;QAChC,aAAa,QAAQ,CAAC,GAAG,GAAG;QAC5B,aAAa,QAAQ,CAAC,UAAU,GAAG,SAAS,UAAU;QACtD,aAAa,QAAQ,CAAC,UAAU,GAAG,SAAS,UAAU;QACtD,aAAa,QAAQ,CAAC,SAAS,GAAG,SAAS,SAAS;QACpD,aAAa,QAAQ,CAAC,SAAS,GAAG,SAAS,SAAS;QACpD,aAAa,QAAQ,CAAC,KAAK,GAAG,SAAS,KAAK;QAC5C,aAAa,QAAQ,CAAC,cAAc,GAAG,SAAS,cAAc;QAC9D,aAAa,QAAQ,CAAC,cAAc,GAAG,SAAS,cAAc;QAC9D,aAAa,QAAQ,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,GAAG,SAAS,cAAc;QAC3E,aAAa,QAAQ,CAAC,WAAW,GAAG;QACpC,aAAa,MAAM;IACvB;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+CC,GACD,MAAM,sBAAsB;IACxB;;;;;;;;;;;;;KAaC,GACD,KAAK,CAAC,QAAQ,YAAY,YAAY,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE;QACjE,MAAM,eAAe,IAAI,CAAC,mBAAmB;QAC7C,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM,YAAY;YACd,IAAI,OAAO,WAAW,UAAU;gBAC5B,aAAa;gBACb,IAAI;oBACA,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,UAAU,KAAK;gBACnD,EACA,OAAO,OAAO;oBACV,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;oBACvB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;oBACvB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;oBACvB,IAAI,OAAO,YAAY,YACnB,QAAQ;oBACZ,aAAa,uBAAuB;oBACpC;gBACJ;gBACA,IAAI,OAAO,WAAW,YAClB,OAAO;gBACX,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBACrB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBACrB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;gBACrB,aAAa,uBAAuB;YACxC;QACJ;QACA,IAAI,sBAAsB;QAC1B,IAAI,WAAW;QACf,IAAI,YAAY;QAChB,IAAI,0BAA0B;QAC9B,IAAI,eAAe;QACnB,IAAI,gBAAgB;QACpB,IAAI,2BAA2B;QAC/B,IAAI,gBAAgB;QACpB,IAAI,iBAAiB;QACrB,MAAM,kBAAkB;YACpB,IAAI,OAAO,eAAe,YAAY;gBAClC,MAAM,QAAQ,WAAW,eAAe;gBACxC,MAAM,SAAS,YAAY,gBAAgB;gBAC3C,MAAM,mBAAmB,uBAAuB,2BAA2B;gBAC3E,WAAW,IAAI,cAAc,YAAY;oBAAE;oBAAkB;oBAAQ;gBAAM;YAC/E;QACJ;QACA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACvB,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACvB,MAAM,YAAY,IAAI,+IAAA,CAAA,aAAU,CAAC,IAAI,CAAC,uBAAuB;QAC7D,UAAU,eAAe,CAAC;QAC1B,UAAU,gBAAgB,CAAC,IAAI,CAAC,aAAa;QAC7C,UAAU,OAAO,CAAC,IAAI,CAAC,IAAI;QAC3B,UAAU,kBAAkB,CAAC,IAAI,CAAC,eAAe;QACjD,UAAU,IAAI,CAAC,QAAQ,OAAO;YAC1B;;YAEA,GACA,IAAI,OAAO,WAAW,UAClB,MAAM,IAAI,MAAM;YACpB,MAAM;YACN,MAAM;QACV,GAAG,CAAC;YACA,sBAAsB,EAAE,gBAAgB;YACxC,YAAY,EAAE,MAAM;YACpB,WAAW,EAAE,KAAK;YAClB;QACJ,GAAG,CAAC;YACA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YACvB,IAAI,OAAO,YAAY,YACnB,QAAQ;QAChB;QACA,MAAM,gBAAgB,IAAI,+IAAA,CAAA,aAAU,CAAC,IAAI,CAAC,uBAAuB;QACjE,cAAc,eAAe,CAAC;QAC9B,cAAc,gBAAgB,CAAC,IAAI,CAAC,aAAa;QACjD,cAAc,OAAO,CAAC,IAAI,CAAC,IAAI;QAC/B,cAAc,kBAAkB,CAAC,IAAI,CAAC,eAAe;QACrD,cAAc,IAAI,CAAC,YAAY,OAAO;YAClC;;YAEA,GACA,IAAI,OAAO,WAAW,UAClB,MAAM,IAAI,MAAM;YACpB,UAAU;YACV,MAAM;QACV,GAAG,CAAC;YACA,0BAA0B,EAAE,gBAAgB;YAC5C,gBAAgB,EAAE,MAAM;YACxB,eAAe,EAAE,KAAK;YACtB;QACJ,GAAG,CAAC;YACA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YACvB,IAAI,OAAO,YAAY,YACnB,QAAQ;QAChB;QACA,MAAM,iBAAiB,IAAI,+IAAA,CAAA,aAAU,CAAC,IAAI,CAAC,uBAAuB;QAClE,yCAAyC;QACzC,eAAe,gBAAgB,CAAC,IAAI,CAAC,aAAa;QAClD,eAAe,OAAO,CAAC,IAAI,CAAC,IAAI;QAChC,eAAe,kBAAkB,CAAC,IAAI,CAAC,eAAe;QACtD,eAAe,IAAI,CAAC,aAAa,OAAO;YACpC;;YAEA,GACA,IAAI,OAAO,SAAS,UAChB,MAAM,IAAI,MAAM;YACpB,oEAAoE;YACpE,mEAAmE;YACnE,WAAW,KAAK,KAAK,CAAC;YACtB,MAAM;QACV,GAAG,CAAC;YACA,2BAA2B,EAAE,gBAAgB;YAC7C,iBAAiB,EAAE,MAAM;YACzB,gBAAgB,EAAE,KAAK;YACvB;QACJ,GAAG,CAAC;YACA,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YACvB,IAAI,OAAO,YAAY,YACnB,QAAQ;QAChB;QACA,OAAO;IACX;AACJ;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+CC,GACD,MAAM,qBAAqB;IACvB;;;;;;;;;KASC,GACD,KAAK,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE;QACnC,MAAM,eAAe,IAAI,CAAC,mBAAmB;QAC7C,MAAM,SAAS,IAAI,+IAAA,CAAA,aAAU,CAAC,IAAI,CAAC,uBAAuB;QAC1D,OAAO,eAAe,CAAC;QACvB,OAAO,gBAAgB,CAAC,IAAI,CAAC,aAAa;QAC1C,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI;QACxB,OAAO,kBAAkB,CAAC,IAAI,CAAC,eAAe;QAC9C,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACvB,OAAO,IAAI,CAAC,KAAK,OAAO;YACpB;;YAEA,GACA,IAAI,OAAO,SAAS,UAChB,MAAM,IAAI,MAAM;YACpB,MAAM,aAAa,IAAI,WAAW;YAClC,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;gBACA,MAAM,mBAAmB,MAAM,uBAAuB;gBACtD,yCAAyC;gBACzC,UAAU,iBAAiB,GAAG;gBAC9B,cAAc,iBAAiB,OAAO;gBACtC,WAAW,iBAAiB,QAAQ;YACxC,EACA,OAAO,GAAG;gBACN,kDAAkD;gBAClD,IAAI,aAAa,4BAA4B,aAAa,sBAAsB;oBAC5E,QAAQ,IAAI,CAAC,CAAC,yCAAyC,EAAE,IAAI,gFAAgF,CAAC;oBAC9I,WAAW;wBACP,YAAY;4BAAC;4BAAG;4BAAG;yBAAE;wBACrB,YAAY;4BAAC;4BAAG;4BAAG;yBAAE;wBACrB,OAAO;4BAAC;4BAAG;4BAAG;yBAAE;wBAChB,gBAAgB;wBAChB,gBAAgB;wBAChB,WAAW;4BAAC;4BAAG;4BAAG;yBAAE;wBACpB,WAAW;4BAAC;4BAAG;4BAAG;yBAAE;oBACxB;oBACA,UAAU;gBACd,OACK;oBACD,MAAM;gBACV;YACJ;YACA,aAAa;YACb,IAAI;gBACA,MAAM,IAAI,CAAC,MAAM,CAAC,cAAc,UAAU,SAAS;YACvD,EACA,OAAO,OAAO;gBACV,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;gBACvB,IAAI,OAAO,YAAY,YACnB,QAAQ;gBACZ,aAAa,uBAAuB;gBACpC;YACJ;YACA,IAAI,OAAO,WAAW,YAClB,OAAO;YACX,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC;YACrB,aAAa,uBAAuB;QACxC,GAAG,YAAY,CAAC;YACZ,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;YACvB,IAAI,OAAO,YAAY,YACnB,QAAQ;QAChB;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6243, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/styled-jsx/dist/index/index.js"], "sourcesContent": ["require('client-only');\nvar React = require('react');\n\nfunction _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\nvar React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && process.env.NODE_ENV === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node = typeof window !== \"undefined\" && document.querySelector('meta[property=\"csp-nonce\"]');\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if (typeof window !== \"undefined\" && this._optimizeForSpeed) {\n            this._tags[0] = this.makeStyleTag(this._name);\n            this._optimizeForSpeed = \"insertRule\" in this.getSheet();\n            if (!this._optimizeForSpeed) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.\");\n                }\n                this.flush();\n                this._injected = true;\n            }\n            return;\n        }\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (typeof window === \"undefined\") {\n            if (typeof index !== \"number\") {\n                index = this._serverSheet.cssRules.length;\n            }\n            this._serverSheet.insertRule(rule, index);\n            return this._rulesCount++;\n        }\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || typeof window === \"undefined\") {\n            var sheet = typeof window !== \"undefined\" ? this.getSheet() : this._serverSheet;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"old rule at index `\" + index + \"` not found\");\n            tag.textContent = rule;\n        }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (typeof window === \"undefined\") {\n            this._serverSheet.deleteRule(index);\n            return;\n        }\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (typeof window !== \"undefined\") {\n            this._tags.forEach(function(tag) {\n                return tag && tag.parentNode.removeChild(tag);\n            });\n            this._tags = [];\n        } else {\n            // simpler on server\n            this._serverSheet.cssRules = [];\n        }\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (typeof window === \"undefined\") {\n            return this._serverSheet.cssRules;\n        }\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\n\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\n\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (typeof window === \"undefined\") {\n        css = sanitize(css);\n    }\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\n\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if (typeof window !== \"undefined\" && !this._fromServer) {\n            this._fromServer = this.selectFromServer();\n            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {\n                acc[tagName] = 0;\n                return acc;\n            }, {});\n        }\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        })// Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        })// filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState(function() {\n        return rootRegistry || configuredRegistry || createStyleRegistry();\n    }), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\nfunction useStyleRegistry() {\n    return React.useContext(StyleSheetContext);\n}\n\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry = typeof window !== \"undefined\" ? createStyleRegistry() : undefined;\nfunction JSXStyle(props) {\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (typeof window === \"undefined\") {\n        registry.add(props);\n        return null;\n    }\n    useInsertionEffect(function() {\n        registry.add(props);\n        return function() {\n            registry.remove(props);\n        };\n    // props.children can be string[], will be striped since id is identical\n    }, [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\n\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\n"], "names": [], "mappings": ";AACA,IAAI;AAEJ,SAAS,sBAAuB,CAAC;IAAI,OAAO,KAAK,OAAO,MAAM,YAAY,aAAa,IAAI,IAAI;QAAE,WAAW;IAAE;AAAG;AAEjH,IAAI,iBAAiB,WAAW,GAAE,sBAAsB;AAExD;;;AAGA,GAAG,SAAS,kBAAkB,MAAM,EAAE,KAAK;IACvC,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI;QACjC,IAAI,aAAa,KAAK,CAAC,EAAE;QACzB,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QACjD,WAAW,YAAY,GAAG;QAC1B,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QACjD,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAClD;AACJ;AACA,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IACtD,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IACzD,IAAI,aAAa,kBAAkB,aAAa;IAChD,OAAO;AACX;AACA,IAAI,SAAS,OAAO,YAAY,eAAe,QAAQ,GAAG,IAAI,oDAAyB;AACvF,IAAI,WAAW,SAAS,CAAC;IACrB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;AACjD;AACA,IAAI,aAAa,WAAW,GAAG;IAC3B,SAAS,WAAW,KAAK;QACrB,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,OAAO,QAAQ,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,IAAI,eAAe,OAAO,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,sBAAsB,KAAK,IAAI,SAAS;QAChN,YAAY,SAAS,OAAO;QAC5B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,uBAAuB,GAAG,MAAM,OAAO;QAC5C,YAAY,OAAO,qBAAqB,WAAW;QACnD,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,OAAO,OAAO,WAAW,eAAe,SAAS,aAAa,CAAC;QACnE,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK,YAAY,CAAC,aAAa;IACxD;IACA,IAAI,SAAS,WAAW,SAAS;IACjC,OAAO,mBAAmB,GAAG,SAAS,oBAAoB,IAAI;QAC1D,YAAY,OAAO,SAAS,WAAW;QACvC,YAAY,IAAI,CAAC,WAAW,KAAK,GAAG;QACpC,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,MAAM;IACf;IACA,OAAO,kBAAkB,GAAG,SAAS;QACjC,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA,OAAO,MAAM,GAAG,SAAS;QACrB,IAAI,QAAQ,IAAI;QAChB,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE;QAC7B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,OAAO,WAAW,eAAe,IAAI,CAAC,iBAAiB,EAAE;YACzD,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK;YAC5C,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,IAAI,CAAC,QAAQ;YACtD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBACzB,wCAAa;oBACT,QAAQ,IAAI,CAAC;gBACjB;gBACA,IAAI,CAAC,KAAK;gBACV,IAAI,CAAC,SAAS,GAAG;YACrB;YACA;QACJ;QACA,IAAI,CAAC,YAAY,GAAG;YAChB,UAAU,EAAE;YACZ,YAAY,SAAS,IAAI,EAAE,KAAK;gBAC5B,IAAI,OAAO,UAAU,UAAU;oBAC3B,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG;wBACjC,SAAS;oBACb;gBACJ,OAAO;oBACH,MAAM,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;wBAC7B,SAAS;oBACb;gBACJ;gBACA,OAAO;YACX;YACA,YAAY,SAAS,KAAK;gBACtB,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG;YACzC;QACJ;IACJ;IACA,OAAO,cAAc,GAAG,SAAS,eAAe,GAAG;QAC/C,IAAI,IAAI,KAAK,EAAE;YACX,OAAO,IAAI,KAAK;QACpB;QACA,2CAA2C;QAC3C,IAAI,IAAI,IAAI,GAAG,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,IAAI;YAChD,IAAI,SAAS,WAAW,CAAC,EAAE,CAAC,SAAS,KAAK,KAAK;gBAC3C,OAAO,SAAS,WAAW,CAAC,EAAE;YAClC;QACJ;IACJ;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAChE;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,IAAI,EAAE,KAAK;QAC/C,YAAY,SAAS,OAAO;QAC5B,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,OAAO,UAAU,UAAU;gBAC3B,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM;YAC7C;YACA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM;YACnC,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,QAAQ,IAAI,CAAC,QAAQ;YACzB,IAAI,OAAO,UAAU,UAAU;gBAC3B,QAAQ,MAAM,QAAQ,CAAC,MAAM;YACjC;YACA,kDAAkD;YAClD,4FAA4F;YAC5F,IAAI;gBACA,MAAM,UAAU,CAAC,MAAM;YAC3B,EAAE,OAAO,OAAO;gBACZ,wCAAa;oBACT,QAAQ,IAAI,CAAC,mCAAmC,OAAO;gBAC3D;gBACA,OAAO,CAAC;YACZ;QACJ,OAAO;YACH,IAAI,iBAAiB,IAAI,CAAC,KAAK,CAAC,MAAM;YACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM;QACxD;QACA,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,OAAO,WAAW,GAAG,SAAS,YAAY,KAAK,EAAE,IAAI;QACjD,IAAI,IAAI,CAAC,iBAAiB,IAAI,OAAO,WAAW,aAAa;YACzD,IAAI,QAAQ,OAAO,WAAW,cAAc,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY;YAC/E,IAAI,CAAC,KAAK,IAAI,IAAI;gBACd,OAAO,IAAI,CAAC,uBAAuB;YACvC;YACA,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,EAAE;gBACxB,iCAAiC;gBACjC,OAAO;YACX;YACA,MAAM,UAAU,CAAC;YACjB,IAAI;gBACA,MAAM,UAAU,CAAC,MAAM;YAC3B,EAAE,OAAO,OAAO;gBACZ,wCAAa;oBACT,QAAQ,IAAI,CAAC,mCAAmC,OAAO;gBAC3D;gBACA,qEAAqE;gBACrE,MAAM,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACnD;QACJ,OAAO;YACH,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM;YAC3B,YAAY,KAAK,wBAAwB,QAAQ;YACjD,IAAI,WAAW,GAAG;QACtB;QACA,OAAO;IACX;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,KAAK;QACzC,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;YAC7B;QACJ;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,WAAW,CAAC,OAAO;QAC5B,OAAO;YACH,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM;YAC3B,YAAY,KAAK,oBAAoB,QAAQ;YAC7C,IAAI,UAAU,CAAC,WAAW,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QACxB;IACJ;IACA,OAAO,KAAK,GAAG,SAAS;QACpB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,GAAG;gBAC3B,OAAO,OAAO,IAAI,UAAU,CAAC,WAAW,CAAC;YAC7C;YACA,IAAI,CAAC,KAAK,GAAG,EAAE;QACnB,OAAO;YACH,oBAAoB;YACpB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE;QACnC;IACJ;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI;QAChB,IAAI,OAAO,WAAW,aAAa;YAC/B,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;QACrC;QACA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,KAAK,EAAE,GAAG;YACxC,IAAI,KAAK;gBACL,QAAQ,MAAM,MAAM,CAAC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,cAAc,CAAC,KAAK,QAAQ,EAAE,SAAS,IAAI;oBAC3F,OAAO,KAAK,OAAO,KAAK,MAAM,uBAAuB,GAAG,OAAO;gBACnE;YACJ,OAAO;gBACH,MAAM,IAAI,CAAC;YACf;YACA,OAAO;QACX,GAAG,EAAE;IACT;IACA,OAAO,YAAY,GAAG,SAAS,aAAa,IAAI,EAAE,SAAS,EAAE,aAAa;QACtE,IAAI,WAAW;YACX,YAAY,SAAS,YAAY;QACrC;QACA,IAAI,MAAM,SAAS,aAAa,CAAC;QACjC,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,YAAY,CAAC,SAAS,IAAI,CAAC,MAAM;QACtD,IAAI,IAAI,GAAG;QACX,IAAI,YAAY,CAAC,UAAU,MAAM;QACjC,IAAI,WAAW;YACX,IAAI,WAAW,CAAC,SAAS,cAAc,CAAC;QAC5C;QACA,IAAI,OAAO,SAAS,IAAI,IAAI,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE;QACpE,IAAI,eAAe;YACf,KAAK,YAAY,CAAC,KAAK;QAC3B,OAAO;YACH,KAAK,WAAW,CAAC;QACrB;QACA,OAAO;IACX;IACA,aAAa,YAAY;QACrB;YACI,KAAK;YACL,KAAK,SAAS;gBACV,OAAO,IAAI,CAAC,WAAW;YAC3B;QACJ;KACH;IACD,OAAO;AACX;AACA,SAAS,YAAY,SAAS,EAAE,OAAO;IACnC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,iBAAiB,UAAU;IAC/C;AACJ;AAEA,SAAS,KAAK,GAAG;IACb,IAAI,SAAS,MAAM,IAAI,IAAI,MAAM;IACjC,MAAM,EAAE;QACJ,SAAS,SAAS,KAAK,IAAI,UAAU,CAAC,EAAE;IAC5C;IACA;;8DAE0D,GAAG,OAAO,WAAW;AACnF;AACA,IAAI,aAAa;AAEjB,IAAI,WAAW,SAAS,IAAI;IACxB,OAAO,KAAK,OAAO,CAAC,aAAa;AACrC;AACA,IAAI,QAAQ,CAAC;AACb;;;;CAIC,GAAG,SAAS,UAAU,MAAM,EAAE,KAAK;IAChC,IAAI,CAAC,OAAO;QACR,OAAO,SAAS;IACpB;IACA,IAAI,gBAAgB,OAAO;IAC3B,IAAI,MAAM,SAAS;IACnB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QACb,KAAK,CAAC,IAAI,GAAG,SAAS,WAAW,SAAS,MAAM;IACpD;IACA,OAAO,KAAK,CAAC,IAAI;AACrB;AACA;;;;CAIC,GAAG,SAAS,gBAAgB,EAAE,EAAE,GAAG;IAChC,IAAI,2BAA2B;IAC/B,uBAAuB;IACvB,6DAA6D;IAC7D,2EAA2E;IAC3E,IAAI,OAAO,WAAW,aAAa;QAC/B,MAAM,SAAS;IACnB;IACA,IAAI,QAAQ,KAAK;IACjB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACf,KAAK,CAAC,MAAM,GAAG,IAAI,OAAO,CAAC,0BAA0B;IACzD;IACA,OAAO,KAAK,CAAC,MAAM;AACvB;AAEA,SAAS,gBAAgB,QAAQ,EAAE,OAAO;IACtC,IAAI,YAAY,KAAK,GAAG,UAAU,CAAC;IACnC,OAAO,SAAS,GAAG,CAAC,SAAS,IAAI;QAC7B,IAAI,KAAK,IAAI,CAAC,EAAE;QAChB,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,OAAO,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS;YAClE,IAAI,OAAO;YACX,wCAAwC;YACxC,KAAK,OAAO;YACZ,OAAO,QAAQ,KAAK,GAAG,QAAQ,KAAK,GAAG;YACvC,yBAAyB;gBACrB,QAAQ;YACZ;QACJ;IACJ;AACJ;AACA,IAAI,qBAAqB,WAAW,GAAG;IACnC,SAAS,mBAAmB,KAAK;QAC7B,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,OAAO,cAAc,IAAI,UAAU,EAAE,aAAa,gBAAgB,KAAK,IAAI,OAAO,aAAa,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,sBAAsB,KAAK,IAAI,QAAQ;QACrO,IAAI,CAAC,MAAM,GAAG,cAAc,IAAI,WAAW;YACvC,MAAM;YACN,kBAAkB;QACtB;QACA,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,IAAI,cAAc,OAAO,qBAAqB,WAAW;YACrD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;YAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC3D;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,IAAI,SAAS,mBAAmB,SAAS;IACzC,OAAO,GAAG,GAAG,SAAS,IAAI,KAAK;QAC3B,IAAI,QAAQ,IAAI;QAChB,IAAI,cAAc,IAAI,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,iBAAiB,GAAG,MAAM,OAAO,CAAC,MAAM,QAAQ;YACrD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB;YACtD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC3D;QACA,IAAI,OAAO,WAAW,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE;YACpD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB;YACxC,IAAI,CAAC,gBAAgB,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,GAAG,EAAE,OAAO;gBAC9E,GAAG,CAAC,QAAQ,GAAG;gBACf,OAAO;YACX,GAAG,CAAC;QACR;QACA,IAAI,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,UAAU,IAAI,OAAO,EAAE,QAAQ,IAAI,KAAK;QAC7E,+CAA+C;QAC/C,IAAI,WAAW,IAAI,CAAC,gBAAgB,EAAE;YAClC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI;YAClC;QACJ;QACA,IAAI,UAAU,MAAM,GAAG,CAAC,SAAS,IAAI;YACjC,OAAO,MAAM,MAAM,CAAC,UAAU,CAAC;QACnC,GAAE,2BAA2B;SAC5B,MAAM,CAAC,SAAS,KAAK;YAClB,OAAO,UAAU,CAAC;QACtB;QACA,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG;QACzB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG;IACrC;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,KAAK;QACjC,IAAI,QAAQ,IAAI;QAChB,IAAI,UAAU,IAAI,CAAC,aAAa,CAAC,OAAO,OAAO;QAC/C,UAAU,WAAW,IAAI,CAAC,gBAAgB,EAAE,eAAe,UAAU;QACrE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI;QAClC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,GAAG;YACpC,IAAI,gBAAgB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ;YACjE,IAAI,eAAe;gBACf,cAAc,UAAU,CAAC,WAAW,CAAC;gBACrC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;YACpC,OAAO;gBACH,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,KAAK;oBACzC,OAAO,MAAM,MAAM,CAAC,UAAU,CAAC;gBACnC;gBACA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ;YACjC;YACA,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ;QACzC;IACJ;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,KAAK,EAAE,SAAS;QAC5C,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,OAAO,KAAK,GAAG,SAAS;QACpB,IAAI,CAAC,MAAM,CAAC,KAAK;QACjB,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI;QAChB,IAAI,aAAa,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,SAAS,OAAO;YAClF,OAAO;gBACH;gBACA,MAAM,WAAW,CAAC,QAAQ;aAC7B;QACL,KAAK,EAAE;QACP,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ;QACnC,OAAO,WAAW,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,SAAS,OAAO;YACpE,OAAO;gBACH;gBACA,MAAM,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,KAAK;oBACtC,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO;gBAClC,GAAG,IAAI,CAAC,MAAM,iBAAiB,GAAG,KAAK;aAC1C;QACL,GAAE,yBAAyB;SAC1B,MAAM,CAAC,SAAS,IAAI;YACjB,OAAO,QAAQ,IAAI,CAAC,EAAE;QAC1B;IACJ;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,OAAO;QACnC,OAAO,gBAAgB,IAAI,CAAC,QAAQ,IAAI;IAC5C;IACA,OAAO,aAAa,GAAG,SAAS,cAAc,KAAK;QAC/C,IAAI,MAAM,MAAM,QAAQ,EAAE,UAAU,MAAM,OAAO,EAAE,KAAK,MAAM,EAAE;QAChE,IAAI,SAAS;YACT,IAAI,UAAU,UAAU,IAAI;YAC5B,OAAO;gBACH,SAAS;gBACT,OAAO,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG,CAAC,SAAS,IAAI;oBAC7C,OAAO,gBAAgB,SAAS;gBACpC,KAAK;oBACD,gBAAgB,SAAS;iBAC5B;YACL;QACJ;QACA,OAAO;YACH,SAAS,UAAU;YACnB,OAAO,MAAM,OAAO,CAAC,OAAO,MAAM;gBAC9B;aACH;QACL;IACJ;IACA;;;;GAID,GAAG,OAAO,gBAAgB,GAAG,SAAS;QACjC,IAAI,WAAW,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,gBAAgB,CAAC;QACpE,OAAO,SAAS,MAAM,CAAC,SAAS,GAAG,EAAE,OAAO;YACxC,IAAI,KAAK,QAAQ,EAAE,CAAC,KAAK,CAAC;YAC1B,GAAG,CAAC,GAAG,GAAG;YACV,OAAO;QACX,GAAG,CAAC;IACR;IACA,OAAO;AACX;AACA,SAAS,UAAU,SAAS,EAAE,OAAO;IACjC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,yBAAyB,UAAU;IACvD;AACJ;AACA,IAAI,oBAAoB,WAAW,GAAG,MAAM,aAAa,CAAC;AAC1D,kBAAkB,WAAW,GAAG;AAChC,SAAS;IACL,OAAO,IAAI;AACf;AACA,SAAS,cAAc,KAAK;IACxB,IAAI,qBAAqB,MAAM,QAAQ,EAAE,WAAW,MAAM,QAAQ;IAClE,IAAI,eAAe,MAAM,UAAU,CAAC;IACpC,IAAI,MAAM,MAAM,QAAQ,CAAC;QACrB,OAAO,gBAAgB,sBAAsB;IACjD,IAAI,WAAW,GAAG,CAAC,EAAE;IACrB,OAAO,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,kBAAkB,QAAQ,EAAE;QACrF,OAAO;IACX,GAAG;AACP;AACA,SAAS;IACL,OAAO,MAAM,UAAU,CAAC;AAC5B;AAEA,wFAAwF;AACxF,sDAAsD;AACtD,IAAI,qBAAqB,cAAc,CAAC,UAAU,CAAC,kBAAkB,IAAI,cAAc,CAAC,UAAU,CAAC,eAAe;AAClH,IAAI,kBAAkB,OAAO,WAAW,cAAc,wBAAwB;AAC9E,SAAS,SAAS,KAAK;IACnB,IAAI,WAAW,kBAAkB,kBAAkB;IACnD,oDAAoD;IACpD,IAAI,CAAC,UAAU;QACX,OAAO;IACX;IACA,IAAI,OAAO,WAAW,aAAa;QAC/B,SAAS,GAAG,CAAC;QACb,OAAO;IACX;IACA,mBAAmB;QACf,SAAS,GAAG,CAAC;QACb,OAAO;YACH,SAAS,MAAM,CAAC;QACpB;IACJ,wEAAwE;IACxE,GAAG;QACC,MAAM,EAAE;QACR,OAAO,MAAM,OAAO;KACvB;IACD,OAAO;AACX;AACA,SAAS,OAAO,GAAG,SAAS,IAAI;IAC5B,OAAO,KAAK,GAAG,CAAC,SAAS,OAAO;QAC5B,IAAI,SAAS,OAAO,CAAC,EAAE;QACvB,IAAI,QAAQ,OAAO,CAAC,EAAE;QACtB,OAAO,UAAU,QAAQ;IAC7B,GAAG,IAAI,CAAC;AACZ;AAEA,QAAQ,aAAa,GAAG;AACxB,QAAQ,mBAAmB,GAAG;AAC9B,QAAQ,KAAK,GAAG;AAChB,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6743, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/styled-jsx/style.js"], "sourcesContent": ["module.exports = require('./dist/index').style\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,wGAAwB,KAAK", "ignoreList": [0], "debugId": null}}]}
{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/LoadingScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface LoadingScreenProps {\n  onComplete: () => void;\n}\n\nexport default function LoadingScreen({ onComplete }: LoadingScreenProps) {\n  const [progress, setProgress] = useState(0);\n  const [loadingText, setLoadingText] = useState('Memuat Dunia KangPCode...');\n\n  const loadingSteps = [\n    'Memuat Dunia KangPCode...',\n    'Menyiapkan Kamar Virtual...',\n    'Mengaktifkan Komputer 3D...',\n    'Memuat Poster Ilmuwan...',\n    'Menyiapkan Action Figures...',\n    'Dunia KangPCode Siap!'\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setProgress(prev => {\n        const newProgress = prev + 1;\n        \n        // Update loading text based on progress\n        const stepIndex = Math.floor((newProgress / 100) * loadingSteps.length);\n        if (stepIndex < loadingSteps.length) {\n          setLoadingText(loadingSteps[stepIndex]);\n        }\n\n        if (newProgress >= 100) {\n          clearInterval(interval);\n          setTimeout(() => onComplete(), 500);\n          return 100;\n        }\n        return newProgress;\n      });\n    }, 50); // Complete in ~5 seconds\n\n    return () => clearInterval(interval);\n  }, [onComplete]);\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        className=\"fixed inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center z-50\"\n      >\n        <div className=\"text-center space-y-8\">\n          {/* Logo/Avatar */}\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\n            className=\"w-32 h-32 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center shadow-2xl\"\n          >\n            <span className=\"text-4xl font-bold text-white\">KC</span>\n          </motion.div>\n\n          {/* Title */}\n          <motion.h1\n            initial={{ y: 20, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ delay: 0.4 }}\n            className=\"text-4xl md:text-6xl font-bold text-white mb-4\"\n          >\n            KangPCode\n          </motion.h1>\n\n          <motion.p\n            initial={{ y: 20, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ delay: 0.6 }}\n            className=\"text-xl text-blue-200 mb-8\"\n          >\n            Portfolio 3D Interaktif\n          </motion.p>\n\n          {/* Loading Bar */}\n          <div className=\"w-80 mx-auto\">\n            <motion.div\n              initial={{ width: 0 }}\n              animate={{ width: '100%' }}\n              transition={{ delay: 0.8 }}\n              className=\"h-2 bg-gray-700 rounded-full overflow-hidden\"\n            >\n              <motion.div\n                className=\"h-full bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full\"\n                style={{ width: `${progress}%` }}\n                transition={{ duration: 0.1 }}\n              />\n            </motion.div>\n            \n            <div className=\"flex justify-between mt-2 text-sm text-blue-200\">\n              <span>{progress}%</span>\n              <span>Loading...</span>\n            </div>\n          </div>\n\n          {/* Loading Text */}\n          <motion.p\n            key={loadingText}\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -10 }}\n            className=\"text-lg text-cyan-300 font-medium\"\n          >\n            {loadingText}\n          </motion.p>\n\n          {/* Animated Dots */}\n          <div className=\"flex justify-center space-x-2\">\n            {[0, 1, 2].map((i) => (\n              <motion.div\n                key={i}\n                className=\"w-3 h-3 bg-cyan-400 rounded-full\"\n                animate={{\n                  scale: [1, 1.2, 1],\n                  opacity: [0.5, 1, 0.5],\n                }}\n                transition={{\n                  duration: 1.5,\n                  repeat: Infinity,\n                  delay: i * 0.2,\n                }}\n              />\n            ))}\n          </div>\n        </div>\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AASe,SAAS,cAAc,EAAE,UAAU,EAAsB;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,YAAY,CAAA;gBACV,MAAM,cAAc,OAAO;gBAE3B,wCAAwC;gBACxC,MAAM,YAAY,KAAK,KAAK,CAAC,AAAC,cAAc,MAAO,aAAa,MAAM;gBACtE,IAAI,YAAY,aAAa,MAAM,EAAE;oBACnC,eAAe,YAAY,CAAC,UAAU;gBACxC;gBAEA,IAAI,eAAe,KAAK;oBACtB,cAAc;oBACd,WAAW,IAAM,cAAc;oBAC/B,OAAO;gBACT;gBACA,OAAO;YACT;QACF,GAAG,KAAK,yBAAyB;QAEjC,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC;KAAW;IAEf,qBACE,8OAAC,yLAAA,CAAA,kBAAe;kBACd,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;sBAEV,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BAAE,OAAO;4BAAK,MAAM;4BAAU,WAAW;wBAAI;wBACzD,WAAU;kCAEV,cAAA,8OAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;kCAIlD,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCACX;;;;;;kCAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCACX;;;;;;kCAKD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAO;gCACzB,YAAY;oCAAE,OAAO;gCAAI;gCACzB,WAAU;0CAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,SAAS,CAAC,CAAC;oCAAC;oCAC/B,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;0CAIhC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;4CAAM;4CAAS;;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBAEP,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,WAAU;kCAET;uBANI;;;;;kCAUP,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,SAAS;oCACP,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;oCAClB,SAAS;wCAAC;wCAAK;wCAAG;qCAAI;gCACxB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,OAAO,IAAI;gCACb;+BAVK;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBrB", "debugId": null}}, {"offset": {"line": 287, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/ComputerGUI.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface ComputerGUIProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\ntype AppType = 'terminal' | 'resume' | 'projects' | 'games' | 'vscode' | 'browser' | 'settings' | 'info';\n\nexport default function ComputerGUI({ isVisible, onClose }: ComputerGUIProps) {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [activeApp, setActiveApp] = useState<AppType | null>(null);\n  const [isBooting, setIsBooting] = useState(true);\n\n  // Update time every second\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Boot sequence\n  useEffect(() => {\n    if (isVisible) {\n      setIsBooting(true);\n      const bootTimer = setTimeout(() => {\n        setIsBooting(false);\n      }, 2000);\n      return () => clearTimeout(bootTimer);\n    }\n  }, [isVisible]);\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString('id-ID', { \n      hour: '2-digit', \n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n\n  const formatDate = (date: Date) => {\n    return date.toLocaleDateString('id-ID', { \n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const desktopApps = [\n    { id: 'terminal', name: 'Terminal AI', icon: '🧠', color: 'bg-green-600' },\n    { id: 'resume', name: 'Resume', icon: '📄', color: 'bg-blue-600' },\n    { id: 'projects', name: 'My Projects', icon: '💻', color: 'bg-purple-600' },\n    { id: 'games', name: 'Mini Games', icon: '🎮', color: 'bg-red-600' },\n  ];\n\n  const taskbarApps = [\n    { id: 'terminal', name: 'Terminal AI', icon: '🧠' },\n    { id: 'vscode', name: 'VSCode', icon: '💻' },\n    { id: 'browser', name: 'Browser', icon: '🌐' },\n    { id: 'settings', name: 'Settings', icon: '⚙️' },\n    { id: 'info', name: 'System Info', icon: '🧮' },\n    { id: 'games', name: 'Games', icon: '🎮' },\n  ];\n\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50 overflow-hidden\">\n      <AnimatePresence>\n        {isBooting ? (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"flex items-center justify-center h-full bg-black text-white\"\n          >\n            <div className=\"text-center space-y-4\">\n              <div className=\"text-6xl mb-4\">🐧</div>\n              <h2 className=\"text-2xl font-bold\">KangPCode ArchLinux</h2>\n              <p className=\"text-lg\">Booting KDE Plasma...</p>\n              <div className=\"flex justify-center space-x-1 mt-4\">\n                {[0, 1, 2].map((i) => (\n                  <motion.div\n                    key={i}\n                    className=\"w-3 h-3 bg-blue-500 rounded-full\"\n                    animate={{\n                      scale: [1, 1.2, 1],\n                      opacity: [0.5, 1, 0.5],\n                    }}\n                    transition={{\n                      duration: 1,\n                      repeat: Infinity,\n                      delay: i * 0.2,\n                    }}\n                  />\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        ) : (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"h-full bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 relative\"\n          >\n            {/* Desktop Wallpaper */}\n            <div className=\"absolute inset-0 bg-gradient-to-br from-blue-800/50 to-purple-800/50\" />\n            \n            {/* Top Panel */}\n            <div className=\"absolute top-0 left-0 right-0 h-8 bg-gray-900/90 backdrop-blur-sm border-b border-gray-700 flex items-center justify-between px-4 text-white text-sm z-10\">\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"font-bold\">kangpcode@archlinux</span>\n                <span>KDE Plasma 5.27</span>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <span>{formatDate(currentTime)}</span>\n                <span className=\"font-mono\">{formatTime(currentTime)}</span>\n                <button\n                  onClick={onClose}\n                  className=\"hover:bg-red-600 px-2 py-1 rounded transition-colors\"\n                >\n                  ✕\n                </button>\n              </div>\n            </div>\n\n            {/* Desktop Icons */}\n            <div className=\"absolute top-12 left-4 grid grid-cols-1 gap-4 z-10\">\n              {desktopApps.map((app) => (\n                <motion.button\n                  key={app.id}\n                  onClick={() => setActiveApp(app.id as AppType)}\n                  className=\"flex flex-col items-center space-y-1 p-2 rounded hover:bg-white/10 transition-colors group\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <div className={`w-12 h-12 ${app.color} rounded-lg flex items-center justify-center text-2xl shadow-lg group-hover:shadow-xl transition-shadow`}>\n                    {app.icon}\n                  </div>\n                  <span className=\"text-white text-xs font-medium\">{app.name}</span>\n                </motion.button>\n              ))}\n            </div>\n\n            {/* Bottom Taskbar */}\n            <div className=\"absolute bottom-0 left-0 right-0 h-12 bg-gray-900/90 backdrop-blur-sm border-t border-gray-700 flex items-center justify-between px-4 z-10\">\n              {/* Start Menu */}\n              <button className=\"flex items-center space-x-2 px-3 py-2 hover:bg-gray-700/50 rounded transition-colors text-white\">\n                <span className=\"text-xl\">🐧</span>\n                <span className=\"font-medium\">Menu</span>\n              </button>\n\n              {/* App Icons */}\n              <div className=\"flex items-center space-x-2\">\n                {taskbarApps.map((app) => (\n                  <motion.button\n                    key={app.id}\n                    onClick={() => setActiveApp(app.id as AppType)}\n                    className={`w-10 h-10 rounded-lg flex items-center justify-center text-xl transition-colors ${\n                      activeApp === app.id \n                        ? 'bg-blue-600 text-white' \n                        : 'hover:bg-gray-700/50 text-gray-300'\n                    }`}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    title={app.name}\n                  >\n                    {app.icon}\n                  </motion.button>\n                ))}\n              </div>\n\n              {/* System Tray */}\n              <div className=\"flex items-center space-x-2 text-white\">\n                <span className=\"text-sm\">🔊 🔋 📶</span>\n                <span className=\"text-sm font-mono\">{formatTime(currentTime)}</span>\n              </div>\n            </div>\n\n            {/* Active Application Window */}\n            <AnimatePresence>\n              {activeApp && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  exit={{ opacity: 0, scale: 0.9 }}\n                  className=\"absolute inset-8 bg-gray-800 rounded-lg shadow-2xl border border-gray-600 overflow-hidden z-20\"\n                >\n                  {/* Window Title Bar */}\n                  <div className=\"h-8 bg-gray-700 flex items-center justify-between px-4 border-b border-gray-600\">\n                    <span className=\"text-white font-medium\">\n                      {taskbarApps.find(app => app.id === activeApp)?.name}\n                    </span>\n                    <div className=\"flex space-x-2\">\n                      <button className=\"w-4 h-4 bg-yellow-500 rounded-full hover:bg-yellow-400\"></button>\n                      <button className=\"w-4 h-4 bg-green-500 rounded-full hover:bg-green-400\"></button>\n                      <button \n                        onClick={() => setActiveApp(null)}\n                        className=\"w-4 h-4 bg-red-500 rounded-full hover:bg-red-400\"\n                      ></button>\n                    </div>\n                  </div>\n\n                  {/* Window Content */}\n                  <div className=\"flex-1 p-4 text-white overflow-auto\">\n                    {activeApp === 'terminal' && (\n                      <div className=\"font-mono text-green-400 space-y-2\">\n                        <div>KangPCode Terminal AI v1.0</div>\n                        <div>Type 'help' for available commands</div>\n                        <div className=\"text-white\">$ _</div>\n                      </div>\n                    )}\n                    \n                    {activeApp === 'resume' && (\n                      <div className=\"text-center space-y-4\">\n                        <h3 className=\"text-xl font-bold\">Resume Generator</h3>\n                        <p>Generate PDF resume dari data KangPCode</p>\n                        <button className=\"bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors\">\n                          Generate PDF\n                        </button>\n                      </div>\n                    )}\n                    \n                    {activeApp === 'projects' && (\n                      <div className=\"space-y-4\">\n                        <h3 className=\"text-xl font-bold\">My Projects</h3>\n                        <div className=\"grid grid-cols-2 gap-4\">\n                          <div className=\"bg-gray-700 p-4 rounded\">\n                            <h4 className=\"font-bold\">Portfolio 3D</h4>\n                            <p className=\"text-sm text-gray-300\">Interactive 3D portfolio website</p>\n                          </div>\n                          <div className=\"bg-gray-700 p-4 rounded\">\n                            <h4 className=\"font-bold\">Langkah Kode</h4>\n                            <p className=\"text-sm text-gray-300\">Educational programming book</p>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                    \n                    {activeApp === 'games' && (\n                      <div className=\"text-center space-y-4\">\n                        <h3 className=\"text-xl font-bold\">Mini Games</h3>\n                        <div className=\"grid grid-cols-3 gap-4\">\n                          <button className=\"bg-green-600 hover:bg-green-700 p-4 rounded transition-colors\">\n                            🐍 Snake\n                          </button>\n                          <button className=\"bg-blue-600 hover:bg-blue-700 p-4 rounded transition-colors\">\n                            ⭕ TicTacToe\n                          </button>\n                          <button className=\"bg-purple-600 hover:bg-purple-700 p-4 rounded transition-colors\">\n                            🧠 Trivia\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                    \n                    {(activeApp === 'vscode' || activeApp === 'browser' || activeApp === 'settings' || activeApp === 'info') && (\n                      <div className=\"text-center space-y-4\">\n                        <h3 className=\"text-xl font-bold\">Coming Soon</h3>\n                        <p>Aplikasi ini sedang dalam pengembangan</p>\n                      </div>\n                    )}\n                  </div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAYe,SAAS,YAAY,EAAE,SAAS,EAAE,OAAO,EAAoB;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,QAAQ,YAAY;YACxB,eAAe,IAAI;QACrB,GAAG;QACH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,gBAAgB;IAChB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,aAAa;YACb,MAAM,YAAY,WAAW;gBAC3B,aAAa;YACf,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;KAAU;IAEd,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,cAAc;QAClB;YAAE,IAAI;YAAY,MAAM;YAAe,MAAM;YAAM,OAAO;QAAe;QACzE;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM;YAAM,OAAO;QAAc;QACjE;YAAE,IAAI;YAAY,MAAM;YAAe,MAAM;YAAM,OAAO;QAAgB;QAC1E;YAAE,IAAI;YAAS,MAAM;YAAc,MAAM;YAAM,OAAO;QAAa;KACpE;IAED,MAAM,cAAc;QAClB;YAAE,IAAI;YAAY,MAAM;YAAe,MAAM;QAAK;QAClD;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM;QAAK;QAC3C;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAK;QAC7C;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;QAC/C;YAAE,IAAI;YAAQ,MAAM;YAAe,MAAM;QAAK;QAC9C;YAAE,IAAI;YAAS,MAAM;YAAS,MAAM;QAAK;KAC1C;IAED,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;sBACb,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,8OAAC;4BAAE,WAAU;sCAAU;;;;;;sCACvB,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;wCAClB,SAAS;4CAAC;4CAAK;4CAAG;yCAAI;oCACxB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO,IAAI;oCACb;mCAVK;;;;;;;;;;;;;;;;;;;;qCAiBf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;;kCAGV,8OAAC;wBAAI,WAAU;;;;;;kCAGf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAY;;;;;;kDAC5B,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAM,WAAW;;;;;;kDAClB,8OAAC;wCAAK,WAAU;kDAAa,WAAW;;;;;;kDACxC,8OAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,8OAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,oBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,8OAAC;wCAAI,WAAW,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,uGAAuG,CAAC;kDAC5I,IAAI,IAAI;;;;;;kDAEX,8OAAC;wCAAK,WAAU;kDAAkC,IAAI,IAAI;;;;;;;+BATrD,IAAI,EAAE;;;;;;;;;;kCAejB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAIhC,8OAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,oBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS,IAAM,aAAa,IAAI,EAAE;wCAClC,WAAW,CAAC,gFAAgF,EAC1F,cAAc,IAAI,EAAE,GAChB,2BACA,sCACJ;wCACF,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;wCACvB,OAAO,IAAI,IAAI;kDAEd,IAAI,IAAI;uCAXJ,IAAI,EAAE;;;;;;;;;;0CAiBjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,8OAAC;wCAAK,WAAU;kDAAqB,WAAW;;;;;;;;;;;;;;;;;;kCAKpD,8OAAC,yLAAA,CAAA,kBAAe;kCACb,2BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAC/B,WAAU;;8CAGV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDACb,YAAY,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY;;;;;;sDAElD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAO,WAAU;;;;;;8DAClB,8OAAC;oDAAO,WAAU;;;;;;8DAClB,8OAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAU;;;;;;;;;;;;;;;;;;8CAMhB,8OAAC;oCAAI,WAAU;;wCACZ,cAAc,4BACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DAAI;;;;;;8DACL,8OAAC;8DAAI;;;;;;8DACL,8OAAC;oDAAI,WAAU;8DAAa;;;;;;;;;;;;wCAI/B,cAAc,0BACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;8DAAE;;;;;;8DACH,8OAAC;oDAAO,WAAU;8DAAoE;;;;;;;;;;;;wCAMzF,cAAc,4BACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAY;;;;;;8EAC1B,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAY;;;;;;8EAC1B,8OAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;wCAM5C,cAAc,yBACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAO,WAAU;sEAAgE;;;;;;sEAGlF,8OAAC;4DAAO,WAAU;sEAA8D;;;;;;sEAGhF,8OAAC;4DAAO,WAAU;sEAAkE;;;;;;;;;;;;;;;;;;wCAOzF,CAAC,cAAc,YAAY,cAAc,aAAa,cAAc,cAAc,cAAc,MAAM,mBACrG,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,8OAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3B", "debugId": null}}, {"offset": {"line": 1004, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/TerminalAI.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface TerminalAIProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\ninterface TerminalLine {\n  type: 'input' | 'output' | 'error';\n  content: string;\n  timestamp: Date;\n}\n\nexport default function TerminalAI({ isVisible, onClose }: TerminalAIProps) {\n  const [input, setInput] = useState('');\n  const [history, setHistory] = useState<TerminalLine[]>([]);\n  const [isTyping, setIsTyping] = useState(false);\n  const terminalRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // AI Knowledge Base\n  const knowledgeBase = {\n    'siapa kangpcode': 'KangPCode ad<PERSON><PERSON>, developer dan kreator teknologi Nusantara yang berfokus pada pengembangan aplikasi web modern dan edukasi teknologi.',\n    'pendidikan': 'Informatika – Universitas XYZ, dengan fokus pada pengembangan web dan teknologi modern.',\n    'skill': 'Next.js, TailwindCSS, SQLite, Laravel, Bun, React, PWA, Three.js, TypeScript, Python, Docker, dan teknologi web modern lainnya.',\n    'pengalaman': 'Magang di CV Bintang Gumilang, freelance proyek TI lokal, dan berbagai proyek pengembangan web untuk klien Indonesia.',\n    'hobi': 'Ngoding, mempelajari sejarah tokoh teknologi, menonton anime, menulis, dan berbagi pengetahuan teknologi.',\n    'buku': 'Langkah Kode Nusantara - Perjalanan belajar dan berkarya dalam dunia teknologi Indonesia (GitHub: kangpcode/langkah-kode-nusantara)',\n    'proyek': 'Portfolio 3D Interaktif, Langkah Kode Nusantara, berbagai aplikasi web dengan Next.js dan React, serta proyek edukasi teknologi.',\n    'kontak': 'GitHub: github.com/kangpcode | Email: <EMAIL> | Status: Open for Collaboration',\n    'teknologi': 'Spesialisasi dalam JavaScript/TypeScript ecosystem, React/Next.js, modern CSS frameworks, database design, dan 3D web development.',\n    'visi': 'Membangun ekosistem teknologi Indonesia yang kuat melalui edukasi, open source, dan kolaborasi komunitas developer lokal.',\n  };\n\n  const commands = {\n    'help': 'Perintah yang tersedia:\\n- siapa kangpcode\\n- pendidikan\\n- skill\\n- pengalaman\\n- hobi\\n- buku\\n- proyek\\n- kontak\\n- teknologi\\n- visi\\n- clear\\n- help',\n    'clear': 'CLEAR_TERMINAL',\n    'ls': 'projects/\\nbooks/\\nskills/\\ncontacts/\\nexperience/',\n    'pwd': '/home/<USER>/portfolio',\n    'whoami': 'kangpcode (Dhafa Nazula Permadi)',\n    'date': new Date().toLocaleString('id-ID'),\n    'uname': 'KangPCode Terminal AI v1.0 - Interactive Portfolio Assistant',\n  };\n\n  useEffect(() => {\n    if (isVisible) {\n      setHistory([\n        {\n          type: 'output',\n          content: '🧠 KangPCode Terminal AI v1.0\\nSelamat datang! Ketik \"help\" untuk melihat perintah yang tersedia.\\nAtau tanyakan tentang KangPCode dengan bahasa natural.',\n          timestamp: new Date()\n        }\n      ]);\n      setTimeout(() => {\n        inputRef.current?.focus();\n      }, 100);\n    }\n  }, [isVisible]);\n\n  useEffect(() => {\n    if (terminalRef.current) {\n      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;\n    }\n  }, [history]);\n\n  const processCommand = async (cmd: string) => {\n    const command = cmd.toLowerCase().trim();\n    \n    // Add user input to history\n    setHistory(prev => [...prev, {\n      type: 'input',\n      content: `$ ${cmd}`,\n      timestamp: new Date()\n    }]);\n\n    setIsTyping(true);\n\n    // Simulate AI thinking delay\n    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));\n\n    let response = '';\n\n    // Check exact commands first\n    if (commands[command as keyof typeof commands]) {\n      const result = commands[command as keyof typeof commands];\n      if (result === 'CLEAR_TERMINAL') {\n        setHistory([]);\n        setIsTyping(false);\n        return;\n      }\n      response = result;\n    }\n    // Check knowledge base\n    else if (knowledgeBase[command as keyof typeof knowledgeBase]) {\n      response = `🧠 ${knowledgeBase[command as keyof typeof knowledgeBase]}`;\n    }\n    // Natural language processing (simple keyword matching)\n    else {\n      const keywords = Object.keys(knowledgeBase);\n      const matchedKeyword = keywords.find(keyword => \n        command.includes(keyword) || keyword.includes(command.split(' ')[0])\n      );\n      \n      if (matchedKeyword) {\n        response = `🧠 ${knowledgeBase[matchedKeyword as keyof typeof knowledgeBase]}`;\n      } else if (command.includes('halo') || command.includes('hai') || command.includes('hello')) {\n        response = '👋 Halo! Saya AI Assistant KangPCode. Ada yang bisa saya bantu? Ketik \"help\" untuk melihat perintah yang tersedia.';\n      } else if (command.includes('terima kasih') || command.includes('thanks')) {\n        response = '🙏 Sama-sama! Senang bisa membantu. Ada pertanyaan lain tentang KangPCode?';\n      } else if (command.includes('bye') || command.includes('exit') || command.includes('quit')) {\n        response = '👋 Sampai jumpa! Terima kasih telah menggunakan KangPCode Terminal AI.';\n      } else {\n        response = `❓ Maaf, saya tidak mengerti perintah \"${cmd}\". Ketik \"help\" untuk melihat perintah yang tersedia atau tanyakan tentang KangPCode.`;\n      }\n    }\n\n    // Add AI response to history\n    setHistory(prev => [...prev, {\n      type: 'output',\n      content: response,\n      timestamp: new Date()\n    }]);\n\n    setIsTyping(false);\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (input.trim()) {\n      processCommand(input);\n      setInput('');\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className=\"fixed inset-4 bg-black rounded-lg shadow-2xl border border-green-500/30 overflow-hidden z-50\"\n    >\n      {/* Terminal Header */}\n      <div className=\"h-8 bg-gray-900 flex items-center justify-between px-4 border-b border-green-500/30\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n          <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n          <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n          <span className=\"text-green-400 text-sm font-mono ml-4\">🧠 KangPCode Terminal AI</span>\n        </div>\n        <button\n          onClick={onClose}\n          className=\"text-gray-400 hover:text-white transition-colors\"\n        >\n          ✕\n        </button>\n      </div>\n\n      {/* Terminal Content */}\n      <div \n        ref={terminalRef}\n        className=\"flex-1 p-4 font-mono text-sm text-green-400 bg-black overflow-y-auto h-[calc(100%-8rem)]\"\n      >\n        {history.map((line, index) => (\n          <motion.div\n            key={index}\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            className={`mb-2 ${\n              line.type === 'input' \n                ? 'text-cyan-400' \n                : line.type === 'error' \n                ? 'text-red-400' \n                : 'text-green-400'\n            }`}\n          >\n            <pre className=\"whitespace-pre-wrap font-mono\">{line.content}</pre>\n          </motion.div>\n        ))}\n        \n        {isTyping && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"text-yellow-400 flex items-center space-x-2\"\n          >\n            <span>🧠 AI sedang berpikir</span>\n            <div className=\"flex space-x-1\">\n              {[0, 1, 2].map((i) => (\n                <motion.div\n                  key={i}\n                  className=\"w-1 h-1 bg-yellow-400 rounded-full\"\n                  animate={{\n                    scale: [1, 1.5, 1],\n                    opacity: [0.5, 1, 0.5],\n                  }}\n                  transition={{\n                    duration: 1,\n                    repeat: Infinity,\n                    delay: i * 0.2,\n                  }}\n                />\n              ))}\n            </div>\n          </motion.div>\n        )}\n      </div>\n\n      {/* Terminal Input */}\n      <div className=\"h-12 bg-gray-900 border-t border-green-500/30 flex items-center px-4\">\n        <form onSubmit={handleSubmit} className=\"flex-1 flex items-center space-x-2\">\n          <span className=\"text-cyan-400 font-mono\">$</span>\n          <input\n            ref={inputRef}\n            type=\"text\"\n            value={input}\n            onChange={(e) => setInput(e.target.value)}\n            className=\"flex-1 bg-transparent text-green-400 font-mono outline-none placeholder-gray-500\"\n            placeholder=\"Ketik perintah atau tanyakan tentang KangPCode...\"\n            disabled={isTyping}\n          />\n        </form>\n      </div>\n\n      {/* Terminal Footer */}\n      <div className=\"h-6 bg-gray-800 border-t border-green-500/30 flex items-center justify-between px-4 text-xs text-gray-400\">\n        <span>KangPCode Terminal AI - Interactive Portfolio Assistant</span>\n        <span>Press Ctrl+C to exit</span>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAgBe,SAAS,WAAW,EAAE,SAAS,EAAE,OAAO,EAAmB;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,oBAAoB;IACpB,MAAM,gBAAgB;QACpB,mBAAmB;QACnB,cAAc;QACd,SAAS;QACT,cAAc;QACd,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,UAAU;QACV,aAAa;QACb,QAAQ;IACV;IAEA,MAAM,WAAW;QACf,QAAQ;QACR,SAAS;QACT,MAAM;QACN,OAAO;QACP,UAAU;QACV,QAAQ,IAAI,OAAO,cAAc,CAAC;QAClC,SAAS;IACX;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,WAAW;gBACT;oBACE,MAAM;oBACN,SAAS;oBACT,WAAW,IAAI;gBACjB;aACD;YACD,WAAW;gBACT,SAAS,OAAO,EAAE;YACpB,GAAG;QACL;IACF,GAAG;QAAC;KAAU;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,OAAO,EAAE;YACvB,YAAY,OAAO,CAAC,SAAS,GAAG,YAAY,OAAO,CAAC,YAAY;QAClE;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,iBAAiB,OAAO;QAC5B,MAAM,UAAU,IAAI,WAAW,GAAG,IAAI;QAEtC,4BAA4B;QAC5B,WAAW,CAAA,OAAQ;mBAAI;gBAAM;oBAC3B,MAAM;oBACN,SAAS,CAAC,EAAE,EAAE,KAAK;oBACnB,WAAW,IAAI;gBACjB;aAAE;QAEF,YAAY;QAEZ,6BAA6B;QAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,MAAM,KAAK,MAAM,KAAK;QAEvE,IAAI,WAAW;QAEf,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,QAAiC,EAAE;YAC9C,MAAM,SAAS,QAAQ,CAAC,QAAiC;YACzD,IAAI,WAAW,kBAAkB;gBAC/B,WAAW,EAAE;gBACb,YAAY;gBACZ;YACF;YACA,WAAW;QACb,OAEK,IAAI,aAAa,CAAC,QAAsC,EAAE;YAC7D,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,QAAsC,EAAE;QACzE,OAEK;YACH,MAAM,WAAW,OAAO,IAAI,CAAC;YAC7B,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAA,UACnC,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ,KAAK,CAAC,IAAI,CAAC,EAAE;YAGrE,IAAI,gBAAgB;gBAClB,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,eAA6C,EAAE;YAChF,OAAO,IAAI,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,UAAU;gBAC3F,WAAW;YACb,OAAO,IAAI,QAAQ,QAAQ,CAAC,mBAAmB,QAAQ,QAAQ,CAAC,WAAW;gBACzE,WAAW;YACb,OAAO,IAAI,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,SAAS;gBAC1F,WAAW;YACb,OAAO;gBACL,WAAW,CAAC,sCAAsC,EAAE,IAAI,qFAAqF,CAAC;YAChJ;QACF;QAEA,6BAA6B;QAC7B,WAAW,CAAA,OAAQ;mBAAI;gBAAM;oBAC3B,MAAM;oBACN,SAAS;oBACT,WAAW,IAAI;gBACjB;aAAE;QAEF,YAAY;IACd;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,MAAM,IAAI,IAAI;YAChB,eAAe;YACf,SAAS;QACX;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAC/B,WAAU;;0BAGV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAK,WAAU;0CAAwC;;;;;;;;;;;;kCAE1D,8OAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAMH,8OAAC;gBACC,KAAK;gBACL,WAAU;;oBAET,QAAQ,GAAG,CAAC,CAAC,MAAM,sBAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAW,CAAC,KAAK,EACf,KAAK,IAAI,KAAK,UACV,kBACA,KAAK,IAAI,KAAK,UACd,iBACA,kBACJ;sCAEF,cAAA,8OAAC;gCAAI,WAAU;0CAAiC,KAAK,OAAO;;;;;;2BAXvD;;;;;oBAeR,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,WAAU;;0CAEV,8OAAC;0CAAK;;;;;;0CACN,8OAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,SAAS;4CACP,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;4CAClB,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;wCACxB;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,OAAO,IAAI;wCACb;uCAVK;;;;;;;;;;;;;;;;;;;;;;0BAmBjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BAAK,WAAU;sCAA0B;;;;;;sCAC1C,8OAAC;4BACC,KAAK;4BACL,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,WAAU;4BACV,aAAY;4BACZ,UAAU;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAK;;;;;;kCACN,8OAAC;kCAAK;;;;;;;;;;;;;;;;;;AAId", "debugId": null}}, {"offset": {"line": 1360, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/RoomScene3D.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense, useRef, useState } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport {\n  OrbitControls,\n  Environment,\n  Text,\n  Box,\n  Plane,\n  Html,\n  useTexture,\n  PerspectiveCamera\n} from '@react-three/drei';\nimport { motion } from 'framer-motion';\nimport * as THREE from 'three';\nimport ComputerGUI from './ComputerGUI';\nimport TerminalA<PERSON> from './TerminalAI';\nimport LaptopWindows from './LaptopWindows';\n\n// Room Environment Component\nfunction Room() {\n  return (\n    <group>\n      {/* Floor */}\n      <Plane \n        args={[20, 20]} \n        rotation={[-Math.PI / 2, 0, 0]} \n        position={[0, -2, 0]}\n      >\n        <meshStandardMaterial color=\"#8B4513\" />\n      </Plane>\n\n      {/* Walls */}\n      <Plane args={[20, 10]} position={[0, 3, -10]}>\n        <meshStandardMaterial color=\"#F5F5DC\" />\n      </Plane>\n      \n      <Plane args={[20, 10]} position={[-10, 3, 0]} rotation={[0, Math.PI / 2, 0]}>\n        <meshStandardMaterial color=\"#F5F5DC\" />\n      </Plane>\n      \n      <Plane args={[20, 10]} position={[10, 3, 0]} rotation={[0, -Math.PI / 2, 0]}>\n        <meshStandardMaterial color=\"#F5F5DC\" />\n      </Plane>\n\n      {/* Ceiling */}\n      <Plane \n        args={[20, 20]} \n        rotation={[Math.PI / 2, 0, 0]} \n        position={[0, 8, 0]}\n      >\n        <meshStandardMaterial color=\"#FFFFFF\" />\n      </Plane>\n    </group>\n  );\n}\n\n// Computer 3D Component\nfunction Computer3D({ onComputerClick }: { onComputerClick: () => void }) {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const [isHovered, setIsHovered] = useState(false);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;\n    }\n  });\n\n  return (\n    <group position={[-4, -1, -2]}>\n      {/* Monitor */}\n      <Box ref={meshRef} args={[3, 2, 0.2]} position={[0, 1, 0]}>\n        <meshStandardMaterial color=\"#1a1a1a\" />\n      </Box>\n\n      {/* Screen */}\n      <Plane args={[2.8, 1.8]} position={[0, 1, 0.11]}>\n        <meshStandardMaterial\n          color={isHovered ? \"#001080\" : \"#000080\"}\n          emissive={isHovered ? \"#000060\" : \"#000040\"}\n        />\n      </Plane>\n\n      {/* KDE Plasma Logo on Screen */}\n      <Html position={[0, 1, 0.12]} center>\n        <div className=\"text-white text-center pointer-events-none\">\n          <div className=\"text-4xl mb-2\">🐧</div>\n          <div className=\"text-sm font-bold\">KDE Plasma</div>\n        </div>\n      </Html>\n\n      {/* Base */}\n      <Box args={[0.5, 0.8, 0.5]} position={[0, 0, 0]}>\n        <meshStandardMaterial color=\"#333333\" />\n      </Box>\n\n      {/* Keyboard */}\n      <Box args={[2, 0.1, 1]} position={[0, -1.5, 1]}>\n        <meshStandardMaterial color=\"#2a2a2a\" />\n      </Box>\n\n      {/* Click Area */}\n      <Html position={[0, 1, 0.2]} center>\n        <div\n          className=\"w-32 h-20 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onComputerClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Klik untuk membuka KDE Plasma\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              🖱️ Klik untuk membuka\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Laptop 3D Component\nfunction Laptop3D({ onLaptopClick }: { onLaptopClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[4, -1, -1]}>\n      {/* Laptop Base */}\n      <Box args={[2.5, 0.1, 1.8]} position={[0, -1.5, 0]}>\n        <meshStandardMaterial color=\"#C0C0C0\" />\n      </Box>\n\n      {/* Laptop Screen */}\n      <Box args={[2.5, 1.6, 0.1]} position={[0, 0, -0.8]} rotation={[-0.2, 0, 0]}>\n        <meshStandardMaterial color=\"#1a1a1a\" />\n      </Box>\n\n      {/* Screen Display */}\n      <Plane args={[2.3, 1.4]} position={[0, 0, -0.75]} rotation={[-0.2, 0, 0]}>\n        <meshStandardMaterial\n          color={isHovered ? \"#0088D4\" : \"#0078D4\"}\n          emissive={isHovered ? \"#004d8b\" : \"#003d6b\"}\n        />\n      </Plane>\n\n      {/* Windows Logo on Screen */}\n      <Html position={[0, 0, -0.74]} center>\n        <div className=\"text-white text-center pointer-events-none\" style={{ transform: 'rotateX(-11.5deg)' }}>\n          <div className=\"text-3xl mb-1\">🪟</div>\n          <div className=\"text-xs font-bold\">Windows 11</div>\n        </div>\n      </Html>\n\n      {/* Click Area */}\n      <Html position={[0, 0, -0.7]} center>\n        <div\n          className=\"w-24 h-16 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onLaptopClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Klik untuk membuka GitHub Viewer\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              🖱️ GitHub Viewer\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Book 3D Component (Placeholder)\nfunction Book3D() {\n  return (\n    <group position={[0, -1.3, 0]}>\n      <Box args={[1.5, 0.2, 1]} position={[0, 0, 0]}>\n        <meshStandardMaterial color=\"#8B4513\" />\n      </Box>\n      \n      <Html position={[0, 0.2, 0]} center>\n        <div \n          className=\"w-16 h-12 bg-transparent cursor-pointer hover:bg-yellow-500/20 rounded transition-colors\"\n          onClick={() => console.log('Book clicked!')}\n          title=\"Langkah Kode Nusantara\"\n        />\n      </Html>\n    </group>\n  );\n}\n\n// ID Card Component (Placeholder)\nfunction IDCard3D() {\n  return (\n    <group position={[0, -0.9, 0.3]}>\n      <Box args={[0.8, 0.05, 0.5]} position={[0, 0, 0]}>\n        <meshStandardMaterial color=\"#FFFFFF\" />\n      </Box>\n      \n      <Html position={[0, 0.1, 0]} center>\n        <div \n          className=\"w-8 h-6 bg-transparent cursor-pointer hover:bg-green-500/20 rounded transition-colors\"\n          onClick={() => console.log('ID Card clicked!')}\n          title=\"ID Card KangPCode\"\n        />\n      </Html>\n    </group>\n  );\n}\n\n// Lighting Setup\nfunction Lighting() {\n  return (\n    <>\n      <ambientLight intensity={0.4} />\n      <pointLight position={[0, 6, 0]} intensity={0.8} />\n      <pointLight position={[-5, 4, -5]} intensity={0.6} color=\"#FFE4B5\" />\n      <pointLight position={[5, 4, -5]} intensity={0.6} color=\"#E6E6FA\" />\n      <directionalLight \n        position={[10, 10, 5]} \n        intensity={0.5}\n        castShadow\n        shadow-mapSize-width={2048}\n        shadow-mapSize-height={2048}\n      />\n    </>\n  );\n}\n\n// Main RoomScene3D Component\ninterface RoomScene3DProps {\n  onExitRoom?: () => void;\n}\n\nexport default function RoomScene3D({ onExitRoom }: RoomScene3DProps) {\n  const [isLoading, setIsLoading] = useState(true);\n  const [showComputerGUI, setShowComputerGUI] = useState(false);\n  const [showTerminalAI, setShowTerminalAI] = useState(false);\n  const [showLaptopWindows, setShowLaptopWindows] = useState(false);\n\n  const handleComputerClick = () => {\n    setShowComputerGUI(true);\n  };\n\n  const handleLaptopClick = () => {\n    setShowLaptopWindows(true);\n  };\n\n  const handleCloseComputer = () => {\n    setShowComputerGUI(false);\n    setShowTerminalAI(false);\n  };\n\n  const handleCloseLaptop = () => {\n    setShowLaptopWindows(false);\n  };\n\n  return (\n    <div className=\"w-full h-screen relative\">\n      <Canvas\n        shadows\n        camera={{ position: [0, 2, 8], fov: 60 }}\n        onCreated={() => setIsLoading(false)}\n      >\n        <Suspense fallback={null}>\n          <Lighting />\n          <Room />\n          <Computer3D onComputerClick={handleComputerClick} />\n          <Laptop3D onLaptopClick={handleLaptopClick} />\n          <Book3D />\n          <IDCard3D />\n          \n          <OrbitControls\n            enablePan={true}\n            enableZoom={true}\n            enableRotate={true}\n            minDistance={3}\n            maxDistance={15}\n            minPolarAngle={0}\n            maxPolarAngle={Math.PI / 2}\n          />\n          \n          <Environment preset=\"apartment\" />\n        </Suspense>\n      </Canvas>\n\n      {/* UI Overlay */}\n      <div className=\"absolute top-4 left-4 z-10\">\n        <motion.div\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          className=\"bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white\"\n        >\n          <h2 className=\"text-xl font-bold mb-2\">Kamar KangPCode</h2>\n          <p className=\"text-sm text-gray-300\">\n            Klik objek untuk berinteraksi\n          </p>\n        </motion.div>\n      </div>\n\n      {/* Exit Button */}\n      <div className=\"absolute top-4 right-4 z-10\">\n        <motion.button\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          onClick={onExitRoom}\n          className=\"bg-red-500/80 hover:bg-red-600/80 backdrop-blur-sm rounded-lg px-4 py-2 text-white font-medium transition-colors\"\n        >\n          Keluar Kamar\n        </motion.button>\n      </div>\n\n      {/* Loading Overlay */}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center z-20\">\n          <div className=\"text-white text-xl\">Memuat Scene 3D...</div>\n        </div>\n      )}\n\n      {/* Computer GUI Overlay */}\n      <ComputerGUI\n        isVisible={showComputerGUI}\n        onClose={handleCloseComputer}\n      />\n\n      {/* Terminal AI Overlay */}\n      <TerminalAI\n        isVisible={showTerminalAI}\n        onClose={() => setShowTerminalAI(false)}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAUA;AAEA;AACA;AAjBA;;;;;;;;AAoBA,6BAA6B;AAC7B,SAAS;IACP,qBACE,8OAAC;;0BAEC,8OAAC,0JAAA,CAAA,QAAK;gBACJ,MAAM;oBAAC;oBAAI;iBAAG;gBACd,UAAU;oBAAC,CAAC,KAAK,EAAE,GAAG;oBAAG;oBAAG;iBAAE;gBAC9B,UAAU;oBAAC;oBAAG,CAAC;oBAAG;iBAAE;0BAEpB,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAG;0BAC1C,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAG9B,8OAAC,0JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAI;iBAAG;gBAAE,UAAU;oBAAC,CAAC;oBAAI;oBAAG;iBAAE;gBAAE,UAAU;oBAAC;oBAAG,KAAK,EAAE,GAAG;oBAAG;iBAAE;0BACzE,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAG9B,8OAAC,0JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAI;oBAAG;iBAAE;gBAAE,UAAU;oBAAC;oBAAG,CAAC,KAAK,EAAE,GAAG;oBAAG;iBAAE;0BACzE,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,QAAK;gBACJ,MAAM;oBAAC;oBAAI;iBAAG;gBACd,UAAU;oBAAC,KAAK,EAAE,GAAG;oBAAG;oBAAG;iBAAE;gBAC7B,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAEnB,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;;;;;;;AAIpC;AAEA,wBAAwB;AACxB,SAAS,WAAW,EAAE,eAAe,EAAmC;IACtE,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,+MAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,IAAI,QAAQ,OAAO,EAAE;YACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;QACzE;IACF;IAEA,qBACE,8OAAC;QAAM,UAAU;YAAC,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;;0BAE3B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,KAAK;gBAAS,MAAM;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BACvD,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;0BAC7C,cAAA,8OAAC;oBACC,OAAO,YAAY,YAAY;oBAC/B,UAAU,YAAY,YAAY;;;;;;;;;;;0BAKtC,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;gBAAE,MAAM;0BAClC,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAI,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAKvC,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC7C,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAE;0BAC5C,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,MAAM;0BACjC,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,8OAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;AAEA,sBAAsB;AACtB,SAAS,SAAS,EAAE,aAAa,EAAiC;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAM,UAAU;YAAC;YAAG,CAAC;YAAG,CAAC;SAAE;;0BAE1B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAE;0BAChD,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAI;gBAAE,UAAU;oBAAC,CAAC;oBAAK;oBAAG;iBAAE;0BACxE,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,8OAAC,0JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAK;gBAAE,UAAU;oBAAC,CAAC;oBAAK;oBAAG;iBAAE;0BACtE,cAAA,8OAAC;oBACC,OAAO,YAAY,YAAY;oBAC/B,UAAU,YAAY,YAAY;;;;;;;;;;;0BAKtC,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAK;gBAAE,MAAM;0BACnC,cAAA,8OAAC;oBAAI,WAAU;oBAA6C,OAAO;wBAAE,WAAW;oBAAoB;;sCAClG,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,8OAAC;4BAAI,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAKvC,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAI;gBAAE,MAAM;0BAClC,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,8OAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;AAEA,kCAAkC;AAClC,SAAS;IACP,qBACE,8OAAC;QAAM,UAAU;YAAC;YAAG,CAAC;YAAK;SAAE;;0BAC3B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAE;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC3C,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAG9B,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAK;iBAAE;gBAAE,MAAM;0BACjC,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS,IAAM,QAAQ,GAAG,CAAC;oBAC3B,OAAM;;;;;;;;;;;;;;;;;AAKhB;AAEA,kCAAkC;AAClC,SAAS;IACP,qBACE,8OAAC;QAAM,UAAU;YAAC;YAAG,CAAC;YAAK;SAAI;;0BAC7B,8OAAC,0JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC9C,cAAA,8OAAC;oBAAqB,OAAM;;;;;;;;;;;0BAG9B,8OAAC,uJAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAK;iBAAE;gBAAE,MAAM;0BACjC,cAAA,8OAAC;oBACC,WAAU;oBACV,SAAS,IAAM,QAAQ,GAAG,CAAC;oBAC3B,OAAM;;;;;;;;;;;;;;;;;AAKhB;AAEA,iBAAiB;AACjB,SAAS;IACP,qBACE;;0BACE,8OAAC;gBAAa,WAAW;;;;;;0BACzB,8OAAC;gBAAW,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,WAAW;;;;;;0BAC5C,8OAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAG;oBAAG,CAAC;iBAAE;gBAAE,WAAW;gBAAK,OAAM;;;;;;0BACzD,8OAAC;gBAAW,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAE;gBAAE,WAAW;gBAAK,OAAM;;;;;;0BACxD,8OAAC;gBACC,UAAU;oBAAC;oBAAI;oBAAI;iBAAE;gBACrB,WAAW;gBACX,UAAU;gBACV,wBAAsB;gBACtB,yBAAuB;;;;;;;;AAI/B;AAOe,SAAS,YAAY,EAAE,UAAU,EAAoB;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,sBAAsB;QAC1B,mBAAmB;IACrB;IAEA,MAAM,oBAAoB;QACxB,qBAAqB;IACvB;IAEA,MAAM,sBAAsB;QAC1B,mBAAmB;QACnB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB;QACxB,qBAAqB;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mMAAA,CAAA,SAAM;gBACL,OAAO;gBACP,QAAQ;oBAAE,UAAU;wBAAC;wBAAG;wBAAG;qBAAE;oBAAE,KAAK;gBAAG;gBACvC,WAAW,IAAM,aAAa;0BAE9B,cAAA,8OAAC,qMAAA,CAAA,WAAQ;oBAAC,UAAU;;sCAClB,8OAAC;;;;;sCACD,8OAAC;;;;;sCACD,8OAAC;4BAAW,iBAAiB;;;;;;sCAC7B,8OAAC;4BAAS,eAAe;;;;;;sCACzB,8OAAC;;;;;sCACD,8OAAC;;;;;sCAED,8OAAC,iKAAA,CAAA,gBAAa;4BACZ,WAAW;4BACX,YAAY;4BACZ,cAAc;4BACd,aAAa;4BACb,aAAa;4BACb,eAAe;4BACf,eAAe,KAAK,EAAE,GAAG;;;;;;sCAG3B,8OAAC,+JAAA,CAAA,cAAW;4BAAC,QAAO;;;;;;;;;;;;;;;;;0BAKxB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAyB;;;;;;sCACvC,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAOzC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;YAMF,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAAqB;;;;;;;;;;;0BAKxC,8OAAC,0HAAA,CAAA,UAAW;gBACV,WAAW;gBACX,SAAS;;;;;;0BAIX,8OAAC,yHAAA,CAAA,UAAU;gBACT,WAAW;gBACX,SAAS,IAAM,kBAAkB;;;;;;;;;;;;AAIzC", "debugId": null}}, {"offset": {"line": 2269, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport LoadingScreen from '@/components/LoadingScreen';\nimport RoomScene3D from '@/components/RoomScene3D';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ntype SceneType = 'loading' | 'intro' | 'room';\n\nexport default function Home() {\n  const [currentScene, setCurrentScene] = useState<SceneType>('loading');\n\n  // Disable right-click context menu for security\n  useEffect(() => {\n    const disableRightClick = (e: MouseEvent) => e.preventDefault();\n    document.addEventListener('contextmenu', disableRightClick);\n    return () => document.removeEventListener('contextmenu', disableRightClick);\n  }, []);\n\n  const handleLoadingComplete = () => {\n    setCurrentScene('intro');\n  };\n\n  const handleEnterRoom = () => {\n    setCurrentScene('room');\n  };\n\n  const handleExitRoom = () => {\n    setCurrentScene('intro');\n  };\n\n  return (\n    <div className=\"w-full h-screen overflow-hidden\">\n      <AnimatePresence mode=\"wait\">\n        {currentScene === 'loading' && (\n          <LoadingScreen key=\"loading\" onComplete={handleLoadingComplete} />\n        )}\n\n        {currentScene === 'intro' && (\n          <motion.div\n            key=\"intro\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"w-full h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center relative overflow-hidden\"\n          >\n            {/* Background Animation */}\n            <div className=\"absolute inset-0\">\n              {[...Array(50)].map((_, i) => (\n                <motion.div\n                  key={i}\n                  className=\"absolute w-1 h-1 bg-white rounded-full\"\n                  style={{\n                    left: `${Math.random() * 100}%`,\n                    top: `${Math.random() * 100}%`,\n                  }}\n                  animate={{\n                    opacity: [0, 1, 0],\n                    scale: [0, 1, 0],\n                  }}\n                  transition={{\n                    duration: 3,\n                    repeat: Infinity,\n                    delay: Math.random() * 3,\n                  }}\n                />\n              ))}\n            </div>\n\n            {/* Main Content */}\n            <div className=\"text-center z-10 space-y-8\">\n              <motion.h1\n                initial={{ y: -50, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ delay: 0.2 }}\n                className=\"text-6xl md:text-8xl font-bold text-white mb-4\"\n              >\n                KangPCode\n              </motion.h1>\n\n              <motion.p\n                initial={{ y: 50, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ delay: 0.4 }}\n                className=\"text-2xl text-purple-200 mb-8\"\n              >\n                Portfolio 3D Interaktif\n              </motion.p>\n\n              <motion.p\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.6 }}\n                className=\"text-lg text-purple-300 mb-12 max-w-2xl mx-auto\"\n              >\n                Selamat datang di dunia virtual KangPCode! Jelajahi kamar virtual yang penuh dengan teknologi,\n                proyek, dan inspirasi dari seorang developer Indonesia.\n              </motion.p>\n\n              {/* Door to Room */}\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ delay: 0.8, type: \"spring\", stiffness: 200 }}\n                className=\"relative\"\n              >\n                <motion.button\n                  onClick={handleEnterRoom}\n                  className=\"group relative bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-bold py-4 px-8 rounded-lg text-xl shadow-2xl transition-all duration-300 transform hover:scale-105\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <span className=\"relative z-10\">🚪 Masuk ke Kamar KangPCode</span>\n\n                  {/* Glow Effect */}\n                  <motion.div\n                    className=\"absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-lg blur-lg opacity-30\"\n                    animate={{\n                      scale: [1, 1.1, 1],\n                      opacity: [0.3, 0.5, 0.3],\n                    }}\n                    transition={{\n                      duration: 2,\n                      repeat: Infinity,\n                    }}\n                  />\n                </motion.button>\n              </motion.div>\n\n              <motion.p\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 1 }}\n                className=\"text-sm text-purple-400 mt-4\"\n              >\n                Klik pintu untuk memulai petualangan 3D Anda\n              </motion.p>\n            </div>\n          </motion.div>\n        )}\n\n        {currentScene === 'room' && (\n          <motion.div\n            key=\"room\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n          >\n            <RoomScene3D onExitRoom={handleExitRoom} />\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa;IAE5D,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB,CAAC,IAAkB,EAAE,cAAc;QAC7D,SAAS,gBAAgB,CAAC,eAAe;QACzC,OAAO,IAAM,SAAS,mBAAmB,CAAC,eAAe;IAC3D,GAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,gBAAgB;IAClB;IAEA,MAAM,kBAAkB;QACtB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB;QACrB,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,yLAAA,CAAA,kBAAe;YAAC,MAAK;;gBACnB,iBAAiB,2BAChB,8OAAC,4HAAA,CAAA,UAAa;oBAAe,YAAY;mBAAtB;;;;;gBAGpB,iBAAiB,yBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,OAAO;wCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAChC;oCACA,SAAS;wCACP,SAAS;4CAAC;4CAAG;4CAAG;yCAAE;wCAClB,OAAO;4CAAC;4CAAG;4CAAG;yCAAE;oCAClB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO,KAAK,MAAM,KAAK;oCACzB;mCAdK;;;;;;;;;;sCAoBX,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,GAAG,CAAC;wCAAI,SAAS;oCAAE;oCAC9B,SAAS;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;8CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,SAAS;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;8CAID,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;8CAMD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,YAAY;wCAAE,OAAO;wCAAK,MAAM;wCAAU,WAAW;oCAAI;oCACzD,WAAU;8CAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAGhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDACP,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;oDAClB,SAAS;wDAAC;wDAAK;wDAAK;qDAAI;gDAC1B;gDACA,YAAY;oDACV,UAAU;oDACV,QAAQ;gDACV;;;;;;;;;;;;;;;;;8CAKN,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,OAAO;oCAAE;oCACvB,WAAU;8CACX;;;;;;;;;;;;;mBA9FC;;;;;gBAqGP,iBAAiB,wBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;8BAEnB,cAAA,8OAAC,0HAAA,CAAA,UAAW;wBAAC,YAAY;;;;;;mBALrB;;;;;;;;;;;;;;;;AAWhB", "debugId": null}}]}
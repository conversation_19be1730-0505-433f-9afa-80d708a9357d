{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_e531dabc-module__QGiZLq__className\",\n  \"variable\": \"geist_e531dabc-module__QGiZLq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 16, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_e531dabc.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_mono_68a01160-module__YLcDdW__className\",\n  \"variable\": \"geist_mono_68a01160-module__YLcDdW__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 47, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_mono_68a01160.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,0JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,0JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from \"next/font/google\";\nimport \"./globals.css\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"KangPCode Portfolio 3D - Interactive Developer Portfolio\",\n  description: \"Explore the interactive 3D portfolio of <PERSON><PERSON><PERSON> (KangPCode), a fullstack developer and content creator from Indonesia. Experience a game-like journey through technology, projects, and inspiration.\",\n  keywords: \"portfolio, 3D, interactive, developer, fullstack, Indonesia, KangPCode, <PERSON><PERSON><PERSON>, web development, technology\",\n  authors: [{ name: \"<PERSON><PERSON><PERSON>\", url: \"https://kangpcode.dev\" }],\n  creator: \"KangPCode\",\n  publisher: \"KangPCode\",\n  formatDetection: {\n    email: false,\n    address: false,\n    telephone: false,\n  },\n  metadataBase: new URL('https://kangpcode.dev'),\n  alternates: {\n    canonical: '/',\n    languages: {\n      'id-ID': '/id',\n      'en-US': '/en',\n    },\n  },\n  openGraph: {\n    title: \"KangPCode Portfolio 3D\",\n    description: \"Interactive 3D Portfolio - Explore technology, projects, and inspiration\",\n    url: \"https://kangpcode.dev\",\n    siteName: \"KangPCode Portfolio\",\n    images: [\n      {\n        url: \"/og-image.png\",\n        width: 1200,\n        height: 630,\n        alt: \"KangPCode 3D Portfolio Preview\",\n      },\n    ],\n    locale: \"id_ID\",\n    type: \"website\",\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: \"KangPCode Portfolio 3D\",\n    description: \"Interactive 3D Portfolio - Explore technology, projects, and inspiration\",\n    images: [\"/twitter-image.png\"],\n    creator: \"@kangpcode\",\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      'max-video-preview': -1,\n      'max-image-preview': 'large',\n      'max-snippet': -1,\n    },\n  },\n  manifest: \"/manifest.json\",\n  icons: {\n    icon: [\n      { url: \"/icons/icon-192x192.png\", sizes: \"192x192\", type: \"image/png\" },\n      { url: \"/icons/icon-512x512.png\", sizes: \"512x512\", type: \"image/png\" },\n    ],\n    apple: [\n      { url: \"/icons/icon-152x152.png\", sizes: \"152x152\", type: \"image/png\" },\n    ],\n  },\n  appleWebApp: {\n    capable: true,\n    statusBarStyle: \"default\",\n    title: \"KangPCode Portfolio\",\n  },\n  verification: {\n    google: \"your-google-verification-code\",\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"id\" className=\"scroll-smooth\">\n      <head>\n        <meta name=\"theme-color\" content=\"#3b82f6\" />\n        <meta name=\"apple-mobile-web-app-capable\" content=\"yes\" />\n        <meta name=\"apple-mobile-web-app-status-bar-style\" content=\"default\" />\n        <meta name=\"apple-mobile-web-app-title\" content=\"KangPCode Portfolio\" />\n        <meta name=\"mobile-web-app-capable\" content=\"yes\" />\n        <meta name=\"msapplication-TileColor\" content=\"#3b82f6\" />\n        <meta name=\"msapplication-tap-highlight\" content=\"no\" />\n        <link rel=\"apple-touch-icon\" href=\"/icons/icon-152x152.png\" />\n        <link rel=\"mask-icon\" href=\"/icons/safari-pinned-tab.svg\" color=\"#3b82f6\" />\n      </head>\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-white dark:bg-gray-900 transition-colors duration-300`}\n        suppressHydrationWarning={true}\n      >\n        {children}\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAcO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;IACV,SAAS;QAAC;YAAE,MAAM;YAAwB,KAAK;QAAwB;KAAE;IACzE,SAAS;IACT,WAAW;IACX,iBAAiB;QACf,OAAO;QACP,SAAS;QACT,WAAW;IACb;IACA,cAAc,IAAI,IAAI;IACtB,YAAY;QACV,WAAW;QACX,WAAW;YACT,SAAS;YACT,SAAS;QACX;IACF;IACA,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;YACN;gBACE,KAAK;gBACL,OAAO;gBACP,QAAQ;gBACR,KAAK;YACP;SACD;QACD,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;QACb,QAAQ;YAAC;SAAqB;QAC9B,SAAS;IACX;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,UAAU;IACV,OAAO;QACL,MAAM;YACJ;gBAAE,KAAK;gBAA2B,OAAO;gBAAW,MAAM;YAAY;YACtE;gBAAE,KAAK;gBAA2B,OAAO;gBAAW,MAAM;YAAY;SACvE;QACD,OAAO;YACL;gBAAE,KAAK;gBAA2B,OAAO;gBAAW,MAAM;YAAY;SACvE;IACH;IACA,aAAa;QACX,SAAS;QACT,gBAAgB;QAChB,OAAO;IACT;IACA,cAAc;QACZ,QAAQ;IACV;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAU;;0BACxB,8OAAC;;kCACC,8OAAC;wBAAK,MAAK;wBAAc,SAAQ;;;;;;kCACjC,8OAAC;wBAAK,MAAK;wBAA+B,SAAQ;;;;;;kCAClD,8OAAC;wBAAK,MAAK;wBAAwC,SAAQ;;;;;;kCAC3D,8OAAC;wBAAK,MAAK;wBAA6B,SAAQ;;;;;;kCAChD,8OAAC;wBAAK,MAAK;wBAAyB,SAAQ;;;;;;kCAC5C,8OAAC;wBAAK,MAAK;wBAA0B,SAAQ;;;;;;kCAC7C,8OAAC;wBAAK,MAAK;wBAA8B,SAAQ;;;;;;kCACjD,8OAAC;wBAAK,KAAI;wBAAmB,MAAK;;;;;;kCAClC,8OAAC;wBAAK,KAAI;wBAAY,MAAK;wBAA+B,OAAM;;;;;;;;;;;;0BAElE,8OAAC;gBACC,WAAW,GAAG,yIAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,CAAC,EAAE,8IAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,qEAAqE,CAAC;gBAC7H,0BAA0B;0BAEzB;;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 279, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-rsc'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}]}
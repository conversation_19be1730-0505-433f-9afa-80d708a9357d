import { NextRequest, NextResponse } from 'next/server';
import db from '@/database/db';
import { analytics } from '@/database/schema';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { event, target, data } = body;

    if (!event || !target) {
      return NextResponse.json(
        { error: 'Event and target are required' },
        { status: 400 }
      );
    }

    // Get user info
    const userAgent = request.headers.get('user-agent') || '';
    const forwarded = request.headers.get('x-forwarded-for');
    const ip = forwarded ? forwarded.split(',')[0] : 
               request.headers.get('x-real-ip') || 
               'unknown';

    // Insert analytics data
    await db.insert(analytics).values({
      event,
      target,
      data: data ? JSON.stringify(data) : null,
      userAgent,
      ip,
      timestamp: new Date(),
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Analytics error:', error);
    return NextResponse.json(
      { error: 'Failed to record analytics' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '100');
    const event = searchParams.get('event');
    const target = searchParams.get('target');

    let query = db.select().from(analytics);

    // Apply filters if provided
    if (event) {
      query = query.where(analytics.event.eq(event));
    }
    if (target) {
      query = query.where(analytics.target.eq(target));
    }

    const results = await query
      .orderBy(analytics.timestamp.desc())
      .limit(limit);

    // Basic analytics aggregation
    const stats = {
      totalEvents: results.length,
      uniqueTargets: [...new Set(results.map(r => r.target))].length,
      eventTypes: results.reduce((acc, r) => {
        acc[r.event] = (acc[r.event] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      topTargets: Object.entries(
        results.reduce((acc, r) => {
          acc[r.target] = (acc[r.target] || 0) + 1;
          return acc;
        }, {} as Record<string, number>)
      )
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10)
        .map(([target, count]) => ({ target, count })),
    };

    return NextResponse.json({
      events: results,
      stats,
    });
  } catch (error) {
    console.error('Analytics fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}

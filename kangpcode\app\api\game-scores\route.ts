import { NextRequest, NextResponse } from 'next/server';
import db from '@/database/db';
import { gameScore } from '@/database/schema';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const game = searchParams.get('game');
    const limit = parseInt(searchParams.get('limit') || '10');

    let query = db.select().from(gameScore);

    if (game) {
      query = query.where(gameScore.game.eq(game));
    }

    const results = await query
      .orderBy(gameScore.score.desc())
      .limit(limit);

    return NextResponse.json(results);
  } catch (error) {
    console.error('Game scores fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch game scores' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { game, score, playerName, data } = body;

    if (!game || score === undefined) {
      return NextResponse.json(
        { error: 'Game and score are required' },
        { status: 400 }
      );
    }

    await db.insert(gameScore).values({
      game,
      score,
      playerName,
      data: data ? JSON.stringify(data) : null,
      timestamp: new Date(),
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Game score save error:', error);
    return NextResponse.json(
      { error: 'Failed to save game score' },
      { status: 500 }
    );
  }
}

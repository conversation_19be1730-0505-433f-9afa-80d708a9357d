'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface ActionFigureProps {
  isVisible: boolean;
  onClose: () => void;
}

interface Figure {
  id: number;
  name: string;
  anime: string;
  quote: string;
  philosophy: string;
  image: string;
  color: string;
  personality: string;
  lifeLesson: string;
  relevantToTech: string;
  position: { x: number; y: number; z: number };
}

export default function ActionFigure({ isVisible, onClose }: ActionFigureProps) {
  const [selectedFigure, setSelectedFigure] = useState<Figure | null>(null);
  const [currentView, setCurrentView] = useState<'shelf' | 'detail'>('shelf');

  const figures: Figure[] = [
    {
      id: 1,
      name: "Monkey D. <PERSON>",
      anime: "One Piece",
      quote: "Aku akan menjadi Raja Bajak Laut!",
      philosophy: "Tidak pernah menyerah pada impian, selalu optimis menghadapi tantangan",
      image: "🏴‍☠️",
      color: "red",
      personality: "Opti<PERSON>, pantang menyerah, loyal pada teman",
      lifeLesson: "Impian yang besar membutuhkan tekad yang kuat dan kerja keras yang konsisten",
      relevantToTech: "Seperti developer yang bermimpi membuat aplikasi yang mengubah dunia - butuh persistence dan passion",
      position: { x: -3, y: 0, z: 0 }
    },
    {
      id: 2,
      name: "Shanks",
      anime: "One Piece",
      quote: "Percaya itu yang paling penting dalam dunia ini.",
      philosophy: "Kepercayaan adalah fondasi dari semua hubungan yang kuat",
      image: "⚔️",
      color: "orange",
      personality: "Bijaksana, tenang, inspiratif",
      lifeLesson: "Membangun kepercayaan membutuhkan waktu, tapi sekali rusak sulit diperbaiki",
      relevantToTech: "Dalam tim development, trust adalah kunci kolaborasi yang efektif",
      position: { x: -1, y: 0, z: 0 }
    },
    {
      id: 3,
      name: "Uzumaki Naruto",
      anime: "Naruto",
      quote: "Aku tidak akan mundur atau menarik kata-kataku!",
      philosophy: "Komitmen pada janji dan prinsip adalah kekuatan sejati",
      image: "🍥",
      color: "orange",
      personality: "Gigih, setia kawan, tidak pernah menyerah",
      lifeLesson: "Kegagalan adalah bagian dari perjalanan menuju kesuksesan",
      relevantToTech: "Debugging code yang sulit membutuhkan persistence seperti Naruto",
      position: { x: 1, y: 0, z: 0 }
    },
    {
      id: 4,
      name: "Hatake Kakashi",
      anime: "Naruto",
      quote: "Mereka yang meninggalkan teman adalah sampah.",
      philosophy: "Loyalitas dan kerja sama tim lebih penting dari prestasi individual",
      image: "⚡",
      color: "blue",
      personality: "Tenang, strategis, peduli pada tim",
      lifeLesson: "Kesuksesan sejati adalah ketika kita bisa membawa orang lain ikut sukses",
      relevantToTech: "Code review dan mentoring junior developer adalah bentuk loyalitas pada tim",
      position: { x: 3, y: 0, z: 0 }
    },
    {
      id: 5,
      name: "Senku Ishigami",
      anime: "Dr. Stone",
      quote: "This is exhilarating! Science is the power to overcome any obstacle!",
      philosophy: "Sains dan logika adalah kunci untuk memecahkan masalah apapun",
      image: "🧪",
      color: "green",
      personality: "Analitis, inovatif, rasional",
      lifeLesson: "Pengetahuan adalah kekuatan yang paling powerful untuk mengubah dunia",
      relevantToTech: "Approach scientific dalam problem solving adalah essence dari programming",
      position: { x: 0, y: 1, z: -1 }
    },
    {
      id: 6,
      name: "Edward Elric",
      anime: "Fullmetal Alchemist",
      quote: "To obtain something, something of equal value must be lost.",
      philosophy: "Hukum pertukaran setara - tidak ada yang gratis di dunia ini",
      image: "⚗️",
      color: "yellow",
      personality: "Tekun, bertanggung jawab, protective",
      lifeLesson: "Setiap pencapaian membutuhkan pengorbanan dan kerja keras",
      relevantToTech: "Untuk menjadi expert developer, harus sacrifice time dan comfort zone",
      position: { x: -2, y: 1, z: -1 }
    }
  ];

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-4 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 rounded-lg shadow-2xl border overflow-hidden z-50"
    >
      {/* Header */}
      <div className="h-16 bg-black/30 backdrop-blur-sm flex items-center justify-between px-6 text-white border-b border-white/20">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">🧸</span>
          <div>
            <h2 className="font-bold text-lg">Action Figure Collection</h2>
            <p className="text-sm text-purple-200">Filosofi Hidup dari Karakter Anime</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setCurrentView(currentView === 'shelf' ? 'detail' : 'shelf')}
            className="px-3 py-1 bg-white/20 hover:bg-white/30 rounded transition-colors text-sm"
          >
            {currentView === 'shelf' ? '📋 Detail View' : '🏠 Shelf View'}
          </button>
          <button
            onClick={onClose}
            className="hover:bg-white/20 p-2 rounded transition-colors"
          >
            ✕
          </button>
        </div>
      </div>

      <AnimatePresence mode="wait">
        {currentView === 'shelf' ? (
          <motion.div
            key="shelf"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="h-[calc(100%-4rem)] p-8"
          >
            {/* 3D Shelf Simulation */}
            <div className="h-full relative bg-gradient-to-b from-amber-100 to-amber-200 rounded-lg border-4 border-amber-800 shadow-inner">
              {/* Shelf Background */}
              <div className="absolute inset-4 bg-gradient-to-b from-amber-50 to-amber-100 rounded border-2 border-amber-600">
                {/* Shelf Lines */}
                <div className="absolute top-1/3 left-0 right-0 h-1 bg-amber-600"></div>
                <div className="absolute top-2/3 left-0 right-0 h-1 bg-amber-600"></div>
              </div>

              {/* Figures on Shelf */}
              <div className="relative h-full p-8">
                {/* Top Shelf */}
                <div className="absolute top-8 left-8 right-8 h-32 flex items-end justify-around">
                  {figures.slice(0, 3).map((figure, index) => (
                    <motion.div
                      key={figure.id}
                      onClick={() => setSelectedFigure(figure)}
                      className="cursor-pointer group"
                      whileHover={{ scale: 1.1, y: -5 }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.2 }}
                    >
                      <div className={`w-16 h-20 bg-gradient-to-b from-${figure.color}-400 to-${figure.color}-600 rounded-lg shadow-lg flex items-center justify-center text-2xl border-2 border-${figure.color}-700 group-hover:shadow-xl transition-all`}>
                        {figure.image}
                      </div>
                      <div className="text-center mt-2">
                        <div className="text-xs font-bold text-gray-800">{figure.name}</div>
                        <div className="text-xs text-gray-600">{figure.anime}</div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Bottom Shelf */}
                <div className="absolute bottom-8 left-8 right-8 h-32 flex items-end justify-around">
                  {figures.slice(3).map((figure, index) => (
                    <motion.div
                      key={figure.id}
                      onClick={() => setSelectedFigure(figure)}
                      className="cursor-pointer group"
                      whileHover={{ scale: 1.1, y: -5 }}
                      whileTap={{ scale: 0.95 }}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: (index + 3) * 0.2 }}
                    >
                      <div className={`w-16 h-20 bg-gradient-to-b from-${figure.color}-400 to-${figure.color}-600 rounded-lg shadow-lg flex items-center justify-center text-2xl border-2 border-${figure.color}-700 group-hover:shadow-xl transition-all`}>
                        {figure.image}
                      </div>
                      <div className="text-center mt-2">
                        <div className="text-xs font-bold text-gray-800">{figure.name}</div>
                        <div className="text-xs text-gray-600">{figure.anime}</div>
                      </div>
                    </motion.div>
                  ))}
                </div>

                {/* Instructions */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 text-center">
                  <div className="bg-black/20 backdrop-blur-sm text-white px-4 py-2 rounded-lg">
                    🖱️ Klik figure untuk melihat filosofi hidup
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ) : (
          <motion.div
            key="detail"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            className="h-[calc(100%-4rem)] flex"
          >
            {/* Figure List */}
            <div className="w-1/3 p-4 border-r border-white/20 overflow-y-auto">
              <h3 className="text-white font-bold mb-4">Pilih Figure</h3>
              <div className="space-y-2">
                {figures.map((figure) => (
                  <motion.button
                    key={figure.id}
                    onClick={() => setSelectedFigure(figure)}
                    className={`w-full p-3 rounded-lg text-left transition-all ${
                      selectedFigure?.id === figure.id
                        ? 'bg-white/20 border border-white/40'
                        : 'bg-white/10 hover:bg-white/15'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{figure.image}</span>
                      <div>
                        <div className="text-white font-medium">{figure.name}</div>
                        <div className="text-purple-200 text-sm">{figure.anime}</div>
                      </div>
                    </div>
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Figure Detail */}
            <div className="flex-1 p-6 overflow-y-auto">
              <AnimatePresence mode="wait">
                {selectedFigure ? (
                  <motion.div
                    key={selectedFigure.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="space-y-6 text-white"
                  >
                    {/* Character Header */}
                    <div className="text-center">
                      <div className="text-8xl mb-4">{selectedFigure.image}</div>
                      <h2 className="text-3xl font-bold mb-2">{selectedFigure.name}</h2>
                      <p className="text-xl text-purple-200 mb-4">{selectedFigure.anime}</p>
                      <div className="inline-block bg-white/20 px-4 py-2 rounded-full">
                        {selectedFigure.personality}
                      </div>
                    </div>

                    {/* Quote */}
                    <div className="bg-white/10 backdrop-blur-sm p-6 rounded-lg border border-white/20">
                      <h3 className="text-xl font-bold mb-3 text-yellow-300">💬 Quote Ikonik</h3>
                      <blockquote className="text-lg italic text-center">
                        "{selectedFigure.quote}"
                      </blockquote>
                    </div>

                    {/* Philosophy */}
                    <div className="bg-white/10 backdrop-blur-sm p-6 rounded-lg border border-white/20">
                      <h3 className="text-xl font-bold mb-3 text-blue-300">🧠 Filosofi Hidup</h3>
                      <p className="leading-relaxed">{selectedFigure.philosophy}</p>
                    </div>

                    {/* Life Lesson */}
                    <div className="bg-white/10 backdrop-blur-sm p-6 rounded-lg border border-white/20">
                      <h3 className="text-xl font-bold mb-3 text-green-300">📚 Pelajaran Hidup</h3>
                      <p className="leading-relaxed">{selectedFigure.lifeLesson}</p>
                    </div>

                    {/* Tech Relevance */}
                    <div className="bg-white/10 backdrop-blur-sm p-6 rounded-lg border border-white/20">
                      <h3 className="text-xl font-bold mb-3 text-purple-300">💻 Relevansi dengan Tech</h3>
                      <p className="leading-relaxed">{selectedFigure.relevantToTech}</p>
                    </div>
                  </motion.div>
                ) : (
                  <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="flex items-center justify-center h-full text-center"
                  >
                    <div className="space-y-4 text-white">
                      <div className="text-6xl">🧸</div>
                      <h3 className="text-xl font-bold">Pilih Action Figure</h3>
                      <p className="text-purple-200">Klik pada figure di sebelah kiri untuk melihat filosofi hidup</p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
}

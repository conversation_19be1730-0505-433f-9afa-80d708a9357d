import { NextRequest, NextResponse } from 'next/server';
import db from '@/database/db';
import { biodata } from '@/database/schema';

export async function GET() {
  try {
    const result = await db.select().from(biodata).limit(1);
    
    if (result.length === 0) {
      return NextResponse.json(
        { error: 'Biodata not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(result[0]);
  } catch (error) {
    console.error('Biodata fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch biodata' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      role,
      education,
      university,
      status,
      bio,
      github,
      email,
      phone,
      location,
      avatar,
    } = body;

    // Validate required fields
    if (!name || !role || !education || !university || !status || !bio || !github) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const now = new Date();

    // Check if biodata exists
    const existing = await db.select().from(biodata).limit(1);

    if (existing.length === 0) {
      // Insert new biodata
      await db.insert(biodata).values({
        name,
        role,
        education,
        university,
        status,
        bio,
        github,
        email,
        phone,
        location,
        avatar,
        createdAt: now,
        updatedAt: now,
      });
    } else {
      // Update existing biodata
      await db.update(biodata)
        .set({
          name,
          role,
          education,
          university,
          status,
          bio,
          github,
          email,
          phone,
          location,
          avatar,
          updatedAt: now,
        })
        .where(biodata.id.eq(existing[0].id));
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Biodata update error:', error);
    return NextResponse.json(
      { error: 'Failed to update biodata' },
      { status: 500 }
    );
  }
}

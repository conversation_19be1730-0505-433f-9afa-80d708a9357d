'use client';

import { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface BookKangPCodeProps {
  isVisible: boolean;
  onClose: () => void;
}

export default function BookKangPCode({ isVisible, onClose }: BookKangPCodeProps) {
  const [currentPage, setCurrentPage] = useState(0);

  const bookData = {
    title: "Langkah Kode Nusantara",
    subtitle: "Perjalanan Belajar & Berkarya dalam Dunia Teknologi Indonesia",
    author: "<PERSON><PERSON><PERSON> (KangPCode)",
    description: "Sebuah panduan komprehensif untuk developer Indonesia yang ingin membangun karir di dunia teknologi dengan tetap mengangkat nilai-nilai lokal dan berkontribusi untuk kemajuan bangsa.",
    githubUrl: "https://github.com/kangpcode/langkah-kode-nusantara",
    pdfUrl: "/assets/book/langkah-kode-nusantara-sample.pdf",
    coverImage: "/assets/images/book/cover.jpg",
    chapters: [
      {
        number: 1,
        title: "Pengenalan Dunia Teknologi Indonesia",
        description: "Sejarah perkembangan teknologi di Indonesia dan peluang masa depan"
      },
      {
        number: 2,
        title: "Dasar-Dasar Programming",
        description: "Fundamental programming dengan pendekatan yang mudah dipahami"
      },
      {
        number: 3,
        title: "Web Development Modern",
        description: "Membangun aplikasi web dengan teknologi terkini"
      },
      {
        number: 4,
        title: "Mobile Development",
        description: "Pengembangan aplikasi mobile untuk platform Android dan iOS"
      },
      {
        number: 5,
        title: "DevOps & Deployment",
        description: "Praktik terbaik dalam deployment dan maintenance aplikasi"
      },
      {
        number: 6,
        title: "Membangun Karir Tech di Indonesia",
        description: "Strategi membangun karir teknologi yang berkelanjutan"
      }
    ],
    skills: [
      "JavaScript & TypeScript",
      "React & Next.js",
      "Node.js & Express",
      "Python & Django",
      "Database Design",
      "Docker & Kubernetes",
      "AWS & Cloud Computing",
      "Git & Version Control",
      "Testing & Quality Assurance",
      "UI/UX Design Principles"
    ],
    stats: {
      pages: 350,
      chapters: 6,
      codeExamples: 150,
      projects: 12
    }
  };

  const pages = [
    // Cover Page
    {
      type: 'cover',
      content: (
        <div className="h-full bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white flex flex-col justify-center items-center p-8 text-center">
          <div className="text-6xl mb-6">📘</div>
          <h1 className="text-4xl font-bold mb-4">{bookData.title}</h1>
          <h2 className="text-xl mb-6 text-blue-200">{bookData.subtitle}</h2>
          <p className="text-lg mb-8">oleh {bookData.author}</p>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="bg-white/10 p-3 rounded">
              <div className="text-2xl font-bold">{bookData.stats.pages}</div>
              <div>Halaman</div>
            </div>
            <div className="bg-white/10 p-3 rounded">
              <div className="text-2xl font-bold">{bookData.stats.chapters}</div>
              <div>Bab</div>
            </div>
            <div className="bg-white/10 p-3 rounded">
              <div className="text-2xl font-bold">{bookData.stats.codeExamples}</div>
              <div>Contoh Kode</div>
            </div>
            <div className="bg-white/10 p-3 rounded">
              <div className="text-2xl font-bold">{bookData.stats.projects}</div>
              <div>Proyek</div>
            </div>
          </div>
        </div>
      )
    },
    // About Page
    {
      type: 'about',
      content: (
        <div className="h-full bg-white p-8 overflow-y-auto">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Tentang Buku</h2>
          <p className="text-lg text-gray-700 mb-6 leading-relaxed">
            {bookData.description}
          </p>
          
          <h3 className="text-2xl font-bold text-gray-900 mb-4">Mengapa Buku Ini?</h3>
          <ul className="list-disc list-inside text-gray-700 space-y-2 mb-6">
            <li>Pendekatan pembelajaran yang disesuaikan dengan konteks Indonesia</li>
            <li>Studi kasus dari startup dan perusahaan teknologi lokal</li>
            <li>Panduan praktis membangun portfolio yang menarik</li>
            <li>Tips networking dan membangun personal branding</li>
            <li>Strategi menghadapi tantangan industri teknologi Indonesia</li>
          </ul>

          <h3 className="text-2xl font-bold text-gray-900 mb-4">Target Pembaca</h3>
          <ul className="list-disc list-inside text-gray-700 space-y-2">
            <li>Mahasiswa informatika dan teknik komputer</li>
            <li>Fresh graduate yang ingin berkarir di bidang teknologi</li>
            <li>Developer yang ingin meningkatkan skill dan karir</li>
            <li>Entrepreneur yang ingin membangun startup teknologi</li>
            <li>Siapa saja yang tertarik dengan dunia teknologi Indonesia</li>
          </ul>
        </div>
      )
    },
    // Chapters Page
    {
      type: 'chapters',
      content: (
        <div className="h-full bg-white p-8 overflow-y-auto">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Daftar Isi</h2>
          <div className="space-y-4">
            {bookData.chapters.map((chapter) => (
              <div key={chapter.number} className="border-l-4 border-blue-500 pl-4 py-2">
                <h3 className="text-xl font-semibold text-gray-900">
                  Bab {chapter.number}: {chapter.title}
                </h3>
                <p className="text-gray-600 mt-1">{chapter.description}</p>
              </div>
            ))}
          </div>
        </div>
      )
    },
    // Skills Page
    {
      type: 'skills',
      content: (
        <div className="h-full bg-white p-8 overflow-y-auto">
          <h2 className="text-3xl font-bold text-gray-900 mb-6">Teknologi yang Dibahas</h2>
          <div className="grid grid-cols-2 gap-4">
            {bookData.skills.map((skill, index) => (
              <motion.div
                key={skill}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-gradient-to-r from-blue-50 to-purple-50 p-4 rounded-lg border border-blue-200"
              >
                <div className="flex items-center space-x-3">
                  <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                  <span className="font-medium text-gray-900">{skill}</span>
                </div>
              </motion.div>
            ))}
          </div>
          
          <div className="mt-8 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
            <h3 className="text-xl font-bold text-gray-900 mb-3">💡 Bonus Content</h3>
            <ul className="list-disc list-inside text-gray-700 space-y-1">
              <li>Template CV dan Portfolio untuk Developer</li>
              <li>Cheat Sheet untuk Interview Technical</li>
              <li>Resource Learning Path yang Terstruktur</li>
              <li>Komunitas Developer Indonesia</li>
            </ul>
          </div>
        </div>
      )
    }
  ];

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-4 bg-white rounded-lg shadow-2xl border overflow-hidden z-50"
    >
      {/* Book Header */}
      <div className="h-16 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between px-6 text-white">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">📘</span>
          <div>
            <h2 className="font-bold">{bookData.title}</h2>
            <p className="text-sm text-blue-100">by {bookData.author}</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="hover:bg-white/20 p-2 rounded transition-colors"
        >
          ✕
        </button>
      </div>

      {/* Book Content */}
      <div className="flex h-[calc(100%-4rem)]">
        {/* Page Content */}
        <div className="flex-1">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentPage}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="h-full"
            >
              {pages[currentPage]?.content}
            </motion.div>
          </AnimatePresence>
        </div>
      </div>

      {/* Book Navigation */}
      <div className="h-16 bg-gray-100 border-t flex items-center justify-between px-6">
        <div className="flex space-x-2">
          {pages.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentPage(index)}
              className={`w-3 h-3 rounded-full transition-colors ${
                currentPage === index ? 'bg-blue-600' : 'bg-gray-300 hover:bg-gray-400'
              }`}
            />
          ))}
        </div>

        <div className="flex items-center space-x-4">
          <button
            onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}
            disabled={currentPage === 0}
            className="px-4 py-2 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed rounded transition-colors"
          >
            ← Prev
          </button>
          
          <span className="text-sm text-gray-600">
            {currentPage + 1} / {pages.length}
          </span>
          
          <button
            onClick={() => setCurrentPage(Math.min(pages.length - 1, currentPage + 1))}
            disabled={currentPage === pages.length - 1}
            className="px-4 py-2 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed rounded transition-colors"
          >
            Next →
          </button>
        </div>

        <div className="flex space-x-2">
          <a
            href={bookData.githubUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="px-4 py-2 bg-gray-900 hover:bg-gray-800 text-white rounded transition-colors"
          >
            📁 GitHub
          </a>
          <a
            href={bookData.pdfUrl}
            download
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
          >
            📄 Download PDF
          </a>
        </div>
      </div>
    </motion.div>
  );
}

import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  try {
    // Static figures data
    const figures = [
      {
        id: 1,
        name: '<PERSON> D<PERSON>',
        anime: 'One Piece',
        quote: 'A<PERSON> akan menjadi Raja <PERSON>!',
        philosophy: 'Tidak pernah menyerah pada impian, selalu optimis menghadapi tantangan',
        image: '🏴‍☠️',
        color: '#FF6B6B',
        personality: '<PERSON><PERSON><PERSON>, pantang menyerah, loyal pada teman',
        lifeLesson: 'Impian yang besar membutuhkan tekad yang kuat dan kerja keras yang konsisten',
        relevantToTech: 'Seperti developer yang bermimpi membuat aplikasi yang mengubah dunia - butuh persistence dan passion',
        position: { x: -3, y: 0, z: 0 },
        featured: true,
        additionalQuotes: [
          'Aku tidak peduli siapa yang jadi musuhku! Aku akan melindungi teman-temanku!',
          'Jika aku mati karena mencoba, setidaknya aku sudah mencoba!'
        ],
        backstory: '<PERSON><PERSON> adalah kapten Bajak Laut Topi Jerami yang bermimpi menjadi Raja Bajak Laut. Optimisme dan tekadnya yang kuat menginspirasi banyak orang.',
        techParallel: 'Seperti startup founder yang tidak pernah menyerah meski menghadapi banyak kegagalan.'
      },
      {
        id: 2,
        name: 'Uzumaki Naruto',
        anime: 'Naruto',
        quote: 'Aku tidak akan mundur atau menarik kata-kataku!',
        philosophy: 'Komitmen pada janji dan prinsip adalah kekuatan sejati',
        image: '🍥',
        color: '#FF9500',
        personality: 'Gigih, setia kawan, tidak pernah menyerah',
        lifeLesson: 'Kegagalan adalah bagian dari perjalanan menuju kesuksesan',
        relevantToTech: 'Debugging code yang sulit membutuhkan persistence seperti Naruto',
        position: { x: 1, y: 0, z: 0 },
        featured: true,
        additionalQuotes: [
          'Aku akan menjadi Hokage yang diakui oleh semua orang!',
          'Jika tidak bisa melakukan dengan baik, maka lakukan dengan cara yang berbeda!'
        ],
        backstory: 'Naruto adalah ninja yang bermimpi menjadi Hokage. Meski sering gagal, dia tidak pernah menyerah dan selalu berusaha melindungi orang-orang yang dicintainya.',
        techParallel: 'Seperti developer yang terus belajar dari error dan bug untuk menjadi lebih baik.'
      },
      {
        id: 3,
        name: 'Edward Elric',
        anime: 'Fullmetal Alchemist',
        quote: 'Untuk mendapatkan sesuatu, kau harus mengorbankan sesuatu yang setara nilainya.',
        philosophy: 'Hukum pertukaran setara - tidak ada yang gratis di dunia ini',
        image: '⚗️',
        color: '#FFD700',
        personality: 'Cerdas, determinasi tinggi, bertanggung jawab',
        lifeLesson: 'Setiap pencapaian membutuhkan pengorbanan dan kerja keras',
        relevantToTech: 'Seperti belajar programming - harus mengorbankan waktu dan effort untuk menguasai skill',
        position: { x: 3, y: 0, z: 2 },
        featured: true,
        additionalQuotes: [
          'Manusia tanpa pengorbanan tidak akan mendapatkan apa-apa.',
          'Kami bukan dewa. Kami hanya manusia biasa yang tidak bisa menghidupkan orang mati.'
        ],
        backstory: 'Edward adalah alkemis termuda yang mencari Philosopher\'s Stone untuk mengembalikan tubuh adiknya. Dia memahami bahwa setiap kekuatan memiliki harga.',
        techParallel: 'Seperti software engineer yang memahami trade-off dalam setiap keputusan arsitektur.'
      },
      {
        id: 4,
        name: 'Senku Ishigami',
        anime: 'Dr. Stone',
        quote: 'Ini sangat menarik secara eksponensial!',
        philosophy: 'Sains dan logika adalah kunci untuk memecahkan semua masalah',
        image: '🧪',
        color: '#4ECDC4',
        personality: 'Genius, rasional, passionate tentang sains',
        lifeLesson: 'Pengetahuan dan sains dapat mengubah dunia',
        relevantToTech: 'Seperti developer yang menggunakan teknologi untuk memecahkan masalah dunia nyata',
        position: { x: -1, y: 0, z: 3 },
        featured: true,
        additionalQuotes: [
          'Sains adalah kekuatan yang paling menakjubkan di dunia!',
          'Dengan sains, kita bisa membangun peradaban dari nol!'
        ],
        backstory: 'Senku adalah genius sains yang terbangun di dunia batu dan menggunakan pengetahuan ilmiahnya untuk membangun kembali peradaban.',
        techParallel: 'Seperti tech innovator yang menggunakan teknologi untuk membangun solusi dari nol.'
      },
      {
        id: 5,
        name: 'Shikamaru Nara',
        anime: 'Naruto',
        quote: 'Betapa merepotkannya...',
        philosophy: 'Strategi dan perencanaan yang matang lebih penting daripada kekuatan brute',
        image: '🦌',
        color: '#8B4513',
        personality: 'Strategis, malas tapi jenius, loyal',
        lifeLesson: 'Berpikir sebelum bertindak, strategi mengalahkan kekuatan',
        relevantToTech: 'Seperti system architect yang merancang solusi elegant untuk masalah kompleks',
        position: { x: 2, y: 0, z: -2 },
        featured: false,
        additionalQuotes: [
          'Strategi tanpa taktik adalah jalan terlambat menuju kemenangan.',
          'Aku lebih suka menghindari masalah daripada menghadapinya.'
        ],
        backstory: 'Shikamaru adalah ninja jenius dengan IQ tinggi yang lebih suka menghindari masalah tapi selalu bisa menemukan solusi strategis.',
        techParallel: 'Seperti senior developer yang lebih memilih solusi simple dan elegant daripada over-engineering.'
      },
      {
        id: 6,
        name: 'Saitama',
        anime: 'One Punch Man',
        quote: 'OK.',
        philosophy: 'Kekuatan sejati datang dari latihan yang konsisten dan sederhana',
        image: '👊',
        color: '#FFFF00',
        personality: 'Sederhana, humble, sangat kuat',
        lifeLesson: 'Konsistensi dalam hal sederhana bisa menghasilkan hasil luar biasa',
        relevantToTech: 'Seperti developer yang menguasai fundamental dan konsisten practice setiap hari',
        position: { x: 0, y: 0, z: -3 },
        featured: false,
        additionalQuotes: [
          '100 push-up, 100 sit-up, 100 squat, dan lari 10km setiap hari!',
          'Aku hanya hobi jadi hero.'
        ],
        backstory: 'Saitama adalah hero yang menjadi sangat kuat melalui latihan sederhana tapi konsisten. Dia menghadapi masalah karena terlalu kuat.',
        techParallel: 'Seperti developer yang menguasai fundamental programming dengan sangat baik melalui practice konsisten.'
      }
    ];

    return NextResponse.json(figures);
  } catch (error) {
    console.error('Figures fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch figures data' },
      { status: 500 }
    );
  }
}

'use client';

import { Suspense, useRef, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { 
  OrbitControls, 
  Environment, 
  Text, 
  Box, 
  Plane,
  Html,
  useTexture,
  PerspectiveCamera
} from '@react-three/drei';
import { motion } from 'framer-motion';
import * as THREE from 'three';

// Room Environment Component
function Room() {
  return (
    <group>
      {/* Floor */}
      <Plane 
        args={[20, 20]} 
        rotation={[-Math.PI / 2, 0, 0]} 
        position={[0, -2, 0]}
      >
        <meshStandardMaterial color="#8B4513" />
      </Plane>

      {/* Walls */}
      <Plane args={[20, 10]} position={[0, 3, -10]}>
        <meshStandardMaterial color="#F5F5DC" />
      </Plane>
      
      <Plane args={[20, 10]} position={[-10, 3, 0]} rotation={[0, Math.PI / 2, 0]}>
        <meshStandardMaterial color="#F5F5DC" />
      </Plane>
      
      <Plane args={[20, 10]} position={[10, 3, 0]} rotation={[0, -Math.PI / 2, 0]}>
        <meshStandardMaterial color="#F5F5DC" />
      </Plane>

      {/* Ceiling */}
      <Plane 
        args={[20, 20]} 
        rotation={[Math.PI / 2, 0, 0]} 
        position={[0, 8, 0]}
      >
        <meshStandardMaterial color="#FFFFFF" />
      </Plane>
    </group>
  );
}

// Computer 3D Component (Placeholder)
function Computer3D() {
  const meshRef = useRef<THREE.Mesh>(null);
  
  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
    }
  });

  return (
    <group position={[-4, -1, -2]}>
      {/* Monitor */}
      <Box ref={meshRef} args={[3, 2, 0.2]} position={[0, 1, 0]}>
        <meshStandardMaterial color="#1a1a1a" />
      </Box>
      
      {/* Screen */}
      <Plane args={[2.8, 1.8]} position={[0, 1, 0.11]}>
        <meshStandardMaterial color="#000080" emissive="#000040" />
      </Plane>
      
      {/* Base */}
      <Box args={[0.5, 0.8, 0.5]} position={[0, 0, 0]}>
        <meshStandardMaterial color="#333333" />
      </Box>
      
      {/* Keyboard */}
      <Box args={[2, 0.1, 1]} position={[0, -1.5, 1]}>
        <meshStandardMaterial color="#2a2a2a" />
      </Box>

      {/* Click Area */}
      <Html position={[0, 1, 0.2]} center>
        <div 
          className="w-32 h-20 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors"
          onClick={() => console.log('Computer clicked!')}
          title="Klik untuk membuka KDE Plasma"
        />
      </Html>
    </group>
  );
}

// Laptop 3D Component (Placeholder)
function Laptop3D() {
  return (
    <group position={[4, -1, -1]}>
      {/* Laptop Base */}
      <Box args={[2.5, 0.1, 1.8]} position={[0, -1.5, 0]}>
        <meshStandardMaterial color="#C0C0C0" />
      </Box>
      
      {/* Laptop Screen */}
      <Box args={[2.5, 1.6, 0.1]} position={[0, 0, -0.8]} rotation={[-0.2, 0, 0]}>
        <meshStandardMaterial color="#1a1a1a" />
      </Box>
      
      {/* Screen Display */}
      <Plane args={[2.3, 1.4]} position={[0, 0, -0.75]} rotation={[-0.2, 0, 0]}>
        <meshStandardMaterial color="#0078D4" emissive="#003d6b" />
      </Plane>

      {/* Click Area */}
      <Html position={[0, 0, -0.7]} center>
        <div 
          className="w-24 h-16 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors"
          onClick={() => console.log('Laptop clicked!')}
          title="Klik untuk membuka GitHub Viewer"
        />
      </Html>
    </group>
  );
}

// Book 3D Component (Placeholder)
function Book3D() {
  return (
    <group position={[0, -1.3, 0]}>
      <Box args={[1.5, 0.2, 1]} position={[0, 0, 0]}>
        <meshStandardMaterial color="#8B4513" />
      </Box>
      
      <Html position={[0, 0.2, 0]} center>
        <div 
          className="w-16 h-12 bg-transparent cursor-pointer hover:bg-yellow-500/20 rounded transition-colors"
          onClick={() => console.log('Book clicked!')}
          title="Langkah Kode Nusantara"
        />
      </Html>
    </group>
  );
}

// ID Card Component (Placeholder)
function IDCard3D() {
  return (
    <group position={[0, -0.9, 0.3]}>
      <Box args={[0.8, 0.05, 0.5]} position={[0, 0, 0]}>
        <meshStandardMaterial color="#FFFFFF" />
      </Box>
      
      <Html position={[0, 0.1, 0]} center>
        <div 
          className="w-8 h-6 bg-transparent cursor-pointer hover:bg-green-500/20 rounded transition-colors"
          onClick={() => console.log('ID Card clicked!')}
          title="ID Card KangPCode"
        />
      </Html>
    </group>
  );
}

// Lighting Setup
function Lighting() {
  return (
    <>
      <ambientLight intensity={0.4} />
      <pointLight position={[0, 6, 0]} intensity={0.8} />
      <pointLight position={[-5, 4, -5]} intensity={0.6} color="#FFE4B5" />
      <pointLight position={[5, 4, -5]} intensity={0.6} color="#E6E6FA" />
      <directionalLight 
        position={[10, 10, 5]} 
        intensity={0.5}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
    </>
  );
}

// Main RoomScene3D Component
interface RoomScene3DProps {
  onExitRoom?: () => void;
}

export default function RoomScene3D({ onExitRoom }: RoomScene3DProps) {
  const [isLoading, setIsLoading] = useState(true);

  return (
    <div className="w-full h-screen relative">
      <Canvas
        shadows
        camera={{ position: [0, 2, 8], fov: 60 }}
        onCreated={() => setIsLoading(false)}
      >
        <Suspense fallback={null}>
          <Lighting />
          <Room />
          <Computer3D />
          <Laptop3D />
          <Book3D />
          <IDCard3D />
          
          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={3}
            maxDistance={15}
            minPolarAngle={0}
            maxPolarAngle={Math.PI / 2}
          />
          
          <Environment preset="apartment" />
        </Suspense>
      </Canvas>

      {/* UI Overlay */}
      <div className="absolute top-4 left-4 z-10">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white"
        >
          <h2 className="text-xl font-bold mb-2">Kamar KangPCode</h2>
          <p className="text-sm text-gray-300">
            Klik objek untuk berinteraksi
          </p>
        </motion.div>
      </div>

      {/* Exit Button */}
      <div className="absolute top-4 right-4 z-10">
        <motion.button
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          onClick={onExitRoom}
          className="bg-red-500/80 hover:bg-red-600/80 backdrop-blur-sm rounded-lg px-4 py-2 text-white font-medium transition-colors"
        >
          Keluar Kamar
        </motion.button>
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-20">
          <div className="text-white text-xl">Memuat Scene 3D...</div>
        </div>
      )}
    </div>
  );
}

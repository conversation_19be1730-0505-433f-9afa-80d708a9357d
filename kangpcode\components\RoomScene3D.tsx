'use client';

import { Suspense, useRef, useState } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import {
  OrbitControls,
  Environment,
  Text,
  Box,
  Plane,
  Html,
  useTexture,
  PerspectiveCamera
} from '@react-three/drei';
import { motion } from 'framer-motion';
import * as THREE from 'three';
import ComputerGUI from './ComputerGUI';
import TerminalAI from './TerminalAI';
import LaptopWindows from './LaptopWindows';
import BookKangPCode from './BookKangPCode';
import IDCardLanyard from './IDCardLanyard';
import PosterIlmuwan from './PosterIlmuwan';
import ActionFigure from './ActionFigure';
import WhiteboardProject from './WhiteboardProject';
import ResumePDF from './ResumePDF';
import MiniGame from './MiniGame';

// Room Environment Component
function Room() {
  return (
    <group>
      {/* Floor */}
      <Plane 
        args={[20, 20]} 
        rotation={[-Math.PI / 2, 0, 0]} 
        position={[0, -2, 0]}
      >
        <meshStandardMaterial color="#8B4513" />
      </Plane>

      {/* Walls */}
      <Plane args={[20, 10]} position={[0, 3, -10]}>
        <meshStandardMaterial color="#F5F5DC" />
      </Plane>
      
      <Plane args={[20, 10]} position={[-10, 3, 0]} rotation={[0, Math.PI / 2, 0]}>
        <meshStandardMaterial color="#F5F5DC" />
      </Plane>
      
      <Plane args={[20, 10]} position={[10, 3, 0]} rotation={[0, -Math.PI / 2, 0]}>
        <meshStandardMaterial color="#F5F5DC" />
      </Plane>

      {/* Ceiling */}
      <Plane 
        args={[20, 20]} 
        rotation={[Math.PI / 2, 0, 0]} 
        position={[0, 8, 0]}
      >
        <meshStandardMaterial color="#FFFFFF" />
      </Plane>
    </group>
  );
}

// Computer 3D Component
function Computer3D({ onComputerClick }: { onComputerClick: () => void }) {
  const meshRef = useRef<THREE.Mesh>(null);
  const [isHovered, setIsHovered] = useState(false);

  useFrame((state) => {
    if (meshRef.current) {
      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;
    }
  });

  return (
    <group position={[-4, -1, -2]}>
      {/* Monitor */}
      <Box ref={meshRef} args={[3, 2, 0.2]} position={[0, 1, 0]}>
        <meshStandardMaterial color="#1a1a1a" />
      </Box>

      {/* Screen */}
      <Plane args={[2.8, 1.8]} position={[0, 1, 0.11]}>
        <meshStandardMaterial
          color={isHovered ? "#001080" : "#000080"}
          emissive={isHovered ? "#000060" : "#000040"}
        />
      </Plane>

      {/* KDE Plasma Logo on Screen */}
      <Html position={[0, 1, 0.12]} center>
        <div className="text-white text-center pointer-events-none">
          <div className="text-4xl mb-2">🐧</div>
          <div className="text-sm font-bold">KDE Plasma</div>
        </div>
      </Html>

      {/* Base */}
      <Box args={[0.5, 0.8, 0.5]} position={[0, 0, 0]}>
        <meshStandardMaterial color="#333333" />
      </Box>

      {/* Keyboard */}
      <Box args={[2, 0.1, 1]} position={[0, -1.5, 1]}>
        <meshStandardMaterial color="#2a2a2a" />
      </Box>

      {/* Click Area */}
      <Html position={[0, 1, 0.2]} center>
        <div
          className="w-32 h-20 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors flex items-center justify-center"
          onClick={onComputerClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          title="Klik untuk membuka KDE Plasma"
        >
          {isHovered && (
            <div className="text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none">
              🖱️ Klik untuk membuka
            </div>
          )}
        </div>
      </Html>
    </group>
  );
}

// Laptop 3D Component
function Laptop3D({ onLaptopClick }: { onLaptopClick: () => void }) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <group position={[4, -1, -1]}>
      {/* Laptop Base */}
      <Box args={[2.5, 0.1, 1.8]} position={[0, -1.5, 0]}>
        <meshStandardMaterial color="#C0C0C0" />
      </Box>

      {/* Laptop Screen */}
      <Box args={[2.5, 1.6, 0.1]} position={[0, 0, -0.8]} rotation={[-0.2, 0, 0]}>
        <meshStandardMaterial color="#1a1a1a" />
      </Box>

      {/* Screen Display */}
      <Plane args={[2.3, 1.4]} position={[0, 0, -0.75]} rotation={[-0.2, 0, 0]}>
        <meshStandardMaterial
          color={isHovered ? "#0088D4" : "#0078D4"}
          emissive={isHovered ? "#004d8b" : "#003d6b"}
        />
      </Plane>

      {/* Windows Logo on Screen */}
      <Html position={[0, 0, -0.74]} center>
        <div className="text-white text-center pointer-events-none" style={{ transform: 'rotateX(-11.5deg)' }}>
          <div className="text-3xl mb-1">🪟</div>
          <div className="text-xs font-bold">Windows 11</div>
        </div>
      </Html>

      {/* Click Area */}
      <Html position={[0, 0, -0.7]} center>
        <div
          className="w-24 h-16 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors flex items-center justify-center"
          onClick={onLaptopClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          title="Klik untuk membuka GitHub Viewer"
        >
          {isHovered && (
            <div className="text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none">
              🖱️ GitHub Viewer
            </div>
          )}
        </div>
      </Html>
    </group>
  );
}

// Book 3D Component
function Book3D({ onBookClick }: { onBookClick: () => void }) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <group position={[0, -1.3, 0]}>
      <Box args={[1.5, 0.2, 1]} position={[0, 0, 0]}>
        <meshStandardMaterial color={isHovered ? "#A0522D" : "#8B4513"} />
      </Box>

      {/* Book Cover Text */}
      <Html position={[0, 0.15, 0.51]} center>
        <div className="text-white text-center pointer-events-none text-xs">
          <div className="font-bold">📘</div>
          <div className="font-bold">Langkah</div>
          <div className="font-bold">Kode</div>
          <div className="font-bold">Nusantara</div>
        </div>
      </Html>

      <Html position={[0, 0.2, 0]} center>
        <div
          className="w-16 h-12 bg-transparent cursor-pointer hover:bg-yellow-500/20 rounded transition-colors flex items-center justify-center"
          onClick={onBookClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          title="Langkah Kode Nusantara"
        >
          {isHovered && (
            <div className="text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none">
              📖 Baca Buku
            </div>
          )}
        </div>
      </Html>
    </group>
  );
}

// ID Card Component
function IDCard3D({ onIDCardClick }: { onIDCardClick: () => void }) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <group position={[0, -0.9, 0.3]}>
      <Box args={[0.8, 0.05, 0.5]} position={[0, 0, 0]}>
        <meshStandardMaterial color={isHovered ? "#F0F0F0" : "#FFFFFF"} />
      </Box>

      {/* ID Card Content */}
      <Html position={[0, 0.03, 0.26]} center>
        <div className="text-black text-center pointer-events-none text-xs">
          <div className="font-bold">🪪 KangPCode</div>
          <div className="text-xs">Developer ID</div>
        </div>
      </Html>

      <Html position={[0, 0.1, 0]} center>
        <div
          className="w-8 h-6 bg-transparent cursor-pointer hover:bg-green-500/20 rounded transition-colors flex items-center justify-center"
          onClick={onIDCardClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          title="ID Card KangPCode"
        >
          {isHovered && (
            <div className="text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none">
              🪪 Lihat ID
            </div>
          )}
        </div>
      </Html>
    </group>
  );
}

// Poster Ilmuwan 3D Component
function PosterIlmuwan3D({ onPosterClick }: { onPosterClick: () => void }) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <group position={[-8, 2, -9]}>
      {/* Poster Frame */}
      <Box args={[3, 4, 0.1]} position={[0, 0, 0]}>
        <meshStandardMaterial color={isHovered ? "#8B4513" : "#654321"} />
      </Box>

      {/* Poster Content */}
      <Plane args={[2.8, 3.8]} position={[0, 0, 0.06]}>
        <meshStandardMaterial color="#FFFFFF" />
      </Plane>

      {/* Poster Text */}
      <Html position={[0, 0, 0.07]} center>
        <div className="text-black text-center pointer-events-none text-xs w-32">
          <div className="font-bold text-lg">👨‍🔬</div>
          <div className="font-bold">POSTER</div>
          <div className="font-bold">ILMUWAN</div>
          <div className="text-xs mt-2">Tesla, Einstein,</div>
          <div className="text-xs">Al-Khawarizmi,</div>
          <div className="text-xs">Onno W. Purbo</div>
        </div>
      </Html>

      <Html position={[0, 0, 0.1]} center>
        <div
          className="w-32 h-40 bg-transparent cursor-pointer hover:bg-purple-500/20 rounded transition-colors flex items-center justify-center"
          onClick={onPosterClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          title="Poster Ilmuwan Teknologi"
        >
          {isHovered && (
            <div className="text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none">
              👨‍🔬 Lihat Poster
            </div>
          )}
        </div>
      </Html>
    </group>
  );
}

// Action Figure Shelf 3D Component
function ActionFigureShelf3D({ onShelfClick }: { onShelfClick: () => void }) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <group position={[8, 0, -8]}>
      {/* Shelf */}
      <Box args={[2, 3, 0.5]} position={[0, 0, 0]}>
        <meshStandardMaterial color={isHovered ? "#D2B48C" : "#DEB887"} />
      </Box>

      {/* Shelf Dividers */}
      <Box args={[2, 0.1, 0.5]} position={[0, 1, 0]}>
        <meshStandardMaterial color="#8B7355" />
      </Box>
      <Box args={[2, 0.1, 0.5]} position={[0, -1, 0]}>
        <meshStandardMaterial color="#8B7355" />
      </Box>

      {/* Figures */}
      <Html position={[0, 0, 0.3]} center>
        <div className="text-center pointer-events-none">
          <div className="grid grid-cols-3 gap-1 text-lg">
            <div>🏴‍☠️</div>
            <div>⚔️</div>
            <div>🍥</div>
            <div>⚡</div>
            <div>🧪</div>
            <div>⚗️</div>
          </div>
          <div className="text-xs font-bold mt-1">Action Figures</div>
        </div>
      </Html>

      <Html position={[0, 0, 0.4]} center>
        <div
          className="w-20 h-32 bg-transparent cursor-pointer hover:bg-orange-500/20 rounded transition-colors flex items-center justify-center"
          onClick={onShelfClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          title="Action Figure Collection"
        >
          {isHovered && (
            <div className="text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none">
              🧸 Lihat Koleksi
            </div>
          )}
        </div>
      </Html>
    </group>
  );
}

// Whiteboard 3D Component
function Whiteboard3D({ onWhiteboardClick }: { onWhiteboardClick: () => void }) {
  const [isHovered, setIsHovered] = useState(false);

  return (
    <group position={[0, 2, -9]}>
      {/* Whiteboard Frame */}
      <Box args={[4, 3, 0.1]} position={[0, 0, 0]}>
        <meshStandardMaterial color="#2F4F4F" />
      </Box>

      {/* Whiteboard Surface */}
      <Plane args={[3.8, 2.8]} position={[0, 0, 0.06]}>
        <meshStandardMaterial color={isHovered ? "#F8F8FF" : "#FFFFFF"} />
      </Plane>

      {/* Project Pins */}
      <Html position={[0, 0, 0.07]} center>
        <div className="text-black text-center pointer-events-none text-xs">
          <div className="font-bold text-lg">📌</div>
          <div className="font-bold">PROJECT</div>
          <div className="font-bold">WHITEBOARD</div>
          <div className="grid grid-cols-2 gap-2 mt-2 text-xs">
            <div className="bg-yellow-200 p-1 rounded">Portfolio 3D</div>
            <div className="bg-blue-200 p-1 rounded">Langkah Kode</div>
            <div className="bg-green-200 p-1 rounded">DevTools ID</div>
            <div className="bg-red-200 p-1 rounded">Smart Campus</div>
          </div>
        </div>
      </Html>

      <Html position={[0, 0, 0.1]} center>
        <div
          className="w-40 h-32 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors flex items-center justify-center"
          onClick={onWhiteboardClick}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
          title="Project Whiteboard"
        >
          {isHovered && (
            <div className="text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none">
              📌 Lihat Proyek
            </div>
          )}
        </div>
      </Html>
    </group>
  );
}

// Lighting Setup
function Lighting() {
  return (
    <>
      <ambientLight intensity={0.4} />
      <pointLight position={[0, 6, 0]} intensity={0.8} />
      <pointLight position={[-5, 4, -5]} intensity={0.6} color="#FFE4B5" />
      <pointLight position={[5, 4, -5]} intensity={0.6} color="#E6E6FA" />
      <directionalLight 
        position={[10, 10, 5]} 
        intensity={0.5}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
    </>
  );
}

// Main RoomScene3D Component
interface RoomScene3DProps {
  onExitRoom?: () => void;
}

export default function RoomScene3D({ onExitRoom }: RoomScene3DProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [showComputerGUI, setShowComputerGUI] = useState(false);
  const [showTerminalAI, setShowTerminalAI] = useState(false);
  const [showLaptopWindows, setShowLaptopWindows] = useState(false);
  const [showBook, setShowBook] = useState(false);
  const [showIDCard, setShowIDCard] = useState(false);
  const [showPoster, setShowPoster] = useState(false);
  const [showActionFigure, setShowActionFigure] = useState(false);
  const [showWhiteboard, setShowWhiteboard] = useState(false);
  const [showResumePDF, setShowResumePDF] = useState(false);
  const [showMiniGame, setShowMiniGame] = useState(false);

  const handleComputerClick = () => {
    setShowComputerGUI(true);
  };

  const handleLaptopClick = () => {
    setShowLaptopWindows(true);
  };

  const handleBookClick = () => {
    setShowBook(true);
  };

  const handleIDCardClick = () => {
    setShowIDCard(true);
  };

  const handlePosterClick = () => {
    setShowPoster(true);
  };

  const handleActionFigureClick = () => {
    setShowActionFigure(true);
  };

  const handleWhiteboardClick = () => {
    setShowWhiteboard(true);
  };

  const handleCloseComputer = () => {
    setShowComputerGUI(false);
    setShowTerminalAI(false);
  };

  const handleCloseLaptop = () => {
    setShowLaptopWindows(false);
  };

  const handleCloseBook = () => {
    setShowBook(false);
  };

  const handleCloseIDCard = () => {
    setShowIDCard(false);
  };

  const handleClosePoster = () => {
    setShowPoster(false);
  };

  const handleCloseActionFigure = () => {
    setShowActionFigure(false);
  };

  const handleCloseWhiteboard = () => {
    setShowWhiteboard(false);
  };

  return (
    <div className="w-full h-screen relative">
      <Canvas
        shadows
        camera={{ position: [0, 2, 8], fov: 60 }}
        onCreated={() => setIsLoading(false)}
      >
        <Suspense fallback={null}>
          <Lighting />
          <Room />
          <Computer3D onComputerClick={handleComputerClick} />
          <Laptop3D onLaptopClick={handleLaptopClick} />
          <Book3D onBookClick={handleBookClick} />
          <IDCard3D onIDCardClick={handleIDCardClick} />
          <PosterIlmuwan3D onPosterClick={handlePosterClick} />
          <ActionFigureShelf3D onShelfClick={handleActionFigureClick} />
          <Whiteboard3D onWhiteboardClick={handleWhiteboardClick} />
          
          <OrbitControls
            enablePan={true}
            enableZoom={true}
            enableRotate={true}
            minDistance={3}
            maxDistance={15}
            minPolarAngle={0}
            maxPolarAngle={Math.PI / 2}
          />
          
          <Environment preset="apartment" />
        </Suspense>
      </Canvas>

      {/* UI Overlay */}
      <div className="absolute top-4 left-4 z-10">
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white"
        >
          <h2 className="text-xl font-bold mb-2">Kamar KangPCode</h2>
          <p className="text-sm text-gray-300">
            Klik objek untuk berinteraksi
          </p>
        </motion.div>
      </div>

      {/* Exit Button */}
      <div className="absolute top-4 right-4 z-10">
        <motion.button
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          onClick={onExitRoom}
          className="bg-red-500/80 hover:bg-red-600/80 backdrop-blur-sm rounded-lg px-4 py-2 text-white font-medium transition-colors"
        >
          Keluar Kamar
        </motion.button>
      </div>

      {/* Loading Overlay */}
      {isLoading && (
        <div className="absolute inset-0 bg-black/50 flex items-center justify-center z-20">
          <div className="text-white text-xl">Memuat Scene 3D...</div>
        </div>
      )}

      {/* Computer GUI Overlay */}
      <ComputerGUI
        isVisible={showComputerGUI}
        onClose={handleCloseComputer}
      />

      {/* Terminal AI Overlay */}
      <TerminalAI
        isVisible={showTerminalAI}
        onClose={() => setShowTerminalAI(false)}
      />

      {/* Laptop Windows Overlay */}
      <LaptopWindows
        isVisible={showLaptopWindows}
        onClose={handleCloseLaptop}
      />

      {/* Book Overlay */}
      <BookKangPCode
        isVisible={showBook}
        onClose={handleCloseBook}
      />

      {/* ID Card Overlay */}
      <IDCardLanyard
        isVisible={showIDCard}
        onClose={handleCloseIDCard}
      />

      {/* Poster Ilmuwan Overlay */}
      <PosterIlmuwan
        isVisible={showPoster}
        onClose={handleClosePoster}
      />

      {/* Action Figure Overlay */}
      <ActionFigure
        isVisible={showActionFigure}
        onClose={handleCloseActionFigure}
      />

      {/* Whiteboard Project Overlay */}
      <WhiteboardProject
        isVisible={showWhiteboard}
        onClose={handleCloseWhiteboard}
      />
    </div>
  );
}

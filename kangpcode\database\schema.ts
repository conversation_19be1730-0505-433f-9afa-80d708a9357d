import { sqliteTable, text, integer, real, blob } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

// Biodata KangPCode
export const biodata = sqliteTable('biodata', {
  id: integer('id').primaryKey(),
  name: text('name').notNull(),
  role: text('role').notNull(),
  education: text('education').notNull(),
  university: text('university').notNull(),
  status: text('status').notNull(),
  bio: text('bio').notNull(),
  github: text('github').notNull(),
  email: text('email'),
  phone: text('phone'),
  location: text('location'),
  avatar: text('avatar'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
});

// <PERSON>uku "Langkah Kode Nusantara"
export const book = sqliteTable('book', {
  id: integer('id').primaryKey(),
  title: text('title').notNull(),
  description: text('description').notNull(),
  githubUrl: text('github_url').notNull(),
  pdfUrl: text('pdf_url'),
  coverImage: text('cover_image'),
  chapters: text('chapters'), // JSON string
  skills: text('skills'), // JSON string
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
});

// ID Card Information
export const idCard = sqliteTable('id_card', {
  id: integer('id').primaryKey(),
  name: text('name').notNull(),
  role: text('role').notNull(),
  status: text('status').notNull(),
  photo: text('photo').notNull(),
  qrCode: text('qr_code').notNull(),
  githubUrl: text('github_url').notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
});

// Projects
export const project = sqliteTable('project', {
  id: integer('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description').notNull(),
  stack: text('stack').notNull(), // JSON string
  previewImage: text('preview_image'),
  demoUrl: text('demo_url'),
  githubUrl: text('github_url'),
  status: text('status').notNull(), // 'completed', 'in-progress', 'planned'
  featured: integer('featured', { mode: 'boolean' }).default(false),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
});

// Scientists/Ilmuwan
export const scientist = sqliteTable('scientist', {
  id: integer('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description').notNull(),
  contribution: text('contribution').notNull(),
  image: text('image'),
  category: text('category').notNull(), // 'teknologi', 'nusantara', 'dunia'
  birthYear: integer('birth_year'),
  deathYear: integer('death_year'),
  nationality: text('nationality'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
});

// Action Figures
export const figure = sqliteTable('figure', {
  id: integer('id').primaryKey(),
  name: text('name').notNull(),
  anime: text('anime').notNull(),
  quote: text('quote').notNull(),
  philosophy: text('philosophy').notNull(),
  image: text('image'),
  position: text('position'), // JSON string for 3D position
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
});

// Analytics
export const analytics = sqliteTable('analytics', {
  id: integer('id').primaryKey(),
  event: text('event').notNull(), // 'click', 'hover', 'visit', 'download'
  target: text('target').notNull(), // component/element name
  data: text('data'), // JSON string for additional data
  userAgent: text('user_agent'),
  ip: text('ip'),
  timestamp: integer('timestamp', { mode: 'timestamp' }).notNull(),
});

// Mini Game Scores
export const gameScore = sqliteTable('game_score', {
  id: integer('id').primaryKey(),
  game: text('game').notNull(), // 'snake', 'tictactoe', 'trivia'
  score: integer('score').notNull(),
  playerName: text('player_name'),
  data: text('data'), // JSON string for game-specific data
  timestamp: integer('timestamp', { mode: 'timestamp' }).notNull(),
});

// User Settings
export const userSettings = sqliteTable('user_settings', {
  id: integer('id').primaryKey(),
  key: text('key').notNull().unique(),
  value: text('value').notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
});

// User Sessions table - Track user sessions
export const userSession = sqliteTable('user_session', {
  id: integer('id').primaryKey(),
  sessionId: text('session_id').notNull().unique(),
  userAgent: text('user_agent'),
  ip: text('ip'),
  country: text('country'),
  city: text('city'),
  device: text('device'),
  browser: text('browser'),
  os: text('os'),
  referrer: text('referrer'),
  landingPage: text('landing_page'),
  language: text('language'),
  theme: text('theme'), // 'light', 'dark'
  musicEnabled: integer('music_enabled', { mode: 'boolean' }).default(false),
  startTime: integer('start_time', { mode: 'timestamp' }).default(sql`(unixepoch())`),
  endTime: integer('end_time', { mode: 'timestamp' }),
  duration: integer('duration'), // in seconds
  pageViews: integer('page_views').default(0),
  interactions: integer('interactions').default(0),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`(unixepoch())`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`(unixepoch())`),
});

// Performance Metrics table - Track performance data
export const performanceMetric = sqliteTable('performance_metric', {
  id: integer('id').primaryKey(),
  sessionId: text('session_id').notNull(),
  metric: text('metric').notNull(), // 'FCP', 'LCP', 'FID', 'CLS', etc.
  value: real('value').notNull(),
  unit: text('unit'), // 'ms', 'score', etc.
  page: text('page'),
  device: text('device'),
  connection: text('connection'),
  timestamp: integer('timestamp', { mode: 'timestamp' }).default(sql`(unixepoch())`),
});

// Error Logs table - Track application errors
export const errorLog = sqliteTable('error_log', {
  id: integer('id').primaryKey(),
  sessionId: text('session_id'),
  error: text('error').notNull(),
  stack: text('stack'),
  context: text('context'),
  page: text('page'),
  userAgent: text('user_agent'),
  timestamp: integer('timestamp', { mode: 'timestamp' }).default(sql`(unixepoch())`),
});

// Export all tables for easy access
export const tables = {
  biodata,
  book,
  idCard,
  project,
  scientist,
  figure,
  analytics,
  gameScore,
  userSettings,
  userSession,
  performanceMetric,
  errorLog,
};

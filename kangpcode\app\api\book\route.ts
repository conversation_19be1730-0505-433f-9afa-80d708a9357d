import { NextRequest, NextResponse } from 'next/server';

export async function GET() {
  try {
    // Return static book data for now
    // In production, this would fetch from database
    const bookData = {
      id: 1,
      title: 'Lang<PERSON>h Kode Nusantara',
      subtitle: '<PERSON><PERSON><PERSON><PERSON> & <PERSON><PERSON><PERSON> dalam Dunia Teknologi Indonesia',
      author: '<PERSON><PERSON><PERSON> (KangPCode)',
      description: '<PERSON><PERSON><PERSON> panduan komprehensif untuk developer Indonesia yang ingin membangun karir di dunia teknologi dengan tetap mengangkat nilai-nilai lokal dan berkontribusi untuk kemajuan bangsa.',
      githubUrl: 'https://github.com/kangpcode/langkah-kode-nusantara',
      pdfUrl: '/assets/book/langkah-kode-nusantara-sample.pdf',
      coverImage: '/assets/images/book/cover.jpg',
      chapters: [
        { number: 1, title: 'Pengenalan Dunia Teknologi Indonesia', pages: 45 },
        { number: 2, title: 'Dasar-Dasar Programming', pages: 60 },
        { number: 3, title: 'Web Development Modern', pages: 80 },
        { number: 4, title: 'Mobile Development', pages: 55 },
        { number: 5, title: 'DevOps & Deployment', pages: 50 },
        { number: 6, title: 'Membangun Karir Tech di Indonesia', pages: 60 }
      ],
      skills: [
        'JavaScript & TypeScript',
        'React & Next.js',
        'Node.js & Express',
        'Python & Django',
        'Database Design',
        'Docker & Kubernetes',
        'AWS & Cloud Computing',
        'Git & Version Control'
      ],
      stats: {
        pages: 350,
        chapters: 6,
        codeExamples: 150,
        projects: 12,
        downloads: 1250,
        rating: 4.8
      },
      featured: true,
      reviews: [
        {
          name: 'Ahmad Rizki',
          rating: 5,
          comment: 'Buku yang sangat membantu untuk developer Indonesia. Kontennya praktis dan mudah dipahami.'
        },
        {
          name: 'Sari Dewi',
          rating: 5,
          comment: 'Panduan yang lengkap untuk memulai karir di tech. Sangat recommended!'
        },
        {
          name: 'Budi Santoso',
          rating: 4,
          comment: 'Konten berkualitas dengan pendekatan yang unik untuk konteks Indonesia.'
        }
      ],
      tableOfContents: [
        {
          chapter: 1,
          title: 'Pengenalan Dunia Teknologi Indonesia',
          sections: [
            'Sejarah Perkembangan Teknologi di Indonesia',
            'Ekosistem Startup dan Tech Company',
            'Peluang Karir di Industri Teknologi',
            'Tantangan dan Solusi untuk Developer Lokal'
          ]
        },
        {
          chapter: 2,
          title: 'Dasar-Dasar Programming',
          sections: [
            'Memilih Bahasa Pemrograman Pertama',
            'Konsep Fundamental Programming',
            'Best Practices dan Clean Code',
            'Version Control dengan Git'
          ]
        },
        {
          chapter: 3,
          title: 'Web Development Modern',
          sections: [
            'HTML, CSS, dan JavaScript Modern',
            'Framework Frontend (React, Vue, Angular)',
            'Backend Development dengan Node.js',
            'Database dan API Design'
          ]
        },
        {
          chapter: 4,
          title: 'Mobile Development',
          sections: [
            'Native vs Cross-Platform Development',
            'React Native untuk Pemula',
            'Flutter Development',
            'Publishing ke App Store dan Play Store'
          ]
        },
        {
          chapter: 5,
          title: 'DevOps & Deployment',
          sections: [
            'Containerization dengan Docker',
            'CI/CD Pipeline',
            'Cloud Computing (AWS, GCP, Azure)',
            'Monitoring dan Logging'
          ]
        },
        {
          chapter: 6,
          title: 'Membangun Karir Tech di Indonesia',
          sections: [
            'Networking dan Community Building',
            'Portfolio dan Personal Branding',
            'Interview Preparation',
            'Freelancing vs Full-time Employment'
          ]
        }
      ],
      createdAt: '2024-01-15T00:00:00Z',
      updatedAt: '2024-01-15T00:00:00Z'
    };

    return NextResponse.json(bookData);
  } catch (error) {
    console.error('Book data fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch book data' },
      { status: 500 }
    );
  }
}

# 🌐 KangPCode Portfolio 3D

> **Interactive 3D Portfolio Website** - Explore technology, projects, and inspiration in a game-like virtual environment

[![Next.js](https://img.shields.io/badge/Next.js-14-black?style=for-the-badge&logo=next.js)](https://nextjs.org/)
[![Three.js](https://img.shields.io/badge/Three.js-Latest-blue?style=for-the-badge&logo=three.js)](https://threejs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue?style=for-the-badge&logo=typescript)](https://www.typescriptlang.org/)
[![TailwindCSS](https://img.shields.io/badge/TailwindCSS-3.0-38B2AC?style=for-the-badge&logo=tailwind-css)](https://tailwindcss.com/)
[![PWA](https://img.shields.io/badge/PWA-Ready-green?style=for-the-badge)](https://web.dev/progressive-web-apps/)

## 🎯 About

**KangPCode Portfolio 3D** adalah website portfolio interaktif berbasis 3D yang menampilkan perjalanan karir **Dhafa Nazula Permadi (KangPCode)** sebagai fullstack developer dan content creator Indonesia. Website ini menghadirkan pengalaman eksplorasi seperti game dengan berbagai fitur edukatif dan informatif.

### ✨ Key Features

- 🎮 **Game-like 3D Experience** - Eksplorasi kamar virtual dengan interaksi real-time
- 🖥️ **Interactive Computer (KDE Plasma)** - Desktop environment dengan Terminal AI dan aplikasi
- 💻 **GitHub Viewer (Windows)** - Live integration dengan GitHub API
- 📚 **Digital Book** - "Langkah Kode Nusantara" dengan konten edukatif
- 🪪 **Interactive ID Card** - Personal information dengan QR code
- 👨‍🔬 **Educational Posters** - Profil ilmuwan teknologi dunia dan nusantara
- 🧸 **Action Figure Collection** - Quotes filosofis dari karakter anime
- 📌 **Project Whiteboard** - Showcase proyek dengan detail teknis
- 🎮 **Mini Games** - Snake, TicTacToe, dan Tech Trivia
- 📄 **PDF Resume Generator** - Generate resume real-time
- 🌓 **Dark/Light Mode** - Theme switching dengan animasi smooth
- 🌐 **Multilingual** - Support Bahasa Indonesia dan English
- 📱 **PWA Ready** - Installable sebagai aplikasi mobile
- 🔒 **Security Features** - Anti right-click dan developer tools protection
- 📊 **Analytics** - Local analytics dengan SQLite

## 🛠️ Tech Stack

### Frontend
- **Next.js 14** - React framework dengan App Router
- **Three.js** - 3D graphics library
- **@react-three/fiber** - React renderer untuk Three.js
- **@react-three/drei** - Useful helpers untuk R3F
- **TailwindCSS** - Utility-first CSS framework
- **Framer Motion** - Animation library
- **TypeScript** - Type-safe JavaScript

### Backend & Database
- **Bun** - Fast JavaScript runtime dan package manager
- **SQLite** - Lightweight database
- **Drizzle ORM** - Type-safe ORM untuk TypeScript
- **Next.js API Routes** - Serverless API endpoints

### PWA & Optimization
- **next-pwa** - Progressive Web App support
- **next-i18next** - Internationalization
- **Workbox** - Service worker untuk caching

## 🚀 Quick Start

### Prerequisites
- **Bun** >= 1.0.0
- **Node.js** >= 18.0.0

### Installation

```bash
# Clone repository
git clone https://github.com/kangpcode/portfolio-3d.git
cd portfolio-3d

# Install dependencies
bun install

# Setup database
bun run db:generate
bun run db:migrate

# Start development server
bun run dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## 🎮 Features Guide

### 3D Room Navigation
- **Mouse**: Orbit around the room
- **Scroll**: Zoom in/out
- **Click Objects**: Interact with 3D elements

### Interactive Elements
- **🖥️ Computer**: KDE Plasma desktop with apps
- **💻 Laptop**: GitHub repository viewer
- **📚 Book**: Digital book reader
- **🪪 ID Card**: Personal information
- **📌 Whiteboard**: Project showcase
- **👨‍🔬 Posters**: Scientist profiles
- **🧸 Figures**: Anime character quotes

### Applications
- **🧠 Terminal AI**: Interactive CLI assistant
- **📄 Resume Generator**: PDF export functionality
- **🎮 Mini Games**: Entertainment features
- **🎵 Music Player**: Background ambient music

## 🌐 PWA Features

- **📱 Installable**: Add to home screen
- **⚡ Fast Loading**: Service worker caching
- **🔄 Offline Support**: Works without internet
- **📊 Background Sync**: Analytics when online

## 🔒 Security Features

- **🚫 Right-click Protection**: Disabled context menu
- **⌨️ Keyboard Shortcuts**: Disabled dev tools shortcuts
- **🔍 DevTools Detection**: Blur content when detected
- **🖼️ Image Protection**: Disabled dragging and saving

## 👨‍💻 Author

**Dhafa Nazula Permadi (KangPCode)**
- 🌐 Website: [kangpcode.dev](https://kangpcode.dev)
- 📧 Email: <EMAIL>
- 🐙 GitHub: [@kangpcode](https://github.com/kangpcode)

---

<div align="center">

**⭐ Star this repository if you found it helpful!**

Made with ❤️ by [KangPCode](https://github.com/kangpcode)

</div>

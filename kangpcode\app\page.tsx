'use client';

import { useState, useEffect } from 'react';
import LoadingScreen from '@/components/LoadingScreen';
import RoomScene3D from '@/components/RoomScene3D';
import { motion, AnimatePresence } from 'framer-motion';
import { useAnalytics } from '@/lib/analytics';
import { useSecurity } from '@/lib/security';

type SceneType = 'loading' | 'intro' | 'room';

export default function Home() {
  const [currentScene, setCurrentScene] = useState<SceneType>('loading');
  const { trackPageView, trackClick } = useAnalytics();
  const { initializeSecurity } = useSecurity();

  // Initialize security and analytics
  useEffect(() => {
    initializeSecurity();
    trackPageView('home');
  }, [initializeSecurity, trackPageView]);

  const handleLoadingComplete = () => {
    setCurrentScene('intro');
    trackPageView('intro');
  };

  const handleEnterRoom = () => {
    setCurrentScene('room');
    trackClick('enter_room');
    trackPageView('room');
  };

  const handleExitRoom = () => {
    setCurrentScene('intro');
    trackClick('exit_room');
    trackPageView('intro');
  };

  return (
    <div className="w-full h-screen overflow-hidden">
      <AnimatePresence mode="wait">
        {currentScene === 'loading' && (
          <LoadingScreen key="loading" onComplete={handleLoadingComplete} />
        )}

        {currentScene === 'intro' && (
          <motion.div
            key="intro"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="w-full h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center relative overflow-hidden"
          >
            {/* Background Animation */}
            <div className="absolute inset-0">
              {[...Array(50)].map((_, i) => (
                <motion.div
                  key={i}
                  className="absolute w-1 h-1 bg-white rounded-full"
                  style={{
                    left: `${Math.random() * 100}%`,
                    top: `${Math.random() * 100}%`,
                  }}
                  animate={{
                    opacity: [0, 1, 0],
                    scale: [0, 1, 0],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: Math.random() * 3,
                  }}
                />
              ))}
            </div>

            {/* Main Content */}
            <div className="text-center z-10 space-y-8">
              <motion.h1
                initial={{ y: -50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.2 }}
                className="text-6xl md:text-8xl font-bold text-white mb-4"
              >
                KangPCode
              </motion.h1>

              <motion.p
                initial={{ y: 50, opacity: 0 }}
                animate={{ y: 0, opacity: 1 }}
                transition={{ delay: 0.4 }}
                className="text-2xl text-purple-200 mb-8"
              >
                Portfolio 3D Interaktif
              </motion.p>

              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.6 }}
                className="text-lg text-purple-300 mb-12 max-w-2xl mx-auto"
              >
                Selamat datang di dunia virtual KangPCode! Jelajahi kamar virtual yang penuh dengan teknologi,
                proyek, dan inspirasi dari seorang developer Indonesia.
              </motion.p>

              {/* Door to Room */}
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.8, type: "spring", stiffness: 200 }}
                className="relative"
              >
                <motion.button
                  onClick={handleEnterRoom}
                  className="group relative bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-bold py-4 px-8 rounded-lg text-xl shadow-2xl transition-all duration-300 transform hover:scale-105"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span className="relative z-10">🚪 Masuk ke Kamar KangPCode</span>

                  {/* Glow Effect */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-lg blur-lg opacity-30"
                    animate={{
                      scale: [1, 1.1, 1],
                      opacity: [0.3, 0.5, 0.3],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                    }}
                  />
                </motion.button>
              </motion.div>

              <motion.p
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 1 }}
                className="text-sm text-purple-400 mt-4"
              >
                Klik pintu untuk memulai petualangan 3D Anda
              </motion.p>
            </div>
          </motion.div>
        )}

        {currentScene === 'room' && (
          <motion.div
            key="room"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
          >
            <RoomScene3D onExitRoom={handleExitRoom} />
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

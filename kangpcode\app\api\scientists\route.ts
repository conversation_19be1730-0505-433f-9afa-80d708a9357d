import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');

    // Static scientists data
    const scientists = [
      {
        id: 1,
        name: '<PERSON> Tesla',
        description: 'Penemu motor induksi, listrik AC modern',
        contribution: 'Sistem tenaga listrik AC, motor induksi, teknologi nirkabel',
        category: 'teknologi',
        birthYear: 1856,
        deathYear: 1943,
        nationality: 'Serbia-Amerika',
        image: '🔌',
        quotes: [
          'The present is theirs; the future, for which I really worked, is mine.',
          'If you want to find the secrets of the universe, think in terms of energy, frequency and vibration.'
        ],
        achievements: [
          'Sistem distribusi listrik AC',
          'Motor induksi polyphase',
          'Teknologi transmisi nirkabel',
          'Lebih dari 300 paten'
        ],
        legacy: 'Teknologi listrik modern yang kita gunakan hari ini sebagian besar berdasarkan penemuan Tesla.',
        featured: true
      },
      {
        id: 2,
        name: '<PERSON><PERSON>',
        description: 'Pelopor internet Indonesia, edukator teknologi',
        contribution: 'Pengembangan internet di Indonesia, edukasi teknologi',
        category: 'nusantara',
        birthYear: 1962,
        nationality: 'Indonesia',
        image: '🌐',
        quotes: [
          'Internet adalah alat untuk mencerdaskan bangsa.',
          'Teknologi harus bisa diakses oleh semua lapisan masyarakat.'
        ],
        achievements: [
          'Pelopor internet di Indonesia',
          'Pendiri komunitas Linux Indonesia',
          'Penulis 40+ buku teknologi',
          'Advokat open source'
        ],
        legacy: 'Membangun fondasi internet Indonesia dan menginspirasi generasi teknologi lokal.',
        featured: true
      },
      {
        id: 3,
        name: 'Al-Khawarizmi',
        description: 'Bapak algoritma, dasar ilmu komputasi',
        contribution: 'Algoritma, aljabar, sistem bilangan',
        category: 'dunia',
        birthYear: 780,
        deathYear: 850,
        nationality: 'Persia',
        image: '🔢',
        quotes: [
          'That which is sought is found by methodical calculation.',
          'Mathematics is the key to understanding the universe.'
        ],
        achievements: [
          'Menciptakan konsep algoritma',
          'Mengembangkan aljabar',
          'Sistem bilangan Hindu-Arab',
          'Dasar matematika modern'
        ],
        legacy: 'Algoritma yang dia ciptakan menjadi dasar semua komputasi modern.',
        featured: true
      },
      {
        id: 4,
        name: 'B.J. Habibie',
        description: 'Bapak teknologi Indonesia, ahli pesawat terbang',
        contribution: 'Teknologi penerbangan, industri strategis Indonesia',
        category: 'nusantara',
        birthYear: 1936,
        deathYear: 2019,
        nationality: 'Indonesia',
        image: '✈️',
        quotes: [
          'Teknologi adalah kunci kemajuan bangsa.',
          'Kita harus menguasai teknologi, bukan dikuasai oleh teknologi.'
        ],
        achievements: [
          'Pengembangan pesawat N-250',
          'Menteri Riset dan Teknologi',
          'Presiden RI ke-3',
          'Pelopor industri strategis Indonesia'
        ],
        legacy: 'Membangun fondasi industri teknologi strategis Indonesia.',
        featured: true
      },
      {
        id: 5,
        name: 'Tim Berners-Lee',
        description: 'Penemu World Wide Web',
        contribution: 'World Wide Web, HTTP, HTML, URL',
        category: 'teknologi',
        birthYear: 1955,
        nationality: 'Inggris',
        image: '🌍',
        quotes: [
          'The Web as I envisaged it, we have not seen it yet.',
          'The original idea of the web was that it should be a collaborative space.'
        ],
        achievements: [
          'Menciptakan World Wide Web',
          'Mengembangkan HTTP dan HTML',
          'Direktur W3C',
          'Advokat web terbuka'
        ],
        legacy: 'Mengubah cara dunia berkomunikasi dan berbagi informasi.',
        featured: true
      },
      {
        id: 6,
        name: 'Linus Torvalds',
        description: 'Pencipta Linux kernel',
        contribution: 'Linux operating system, Git version control',
        category: 'teknologi',
        birthYear: 1969,
        nationality: 'Finlandia',
        image: '🐧',
        quotes: [
          'Talk is cheap. Show me the code.',
          'Software is like sex: it\'s better when it\'s free.'
        ],
        achievements: [
          'Menciptakan Linux kernel',
          'Mengembangkan Git',
          'Revolusi open source',
          'Mempengaruhi industri teknologi global'
        ],
        legacy: 'Linux menjadi backbone internet dan cloud computing modern.',
        featured: true
      }
    ];

    // Filter by category if specified
    let filteredScientists = scientists;
    if (category) {
      filteredScientists = scientists.filter(s => s.category === category);
    }

    return NextResponse.json(filteredScientists);
  } catch (error) {
    console.error('Scientists fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch scientists data' },
      { status: 500 }
    );
  }
}

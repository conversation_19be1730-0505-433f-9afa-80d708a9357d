{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/LoadingScreen.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface LoadingScreenProps {\n  onComplete: () => void;\n}\n\nexport default function LoadingScreen({ onComplete }: LoadingScreenProps) {\n  const [progress, setProgress] = useState(0);\n  const [loadingText, setLoadingText] = useState('Memuat Dunia KangPCode...');\n\n  const loadingSteps = [\n    'Memuat Dunia KangPCode...',\n    'Menyiapkan Kamar Virtual...',\n    'Mengaktifkan Komputer 3D...',\n    'Memuat Poster Ilmuwan...',\n    'Menyiapkan Action Figures...',\n    'Dunia KangPCode Siap!'\n  ];\n\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setProgress(prev => {\n        const newProgress = prev + 1;\n        \n        // Update loading text based on progress\n        const stepIndex = Math.floor((newProgress / 100) * loadingSteps.length);\n        if (stepIndex < loadingSteps.length) {\n          setLoadingText(loadingSteps[stepIndex]);\n        }\n\n        if (newProgress >= 100) {\n          clearInterval(interval);\n          setTimeout(() => onComplete(), 500);\n          return 100;\n        }\n        return newProgress;\n      });\n    }, 50); // Complete in ~5 seconds\n\n    return () => clearInterval(interval);\n  }, [onComplete]);\n\n  return (\n    <AnimatePresence>\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n        className=\"fixed inset-0 bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center z-50\"\n      >\n        <div className=\"text-center space-y-8\">\n          {/* Logo/Avatar */}\n          <motion.div\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\n            className=\"w-32 h-32 mx-auto bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center shadow-2xl\"\n          >\n            <span className=\"text-4xl font-bold text-white\">KC</span>\n          </motion.div>\n\n          {/* Title */}\n          <motion.h1\n            initial={{ y: 20, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ delay: 0.4 }}\n            className=\"text-4xl md:text-6xl font-bold text-white mb-4\"\n          >\n            KangPCode\n          </motion.h1>\n\n          <motion.p\n            initial={{ y: 20, opacity: 0 }}\n            animate={{ y: 0, opacity: 1 }}\n            transition={{ delay: 0.6 }}\n            className=\"text-xl text-blue-200 mb-8\"\n          >\n            Portfolio 3D Interaktif\n          </motion.p>\n\n          {/* Loading Bar */}\n          <div className=\"w-80 mx-auto\">\n            <motion.div\n              initial={{ width: 0 }}\n              animate={{ width: '100%' }}\n              transition={{ delay: 0.8 }}\n              className=\"h-2 bg-gray-700 rounded-full overflow-hidden\"\n            >\n              <motion.div\n                className=\"h-full bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full\"\n                style={{ width: `${progress}%` }}\n                transition={{ duration: 0.1 }}\n              />\n            </motion.div>\n            \n            <div className=\"flex justify-between mt-2 text-sm text-blue-200\">\n              <span>{progress}%</span>\n              <span>Loading...</span>\n            </div>\n          </div>\n\n          {/* Loading Text */}\n          <motion.p\n            key={loadingText}\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -10 }}\n            className=\"text-lg text-cyan-300 font-medium\"\n          >\n            {loadingText}\n          </motion.p>\n\n          {/* Animated Dots */}\n          <div className=\"flex justify-center space-x-2\">\n            {[0, 1, 2].map((i) => (\n              <motion.div\n                key={i}\n                className=\"w-3 h-3 bg-cyan-400 rounded-full\"\n                animate={{\n                  scale: [1, 1.2, 1],\n                  opacity: [0.5, 1, 0.5],\n                }}\n                transition={{\n                  duration: 1.5,\n                  repeat: Infinity,\n                  delay: i * 0.2,\n                }}\n              />\n            ))}\n          </div>\n        </div>\n      </motion.div>\n    </AnimatePresence>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AASe,SAAS,cAAc,EAAE,UAAU,EAAsB;;IACtE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe;QACnB;QACA;QACA;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,WAAW;oDAAY;oBAC3B;4DAAY,CAAA;4BACV,MAAM,cAAc,OAAO;4BAE3B,wCAAwC;4BACxC,MAAM,YAAY,KAAK,KAAK,CAAC,AAAC,cAAc,MAAO,aAAa,MAAM;4BACtE,IAAI,YAAY,aAAa,MAAM,EAAE;gCACnC,eAAe,YAAY,CAAC,UAAU;4BACxC;4BAEA,IAAI,eAAe,KAAK;gCACtB,cAAc;gCACd;wEAAW,IAAM;uEAAc;gCAC/B,OAAO;4BACT;4BACA,OAAO;wBACT;;gBACF;mDAAG,KAAK,yBAAyB;YAEjC;2CAAO,IAAM,cAAc;;QAC7B;kCAAG;QAAC;KAAW;IAEf,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACd,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,WAAU;sBAEV,cAAA,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BAAE,OAAO;4BAAK,MAAM;4BAAU,WAAW;wBAAI;wBACzD,WAAU;kCAEV,cAAA,6LAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;kCAIlD,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCACX;;;;;;kCAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,SAAS;4BAAE,GAAG;4BAAI,SAAS;wBAAE;wBAC7B,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;wBACzB,WAAU;kCACX;;;;;;kCAKD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAO;gCACzB,YAAY;oCAAE,OAAO;gCAAI;gCACzB,WAAU;0CAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,SAAS,CAAC,CAAC;oCAAC;oCAC/B,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;0CAIhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;4CAAM;4CAAS;;;;;;;kDAChB,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAKV,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBAEP,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,MAAM;4BAAE,SAAS;4BAAG,GAAG,CAAC;wBAAG;wBAC3B,WAAU;kCAET;uBANI;;;;;kCAUP,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,SAAS;oCACP,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;oCAClB,SAAS;wCAAC;wCAAK;wCAAG;qCAAI;gCACxB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,OAAO,IAAI;gCACb;+BAVK;;;;;;;;;;;;;;;;;;;;;;;;;;AAkBrB;GAhIwB;KAAA", "debugId": null}}, {"offset": {"line": 290, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/ComputerGUI.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface ComputerGUIProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\ntype AppType = 'terminal' | 'resume' | 'projects' | 'games' | 'vscode' | 'browser' | 'settings' | 'info';\n\nexport default function ComputerGUI({ isVisible, onClose }: ComputerGUIProps) {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [activeApp, setActiveApp] = useState<AppType | null>(null);\n  const [isBooting, setIsBooting] = useState(true);\n\n  // Update time every second\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Boot sequence\n  useEffect(() => {\n    if (isVisible) {\n      setIsBooting(true);\n      const bootTimer = setTimeout(() => {\n        setIsBooting(false);\n      }, 2000);\n      return () => clearTimeout(bootTimer);\n    }\n  }, [isVisible]);\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString('id-ID', { \n      hour: '2-digit', \n      minute: '2-digit',\n      second: '2-digit'\n    });\n  };\n\n  const formatDate = (date: Date) => {\n    return date.toLocaleDateString('id-ID', { \n      weekday: 'long',\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric'\n    });\n  };\n\n  const desktopApps = [\n    { id: 'terminal', name: 'Terminal AI', icon: '🧠', color: 'bg-green-600' },\n    { id: 'resume', name: 'Resume', icon: '📄', color: 'bg-blue-600' },\n    { id: 'projects', name: 'My Projects', icon: '💻', color: 'bg-purple-600' },\n    { id: 'games', name: 'Mini Games', icon: '🎮', color: 'bg-red-600' },\n  ];\n\n  const taskbarApps = [\n    { id: 'terminal', name: 'Terminal AI', icon: '🧠' },\n    { id: 'vscode', name: 'VSCode', icon: '💻' },\n    { id: 'browser', name: 'Browser', icon: '🌐' },\n    { id: 'settings', name: 'Settings', icon: '⚙️' },\n    { id: 'info', name: 'System Info', icon: '🧮' },\n    { id: 'games', name: 'Games', icon: '🎮' },\n  ];\n\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50 overflow-hidden\">\n      <AnimatePresence>\n        {isBooting ? (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"flex items-center justify-center h-full bg-black text-white\"\n          >\n            <div className=\"text-center space-y-4\">\n              <div className=\"text-6xl mb-4\">🐧</div>\n              <h2 className=\"text-2xl font-bold\">KangPCode ArchLinux</h2>\n              <p className=\"text-lg\">Booting KDE Plasma...</p>\n              <div className=\"flex justify-center space-x-1 mt-4\">\n                {[0, 1, 2].map((i) => (\n                  <motion.div\n                    key={i}\n                    className=\"w-3 h-3 bg-blue-500 rounded-full\"\n                    animate={{\n                      scale: [1, 1.2, 1],\n                      opacity: [0.5, 1, 0.5],\n                    }}\n                    transition={{\n                      duration: 1,\n                      repeat: Infinity,\n                      delay: i * 0.2,\n                    }}\n                  />\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        ) : (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"h-full bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 relative\"\n          >\n            {/* Desktop Wallpaper */}\n            <div className=\"absolute inset-0 bg-gradient-to-br from-blue-800/50 to-purple-800/50\" />\n            \n            {/* Top Panel */}\n            <div className=\"absolute top-0 left-0 right-0 h-8 bg-gray-900/90 backdrop-blur-sm border-b border-gray-700 flex items-center justify-between px-4 text-white text-sm z-10\">\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"font-bold\">kangpcode@archlinux</span>\n                <span>KDE Plasma 5.27</span>\n              </div>\n              <div className=\"flex items-center space-x-4\">\n                <span>{formatDate(currentTime)}</span>\n                <span className=\"font-mono\">{formatTime(currentTime)}</span>\n                <button\n                  onClick={onClose}\n                  className=\"hover:bg-red-600 px-2 py-1 rounded transition-colors\"\n                >\n                  ✕\n                </button>\n              </div>\n            </div>\n\n            {/* Desktop Icons */}\n            <div className=\"absolute top-12 left-4 grid grid-cols-1 gap-4 z-10\">\n              {desktopApps.map((app) => (\n                <motion.button\n                  key={app.id}\n                  onClick={() => setActiveApp(app.id as AppType)}\n                  className=\"flex flex-col items-center space-y-1 p-2 rounded hover:bg-white/10 transition-colors group\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <div className={`w-12 h-12 ${app.color} rounded-lg flex items-center justify-center text-2xl shadow-lg group-hover:shadow-xl transition-shadow`}>\n                    {app.icon}\n                  </div>\n                  <span className=\"text-white text-xs font-medium\">{app.name}</span>\n                </motion.button>\n              ))}\n            </div>\n\n            {/* Bottom Taskbar */}\n            <div className=\"absolute bottom-0 left-0 right-0 h-12 bg-gray-900/90 backdrop-blur-sm border-t border-gray-700 flex items-center justify-between px-4 z-10\">\n              {/* Start Menu */}\n              <button className=\"flex items-center space-x-2 px-3 py-2 hover:bg-gray-700/50 rounded transition-colors text-white\">\n                <span className=\"text-xl\">🐧</span>\n                <span className=\"font-medium\">Menu</span>\n              </button>\n\n              {/* App Icons */}\n              <div className=\"flex items-center space-x-2\">\n                {taskbarApps.map((app) => (\n                  <motion.button\n                    key={app.id}\n                    onClick={() => setActiveApp(app.id as AppType)}\n                    className={`w-10 h-10 rounded-lg flex items-center justify-center text-xl transition-colors ${\n                      activeApp === app.id \n                        ? 'bg-blue-600 text-white' \n                        : 'hover:bg-gray-700/50 text-gray-300'\n                    }`}\n                    whileHover={{ scale: 1.1 }}\n                    whileTap={{ scale: 0.9 }}\n                    title={app.name}\n                  >\n                    {app.icon}\n                  </motion.button>\n                ))}\n              </div>\n\n              {/* System Tray */}\n              <div className=\"flex items-center space-x-2 text-white\">\n                <span className=\"text-sm\">🔊 🔋 📶</span>\n                <span className=\"text-sm font-mono\">{formatTime(currentTime)}</span>\n              </div>\n            </div>\n\n            {/* Active Application Window */}\n            <AnimatePresence>\n              {activeApp && (\n                <motion.div\n                  initial={{ opacity: 0, scale: 0.9 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  exit={{ opacity: 0, scale: 0.9 }}\n                  className=\"absolute inset-8 bg-gray-800 rounded-lg shadow-2xl border border-gray-600 overflow-hidden z-20\"\n                >\n                  {/* Window Title Bar */}\n                  <div className=\"h-8 bg-gray-700 flex items-center justify-between px-4 border-b border-gray-600\">\n                    <span className=\"text-white font-medium\">\n                      {taskbarApps.find(app => app.id === activeApp)?.name}\n                    </span>\n                    <div className=\"flex space-x-2\">\n                      <button className=\"w-4 h-4 bg-yellow-500 rounded-full hover:bg-yellow-400\"></button>\n                      <button className=\"w-4 h-4 bg-green-500 rounded-full hover:bg-green-400\"></button>\n                      <button \n                        onClick={() => setActiveApp(null)}\n                        className=\"w-4 h-4 bg-red-500 rounded-full hover:bg-red-400\"\n                      ></button>\n                    </div>\n                  </div>\n\n                  {/* Window Content */}\n                  <div className=\"flex-1 p-4 text-white overflow-auto\">\n                    {activeApp === 'terminal' && (\n                      <div className=\"font-mono text-green-400 space-y-2\">\n                        <div>KangPCode Terminal AI v1.0</div>\n                        <div>Type 'help' for available commands</div>\n                        <div className=\"text-white\">$ _</div>\n                      </div>\n                    )}\n                    \n                    {activeApp === 'resume' && (\n                      <div className=\"text-center space-y-4\">\n                        <h3 className=\"text-xl font-bold\">Resume Generator</h3>\n                        <p>Generate PDF resume dari data KangPCode</p>\n                        <button className=\"bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors\">\n                          Generate PDF\n                        </button>\n                      </div>\n                    )}\n                    \n                    {activeApp === 'projects' && (\n                      <div className=\"space-y-4\">\n                        <h3 className=\"text-xl font-bold\">My Projects</h3>\n                        <div className=\"grid grid-cols-2 gap-4\">\n                          <div className=\"bg-gray-700 p-4 rounded\">\n                            <h4 className=\"font-bold\">Portfolio 3D</h4>\n                            <p className=\"text-sm text-gray-300\">Interactive 3D portfolio website</p>\n                          </div>\n                          <div className=\"bg-gray-700 p-4 rounded\">\n                            <h4 className=\"font-bold\">Langkah Kode</h4>\n                            <p className=\"text-sm text-gray-300\">Educational programming book</p>\n                          </div>\n                        </div>\n                      </div>\n                    )}\n                    \n                    {activeApp === 'games' && (\n                      <div className=\"text-center space-y-4\">\n                        <h3 className=\"text-xl font-bold\">Mini Games</h3>\n                        <div className=\"grid grid-cols-3 gap-4\">\n                          <button className=\"bg-green-600 hover:bg-green-700 p-4 rounded transition-colors\">\n                            🐍 Snake\n                          </button>\n                          <button className=\"bg-blue-600 hover:bg-blue-700 p-4 rounded transition-colors\">\n                            ⭕ TicTacToe\n                          </button>\n                          <button className=\"bg-purple-600 hover:bg-purple-700 p-4 rounded transition-colors\">\n                            🧠 Trivia\n                          </button>\n                        </div>\n                      </div>\n                    )}\n                    \n                    {(activeApp === 'vscode' || activeApp === 'browser' || activeApp === 'settings' || activeApp === 'info') && (\n                      <div className=\"text-center space-y-4\">\n                        <h3 className=\"text-xl font-bold\">Coming Soon</h3>\n                        <p>Aplikasi ini sedang dalam pengembangan</p>\n                      </div>\n                    )}\n                  </div>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAYe,SAAS,YAAY,EAAE,SAAS,EAAE,OAAO,EAAoB;;IAC1E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,QAAQ;+CAAY;oBACxB,eAAe,IAAI;gBACrB;8CAAG;YACH;yCAAO,IAAM,cAAc;;QAC7B;gCAAG,EAAE;IAEL,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,IAAI,WAAW;gBACb,aAAa;gBACb,MAAM,YAAY;uDAAW;wBAC3B,aAAa;oBACf;sDAAG;gBACH;6CAAO,IAAM,aAAa;;YAC5B;QACF;gCAAG;QAAC;KAAU;IAEd,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,SAAS;YACT,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,cAAc;QAClB;YAAE,IAAI;YAAY,MAAM;YAAe,MAAM;YAAM,OAAO;QAAe;QACzE;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM;YAAM,OAAO;QAAc;QACjE;YAAE,IAAI;YAAY,MAAM;YAAe,MAAM;YAAM,OAAO;QAAgB;QAC1E;YAAE,IAAI;YAAS,MAAM;YAAc,MAAM;YAAM,OAAO;QAAa;KACpE;IAED,MAAM,cAAc;QAClB;YAAE,IAAI;YAAY,MAAM;YAAe,MAAM;QAAK;QAClD;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM;QAAK;QAC3C;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAK;QAC7C;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;QAC/C;YAAE,IAAI;YAAQ,MAAM;YAAe,MAAM;QAAK;QAC9C;YAAE,IAAI;YAAS,MAAM;YAAS,MAAM;QAAK;KAC1C;IAED,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;sBACb,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,6LAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,6LAAC;4BAAE,WAAU;sCAAU;;;;;;sCACvB,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;wCAClB,SAAS;4CAAC;4CAAK;4CAAG;yCAAI;oCACxB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO,IAAI;oCACb;mCAVK;;;;;;;;;;;;;;;;;;;;qCAiBf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;;kCAGV,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAY;;;;;;kDAC5B,6LAAC;kDAAK;;;;;;;;;;;;0CAER,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAM,WAAW;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAa,WAAW;;;;;;kDACxC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,6LAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,oBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,6LAAC;wCAAI,WAAW,CAAC,UAAU,EAAE,IAAI,KAAK,CAAC,uGAAuG,CAAC;kDAC5I,IAAI,IAAI;;;;;;kDAEX,6LAAC;wCAAK,WAAU;kDAAkC,IAAI,IAAI;;;;;;;+BATrD,IAAI,EAAE;;;;;;;;;;kCAejB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAIhC,6LAAC;gCAAI,WAAU;0CACZ,YAAY,GAAG,CAAC,CAAC,oBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,SAAS,IAAM,aAAa,IAAI,EAAE;wCAClC,WAAW,CAAC,gFAAgF,EAC1F,cAAc,IAAI,EAAE,GAChB,2BACA,sCACJ;wCACF,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;wCACvB,OAAO,IAAI,IAAI;kDAEd,IAAI,IAAI;uCAXJ,IAAI,EAAE;;;;;;;;;;0CAiBjB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAK,WAAU;kDAAqB,WAAW;;;;;;;;;;;;;;;;;;kCAKpD,6LAAC,4LAAA,CAAA,kBAAe;kCACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,MAAM;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAC/B,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDACb,YAAY,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK,YAAY;;;;;;sDAElD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAO,WAAU;;;;;;8DAClB,6LAAC;oDAAO,WAAU;;;;;;8DAClB,6LAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAU;;;;;;;;;;;;;;;;;;8CAMhB,6LAAC;oCAAI,WAAU;;wCACZ,cAAc,4BACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAI;;;;;;8DACL,6LAAC;8DAAI;;;;;;8DACL,6LAAC;oDAAI,WAAU;8DAAa;;;;;;;;;;;;wCAI/B,cAAc,0BACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,6LAAC;8DAAE;;;;;;8DACH,6LAAC;oDAAO,WAAU;8DAAoE;;;;;;;;;;;;wCAMzF,cAAc,4BACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAY;;;;;;8EAC1B,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;sEAEvC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAY;;;;;;8EAC1B,6LAAC;oEAAE,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;;;;;;;wCAM5C,cAAc,yBACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAO,WAAU;sEAAgE;;;;;;sEAGlF,6LAAC;4DAAO,WAAU;sEAA8D;;;;;;sEAGhF,6LAAC;4DAAO,WAAU;sEAAkE;;;;;;;;;;;;;;;;;;wCAOzF,CAAC,cAAc,YAAY,cAAc,aAAa,cAAc,cAAc,cAAc,MAAM,mBACrG,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,6LAAC;8DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY3B;GAxQwB;KAAA", "debugId": null}}, {"offset": {"line": 1028, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/TerminalAI.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { motion } from 'framer-motion';\n\ninterface TerminalAIProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\ninterface TerminalLine {\n  type: 'input' | 'output' | 'error';\n  content: string;\n  timestamp: Date;\n}\n\nexport default function TerminalAI({ isVisible, onClose }: TerminalAIProps) {\n  const [input, setInput] = useState('');\n  const [history, setHistory] = useState<TerminalLine[]>([]);\n  const [isTyping, setIsTyping] = useState(false);\n  const terminalRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // AI Knowledge Base\n  const knowledgeBase = {\n    'siapa kangpcode': 'KangPCode ad<PERSON><PERSON>, developer dan kreator teknologi Nusantara yang berfokus pada pengembangan aplikasi web modern dan edukasi teknologi.',\n    'pendidikan': 'Informatika – Universitas XYZ, dengan fokus pada pengembangan web dan teknologi modern.',\n    'skill': 'Next.js, TailwindCSS, SQLite, Laravel, Bun, React, PWA, Three.js, TypeScript, Python, Docker, dan teknologi web modern lainnya.',\n    'pengalaman': 'Magang di CV Bintang Gumilang, freelance proyek TI lokal, dan berbagai proyek pengembangan web untuk klien Indonesia.',\n    'hobi': 'Ngoding, mempelajari sejarah tokoh teknologi, menonton anime, menulis, dan berbagi pengetahuan teknologi.',\n    'buku': 'Langkah Kode Nusantara - Perjalanan belajar dan berkarya dalam dunia teknologi Indonesia (GitHub: kangpcode/langkah-kode-nusantara)',\n    'proyek': 'Portfolio 3D Interaktif, Langkah Kode Nusantara, berbagai aplikasi web dengan Next.js dan React, serta proyek edukasi teknologi.',\n    'kontak': 'GitHub: github.com/kangpcode | Email: <EMAIL> | Status: Open for Collaboration',\n    'teknologi': 'Spesialisasi dalam JavaScript/TypeScript ecosystem, React/Next.js, modern CSS frameworks, database design, dan 3D web development.',\n    'visi': 'Membangun ekosistem teknologi Indonesia yang kuat melalui edukasi, open source, dan kolaborasi komunitas developer lokal.',\n  };\n\n  const commands = {\n    'help': 'Perintah yang tersedia:\\n- siapa kangpcode\\n- pendidikan\\n- skill\\n- pengalaman\\n- hobi\\n- buku\\n- proyek\\n- kontak\\n- teknologi\\n- visi\\n- clear\\n- help',\n    'clear': 'CLEAR_TERMINAL',\n    'ls': 'projects/\\nbooks/\\nskills/\\ncontacts/\\nexperience/',\n    'pwd': '/home/<USER>/portfolio',\n    'whoami': 'kangpcode (Dhafa Nazula Permadi)',\n    'date': new Date().toLocaleString('id-ID'),\n    'uname': 'KangPCode Terminal AI v1.0 - Interactive Portfolio Assistant',\n  };\n\n  useEffect(() => {\n    if (isVisible) {\n      setHistory([\n        {\n          type: 'output',\n          content: '🧠 KangPCode Terminal AI v1.0\\nSelamat datang! Ketik \"help\" untuk melihat perintah yang tersedia.\\nAtau tanyakan tentang KangPCode dengan bahasa natural.',\n          timestamp: new Date()\n        }\n      ]);\n      setTimeout(() => {\n        inputRef.current?.focus();\n      }, 100);\n    }\n  }, [isVisible]);\n\n  useEffect(() => {\n    if (terminalRef.current) {\n      terminalRef.current.scrollTop = terminalRef.current.scrollHeight;\n    }\n  }, [history]);\n\n  const processCommand = async (cmd: string) => {\n    const command = cmd.toLowerCase().trim();\n    \n    // Add user input to history\n    setHistory(prev => [...prev, {\n      type: 'input',\n      content: `$ ${cmd}`,\n      timestamp: new Date()\n    }]);\n\n    setIsTyping(true);\n\n    // Simulate AI thinking delay\n    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));\n\n    let response = '';\n\n    // Check exact commands first\n    if (commands[command as keyof typeof commands]) {\n      const result = commands[command as keyof typeof commands];\n      if (result === 'CLEAR_TERMINAL') {\n        setHistory([]);\n        setIsTyping(false);\n        return;\n      }\n      response = result;\n    }\n    // Check knowledge base\n    else if (knowledgeBase[command as keyof typeof knowledgeBase]) {\n      response = `🧠 ${knowledgeBase[command as keyof typeof knowledgeBase]}`;\n    }\n    // Natural language processing (simple keyword matching)\n    else {\n      const keywords = Object.keys(knowledgeBase);\n      const matchedKeyword = keywords.find(keyword => \n        command.includes(keyword) || keyword.includes(command.split(' ')[0])\n      );\n      \n      if (matchedKeyword) {\n        response = `🧠 ${knowledgeBase[matchedKeyword as keyof typeof knowledgeBase]}`;\n      } else if (command.includes('halo') || command.includes('hai') || command.includes('hello')) {\n        response = '👋 Halo! Saya AI Assistant KangPCode. Ada yang bisa saya bantu? Ketik \"help\" untuk melihat perintah yang tersedia.';\n      } else if (command.includes('terima kasih') || command.includes('thanks')) {\n        response = '🙏 Sama-sama! Senang bisa membantu. Ada pertanyaan lain tentang KangPCode?';\n      } else if (command.includes('bye') || command.includes('exit') || command.includes('quit')) {\n        response = '👋 Sampai jumpa! Terima kasih telah menggunakan KangPCode Terminal AI.';\n      } else {\n        response = `❓ Maaf, saya tidak mengerti perintah \"${cmd}\". Ketik \"help\" untuk melihat perintah yang tersedia atau tanyakan tentang KangPCode.`;\n      }\n    }\n\n    // Add AI response to history\n    setHistory(prev => [...prev, {\n      type: 'output',\n      content: response,\n      timestamp: new Date()\n    }]);\n\n    setIsTyping(false);\n  };\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    if (input.trim()) {\n      processCommand(input);\n      setInput('');\n    }\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      exit={{ opacity: 0, scale: 0.9 }}\n      className=\"fixed inset-4 bg-black rounded-lg shadow-2xl border border-green-500/30 overflow-hidden z-50\"\n    >\n      {/* Terminal Header */}\n      <div className=\"h-8 bg-gray-900 flex items-center justify-between px-4 border-b border-green-500/30\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n          <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n          <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n          <span className=\"text-green-400 text-sm font-mono ml-4\">🧠 KangPCode Terminal AI</span>\n        </div>\n        <button\n          onClick={onClose}\n          className=\"text-gray-400 hover:text-white transition-colors\"\n        >\n          ✕\n        </button>\n      </div>\n\n      {/* Terminal Content */}\n      <div \n        ref={terminalRef}\n        className=\"flex-1 p-4 font-mono text-sm text-green-400 bg-black overflow-y-auto h-[calc(100%-8rem)]\"\n      >\n        {history.map((line, index) => (\n          <motion.div\n            key={index}\n            initial={{ opacity: 0, y: 10 }}\n            animate={{ opacity: 1, y: 0 }}\n            className={`mb-2 ${\n              line.type === 'input' \n                ? 'text-cyan-400' \n                : line.type === 'error' \n                ? 'text-red-400' \n                : 'text-green-400'\n            }`}\n          >\n            <pre className=\"whitespace-pre-wrap font-mono\">{line.content}</pre>\n          </motion.div>\n        ))}\n        \n        {isTyping && (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"text-yellow-400 flex items-center space-x-2\"\n          >\n            <span>🧠 AI sedang berpikir</span>\n            <div className=\"flex space-x-1\">\n              {[0, 1, 2].map((i) => (\n                <motion.div\n                  key={i}\n                  className=\"w-1 h-1 bg-yellow-400 rounded-full\"\n                  animate={{\n                    scale: [1, 1.5, 1],\n                    opacity: [0.5, 1, 0.5],\n                  }}\n                  transition={{\n                    duration: 1,\n                    repeat: Infinity,\n                    delay: i * 0.2,\n                  }}\n                />\n              ))}\n            </div>\n          </motion.div>\n        )}\n      </div>\n\n      {/* Terminal Input */}\n      <div className=\"h-12 bg-gray-900 border-t border-green-500/30 flex items-center px-4\">\n        <form onSubmit={handleSubmit} className=\"flex-1 flex items-center space-x-2\">\n          <span className=\"text-cyan-400 font-mono\">$</span>\n          <input\n            ref={inputRef}\n            type=\"text\"\n            value={input}\n            onChange={(e) => setInput(e.target.value)}\n            className=\"flex-1 bg-transparent text-green-400 font-mono outline-none placeholder-gray-500\"\n            placeholder=\"Ketik perintah atau tanyakan tentang KangPCode...\"\n            disabled={isTyping}\n          />\n        </form>\n      </div>\n\n      {/* Terminal Footer */}\n      <div className=\"h-6 bg-gray-800 border-t border-green-500/30 flex items-center justify-between px-4 text-xs text-gray-400\">\n        <span>KangPCode Terminal AI - Interactive Portfolio Assistant</span>\n        <span>Press Ctrl+C to exit</span>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAgBe,SAAS,WAAW,EAAE,SAAS,EAAE,OAAO,EAAmB;;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,oBAAoB;IACpB,MAAM,gBAAgB;QACpB,mBAAmB;QACnB,cAAc;QACd,SAAS;QACT,cAAc;QACd,QAAQ;QACR,QAAQ;QACR,UAAU;QACV,UAAU;QACV,aAAa;QACb,QAAQ;IACV;IAEA,MAAM,WAAW;QACf,QAAQ;QACR,SAAS;QACT,MAAM;QACN,OAAO;QACP,UAAU;QACV,QAAQ,IAAI,OAAO,cAAc,CAAC;QAClC,SAAS;IACX;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,WAAW;gBACb,WAAW;oBACT;wBACE,MAAM;wBACN,SAAS;wBACT,WAAW,IAAI;oBACjB;iBACD;gBACD;4CAAW;wBACT,SAAS,OAAO,EAAE;oBACpB;2CAAG;YACL;QACF;+BAAG;QAAC;KAAU;IAEd,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,IAAI,YAAY,OAAO,EAAE;gBACvB,YAAY,OAAO,CAAC,SAAS,GAAG,YAAY,OAAO,CAAC,YAAY;YAClE;QACF;+BAAG;QAAC;KAAQ;IAEZ,MAAM,iBAAiB,OAAO;QAC5B,MAAM,UAAU,IAAI,WAAW,GAAG,IAAI;QAEtC,4BAA4B;QAC5B,WAAW,CAAA,OAAQ;mBAAI;gBAAM;oBAC3B,MAAM;oBACN,SAAS,CAAC,EAAE,EAAE,KAAK;oBACnB,WAAW,IAAI;gBACjB;aAAE;QAEF,YAAY;QAEZ,6BAA6B;QAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,MAAM,KAAK,MAAM,KAAK;QAEvE,IAAI,WAAW;QAEf,6BAA6B;QAC7B,IAAI,QAAQ,CAAC,QAAiC,EAAE;YAC9C,MAAM,SAAS,QAAQ,CAAC,QAAiC;YACzD,IAAI,WAAW,kBAAkB;gBAC/B,WAAW,EAAE;gBACb,YAAY;gBACZ;YACF;YACA,WAAW;QACb,OAEK,IAAI,aAAa,CAAC,QAAsC,EAAE;YAC7D,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,QAAsC,EAAE;QACzE,OAEK;YACH,MAAM,WAAW,OAAO,IAAI,CAAC;YAC7B,MAAM,iBAAiB,SAAS,IAAI,CAAC,CAAA,UACnC,QAAQ,QAAQ,CAAC,YAAY,QAAQ,QAAQ,CAAC,QAAQ,KAAK,CAAC,IAAI,CAAC,EAAE;YAGrE,IAAI,gBAAgB;gBAClB,WAAW,CAAC,GAAG,EAAE,aAAa,CAAC,eAA6C,EAAE;YAChF,OAAO,IAAI,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,UAAU;gBAC3F,WAAW;YACb,OAAO,IAAI,QAAQ,QAAQ,CAAC,mBAAmB,QAAQ,QAAQ,CAAC,WAAW;gBACzE,WAAW;YACb,OAAO,IAAI,QAAQ,QAAQ,CAAC,UAAU,QAAQ,QAAQ,CAAC,WAAW,QAAQ,QAAQ,CAAC,SAAS;gBAC1F,WAAW;YACb,OAAO;gBACL,WAAW,CAAC,sCAAsC,EAAE,IAAI,qFAAqF,CAAC;YAChJ;QACF;QAEA,6BAA6B;QAC7B,WAAW,CAAA,OAAQ;mBAAI;gBAAM;oBAC3B,MAAM;oBACN,SAAS;oBACT,WAAW,IAAI;gBACjB;aAAE;QAEF,YAAY;IACd;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,IAAI,MAAM,IAAI,IAAI;YAChB,eAAe;YACf,SAAS;QACX;IACF;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,MAAM;YAAE,SAAS;YAAG,OAAO;QAAI;QAC/B,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAK,WAAU;0CAAwC;;;;;;;;;;;;kCAE1D,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAMH,6LAAC;gBACC,KAAK;gBACL,WAAU;;oBAET,QAAQ,GAAG,CAAC,CAAC,MAAM,sBAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAW,CAAC,KAAK,EACf,KAAK,IAAI,KAAK,UACV,kBACA,KAAK,IAAI,KAAK,UACd,iBACA,kBACJ;sCAEF,cAAA,6LAAC;gCAAI,WAAU;0CAAiC,KAAK,OAAO;;;;;;2BAXvD;;;;;oBAeR,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,WAAU;;0CAEV,6LAAC;0CAAK;;;;;;0CACN,6LAAC;gCAAI,WAAU;0CACZ;oCAAC;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAC,kBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,SAAS;4CACP,OAAO;gDAAC;gDAAG;gDAAK;6CAAE;4CAClB,SAAS;gDAAC;gDAAK;gDAAG;6CAAI;wCACxB;wCACA,YAAY;4CACV,UAAU;4CACV,QAAQ;4CACR,OAAO,IAAI;wCACb;uCAVK;;;;;;;;;;;;;;;;;;;;;;0BAmBjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,6LAAC;4BAAK,WAAU;sCAA0B;;;;;;sCAC1C,6LAAC;4BACC,KAAK;4BACL,MAAK;4BACL,OAAO;4BACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4BACxC,WAAU;4BACV,aAAY;4BACZ,UAAU;;;;;;;;;;;;;;;;;0BAMhB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAK;;;;;;kCACN,6LAAC;kCAAK;;;;;;;;;;;;;;;;;;AAId;GA3NwB;KAAA", "debugId": null}}, {"offset": {"line": 1399, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/LaptopWindows.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ninterface LaptopWindowsProps {\n  isVisible: boolean;\n  onClose: () => void;\n}\n\ninterface GitHubRepo {\n  id: number;\n  name: string;\n  description: string;\n  language: string;\n  stargazers_count: number;\n  forks_count: number;\n  html_url: string;\n  updated_at: string;\n}\n\ninterface GitHubUser {\n  login: string;\n  name: string;\n  bio: string;\n  public_repos: number;\n  followers: number;\n  following: number;\n  avatar_url: string;\n  html_url: string;\n}\n\nexport default function LaptopWindows({ isVisible, onClose }: LaptopWindowsProps) {\n  const [currentTime, setCurrentTime] = useState(new Date());\n  const [isBooting, setIsBooting] = useState(true);\n  const [githubUser, setGithubUser] = useState<GitHubUser | null>(null);\n  const [githubRepos, setGithubRepos] = useState<GitHubRepo[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState<'profile' | 'repos' | 'stats'>('profile');\n\n  // Update time every second\n  useEffect(() => {\n    const timer = setInterval(() => {\n      setCurrentTime(new Date());\n    }, 1000);\n    return () => clearInterval(timer);\n  }, []);\n\n  // Boot sequence\n  useEffect(() => {\n    if (isVisible) {\n      setIsBooting(true);\n      const bootTimer = setTimeout(() => {\n        setIsBooting(false);\n        fetchGitHubData();\n      }, 2000);\n      return () => clearTimeout(bootTimer);\n    }\n  }, [isVisible]);\n\n  const fetchGitHubData = async () => {\n    setIsLoading(true);\n    try {\n      // Fetch user data\n      const userResponse = await fetch('https://api.github.com/users/kangpcode');\n      if (userResponse.ok) {\n        const userData = await userResponse.json();\n        setGithubUser(userData);\n      }\n\n      // Fetch repositories\n      const reposResponse = await fetch('https://api.github.com/users/kangpcode/repos?sort=updated&per_page=10');\n      if (reposResponse.ok) {\n        const reposData = await reposResponse.json();\n        setGithubRepos(reposData);\n      }\n    } catch (error) {\n      console.error('Error fetching GitHub data:', error);\n      // Set mock data if API fails\n      setGithubUser({\n        login: 'kangpcode',\n        name: 'Dhafa Nazula Permadi',\n        bio: 'Fullstack Developer & Content Creator | Building the future of Indonesian tech',\n        public_repos: 25,\n        followers: 150,\n        following: 80,\n        avatar_url: '/assets/images/kangpcode-avatar.jpg',\n        html_url: 'https://github.com/kangpcode'\n      });\n      \n      setGithubRepos([\n        {\n          id: 1,\n          name: 'portfolio-3d',\n          description: 'Interactive 3D Portfolio Website with Three.js',\n          language: 'TypeScript',\n          stargazers_count: 45,\n          forks_count: 12,\n          html_url: 'https://github.com/kangpcode/portfolio-3d',\n          updated_at: '2024-01-15T10:30:00Z'\n        },\n        {\n          id: 2,\n          name: 'langkah-kode-nusantara',\n          description: 'Educational programming book for Indonesian developers',\n          language: 'Markdown',\n          stargazers_count: 89,\n          forks_count: 23,\n          html_url: 'https://github.com/kangpcode/langkah-kode-nusantara',\n          updated_at: '2024-01-10T14:20:00Z'\n        }\n      ]);\n    }\n    setIsLoading(false);\n  };\n\n  const formatTime = (date: Date) => {\n    return date.toLocaleTimeString('en-US', { \n      hour: '2-digit', \n      minute: '2-digit',\n      hour12: true\n    });\n  };\n\n  const formatDate = (date: Date) => {\n    return date.toLocaleDateString('en-US', { \n      weekday: 'short',\n      month: 'short',\n      day: 'numeric'\n    });\n  };\n\n  const getLanguageColor = (language: string) => {\n    const colors: { [key: string]: string } = {\n      'TypeScript': '#3178c6',\n      'JavaScript': '#f1e05a',\n      'Python': '#3572A5',\n      'Java': '#b07219',\n      'C++': '#f34b7d',\n      'HTML': '#e34c26',\n      'CSS': '#1572B6',\n      'Markdown': '#083fa1',\n      'PHP': '#4F5D95'\n    };\n    return colors[language] || '#6b7280';\n  };\n\n  if (!isVisible) return null;\n\n  return (\n    <div className=\"fixed inset-0 bg-black z-50 overflow-hidden\">\n      <AnimatePresence>\n        {isBooting ? (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"flex items-center justify-center h-full bg-blue-600 text-white\"\n          >\n            <div className=\"text-center space-y-4\">\n              <div className=\"text-6xl mb-4\">🪟</div>\n              <h2 className=\"text-2xl font-bold\">Windows 11</h2>\n              <p className=\"text-lg\">Starting GitHub Viewer...</p>\n              <div className=\"flex justify-center space-x-1 mt-4\">\n                {[0, 1, 2, 3, 4].map((i) => (\n                  <motion.div\n                    key={i}\n                    className=\"w-2 h-2 bg-white rounded-full\"\n                    animate={{\n                      scale: [1, 1.2, 1],\n                      opacity: [0.5, 1, 0.5],\n                    }}\n                    transition={{\n                      duration: 1,\n                      repeat: Infinity,\n                      delay: i * 0.1,\n                    }}\n                  />\n                ))}\n              </div>\n            </div>\n          </motion.div>\n        ) : (\n          <motion.div\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            className=\"h-full bg-gradient-to-br from-blue-100 to-blue-200 relative\"\n          >\n            {/* Windows Taskbar */}\n            <div className=\"absolute bottom-0 left-0 right-0 h-12 bg-gray-900/95 backdrop-blur-sm flex items-center justify-between px-4 z-10\">\n              {/* Start Button */}\n              <button className=\"flex items-center space-x-2 px-3 py-2 hover:bg-gray-700/50 rounded transition-colors text-white\">\n                <span className=\"text-xl\">🪟</span>\n                <span className=\"font-medium\">Start</span>\n              </button>\n\n              {/* App Icons */}\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center text-xl text-white\">\n                  🌐\n                </div>\n                <div className=\"w-10 h-10 bg-gray-700 rounded-lg flex items-center justify-center text-xl text-white\">\n                  📁\n                </div>\n              </div>\n\n              {/* System Tray */}\n              <div className=\"flex items-center space-x-4 text-white text-sm\">\n                <span>🔊 🔋 📶</span>\n                <div className=\"text-right\">\n                  <div className=\"font-mono\">{formatTime(currentTime)}</div>\n                  <div className=\"text-xs\">{formatDate(currentTime)}</div>\n                </div>\n                <button\n                  onClick={onClose}\n                  className=\"hover:bg-red-600 px-2 py-1 rounded transition-colors\"\n                >\n                  ✕\n                </button>\n              </div>\n            </div>\n\n            {/* Main Content - GitHub Viewer */}\n            <div className=\"h-[calc(100%-3rem)] p-4\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                className=\"h-full bg-white rounded-lg shadow-2xl overflow-hidden\"\n              >\n                {/* Browser Header */}\n                <div className=\"h-12 bg-gray-100 border-b flex items-center justify-between px-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <div className=\"flex space-x-1\">\n                      <div className=\"w-3 h-3 bg-red-500 rounded-full\"></div>\n                      <div className=\"w-3 h-3 bg-yellow-500 rounded-full\"></div>\n                      <div className=\"w-3 h-3 bg-green-500 rounded-full\"></div>\n                    </div>\n                    <div className=\"ml-4 bg-gray-200 rounded px-3 py-1 text-sm text-gray-600\">\n                      🔒 github.com/kangpcode\n                    </div>\n                  </div>\n                  <div className=\"text-lg font-bold text-gray-700\">GitHub Profile</div>\n                </div>\n\n                {/* Tab Navigation */}\n                <div className=\"h-10 bg-gray-50 border-b flex items-center px-4\">\n                  {[\n                    { id: 'profile', name: 'Profile', icon: '👤' },\n                    { id: 'repos', name: 'Repositories', icon: '📁' },\n                    { id: 'stats', name: 'Statistics', icon: '📊' }\n                  ].map((tab) => (\n                    <button\n                      key={tab.id}\n                      onClick={() => setActiveTab(tab.id as any)}\n                      className={`flex items-center space-x-2 px-4 py-2 rounded-t transition-colors ${\n                        activeTab === tab.id \n                          ? 'bg-white border-t-2 border-blue-500 text-blue-600' \n                          : 'hover:bg-gray-100 text-gray-600'\n                      }`}\n                    >\n                      <span>{tab.icon}</span>\n                      <span className=\"font-medium\">{tab.name}</span>\n                    </button>\n                  ))}\n                </div>\n\n                {/* Content Area */}\n                <div className=\"flex-1 p-6 overflow-y-auto\">\n                  {isLoading ? (\n                    <div className=\"flex items-center justify-center h-64\">\n                      <div className=\"text-center space-y-4\">\n                        <div className=\"text-4xl\">⏳</div>\n                        <p className=\"text-gray-600\">Loading GitHub data...</p>\n                      </div>\n                    </div>\n                  ) : (\n                    <AnimatePresence mode=\"wait\">\n                      {activeTab === 'profile' && githubUser && (\n                        <motion.div\n                          key=\"profile\"\n                          initial={{ opacity: 0, x: 20 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          exit={{ opacity: 0, x: -20 }}\n                          className=\"space-y-6\"\n                        >\n                          <div className=\"flex items-start space-x-6\">\n                            <div className=\"w-32 h-32 bg-gray-300 rounded-full flex items-center justify-center text-4xl\">\n                              👨‍💻\n                            </div>\n                            <div className=\"flex-1\">\n                              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">{githubUser.name}</h1>\n                              <p className=\"text-xl text-gray-600 mb-4\">@{githubUser.login}</p>\n                              <p className=\"text-gray-700 mb-4\">{githubUser.bio}</p>\n                              <div className=\"flex space-x-6 text-sm text-gray-600\">\n                                <span>👥 {githubUser.followers} followers</span>\n                                <span>👤 {githubUser.following} following</span>\n                                <span>📁 {githubUser.public_repos} repositories</span>\n                              </div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      )}\n\n                      {activeTab === 'repos' && (\n                        <motion.div\n                          key=\"repos\"\n                          initial={{ opacity: 0, x: 20 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          exit={{ opacity: 0, x: -20 }}\n                          className=\"space-y-4\"\n                        >\n                          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">Recent Repositories</h2>\n                          {githubRepos.map((repo) => (\n                            <div key={repo.id} className=\"border rounded-lg p-4 hover:shadow-md transition-shadow\">\n                              <div className=\"flex items-start justify-between\">\n                                <div className=\"flex-1\">\n                                  <h3 className=\"text-lg font-semibold text-blue-600 mb-2\">{repo.name}</h3>\n                                  <p className=\"text-gray-700 mb-3\">{repo.description}</p>\n                                  <div className=\"flex items-center space-x-4 text-sm text-gray-600\">\n                                    <span className=\"flex items-center space-x-1\">\n                                      <div \n                                        className=\"w-3 h-3 rounded-full\" \n                                        style={{ backgroundColor: getLanguageColor(repo.language) }}\n                                      ></div>\n                                      <span>{repo.language}</span>\n                                    </span>\n                                    <span>⭐ {repo.stargazers_count}</span>\n                                    <span>🍴 {repo.forks_count}</span>\n                                  </div>\n                                </div>\n                              </div>\n                            </div>\n                          ))}\n                        </motion.div>\n                      )}\n\n                      {activeTab === 'stats' && githubUser && (\n                        <motion.div\n                          key=\"stats\"\n                          initial={{ opacity: 0, x: 20 }}\n                          animate={{ opacity: 1, x: 0 }}\n                          exit={{ opacity: 0, x: -20 }}\n                          className=\"space-y-6\"\n                        >\n                          <h2 className=\"text-2xl font-bold text-gray-900 mb-4\">GitHub Statistics</h2>\n                          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                            <div className=\"bg-blue-50 p-4 rounded-lg text-center\">\n                              <div className=\"text-2xl font-bold text-blue-600\">{githubUser.public_repos}</div>\n                              <div className=\"text-sm text-gray-600\">Repositories</div>\n                            </div>\n                            <div className=\"bg-green-50 p-4 rounded-lg text-center\">\n                              <div className=\"text-2xl font-bold text-green-600\">{githubUser.followers}</div>\n                              <div className=\"text-sm text-gray-600\">Followers</div>\n                            </div>\n                            <div className=\"bg-purple-50 p-4 rounded-lg text-center\">\n                              <div className=\"text-2xl font-bold text-purple-600\">{githubUser.following}</div>\n                              <div className=\"text-sm text-gray-600\">Following</div>\n                            </div>\n                            <div className=\"bg-orange-50 p-4 rounded-lg text-center\">\n                              <div className=\"text-2xl font-bold text-orange-600\">\n                                {githubRepos.reduce((sum, repo) => sum + repo.stargazers_count, 0)}\n                              </div>\n                              <div className=\"text-sm text-gray-600\">Total Stars</div>\n                            </div>\n                          </div>\n                        </motion.div>\n                      )}\n                    </AnimatePresence>\n                  )}\n                </div>\n              </motion.div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAgCe,SAAS,cAAc,EAAE,SAAS,EAAE,OAAO,EAAsB;;IAC9E,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,IAAI;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC;IAE1E,2BAA2B;IAC3B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,QAAQ;iDAAY;oBACxB,eAAe,IAAI;gBACrB;gDAAG;YACH;2CAAO,IAAM,cAAc;;QAC7B;kCAAG,EAAE;IAEL,gBAAgB;IAChB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,WAAW;gBACb,aAAa;gBACb,MAAM,YAAY;yDAAW;wBAC3B,aAAa;wBACb;oBACF;wDAAG;gBACH;+CAAO,IAAM,aAAa;;YAC5B;QACF;kCAAG;QAAC;KAAU;IAEd,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,kBAAkB;YAClB,MAAM,eAAe,MAAM,MAAM;YACjC,IAAI,aAAa,EAAE,EAAE;gBACnB,MAAM,WAAW,MAAM,aAAa,IAAI;gBACxC,cAAc;YAChB;YAEA,qBAAqB;YACrB,MAAM,gBAAgB,MAAM,MAAM;YAClC,IAAI,cAAc,EAAE,EAAE;gBACpB,MAAM,YAAY,MAAM,cAAc,IAAI;gBAC1C,eAAe;YACjB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,6BAA6B;YAC7B,cAAc;gBACZ,OAAO;gBACP,MAAM;gBACN,KAAK;gBACL,cAAc;gBACd,WAAW;gBACX,WAAW;gBACX,YAAY;gBACZ,UAAU;YACZ;YAEA,eAAe;gBACb;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,kBAAkB;oBAClB,aAAa;oBACb,UAAU;oBACV,YAAY;gBACd;gBACA;oBACE,IAAI;oBACJ,MAAM;oBACN,aAAa;oBACb,UAAU;oBACV,kBAAkB;oBAClB,aAAa;oBACb,UAAU;oBACV,YAAY;gBACd;aACD;QACH;QACA,aAAa;IACf;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,SAAS;YACT,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAoC;YACxC,cAAc;YACd,cAAc;YACd,UAAU;YACV,QAAQ;YACR,OAAO;YACP,QAAQ;YACR,OAAO;YACP,YAAY;YACZ,OAAO;QACT;QACA,OAAO,MAAM,CAAC,SAAS,IAAI;IAC7B;IAEA,IAAI,CAAC,WAAW,OAAO;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;sBACb,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;gBACnB,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,6LAAC;4BAAG,WAAU;sCAAqB;;;;;;sCACnC,6LAAC;4BAAE,WAAU;sCAAU;;;;;;sCACvB,6LAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,kBACpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCACP,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;wCAClB,SAAS;4CAAC;4CAAK;4CAAG;yCAAI;oCACxB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO,IAAI;oCACb;mCAVK;;;;;;;;;;;;;;;;;;;;qCAiBf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;;kCAGV,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,6LAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAIhC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAuF;;;;;;kDAGtG,6LAAC;wCAAI,WAAU;kDAAuF;;;;;;;;;;;;0CAMxG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAa,WAAW;;;;;;0DACvC,6LAAC;gDAAI,WAAU;0DAAW,WAAW;;;;;;;;;;;;kDAEvC,6LAAC;wCACC,SAAS;wCACT,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAOL,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,WAAU;;8CAGV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,6LAAC;oDAAI,WAAU;8DAA2D;;;;;;;;;;;;sDAI5E,6LAAC;4CAAI,WAAU;sDAAkC;;;;;;;;;;;;8CAInD,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,IAAI;4CAAW,MAAM;4CAAW,MAAM;wCAAK;wCAC7C;4CAAE,IAAI;4CAAS,MAAM;4CAAgB,MAAM;wCAAK;wCAChD;4CAAE,IAAI;4CAAS,MAAM;4CAAc,MAAM;wCAAK;qCAC/C,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;4CAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4CAClC,WAAW,CAAC,kEAAkE,EAC5E,cAAc,IAAI,EAAE,GAChB,sDACA,mCACJ;;8DAEF,6LAAC;8DAAM,IAAI,IAAI;;;;;;8DACf,6LAAC;oDAAK,WAAU;8DAAe,IAAI,IAAI;;;;;;;2CATlC,IAAI,EAAE;;;;;;;;;;8CAejB,6LAAC;oCAAI,WAAU;8CACZ,0BACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAW;;;;;;8DAC1B,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;6DAIjC,6LAAC,4LAAA,CAAA,kBAAe;wCAAC,MAAK;;4CACnB,cAAc,aAAa,4BAC1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,WAAU;0DAEV,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA+E;;;;;;sEAG9F,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAG,WAAU;8EAAyC,WAAW,IAAI;;;;;;8EACtE,6LAAC;oEAAE,WAAU;;wEAA6B;wEAAE,WAAW,KAAK;;;;;;;8EAC5D,6LAAC;oEAAE,WAAU;8EAAsB,WAAW,GAAG;;;;;;8EACjD,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;;gFAAK;gFAAI,WAAW,SAAS;gFAAC;;;;;;;sFAC/B,6LAAC;;gFAAK;gFAAI,WAAW,SAAS;gFAAC;;;;;;;sFAC/B,6LAAC;;gFAAK;gFAAI,WAAW,YAAY;gFAAC;;;;;;;;;;;;;;;;;;;;;;;;;+CAjBpC;;;;;4CAwBP,cAAc,yBACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,WAAU;;kEAEV,6LAAC;wDAAG,WAAU;kEAAwC;;;;;;oDACrD,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;4DAAkB,WAAU;sEAC3B,cAAA,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAG,WAAU;sFAA4C,KAAK,IAAI;;;;;;sFACnE,6LAAC;4EAAE,WAAU;sFAAsB,KAAK,WAAW;;;;;;sFACnD,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAK,WAAU;;sGACd,6LAAC;4FACC,WAAU;4FACV,OAAO;gGAAE,iBAAiB,iBAAiB,KAAK,QAAQ;4FAAE;;;;;;sGAE5D,6LAAC;sGAAM,KAAK,QAAQ;;;;;;;;;;;;8FAEtB,6LAAC;;wFAAK;wFAAG,KAAK,gBAAgB;;;;;;;8FAC9B,6LAAC;;wFAAK;wFAAI,KAAK,WAAW;;;;;;;;;;;;;;;;;;;;;;;;2DAdxB,KAAK,EAAE;;;;;;+CARf;;;;;4CA+BP,cAAc,WAAW,4BACxB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC3B,WAAU;;kEAEV,6LAAC;wDAAG,WAAU;kEAAwC;;;;;;kEACtD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAoC,WAAW,YAAY;;;;;;kFAC1E,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAqC,WAAW,SAAS;;;;;;kFACxE,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAAsC,WAAW,SAAS;;;;;;kFACzE,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;0EAEzC,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACZ,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,gBAAgB,EAAE;;;;;;kFAElE,6LAAC;wEAAI,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;+CAxBvC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuC9B;GAzVwB;KAAA", "debugId": null}}, {"offset": {"line": 2327, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/components/RoomScene3D.tsx"], "sourcesContent": ["'use client';\n\nimport { Suspense, useRef, useState } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport {\n  OrbitControls,\n  Environment,\n  Text,\n  Box,\n  Plane,\n  Html,\n  useTexture,\n  PerspectiveCamera\n} from '@react-three/drei';\nimport { motion } from 'framer-motion';\nimport * as THREE from 'three';\nimport ComputerGUI from './ComputerGUI';\nimport TerminalA<PERSON> from './TerminalAI';\nimport LaptopWindows from './LaptopWindows';\nimport BookKangPCode from './BookKangPCode';\nimport IDCardLanyard from './IDCardLanyard';\n\n// Room Environment Component\nfunction Room() {\n  return (\n    <group>\n      {/* Floor */}\n      <Plane \n        args={[20, 20]} \n        rotation={[-Math.PI / 2, 0, 0]} \n        position={[0, -2, 0]}\n      >\n        <meshStandardMaterial color=\"#8B4513\" />\n      </Plane>\n\n      {/* Walls */}\n      <Plane args={[20, 10]} position={[0, 3, -10]}>\n        <meshStandardMaterial color=\"#F5F5DC\" />\n      </Plane>\n      \n      <Plane args={[20, 10]} position={[-10, 3, 0]} rotation={[0, Math.PI / 2, 0]}>\n        <meshStandardMaterial color=\"#F5F5DC\" />\n      </Plane>\n      \n      <Plane args={[20, 10]} position={[10, 3, 0]} rotation={[0, -Math.PI / 2, 0]}>\n        <meshStandardMaterial color=\"#F5F5DC\" />\n      </Plane>\n\n      {/* Ceiling */}\n      <Plane \n        args={[20, 20]} \n        rotation={[Math.PI / 2, 0, 0]} \n        position={[0, 8, 0]}\n      >\n        <meshStandardMaterial color=\"#FFFFFF\" />\n      </Plane>\n    </group>\n  );\n}\n\n// Computer 3D Component\nfunction Computer3D({ onComputerClick }: { onComputerClick: () => void }) {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const [isHovered, setIsHovered] = useState(false);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      meshRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.5) * 0.1;\n    }\n  });\n\n  return (\n    <group position={[-4, -1, -2]}>\n      {/* Monitor */}\n      <Box ref={meshRef} args={[3, 2, 0.2]} position={[0, 1, 0]}>\n        <meshStandardMaterial color=\"#1a1a1a\" />\n      </Box>\n\n      {/* Screen */}\n      <Plane args={[2.8, 1.8]} position={[0, 1, 0.11]}>\n        <meshStandardMaterial\n          color={isHovered ? \"#001080\" : \"#000080\"}\n          emissive={isHovered ? \"#000060\" : \"#000040\"}\n        />\n      </Plane>\n\n      {/* KDE Plasma Logo on Screen */}\n      <Html position={[0, 1, 0.12]} center>\n        <div className=\"text-white text-center pointer-events-none\">\n          <div className=\"text-4xl mb-2\">🐧</div>\n          <div className=\"text-sm font-bold\">KDE Plasma</div>\n        </div>\n      </Html>\n\n      {/* Base */}\n      <Box args={[0.5, 0.8, 0.5]} position={[0, 0, 0]}>\n        <meshStandardMaterial color=\"#333333\" />\n      </Box>\n\n      {/* Keyboard */}\n      <Box args={[2, 0.1, 1]} position={[0, -1.5, 1]}>\n        <meshStandardMaterial color=\"#2a2a2a\" />\n      </Box>\n\n      {/* Click Area */}\n      <Html position={[0, 1, 0.2]} center>\n        <div\n          className=\"w-32 h-20 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onComputerClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Klik untuk membuka KDE Plasma\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              🖱️ Klik untuk membuka\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Laptop 3D Component\nfunction Laptop3D({ onLaptopClick }: { onLaptopClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[4, -1, -1]}>\n      {/* Laptop Base */}\n      <Box args={[2.5, 0.1, 1.8]} position={[0, -1.5, 0]}>\n        <meshStandardMaterial color=\"#C0C0C0\" />\n      </Box>\n\n      {/* Laptop Screen */}\n      <Box args={[2.5, 1.6, 0.1]} position={[0, 0, -0.8]} rotation={[-0.2, 0, 0]}>\n        <meshStandardMaterial color=\"#1a1a1a\" />\n      </Box>\n\n      {/* Screen Display */}\n      <Plane args={[2.3, 1.4]} position={[0, 0, -0.75]} rotation={[-0.2, 0, 0]}>\n        <meshStandardMaterial\n          color={isHovered ? \"#0088D4\" : \"#0078D4\"}\n          emissive={isHovered ? \"#004d8b\" : \"#003d6b\"}\n        />\n      </Plane>\n\n      {/* Windows Logo on Screen */}\n      <Html position={[0, 0, -0.74]} center>\n        <div className=\"text-white text-center pointer-events-none\" style={{ transform: 'rotateX(-11.5deg)' }}>\n          <div className=\"text-3xl mb-1\">🪟</div>\n          <div className=\"text-xs font-bold\">Windows 11</div>\n        </div>\n      </Html>\n\n      {/* Click Area */}\n      <Html position={[0, 0, -0.7]} center>\n        <div\n          className=\"w-24 h-16 bg-transparent cursor-pointer hover:bg-blue-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onLaptopClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Klik untuk membuka GitHub Viewer\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              🖱️ GitHub Viewer\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Book 3D Component\nfunction Book3D({ onBookClick }: { onBookClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[0, -1.3, 0]}>\n      <Box args={[1.5, 0.2, 1]} position={[0, 0, 0]}>\n        <meshStandardMaterial color={isHovered ? \"#A0522D\" : \"#8B4513\"} />\n      </Box>\n\n      {/* Book Cover Text */}\n      <Html position={[0, 0.15, 0.51]} center>\n        <div className=\"text-white text-center pointer-events-none text-xs\">\n          <div className=\"font-bold\">📘</div>\n          <div className=\"font-bold\">Langkah</div>\n          <div className=\"font-bold\">Kode</div>\n          <div className=\"font-bold\">Nusantara</div>\n        </div>\n      </Html>\n\n      <Html position={[0, 0.2, 0]} center>\n        <div\n          className=\"w-16 h-12 bg-transparent cursor-pointer hover:bg-yellow-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onBookClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"Langkah Kode Nusantara\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              📖 Baca Buku\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// ID Card Component\nfunction IDCard3D({ onIDCardClick }: { onIDCardClick: () => void }) {\n  const [isHovered, setIsHovered] = useState(false);\n\n  return (\n    <group position={[0, -0.9, 0.3]}>\n      <Box args={[0.8, 0.05, 0.5]} position={[0, 0, 0]}>\n        <meshStandardMaterial color={isHovered ? \"#F0F0F0\" : \"#FFFFFF\"} />\n      </Box>\n\n      {/* ID Card Content */}\n      <Html position={[0, 0.03, 0.26]} center>\n        <div className=\"text-black text-center pointer-events-none text-xs\">\n          <div className=\"font-bold\">🪪 KangPCode</div>\n          <div className=\"text-xs\">Developer ID</div>\n        </div>\n      </Html>\n\n      <Html position={[0, 0.1, 0]} center>\n        <div\n          className=\"w-8 h-6 bg-transparent cursor-pointer hover:bg-green-500/20 rounded transition-colors flex items-center justify-center\"\n          onClick={onIDCardClick}\n          onMouseEnter={() => setIsHovered(true)}\n          onMouseLeave={() => setIsHovered(false)}\n          title=\"ID Card KangPCode\"\n        >\n          {isHovered && (\n            <div className=\"text-white text-xs bg-black/70 px-2 py-1 rounded pointer-events-none\">\n              🪪 Lihat ID\n            </div>\n          )}\n        </div>\n      </Html>\n    </group>\n  );\n}\n\n// Lighting Setup\nfunction Lighting() {\n  return (\n    <>\n      <ambientLight intensity={0.4} />\n      <pointLight position={[0, 6, 0]} intensity={0.8} />\n      <pointLight position={[-5, 4, -5]} intensity={0.6} color=\"#FFE4B5\" />\n      <pointLight position={[5, 4, -5]} intensity={0.6} color=\"#E6E6FA\" />\n      <directionalLight \n        position={[10, 10, 5]} \n        intensity={0.5}\n        castShadow\n        shadow-mapSize-width={2048}\n        shadow-mapSize-height={2048}\n      />\n    </>\n  );\n}\n\n// Main RoomScene3D Component\ninterface RoomScene3DProps {\n  onExitRoom?: () => void;\n}\n\nexport default function RoomScene3D({ onExitRoom }: RoomScene3DProps) {\n  const [isLoading, setIsLoading] = useState(true);\n  const [showComputerGUI, setShowComputerGUI] = useState(false);\n  const [showTerminalAI, setShowTerminalAI] = useState(false);\n  const [showLaptopWindows, setShowLaptopWindows] = useState(false);\n  const [showBook, setShowBook] = useState(false);\n  const [showIDCard, setShowIDCard] = useState(false);\n\n  const handleComputerClick = () => {\n    setShowComputerGUI(true);\n  };\n\n  const handleLaptopClick = () => {\n    setShowLaptopWindows(true);\n  };\n\n  const handleBookClick = () => {\n    setShowBook(true);\n  };\n\n  const handleIDCardClick = () => {\n    setShowIDCard(true);\n  };\n\n  const handleCloseComputer = () => {\n    setShowComputerGUI(false);\n    setShowTerminalAI(false);\n  };\n\n  const handleCloseLaptop = () => {\n    setShowLaptopWindows(false);\n  };\n\n  const handleCloseBook = () => {\n    setShowBook(false);\n  };\n\n  const handleCloseIDCard = () => {\n    setShowIDCard(false);\n  };\n\n  return (\n    <div className=\"w-full h-screen relative\">\n      <Canvas\n        shadows\n        camera={{ position: [0, 2, 8], fov: 60 }}\n        onCreated={() => setIsLoading(false)}\n      >\n        <Suspense fallback={null}>\n          <Lighting />\n          <Room />\n          <Computer3D onComputerClick={handleComputerClick} />\n          <Laptop3D onLaptopClick={handleLaptopClick} />\n          <Book3D onBookClick={handleBookClick} />\n          <IDCard3D onIDCardClick={handleIDCardClick} />\n          \n          <OrbitControls\n            enablePan={true}\n            enableZoom={true}\n            enableRotate={true}\n            minDistance={3}\n            maxDistance={15}\n            minPolarAngle={0}\n            maxPolarAngle={Math.PI / 2}\n          />\n          \n          <Environment preset=\"apartment\" />\n        </Suspense>\n      </Canvas>\n\n      {/* UI Overlay */}\n      <div className=\"absolute top-4 left-4 z-10\">\n        <motion.div\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          className=\"bg-black/50 backdrop-blur-sm rounded-lg p-4 text-white\"\n        >\n          <h2 className=\"text-xl font-bold mb-2\">Kamar KangPCode</h2>\n          <p className=\"text-sm text-gray-300\">\n            Klik objek untuk berinteraksi\n          </p>\n        </motion.div>\n      </div>\n\n      {/* Exit Button */}\n      <div className=\"absolute top-4 right-4 z-10\">\n        <motion.button\n          initial={{ opacity: 0, x: 20 }}\n          animate={{ opacity: 1, x: 0 }}\n          onClick={onExitRoom}\n          className=\"bg-red-500/80 hover:bg-red-600/80 backdrop-blur-sm rounded-lg px-4 py-2 text-white font-medium transition-colors\"\n        >\n          Keluar Kamar\n        </motion.button>\n      </div>\n\n      {/* Loading Overlay */}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center z-20\">\n          <div className=\"text-white text-xl\">Memuat Scene 3D...</div>\n        </div>\n      )}\n\n      {/* Computer GUI Overlay */}\n      <ComputerGUI\n        isVisible={showComputerGUI}\n        onClose={handleCloseComputer}\n      />\n\n      {/* Terminal AI Overlay */}\n      <TerminalAI\n        isVisible={showTerminalAI}\n        onClose={() => setShowTerminalAI(false)}\n      />\n\n      {/* Laptop Windows Overlay */}\n      <LaptopWindows\n        isVisible={showLaptopWindows}\n        onClose={handleCloseLaptop}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAUA;AAEA;AACA;AACA;;;AAlBA;;;;;;;;AAsBA,6BAA6B;AAC7B,SAAS;IACP,qBACE,6LAAC;;0BAEC,6LAAC,6JAAA,CAAA,QAAK;gBACJ,MAAM;oBAAC;oBAAI;iBAAG;gBACd,UAAU;oBAAC,CAAC,KAAK,EAAE,GAAG;oBAAG;oBAAG;iBAAE;gBAC9B,UAAU;oBAAC;oBAAG,CAAC;oBAAG;iBAAE;0BAEpB,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,6JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAG;0BAC1C,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAG9B,6LAAC,6JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAI;iBAAG;gBAAE,UAAU;oBAAC,CAAC;oBAAI;oBAAG;iBAAE;gBAAE,UAAU;oBAAC;oBAAG,KAAK,EAAE,GAAG;oBAAG;iBAAE;0BACzE,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAG9B,6LAAC,6JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAI;iBAAG;gBAAE,UAAU;oBAAC;oBAAI;oBAAG;iBAAE;gBAAE,UAAU;oBAAC;oBAAG,CAAC,KAAK,EAAE,GAAG;oBAAG;iBAAE;0BACzE,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,6JAAA,CAAA,QAAK;gBACJ,MAAM;oBAAC;oBAAI;iBAAG;gBACd,UAAU;oBAAC,KAAK,EAAE,GAAG;oBAAG;oBAAG;iBAAE;gBAC7B,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAEnB,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;;;;;;;AAIpC;KAnCS;AAqCT,wBAAwB;AACxB,SAAS,WAAW,EAAE,eAAe,EAAmC;;IACtE,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAc;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,kNAAA,CAAA,WAAQ,AAAD;+BAAE,CAAC;YACR,IAAI,QAAQ,OAAO,EAAE;gBACnB,QAAQ,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,KAAK,GAAG,CAAC,MAAM,KAAK,CAAC,WAAW,GAAG,OAAO;YACzE;QACF;;IAEA,qBACE,6LAAC;QAAM,UAAU;YAAC,CAAC;YAAG,CAAC;YAAG,CAAC;SAAE;;0BAE3B,6LAAC,6JAAA,CAAA,MAAG;gBAAC,KAAK;gBAAS,MAAM;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BACvD,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,6JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;0BAC7C,cAAA,6LAAC;oBACC,OAAO,YAAY,YAAY;oBAC/B,UAAU,YAAY,YAAY;;;;;;;;;;;0BAKtC,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAK;gBAAE,MAAM;0BAClC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,6LAAC;4BAAI,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAKvC,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC7C,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAG;oBAAK;iBAAE;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAE;0BAC5C,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG;iBAAI;gBAAE,MAAM;0BACjC,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,6LAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;GA7DS;;QAIP,kNAAA,CAAA,WAAQ;;;MAJD;AA+DT,sBAAsB;AACtB,SAAS,SAAS,EAAE,aAAa,EAAiC;;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAM,UAAU;YAAC;YAAG,CAAC;YAAG,CAAC;SAAE;;0BAE1B,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG,CAAC;oBAAK;iBAAE;0BAChD,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAI;gBAAE,UAAU;oBAAC,CAAC;oBAAK;oBAAG;iBAAE;0BACxE,cAAA,6LAAC;oBAAqB,OAAM;;;;;;;;;;;0BAI9B,6LAAC,6JAAA,CAAA,QAAK;gBAAC,MAAM;oBAAC;oBAAK;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAK;gBAAE,UAAU;oBAAC,CAAC;oBAAK;oBAAG;iBAAE;0BACtE,cAAA,6LAAC;oBACC,OAAO,YAAY,YAAY;oBAC/B,UAAU,YAAY,YAAY;;;;;;;;;;;0BAKtC,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAK;gBAAE,MAAM;0BACnC,cAAA,6LAAC;oBAAI,WAAU;oBAA6C,OAAO;wBAAE,WAAW;oBAAoB;;sCAClG,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,6LAAC;4BAAI,WAAU;sCAAoB;;;;;;;;;;;;;;;;;0BAKvC,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAI;gBAAE,MAAM;0BAClC,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,6LAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;IAjDS;MAAA;AAmDT,oBAAoB;AACpB,SAAS,OAAO,EAAE,WAAW,EAA+B;;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAM,UAAU;YAAC;YAAG,CAAC;YAAK;SAAE;;0BAC3B,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAK;iBAAE;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC3C,cAAA,6LAAC;oBAAqB,OAAO,YAAY,YAAY;;;;;;;;;;;0BAIvD,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAM;iBAAK;gBAAE,MAAM;0BACrC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,6LAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,6LAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,6LAAC;4BAAI,WAAU;sCAAY;;;;;;;;;;;;;;;;;0BAI/B,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAK;iBAAE;gBAAE,MAAM;0BACjC,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,6LAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;IApCS;MAAA;AAsCT,oBAAoB;AACpB,SAAS,SAAS,EAAE,aAAa,EAAiC;;IAChE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAM,UAAU;YAAC;YAAG,CAAC;YAAK;SAAI;;0BAC7B,6LAAC,6JAAA,CAAA,MAAG;gBAAC,MAAM;oBAAC;oBAAK;oBAAM;iBAAI;gBAAE,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;0BAC9C,cAAA,6LAAC;oBAAqB,OAAO,YAAY,YAAY;;;;;;;;;;;0BAIvD,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAM;iBAAK;gBAAE,MAAM;0BACrC,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAY;;;;;;sCAC3B,6LAAC;4BAAI,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAI7B,6LAAC,0JAAA,CAAA,OAAI;gBAAC,UAAU;oBAAC;oBAAG;oBAAK;iBAAE;gBAAE,MAAM;0BACjC,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS;oBACT,cAAc,IAAM,aAAa;oBACjC,cAAc,IAAM,aAAa;oBACjC,OAAM;8BAEL,2BACC,6LAAC;wBAAI,WAAU;kCAAuE;;;;;;;;;;;;;;;;;;;;;;AAQlG;IAlCS;MAAA;AAoCT,iBAAiB;AACjB,SAAS;IACP,qBACE;;0BACE,6LAAC;gBAAa,WAAW;;;;;;0BACzB,6LAAC;gBAAW,UAAU;oBAAC;oBAAG;oBAAG;iBAAE;gBAAE,WAAW;;;;;;0BAC5C,6LAAC;gBAAW,UAAU;oBAAC,CAAC;oBAAG;oBAAG,CAAC;iBAAE;gBAAE,WAAW;gBAAK,OAAM;;;;;;0BACzD,6LAAC;gBAAW,UAAU;oBAAC;oBAAG;oBAAG,CAAC;iBAAE;gBAAE,WAAW;gBAAK,OAAM;;;;;;0BACxD,6LAAC;gBACC,UAAU;oBAAC;oBAAI;oBAAI;iBAAE;gBACrB,WAAW;gBACX,UAAU;gBACV,wBAAsB;gBACtB,yBAAuB;;;;;;;;AAI/B;MAhBS;AAuBM,SAAS,YAAY,EAAE,UAAU,EAAoB;;IAClE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,sBAAsB;QAC1B,mBAAmB;IACrB;IAEA,MAAM,oBAAoB;QACxB,qBAAqB;IACvB;IAEA,MAAM,kBAAkB;QACtB,YAAY;IACd;IAEA,MAAM,oBAAoB;QACxB,cAAc;IAChB;IAEA,MAAM,sBAAsB;QAC1B,mBAAmB;QACnB,kBAAkB;IACpB;IAEA,MAAM,oBAAoB;QACxB,qBAAqB;IACvB;IAEA,MAAM,kBAAkB;QACtB,YAAY;IACd;IAEA,MAAM,oBAAoB;QACxB,cAAc;IAChB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,sMAAA,CAAA,SAAM;gBACL,OAAO;gBACP,QAAQ;oBAAE,UAAU;wBAAC;wBAAG;wBAAG;qBAAE;oBAAE,KAAK;gBAAG;gBACvC,WAAW,IAAM,aAAa;0BAE9B,cAAA,6LAAC,6JAAA,CAAA,WAAQ;oBAAC,UAAU;;sCAClB,6LAAC;;;;;sCACD,6LAAC;;;;;sCACD,6LAAC;4BAAW,iBAAiB;;;;;;sCAC7B,6LAAC;4BAAS,eAAe;;;;;;sCACzB,6LAAC;4BAAO,aAAa;;;;;;sCACrB,6LAAC;4BAAS,eAAe;;;;;;sCAEzB,6LAAC,oKAAA,CAAA,gBAAa;4BACZ,WAAW;4BACX,YAAY;4BACZ,cAAc;4BACd,aAAa;4BACb,aAAa;4BACb,eAAe;4BACf,eAAe,KAAK,EAAE,GAAG;;;;;;sCAG3B,6LAAC,kKAAA,CAAA,cAAW;4BAAC,QAAO;;;;;;;;;;;;;;;;;0BAKxB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG,CAAC;oBAAG;oBAC9B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAyB;;;;;;sCACvC,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;0BAOzC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;oBACZ,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;YAMF,2BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BAAqB;;;;;;;;;;;0BAKxC,6LAAC,6HAAA,CAAA,UAAW;gBACV,WAAW;gBACX,SAAS;;;;;;0BAIX,6LAAC,4HAAA,CAAA,UAAU;gBACT,WAAW;gBACX,SAAS,IAAM,kBAAkB;;;;;;0BAInC,6LAAC,+HAAA,CAAA,UAAa;gBACZ,WAAW;gBACX,SAAS;;;;;;;;;;;;AAIjB;IA1HwB;MAAA", "debugId": null}}, {"offset": {"line": 3411, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Users/<USER>/KangPCode%20Porto/kangpcode/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport LoadingScreen from '@/components/LoadingScreen';\nimport RoomScene3D from '@/components/RoomScene3D';\nimport { motion, AnimatePresence } from 'framer-motion';\n\ntype SceneType = 'loading' | 'intro' | 'room';\n\nexport default function Home() {\n  const [currentScene, setCurrentScene] = useState<SceneType>('loading');\n\n  // Disable right-click context menu for security\n  useEffect(() => {\n    const disableRightClick = (e: MouseEvent) => e.preventDefault();\n    document.addEventListener('contextmenu', disableRightClick);\n    return () => document.removeEventListener('contextmenu', disableRightClick);\n  }, []);\n\n  const handleLoadingComplete = () => {\n    setCurrentScene('intro');\n  };\n\n  const handleEnterRoom = () => {\n    setCurrentScene('room');\n  };\n\n  const handleExitRoom = () => {\n    setCurrentScene('intro');\n  };\n\n  return (\n    <div className=\"w-full h-screen overflow-hidden\">\n      <AnimatePresence mode=\"wait\">\n        {currentScene === 'loading' && (\n          <LoadingScreen key=\"loading\" onComplete={handleLoadingComplete} />\n        )}\n\n        {currentScene === 'intro' && (\n          <motion.div\n            key=\"intro\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            className=\"w-full h-screen bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-900 flex items-center justify-center relative overflow-hidden\"\n          >\n            {/* Background Animation */}\n            <div className=\"absolute inset-0\">\n              {[...Array(50)].map((_, i) => (\n                <motion.div\n                  key={i}\n                  className=\"absolute w-1 h-1 bg-white rounded-full\"\n                  style={{\n                    left: `${Math.random() * 100}%`,\n                    top: `${Math.random() * 100}%`,\n                  }}\n                  animate={{\n                    opacity: [0, 1, 0],\n                    scale: [0, 1, 0],\n                  }}\n                  transition={{\n                    duration: 3,\n                    repeat: Infinity,\n                    delay: Math.random() * 3,\n                  }}\n                />\n              ))}\n            </div>\n\n            {/* Main Content */}\n            <div className=\"text-center z-10 space-y-8\">\n              <motion.h1\n                initial={{ y: -50, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ delay: 0.2 }}\n                className=\"text-6xl md:text-8xl font-bold text-white mb-4\"\n              >\n                KangPCode\n              </motion.h1>\n\n              <motion.p\n                initial={{ y: 50, opacity: 0 }}\n                animate={{ y: 0, opacity: 1 }}\n                transition={{ delay: 0.4 }}\n                className=\"text-2xl text-purple-200 mb-8\"\n              >\n                Portfolio 3D Interaktif\n              </motion.p>\n\n              <motion.p\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.6 }}\n                className=\"text-lg text-purple-300 mb-12 max-w-2xl mx-auto\"\n              >\n                Selamat datang di dunia virtual KangPCode! Jelajahi kamar virtual yang penuh dengan teknologi,\n                proyek, dan inspirasi dari seorang developer Indonesia.\n              </motion.p>\n\n              {/* Door to Room */}\n              <motion.div\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ delay: 0.8, type: \"spring\", stiffness: 200 }}\n                className=\"relative\"\n              >\n                <motion.button\n                  onClick={handleEnterRoom}\n                  className=\"group relative bg-gradient-to-r from-cyan-500 to-blue-600 hover:from-cyan-600 hover:to-blue-700 text-white font-bold py-4 px-8 rounded-lg text-xl shadow-2xl transition-all duration-300 transform hover:scale-105\"\n                  whileHover={{ scale: 1.05 }}\n                  whileTap={{ scale: 0.95 }}\n                >\n                  <span className=\"relative z-10\">🚪 Masuk ke Kamar KangPCode</span>\n\n                  {/* Glow Effect */}\n                  <motion.div\n                    className=\"absolute inset-0 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-lg blur-lg opacity-30\"\n                    animate={{\n                      scale: [1, 1.1, 1],\n                      opacity: [0.3, 0.5, 0.3],\n                    }}\n                    transition={{\n                      duration: 2,\n                      repeat: Infinity,\n                    }}\n                  />\n                </motion.button>\n              </motion.div>\n\n              <motion.p\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 1 }}\n                className=\"text-sm text-purple-400 mt-4\"\n              >\n                Klik pintu untuk memulai petualangan 3D Anda\n              </motion.p>\n            </div>\n          </motion.div>\n        )}\n\n        {currentScene === 'room' && (\n          <motion.div\n            key=\"room\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n          >\n            <RoomScene3D onExitRoom={handleExitRoom} />\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;;;AALA;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa;IAE5D,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,MAAM;oDAAoB,CAAC,IAAkB,EAAE,cAAc;;YAC7D,SAAS,gBAAgB,CAAC,eAAe;YACzC;kCAAO,IAAM,SAAS,mBAAmB,CAAC,eAAe;;QAC3D;yBAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,gBAAgB;IAClB;IAEA,MAAM,kBAAkB;QACtB,gBAAgB;IAClB;IAEA,MAAM,iBAAiB;QACrB,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,4LAAA,CAAA,kBAAe;YAAC,MAAK;;gBACnB,iBAAiB,2BAChB,6LAAC,+HAAA,CAAA,UAAa;oBAAe,YAAY;mBAAtB;;;;;gBAGpB,iBAAiB,yBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;sCACZ;mCAAI,MAAM;6BAAI,CAAC,GAAG,CAAC,CAAC,GAAG,kBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,OAAO;wCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wCAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAChC;oCACA,SAAS;wCACP,SAAS;4CAAC;4CAAG;4CAAG;yCAAE;wCAClB,OAAO;4CAAC;4CAAG;4CAAG;yCAAE;oCAClB;oCACA,YAAY;wCACV,UAAU;wCACV,QAAQ;wCACR,OAAO,KAAK,MAAM,KAAK;oCACzB;mCAdK;;;;;;;;;;sCAoBX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,GAAG,CAAC;wCAAI,SAAS;oCAAE;oCAC9B,SAAS;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;8CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,GAAG;wCAAI,SAAS;oCAAE;oCAC7B,SAAS;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;8CAID,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,OAAO;oCAAI;oCACzB,WAAU;8CACX;;;;;;8CAMD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,YAAY;wCAAE,OAAO;wCAAK,MAAM;wCAAU,WAAW;oCAAI;oCACzD,WAAU;8CAEV,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAGhC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDACP,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;oDAClB,SAAS;wDAAC;wDAAK;wDAAK;qDAAI;gDAC1B;gDACA,YAAY;oDACV,UAAU;oDACV,QAAQ;gDACV;;;;;;;;;;;;;;;;;8CAKN,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,OAAO;oCAAE;oCACvB,WAAU;8CACX;;;;;;;;;;;;;mBA9FC;;;;;gBAqGP,iBAAiB,wBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;8BAEnB,cAAA,6LAAC,6HAAA,CAAA,UAAW;wBAAC,YAAY;;;;;;mBALrB;;;;;;;;;;;;;;;;AAWhB;GAjJwB;KAAA", "debugId": null}}]}
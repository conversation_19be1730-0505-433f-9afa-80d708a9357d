'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';

interface ResumePDFProps {
  isVisible: boolean;
  onClose: () => void;
}

export default function ResumePDF({ isVisible, onClose }: ResumePDFProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<'modern' | 'classic' | 'creative'>('modern');

  const resumeData = {
    personalInfo: {
      name: "<PERSON><PERSON><PERSON>",
      title: "Fullstack Developer & Content Creator",
      email: "<EMAIL>",
      phone: "+62-xxx-xxxx-xxxx",
      location: "Indonesia",
      website: "kangpcode.dev",
      github: "github.com/kangpcode",
      linkedin: "linkedin.com/in/kangpcode"
    },
    summary: "Passionate fullstack developer with 3+ years of experience building modern web applications. Specialized in React/Next.js ecosystem with strong background in backend development. Active in tech community and committed to sharing knowledge through content creation and mentoring.",
    experience: [
      {
        title: "Fullstack Developer",
        company: "CV Bintang Gumilang",
        period: "2023 - Present",
        location: "Indonesia",
        responsibilities: [
          "Developed and maintained web applications using React, Next.js, and Node.js",
          "Collaborated with cross-functional teams to deliver high-quality software solutions",
          "Implemented responsive designs and optimized application performance",
          "Mentored junior developers and conducted code reviews"
        ]
      },
      {
        title: "Freelance Developer",
        company: "Various Clients",
        period: "2022 - 2023",
        location: "Remote",
        responsibilities: [
          "Built custom web applications for local businesses",
          "Provided technical consultation and project planning",
          "Delivered projects on time and within budget",
          "Maintained long-term client relationships"
        ]
      }
    ],
    education: [
      {
        degree: "Bachelor of Computer Science",
        institution: "Universitas XYZ",
        period: "2020 - 2024",
        location: "Indonesia",
        gpa: "3.8/4.0"
      }
    ],
    skills: {
      frontend: ["React", "Next.js", "TypeScript", "TailwindCSS", "Three.js"],
      backend: ["Node.js", "Express", "Python", "Django", "FastAPI"],
      database: ["PostgreSQL", "MongoDB", "SQLite", "Redis"],
      tools: ["Git", "Docker", "AWS", "Vercel", "Figma"],
      other: ["REST APIs", "GraphQL", "Testing", "CI/CD", "Agile"]
    },
    projects: [
      {
        name: "Portfolio 3D Interaktif",
        description: "Interactive 3D portfolio website with game-like experience",
        technologies: ["Next.js", "Three.js", "TypeScript", "TailwindCSS"],
        link: "kangpcode.dev"
      },
      {
        name: "Langkah Kode Nusantara",
        description: "Educational programming book for Indonesian developers",
        technologies: ["Markdown", "GitBook", "React"],
        link: "github.com/kangpcode/langkah-kode-nusantara"
      }
    ],
    achievements: [
      "Author of 'Langkah Kode Nusantara' programming book",
      "Active contributor to open source projects",
      "Speaker at local tech meetups",
      "Mentor for junior developers"
    ]
  };

  const templates = [
    {
      id: 'modern',
      name: 'Modern',
      description: 'Clean and professional design with modern typography',
      preview: '📄'
    },
    {
      id: 'classic',
      name: 'Classic',
      description: 'Traditional resume format with elegant styling',
      preview: '📋'
    },
    {
      id: 'creative',
      name: 'Creative',
      description: 'Eye-catching design perfect for creative roles',
      preview: '🎨'
    }
  ];

  const generatePDF = async () => {
    setIsGenerating(true);
    
    try {
      // Simulate PDF generation
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Create a simple HTML content for PDF
      const htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <title>Resume - ${resumeData.personalInfo.name}</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
            .header { text-align: center; margin-bottom: 30px; }
            .name { font-size: 28px; font-weight: bold; margin-bottom: 10px; }
            .title { font-size: 18px; color: #666; margin-bottom: 20px; }
            .contact { font-size: 14px; }
            .section { margin: 30px 0; }
            .section-title { font-size: 20px; font-weight: bold; border-bottom: 2px solid #333; padding-bottom: 5px; margin-bottom: 15px; }
            .experience-item { margin-bottom: 20px; }
            .job-title { font-weight: bold; font-size: 16px; }
            .company { color: #666; }
            .period { float: right; color: #666; }
            .skills-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; }
            .skill-category { margin-bottom: 15px; }
            .skill-category-title { font-weight: bold; margin-bottom: 5px; }
            ul { margin: 10px 0; padding-left: 20px; }
          </style>
        </head>
        <body>
          <div class="header">
            <div class="name">${resumeData.personalInfo.name}</div>
            <div class="title">${resumeData.personalInfo.title}</div>
            <div class="contact">
              ${resumeData.personalInfo.email} | ${resumeData.personalInfo.phone} | ${resumeData.personalInfo.location}<br>
              ${resumeData.personalInfo.website} | ${resumeData.personalInfo.github}
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">Professional Summary</div>
            <p>${resumeData.summary}</p>
          </div>
          
          <div class="section">
            <div class="section-title">Experience</div>
            ${resumeData.experience.map(exp => `
              <div class="experience-item">
                <div class="job-title">${exp.title}</div>
                <div class="company">${exp.company} <span class="period">${exp.period}</span></div>
                <ul>
                  ${exp.responsibilities.map(resp => `<li>${resp}</li>`).join('')}
                </ul>
              </div>
            `).join('')}
          </div>
          
          <div class="section">
            <div class="section-title">Education</div>
            ${resumeData.education.map(edu => `
              <div class="experience-item">
                <div class="job-title">${edu.degree}</div>
                <div class="company">${edu.institution} <span class="period">${edu.period}</span></div>
                <p>GPA: ${edu.gpa}</p>
              </div>
            `).join('')}
          </div>
          
          <div class="section">
            <div class="section-title">Technical Skills</div>
            <div class="skills-grid">
              <div class="skill-category">
                <div class="skill-category-title">Frontend</div>
                <div>${resumeData.skills.frontend.join(', ')}</div>
              </div>
              <div class="skill-category">
                <div class="skill-category-title">Backend</div>
                <div>${resumeData.skills.backend.join(', ')}</div>
              </div>
              <div class="skill-category">
                <div class="skill-category-title">Database</div>
                <div>${resumeData.skills.database.join(', ')}</div>
              </div>
              <div class="skill-category">
                <div class="skill-category-title">Tools & Others</div>
                <div>${resumeData.skills.tools.concat(resumeData.skills.other).join(', ')}</div>
              </div>
            </div>
          </div>
          
          <div class="section">
            <div class="section-title">Key Projects</div>
            ${resumeData.projects.map(project => `
              <div class="experience-item">
                <div class="job-title">${project.name}</div>
                <p>${project.description}</p>
                <p><strong>Technologies:</strong> ${project.technologies.join(', ')}</p>
                <p><strong>Link:</strong> ${project.link}</p>
              </div>
            `).join('')}
          </div>
          
          <div class="section">
            <div class="section-title">Achievements</div>
            <ul>
              ${resumeData.achievements.map(achievement => `<li>${achievement}</li>`).join('')}
            </ul>
          </div>
        </body>
        </html>
      `;
      
      // Create blob and download
      const blob = new Blob([htmlContent], { type: 'text/html' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `Resume_${resumeData.personalInfo.name.replace(/\s+/g, '_')}_${selectedTemplate}.html`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-4 bg-white rounded-lg shadow-2xl border overflow-hidden z-50"
    >
      {/* Header */}
      <div className="h-16 bg-gradient-to-r from-blue-600 to-purple-600 flex items-center justify-between px-6 text-white">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">📄</span>
          <div>
            <h2 className="font-bold text-lg">Resume PDF Generator</h2>
            <p className="text-sm text-blue-100">Generate professional resume in PDF format</p>
          </div>
        </div>
        <button
          onClick={onClose}
          className="hover:bg-white/20 p-2 rounded transition-colors"
        >
          ✕
        </button>
      </div>

      {/* Content */}
      <div className="flex h-[calc(100%-4rem)]">
        {/* Template Selection */}
        <div className="w-1/3 p-6 border-r bg-gray-50">
          <h3 className="text-lg font-bold text-gray-900 mb-4">Choose Template</h3>
          <div className="space-y-3">
            {templates.map((template) => (
              <motion.button
                key={template.id}
                onClick={() => setSelectedTemplate(template.id as any)}
                className={`w-full p-4 rounded-lg border-2 text-left transition-all ${
                  selectedTemplate === template.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 bg-white'
                }`}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <div className="flex items-start space-x-3">
                  <span className="text-2xl">{template.preview}</span>
                  <div>
                    <h4 className="font-bold text-gray-900">{template.name}</h4>
                    <p className="text-sm text-gray-600">{template.description}</p>
                  </div>
                </div>
              </motion.button>
            ))}
          </div>

          {/* Generate Button */}
          <motion.button
            onClick={generatePDF}
            disabled={isGenerating}
            className="w-full mt-6 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-3 px-4 rounded-lg transition-colors"
            whileHover={{ scale: isGenerating ? 1 : 1.02 }}
            whileTap={{ scale: isGenerating ? 1 : 0.98 }}
          >
            {isGenerating ? (
              <div className="flex items-center justify-center space-x-2">
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Generating PDF...</span>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-2">
                <span>📄</span>
                <span>Generate PDF Resume</span>
              </div>
            )}
          </motion.button>
        </div>

        {/* Resume Preview */}
        <div className="flex-1 p-6 overflow-y-auto">
          <div className="max-w-2xl mx-auto bg-white border rounded-lg p-8 shadow-sm">
            {/* Header */}
            <div className="text-center mb-6">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{resumeData.personalInfo.name}</h1>
              <h2 className="text-xl text-gray-600 mb-4">{resumeData.personalInfo.title}</h2>
              <div className="text-sm text-gray-600 space-y-1">
                <div>{resumeData.personalInfo.email} | {resumeData.personalInfo.phone}</div>
                <div>{resumeData.personalInfo.website} | {resumeData.personalInfo.github}</div>
                <div>{resumeData.personalInfo.location}</div>
              </div>
            </div>

            {/* Summary */}
            <div className="mb-6">
              <h3 className="text-lg font-bold text-gray-900 border-b-2 border-gray-300 pb-1 mb-3">Professional Summary</h3>
              <p className="text-gray-700 leading-relaxed">{resumeData.summary}</p>
            </div>

            {/* Experience */}
            <div className="mb-6">
              <h3 className="text-lg font-bold text-gray-900 border-b-2 border-gray-300 pb-1 mb-3">Experience</h3>
              {resumeData.experience.map((exp, index) => (
                <div key={index} className="mb-4">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-bold text-gray-900">{exp.title}</h4>
                      <p className="text-gray-600">{exp.company}</p>
                    </div>
                    <span className="text-sm text-gray-500">{exp.period}</span>
                  </div>
                  <ul className="list-disc list-inside text-gray-700 space-y-1">
                    {exp.responsibilities.map((resp, idx) => (
                      <li key={idx} className="text-sm">{resp}</li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>

            {/* Skills */}
            <div className="mb-6">
              <h3 className="text-lg font-bold text-gray-900 border-b-2 border-gray-300 pb-1 mb-3">Technical Skills</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">Frontend</h4>
                  <p className="text-sm text-gray-700">{resumeData.skills.frontend.join(', ')}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">Backend</h4>
                  <p className="text-sm text-gray-700">{resumeData.skills.backend.join(', ')}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">Database</h4>
                  <p className="text-sm text-gray-700">{resumeData.skills.database.join(', ')}</p>
                </div>
                <div>
                  <h4 className="font-semibold text-gray-900 mb-1">Tools</h4>
                  <p className="text-sm text-gray-700">{resumeData.skills.tools.join(', ')}</p>
                </div>
              </div>
            </div>

            {/* Projects */}
            <div className="mb-6">
              <h3 className="text-lg font-bold text-gray-900 border-b-2 border-gray-300 pb-1 mb-3">Key Projects</h3>
              {resumeData.projects.map((project, index) => (
                <div key={index} className="mb-3">
                  <h4 className="font-bold text-gray-900">{project.name}</h4>
                  <p className="text-sm text-gray-700 mb-1">{project.description}</p>
                  <p className="text-xs text-gray-600">
                    <strong>Tech:</strong> {project.technologies.join(', ')} | <strong>Link:</strong> {project.link}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

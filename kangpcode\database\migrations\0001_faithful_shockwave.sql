CREATE TABLE `error_log` (
	`id` integer PRIMARY KEY NOT NULL,
	`session_id` text,
	`error` text NOT NULL,
	`stack` text,
	`context` text,
	`page` text,
	`user_agent` text,
	`timestamp` integer DEFAULT (unixepoch())
);
--> statement-breakpoint
CREATE TABLE `performance_metric` (
	`id` integer PRIMARY KEY NOT NULL,
	`session_id` text NOT NULL,
	`metric` text NOT NULL,
	`value` real NOT NULL,
	`unit` text,
	`page` text,
	`device` text,
	`connection` text,
	`timestamp` integer DEFAULT (unixepoch())
);
--> statement-breakpoint
CREATE TABLE `user_session` (
	`id` integer PRIMARY KEY NOT NULL,
	`session_id` text NOT NULL,
	`user_agent` text,
	`ip` text,
	`country` text,
	`city` text,
	`device` text,
	`browser` text,
	`os` text,
	`referrer` text,
	`landing_page` text,
	`language` text,
	`theme` text,
	`music_enabled` integer DEFAULT false,
	`start_time` integer DEFAULT (unixepoch()),
	`end_time` integer,
	`duration` integer,
	`page_views` integer DEFAULT 0,
	`interactions` integer DEFAULT 0,
	`created_at` integer DEFAULT (unixepoch()),
	`updated_at` integer DEFAULT (unixepoch())
);
--> statement-breakpoint
CREATE UNIQUE INDEX `user_session_session_id_unique` ON `user_session` (`session_id`);
'use client';

import { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface MiniGameProps {
  isVisible: boolean;
  onClose: () => void;
}

type GameType = 'menu' | 'snake' | 'tictactoe' | 'trivia';

interface TriviaQuestion {
  question: string;
  options: string[];
  correct: number;
  explanation: string;
}

export default function MiniGame({ isVisible, onClose }: MiniGameProps) {
  const [currentGame, setCurrentGame] = useState<GameType>('menu');
  const [score, setScore] = useState(0);

  // Snake Game State
  const [snake, setSnake] = useState([[10, 10]]);
  const [food, setFood] = useState([15, 15]);
  const [direction, setDirection] = useState([0, 1]);
  const [gameRunning, setGameRunning] = useState(false);

  // TicTacToe State
  const [board, setBoard] = useState(Array(9).fill(null));
  const [isXNext, setIsXNext] = useState(true);
  const [winner, setWinner] = useState<string | null>(null);

  // Trivia State
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [triviaScore, setTriviaScore] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<number | null>(null);
  const [showExplanation, setShowExplanation] = useState(false);

  const triviaQuestions: TriviaQuestion[] = [
    {
      question: "Siapa yang menciptakan konsep algoritma?",
      options: ["Al-Khawarizmi", "Alan Turing", "Ada Lovelace", "Charles Babbage"],
      correct: 0,
      explanation: "Al-Khawarizmi (780-850 M) adalah matematikawan Persia yang menciptakan konsep algoritma. Kata 'algoritma' berasal dari nama latinnya 'Algorithmi'."
    },
    {
      question: "Bahasa pemrograman apa yang dibuat oleh Brendan Eich?",
      options: ["Python", "Java", "JavaScript", "C++"],
      correct: 2,
      explanation: "JavaScript dibuat oleh Brendan Eich di Netscape pada tahun 1995 dalam waktu hanya 10 hari."
    },
    {
      question: "Apa kepanjangan dari HTML?",
      options: ["Hyper Text Markup Language", "High Tech Modern Language", "Home Tool Markup Language", "Hyperlink Text Management Language"],
      correct: 0,
      explanation: "HTML adalah HyperText Markup Language, bahasa markup standar untuk membuat halaman web."
    },
    {
      question: "Siapa pendiri Linux?",
      options: ["Bill Gates", "Steve Jobs", "Linus Torvalds", "Richard Stallman"],
      correct: 2,
      explanation: "Linus Torvalds menciptakan kernel Linux pada tahun 1991 ketika masih mahasiswa di University of Helsinki."
    },
    {
      question: "Apa yang dimaksud dengan 'Open Source'?",
      options: ["Software berbayar", "Software dengan source code terbuka", "Software untuk perusahaan", "Software mobile"],
      correct: 1,
      explanation: "Open Source adalah software yang source code-nya tersedia untuk umum dan dapat dimodifikasi serta didistribusikan secara bebas."
    }
  ];

  // Snake Game Logic
  const moveSnake = useCallback(() => {
    if (!gameRunning) return;

    setSnake(currentSnake => {
      const newSnake = [...currentSnake];
      const head = [newSnake[0][0] + direction[0], newSnake[0][1] + direction[1]];

      // Check collision with walls
      if (head[0] < 0 || head[0] >= 20 || head[1] < 0 || head[1] >= 20) {
        setGameRunning(false);
        return currentSnake;
      }

      // Check collision with self
      if (newSnake.some(segment => segment[0] === head[0] && segment[1] === head[1])) {
        setGameRunning(false);
        return currentSnake;
      }

      newSnake.unshift(head);

      // Check if food is eaten
      if (head[0] === food[0] && head[1] === food[1]) {
        setScore(prev => prev + 10);
        setFood([Math.floor(Math.random() * 20), Math.floor(Math.random() * 20)]);
      } else {
        newSnake.pop();
      }

      return newSnake;
    });
  }, [direction, food, gameRunning]);

  useEffect(() => {
    if (currentGame === 'snake' && gameRunning) {
      const interval = setInterval(moveSnake, 200);
      return () => clearInterval(interval);
    }
  }, [moveSnake, currentGame, gameRunning]);

  // TicTacToe Logic
  const checkWinner = (squares: (string | null)[]) => {
    const lines = [
      [0, 1, 2], [3, 4, 5], [6, 7, 8], // rows
      [0, 3, 6], [1, 4, 7], [2, 5, 8], // columns
      [0, 4, 8], [2, 4, 6] // diagonals
    ];

    for (let line of lines) {
      const [a, b, c] = line;
      if (squares[a] && squares[a] === squares[b] && squares[a] === squares[c]) {
        return squares[a];
      }
    }
    return null;
  };

  const handleTicTacToeClick = (index: number) => {
    if (board[index] || winner) return;

    const newBoard = [...board];
    newBoard[index] = isXNext ? 'X' : 'O';
    setBoard(newBoard);
    setIsXNext(!isXNext);

    const gameWinner = checkWinner(newBoard);
    if (gameWinner) {
      setWinner(gameWinner);
      setScore(prev => prev + (gameWinner === 'X' ? 100 : 0));
    }
  };

  // Trivia Logic
  const handleTriviaAnswer = (answerIndex: number) => {
    setSelectedAnswer(answerIndex);
    setShowExplanation(true);
    
    if (answerIndex === triviaQuestions[currentQuestion].correct) {
      setTriviaScore(prev => prev + 20);
    }
  };

  const nextQuestion = () => {
    if (currentQuestion < triviaQuestions.length - 1) {
      setCurrentQuestion(prev => prev + 1);
      setSelectedAnswer(null);
      setShowExplanation(false);
    } else {
      // Game finished
      setScore(triviaScore);
      alert(`Trivia selesai! Skor akhir: ${triviaScore}/${triviaQuestions.length * 20}`);
    }
  };

  // Reset functions
  const resetSnake = () => {
    setSnake([[10, 10]]);
    setFood([15, 15]);
    setDirection([0, 1]);
    setGameRunning(false);
    setScore(0);
  };

  const resetTicTacToe = () => {
    setBoard(Array(9).fill(null));
    setIsXNext(true);
    setWinner(null);
    setScore(0);
  };

  const resetTrivia = () => {
    setCurrentQuestion(0);
    setTriviaScore(0);
    setSelectedAnswer(null);
    setShowExplanation(false);
    setScore(0);
  };

  if (!isVisible) return null;

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-4 bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 rounded-lg shadow-2xl border overflow-hidden z-50"
    >
      {/* Header */}
      <div className="h-16 bg-black/30 backdrop-blur-sm flex items-center justify-between px-6 text-white border-b border-white/20">
        <div className="flex items-center space-x-3">
          <span className="text-2xl">🎮</span>
          <div>
            <h2 className="font-bold text-lg">KangPCode Mini Games</h2>
            <p className="text-sm text-purple-200">Skor: {score}</p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          {currentGame !== 'menu' && (
            <button
              onClick={() => setCurrentGame('menu')}
              className="px-3 py-1 bg-white/20 hover:bg-white/30 rounded transition-colors text-sm"
            >
              🏠 Menu
            </button>
          )}
          <button
            onClick={onClose}
            className="hover:bg-white/20 p-2 rounded transition-colors"
          >
            ✕
          </button>
        </div>
      </div>

      {/* Game Content */}
      <div className="h-[calc(100%-4rem)] p-6">
        <AnimatePresence mode="wait">
          {currentGame === 'menu' && (
            <motion.div
              key="menu"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="h-full flex items-center justify-center"
            >
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl">
                <motion.button
                  onClick={() => {
                    setCurrentGame('snake');
                    resetSnake();
                  }}
                  className="bg-green-600 hover:bg-green-700 text-white p-8 rounded-lg text-center transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <div className="text-6xl mb-4">🐍</div>
                  <h3 className="text-xl font-bold mb-2">Snake Game</h3>
                  <p className="text-sm opacity-90">Klasik game ular yang adiktif</p>
                </motion.button>

                <motion.button
                  onClick={() => {
                    setCurrentGame('tictactoe');
                    resetTicTacToe();
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white p-8 rounded-lg text-center transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <div className="text-6xl mb-4">⭕</div>
                  <h3 className="text-xl font-bold mb-2">Tic Tac Toe</h3>
                  <p className="text-sm opacity-90">Permainan strategi klasik</p>
                </motion.button>

                <motion.button
                  onClick={() => {
                    setCurrentGame('trivia');
                    resetTrivia();
                  }}
                  className="bg-purple-600 hover:bg-purple-700 text-white p-8 rounded-lg text-center transition-colors"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <div className="text-6xl mb-4">🧠</div>
                  <h3 className="text-xl font-bold mb-2">Tech Trivia</h3>
                  <p className="text-sm opacity-90">Kuis sejarah teknologi</p>
                </motion.button>
              </div>
            </motion.div>
          )}

          {currentGame === 'snake' && (
            <motion.div
              key="snake"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="h-full flex flex-col items-center justify-center text-white"
            >
              <div className="mb-4 text-center">
                <h3 className="text-2xl font-bold mb-2">🐍 Snake Game</h3>
                <p className="text-lg">Skor: {score}</p>
              </div>
              
              <div className="grid grid-cols-20 gap-0 bg-gray-800 p-2 rounded-lg mb-4">
                {Array.from({ length: 400 }, (_, i) => {
                  const x = i % 20;
                  const y = Math.floor(i / 20);
                  const isSnake = snake.some(segment => segment[0] === x && segment[1] === y);
                  const isFood = food[0] === x && food[1] === y;
                  
                  return (
                    <div
                      key={i}
                      className={`w-4 h-4 ${
                        isSnake ? 'bg-green-500' : 
                        isFood ? 'bg-red-500' : 
                        'bg-gray-700'
                      }`}
                    />
                  );
                })}
              </div>
              
              <div className="flex space-x-4">
                <button
                  onClick={() => setGameRunning(!gameRunning)}
                  className="px-6 py-2 bg-green-600 hover:bg-green-700 rounded transition-colors"
                >
                  {gameRunning ? 'Pause' : 'Start'}
                </button>
                <button
                  onClick={resetSnake}
                  className="px-6 py-2 bg-red-600 hover:bg-red-700 rounded transition-colors"
                >
                  Reset
                </button>
              </div>
              
              <div className="mt-4 text-center text-sm opacity-75">
                Gunakan WASD atau arrow keys untuk mengontrol ular
              </div>
            </motion.div>
          )}

          {currentGame === 'tictactoe' && (
            <motion.div
              key="tictactoe"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="h-full flex flex-col items-center justify-center text-white"
            >
              <div className="mb-6 text-center">
                <h3 className="text-2xl font-bold mb-2">⭕ Tic Tac Toe</h3>
                {winner ? (
                  <p className="text-lg">🎉 Pemenang: {winner}</p>
                ) : (
                  <p className="text-lg">Giliran: {isXNext ? 'X' : 'O'}</p>
                )}
              </div>
              
              <div className="grid grid-cols-3 gap-2 mb-6">
                {board.map((cell, index) => (
                  <button
                    key={index}
                    onClick={() => handleTicTacToeClick(index)}
                    className="w-20 h-20 bg-gray-700 hover:bg-gray-600 rounded-lg text-3xl font-bold transition-colors"
                  >
                    {cell}
                  </button>
                ))}
              </div>
              
              <button
                onClick={resetTicTacToe}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 rounded transition-colors"
              >
                Reset Game
              </button>
            </motion.div>
          )}

          {currentGame === 'trivia' && (
            <motion.div
              key="trivia"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              className="h-full flex flex-col items-center justify-center text-white max-w-2xl mx-auto"
            >
              <div className="mb-6 text-center">
                <h3 className="text-2xl font-bold mb-2">🧠 Tech Trivia</h3>
                <p className="text-lg">Pertanyaan {currentQuestion + 1} dari {triviaQuestions.length}</p>
                <p className="text-lg">Skor: {triviaScore}</p>
              </div>
              
              <div className="bg-white/10 backdrop-blur-sm p-6 rounded-lg mb-6 w-full">
                <h4 className="text-xl font-bold mb-4">{triviaQuestions[currentQuestion].question}</h4>
                
                <div className="space-y-3">
                  {triviaQuestions[currentQuestion].options.map((option, index) => (
                    <button
                      key={index}
                      onClick={() => handleTriviaAnswer(index)}
                      disabled={selectedAnswer !== null}
                      className={`w-full p-3 rounded-lg text-left transition-colors ${
                        selectedAnswer === null 
                          ? 'bg-white/20 hover:bg-white/30' 
                          : selectedAnswer === index
                          ? index === triviaQuestions[currentQuestion].correct
                            ? 'bg-green-600'
                            : 'bg-red-600'
                          : index === triviaQuestions[currentQuestion].correct
                          ? 'bg-green-600'
                          : 'bg-white/10'
                      }`}
                    >
                      {String.fromCharCode(65 + index)}. {option}
                    </button>
                  ))}
                </div>
                
                {showExplanation && (
                  <div className="mt-4 p-4 bg-blue-600/50 rounded-lg">
                    <p className="text-sm">{triviaQuestions[currentQuestion].explanation}</p>
                  </div>
                )}
              </div>
              
              {showExplanation && (
                <button
                  onClick={nextQuestion}
                  className="px-6 py-2 bg-purple-600 hover:bg-purple-700 rounded transition-colors"
                >
                  {currentQuestion < triviaQuestions.length - 1 ? 'Pertanyaan Berikutnya' : 'Selesai'}
                </button>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </motion.div>
  );
}

'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface ComputerGUIProps {
  isVisible: boolean;
  onClose: () => void;
  onOpenTerminal?: () => void;
  onOpenResume?: () => void;
  onOpenGames?: () => void;
}

type AppType = 'terminal' | 'resume' | 'projects' | 'games' | 'vscode' | 'browser' | 'settings' | 'info';

export default function ComputerGUI({ isVisible, onClose, onOpenTerminal, onOpenResume, onOpenGames }: ComputerGUIProps) {
  const [currentTime, setCurrentTime] = useState(new Date());
  const [activeApp, setActiveApp] = useState<AppType | null>(null);
  const [isBooting, setIsBooting] = useState(true);

  // Update time every second
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Boot sequence
  useEffect(() => {
    if (isVisible) {
      setIsBooting(true);
      const bootTimer = setTimeout(() => {
        setIsBooting(false);
      }, 2000);
      return () => clearTimeout(bootTimer);
    }
  }, [isVisible]);

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('id-ID', { 
      hour: '2-digit', 
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('id-ID', { 
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const desktopApps = [
    { id: 'terminal', name: 'Terminal AI', icon: '🧠', color: 'bg-green-600', action: onOpenTerminal },
    { id: 'resume', name: 'Resume', icon: '📄', color: 'bg-blue-600', action: onOpenResume },
    { id: 'projects', name: 'My Projects', icon: '💻', color: 'bg-purple-600' },
    { id: 'games', name: 'Mini Games', icon: '🎮', color: 'bg-red-600', action: onOpenGames },
  ];

  const taskbarApps = [
    { id: 'terminal', name: 'Terminal AI', icon: '🧠' },
    { id: 'vscode', name: 'VSCode', icon: '💻' },
    { id: 'browser', name: 'Browser', icon: '🌐' },
    { id: 'settings', name: 'Settings', icon: '⚙️' },
    { id: 'info', name: 'System Info', icon: '🧮' },
    { id: 'games', name: 'Games', icon: '🎮' },
  ];

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 bg-black z-50 overflow-hidden">
      <AnimatePresence>
        {isBooting ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex items-center justify-center h-full bg-black text-white"
          >
            <div className="text-center space-y-4">
              <div className="text-6xl mb-4">🐧</div>
              <h2 className="text-2xl font-bold">KangPCode ArchLinux</h2>
              <p className="text-lg">Booting KDE Plasma...</p>
              <div className="flex justify-center space-x-1 mt-4">
                {[0, 1, 2].map((i) => (
                  <motion.div
                    key={i}
                    className="w-3 h-3 bg-blue-500 rounded-full"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.5, 1, 0.5],
                    }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      delay: i * 0.2,
                    }}
                  />
                ))}
              </div>
            </div>
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="h-full bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 relative"
          >
            {/* Desktop Wallpaper */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-800/50 to-purple-800/50" />
            
            {/* Top Panel */}
            <div className="absolute top-0 left-0 right-0 h-8 bg-gray-900/90 backdrop-blur-sm border-b border-gray-700 flex items-center justify-between px-4 text-white text-sm z-10">
              <div className="flex items-center space-x-4">
                <span className="font-bold">kangpcode@archlinux</span>
                <span>KDE Plasma 5.27</span>
              </div>
              <div className="flex items-center space-x-4">
                <span>{formatDate(currentTime)}</span>
                <span className="font-mono">{formatTime(currentTime)}</span>
                <button
                  onClick={onClose}
                  className="hover:bg-red-600 px-2 py-1 rounded transition-colors"
                >
                  ✕
                </button>
              </div>
            </div>

            {/* Desktop Icons */}
            <div className="absolute top-12 left-4 grid grid-cols-1 gap-4 z-10">
              {desktopApps.map((app) => (
                <motion.button
                  key={app.id}
                  onClick={() => {
                    if (app.action) {
                      app.action();
                    } else {
                      setActiveApp(app.id as AppType);
                    }
                  }}
                  className="flex flex-col items-center space-y-1 p-2 rounded hover:bg-white/10 transition-colors group"
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <div className={`w-12 h-12 ${app.color} rounded-lg flex items-center justify-center text-2xl shadow-lg group-hover:shadow-xl transition-shadow`}>
                    {app.icon}
                  </div>
                  <span className="text-white text-xs font-medium">{app.name}</span>
                </motion.button>
              ))}
            </div>

            {/* Bottom Taskbar */}
            <div className="absolute bottom-0 left-0 right-0 h-12 bg-gray-900/90 backdrop-blur-sm border-t border-gray-700 flex items-center justify-between px-4 z-10">
              {/* Start Menu */}
              <button className="flex items-center space-x-2 px-3 py-2 hover:bg-gray-700/50 rounded transition-colors text-white">
                <span className="text-xl">🐧</span>
                <span className="font-medium">Menu</span>
              </button>

              {/* App Icons */}
              <div className="flex items-center space-x-2">
                {taskbarApps.map((app) => (
                  <motion.button
                    key={app.id}
                    onClick={() => setActiveApp(app.id as AppType)}
                    className={`w-10 h-10 rounded-lg flex items-center justify-center text-xl transition-colors ${
                      activeApp === app.id 
                        ? 'bg-blue-600 text-white' 
                        : 'hover:bg-gray-700/50 text-gray-300'
                    }`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    title={app.name}
                  >
                    {app.icon}
                  </motion.button>
                ))}
              </div>

              {/* System Tray */}
              <div className="flex items-center space-x-2 text-white">
                <span className="text-sm">🔊 🔋 📶</span>
                <span className="text-sm font-mono">{formatTime(currentTime)}</span>
              </div>
            </div>

            {/* Active Application Window */}
            <AnimatePresence>
              {activeApp && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  className="absolute inset-8 bg-gray-800 rounded-lg shadow-2xl border border-gray-600 overflow-hidden z-20"
                >
                  {/* Window Title Bar */}
                  <div className="h-8 bg-gray-700 flex items-center justify-between px-4 border-b border-gray-600">
                    <span className="text-white font-medium">
                      {taskbarApps.find(app => app.id === activeApp)?.name}
                    </span>
                    <div className="flex space-x-2">
                      <button className="w-4 h-4 bg-yellow-500 rounded-full hover:bg-yellow-400"></button>
                      <button className="w-4 h-4 bg-green-500 rounded-full hover:bg-green-400"></button>
                      <button 
                        onClick={() => setActiveApp(null)}
                        className="w-4 h-4 bg-red-500 rounded-full hover:bg-red-400"
                      ></button>
                    </div>
                  </div>

                  {/* Window Content */}
                  <div className="flex-1 p-4 text-white overflow-auto">
                    {activeApp === 'terminal' && (
                      <div className="font-mono text-green-400 space-y-2">
                        <div>KangPCode Terminal AI v1.0</div>
                        <div>Type 'help' for available commands</div>
                        <div className="text-white">$ _</div>
                      </div>
                    )}
                    
                    {activeApp === 'resume' && (
                      <div className="text-center space-y-4">
                        <h3 className="text-xl font-bold">Resume Generator</h3>
                        <p>Generate PDF resume dari data KangPCode</p>
                        <button className="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded transition-colors">
                          Generate PDF
                        </button>
                      </div>
                    )}
                    
                    {activeApp === 'projects' && (
                      <div className="space-y-4">
                        <h3 className="text-xl font-bold">My Projects</h3>
                        <div className="grid grid-cols-2 gap-4">
                          <div className="bg-gray-700 p-4 rounded">
                            <h4 className="font-bold">Portfolio 3D</h4>
                            <p className="text-sm text-gray-300">Interactive 3D portfolio website</p>
                          </div>
                          <div className="bg-gray-700 p-4 rounded">
                            <h4 className="font-bold">Langkah Kode</h4>
                            <p className="text-sm text-gray-300">Educational programming book</p>
                          </div>
                        </div>
                      </div>
                    )}
                    
                    {activeApp === 'games' && (
                      <div className="text-center space-y-4">
                        <h3 className="text-xl font-bold">Mini Games</h3>
                        <div className="grid grid-cols-3 gap-4">
                          <button className="bg-green-600 hover:bg-green-700 p-4 rounded transition-colors">
                            🐍 Snake
                          </button>
                          <button className="bg-blue-600 hover:bg-blue-700 p-4 rounded transition-colors">
                            ⭕ TicTacToe
                          </button>
                          <button className="bg-purple-600 hover:bg-purple-700 p-4 rounded transition-colors">
                            🧠 Trivia
                          </button>
                        </div>
                      </div>
                    )}
                    
                    {(activeApp === 'vscode' || activeApp === 'browser' || activeApp === 'settings' || activeApp === 'info') && (
                      <div className="text-center space-y-4">
                        <h3 className="text-xl font-bold">Coming Soon</h3>
                        <p>Aplikasi ini sedang dalam pengembangan</p>
                      </div>
                    )}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
}

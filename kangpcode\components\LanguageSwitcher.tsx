'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface Language {
  code: string;
  name: string;
  flag: string;
  nativeName: string;
}

interface LanguageSwitcherProps {
  onLanguageChange?: (language: string) => void;
}

export default function LanguageSwitcher({ onLanguageChange }: LanguageSwitcherProps) {
  const [currentLanguage, setCurrentLanguage] = useState('id');
  const [isOpen, setIsOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  const languages: Language[] = [
    {
      code: 'id',
      name: 'Indonesian',
      flag: '🇮🇩',
      nativeName: 'Bahasa Indonesia'
    },
    {
      code: 'en',
      name: 'English',
      flag: '🇬🇧',
      nativeName: 'English'
    }
  ];

  useEffect(() => {
    setMounted(true);
    
    // Check for saved language preference
    const savedLanguage = localStorage.getItem('language');
    const browserLanguage = navigator.language.split('-')[0];
    
    const defaultLanguage = savedLanguage || 
      (languages.find(lang => lang.code === browserLanguage)?.code) || 
      'id';
    
    setCurrentLanguage(defaultLanguage);
  }, []);

  const changeLanguage = (languageCode: string) => {
    setCurrentLanguage(languageCode);
    setIsOpen(false);
    
    // Save to localStorage
    localStorage.setItem('language', languageCode);
    
    // Update document language
    document.documentElement.lang = languageCode;
    
    // Notify parent component
    onLanguageChange?.(languageCode);
  };

  const currentLang = languages.find(lang => lang.code === currentLanguage) || languages[0];

  if (!mounted) {
    return null; // Prevent hydration mismatch
  }

  return (
    <div className="relative">
      {/* Current Language Button */}
      <motion.button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-3 py-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg border border-white/20 transition-colors"
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        aria-label="Change language"
      >
        <span className="text-lg">{currentLang.flag}</span>
        <span className="text-white font-medium text-sm">{currentLang.code.toUpperCase()}</span>
        <motion.span
          animate={{ rotate: isOpen ? 180 : 0 }}
          transition={{ duration: 0.2 }}
          className="text-white text-xs"
        >
          ▼
        </motion.span>
      </motion.button>

      {/* Language Dropdown */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-10"
              onClick={() => setIsOpen(false)}
            />
            
            {/* Dropdown Menu */}
            <motion.div
              initial={{ opacity: 0, y: -10, scale: 0.95 }}
              animate={{ opacity: 1, y: 0, scale: 1 }}
              exit={{ opacity: 0, y: -10, scale: 0.95 }}
              transition={{ duration: 0.2 }}
              className="absolute top-full mt-2 right-0 bg-white dark:bg-gray-800 rounded-lg shadow-xl border border-gray-200 dark:border-gray-700 overflow-hidden z-20 min-w-48"
            >
              {languages.map((language) => (
                <motion.button
                  key={language.code}
                  onClick={() => changeLanguage(language.code)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 text-left hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                    currentLanguage === language.code 
                      ? 'bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400' 
                      : 'text-gray-700 dark:text-gray-300'
                  }`}
                  whileHover={{ x: 4 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <span className="text-xl">{language.flag}</span>
                  <div className="flex-1">
                    <div className="font-medium">{language.nativeName}</div>
                    <div className="text-xs opacity-70">{language.name}</div>
                  </div>
                  {currentLanguage === language.code && (
                    <motion.span
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      className="text-blue-500"
                    >
                      ✓
                    </motion.span>
                  )}
                </motion.button>
              ))}
              
              {/* Language Info */}
              <div className="border-t border-gray-200 dark:border-gray-700 p-3 bg-gray-50 dark:bg-gray-800">
                <div className="text-xs text-gray-500 dark:text-gray-400 text-center">
                  🌐 Multilingual Portfolio
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </div>
  );
}

// Hook for using current language in other components
export function useLanguage() {
  const [currentLanguage, setCurrentLanguage] = useState('id');
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    
    const savedLanguage = localStorage.getItem('language');
    if (savedLanguage) {
      setCurrentLanguage(savedLanguage);
    }
    
    // Listen for language changes
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key === 'language' && e.newValue) {
        setCurrentLanguage(e.newValue);
      }
    };
    
    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  return { currentLanguage, mounted };
}

// Translation helper
export const translations = {
  id: {
    // Navigation
    'nav.home': 'Beranda',
    'nav.about': 'Tentang',
    'nav.projects': 'Proyek',
    'nav.contact': 'Kontak',
    
    // Loading
    'loading.title': 'Memuat Dunia KangPCode...',
    'loading.preparing': 'Menyiapkan Kamar Virtual...',
    'loading.computer': 'Mengaktifkan Komputer 3D...',
    'loading.posters': 'Memuat Poster Ilmuwan...',
    'loading.figures': 'Menyiapkan Action Figures...',
    'loading.ready': 'Dunia KangPCode Siap!',
    
    // Intro
    'intro.title': 'KangPCode',
    'intro.subtitle': 'Portfolio 3D Interaktif',
    'intro.description': 'Selamat datang di dunia virtual KangPCode! Jelajahi kamar virtual yang penuh dengan teknologi, proyek, dan inspirasi dari seorang developer Indonesia.',
    'intro.enter': 'Masuk ke Kamar KangPCode',
    'intro.click': 'Klik pintu untuk memulai petualangan 3D Anda',
    
    // Room
    'room.title': 'Kamar KangPCode',
    'room.instruction': 'Klik objek untuk berinteraksi',
    'room.exit': 'Keluar Kamar',
    
    // Components
    'computer.title': 'Komputer KDE Plasma',
    'laptop.title': 'GitHub Viewer',
    'book.title': 'Langkah Kode Nusantara',
    'idcard.title': 'ID Card KangPCode',
    'poster.title': 'Poster Ilmuwan Teknologi',
    'figures.title': 'Action Figure Collection',
    'whiteboard.title': 'Project Whiteboard',
    
    // Common
    'common.close': 'Tutup',
    'common.loading': 'Memuat...',
    'common.error': 'Terjadi kesalahan',
    'common.retry': 'Coba Lagi',
  },
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.about': 'About',
    'nav.projects': 'Projects',
    'nav.contact': 'Contact',
    
    // Loading
    'loading.title': 'Loading KangPCode World...',
    'loading.preparing': 'Preparing Virtual Room...',
    'loading.computer': 'Activating 3D Computer...',
    'loading.posters': 'Loading Scientist Posters...',
    'loading.figures': 'Preparing Action Figures...',
    'loading.ready': 'KangPCode World Ready!',
    
    // Intro
    'intro.title': 'KangPCode',
    'intro.subtitle': 'Interactive 3D Portfolio',
    'intro.description': 'Welcome to KangPCode\'s virtual world! Explore a virtual room full of technology, projects, and inspiration from an Indonesian developer.',
    'intro.enter': 'Enter KangPCode\'s Room',
    'intro.click': 'Click the door to start your 3D adventure',
    
    // Room
    'room.title': 'KangPCode\'s Room',
    'room.instruction': 'Click objects to interact',
    'room.exit': 'Exit Room',
    
    // Components
    'computer.title': 'KDE Plasma Computer',
    'laptop.title': 'GitHub Viewer',
    'book.title': 'Langkah Kode Nusantara',
    'idcard.title': 'KangPCode ID Card',
    'poster.title': 'Technology Scientists Poster',
    'figures.title': 'Action Figure Collection',
    'whiteboard.title': 'Project Whiteboard',
    
    // Common
    'common.close': 'Close',
    'common.loading': 'Loading...',
    'common.error': 'An error occurred',
    'common.retry': 'Try Again',
  }
};

export function useTranslation() {
  const { currentLanguage } = useLanguage();
  
  const t = (key: string): string => {
    const keys = key.split('.');
    let value: any = translations[currentLanguage as keyof typeof translations];
    
    for (const k of keys) {
      value = value?.[k];
    }
    
    return value || key;
  };
  
  return { t, currentLanguage };
}

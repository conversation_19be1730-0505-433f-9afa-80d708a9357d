# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [Unreleased]

- Nothing yet!

## [0.4.2] - 2022-09-02

### Fixed

- Update TypeScript types ([#34](https://github.com/tailwindlabs/tailwindcss-aspect-ratio/pull/34))

## [0.4.1] - 2022-09-01

### Added

- Remove `dist` folder and related dependencies ([#29](https://github.com/tailwindlabs/tailwindcss-aspect-ratio/pull/29))
- Add typescript types ([#33](https://github.com/tailwindlabs/tailwindcss-aspect-ratio/pull/33))

## [0.4.0] - 2021-12-09

### Added

- Make sure that Tailwind CSS v3 is in peerDependencies ([ae97a25](https://github.com/tailwindlabs/tailwindcss-aspect-ratio/commit/ae97a25))

## [0.3.0] - 2021-10-05

### Added

- Support arbitrary values in Tailwind CSS v3.0.0-alpha.1, via the new `matchComponents` API

## [0.2.2] - 2021-10-02

### Fixed

- Fix compatibility with Tailwind CSS v3.0.0-alpha.1

## [0.2.1] - 2021-05-24

### Fixed

- Mark `tailwindcss` as peer-dependency for better monorepo support ([#14](https://github.com/tailwindlabs/tailwindcss-aspect-ratio/pull/14))

## [0.2.0] - 2020-11-16

### Changed

- Prefix custom properties with `tw-` to match Tailwind CSS v2.0 convention ([6802588](https://github.com/tailwindlabs/tailwindcss-aspect-ratio/commit/6802588))

## [0.1.4] - 2020-11-06

### Added

- Add `aspect-none` clas ([73b57ef](https://github.com/tailwindlabs/tailwindcss-aspect-ratio/commit/73b57ef))

## [0.1.3] - 2020-11-06

### Fixed

- Remove debugging statements ([32d68f5](https://github.com/tailwindlabs/tailwindcss-aspect-ratio/commit/32d68f5))

## [0.1.2] - 2020-11-06

### Fixed

- Remove incorrect peer dependencies ([d46bf8f](https://github.com/tailwindlabs/tailwindcss-aspect-ratio/commit/d46bf8f))

## [0.1.1] - 2020-11-06

### Fixed

- Fix entry point so it even works at all ([88fd2f1](https://github.com/tailwindlabs/tailwindcss-aspect-ratio/commit/88fd2f1))

## [0.1.0] - 2020-11-06

Initial release!

[unreleased]: https://github.com/tailwindlabs/tailwindcss-aspect-ratio/compare/v0.4.2...HEAD
[0.4.2]: https://github.com/tailwindlabs/tailwindcss-aspect-ratio/compare/v0.4.1...v0.4.2
[0.4.1]: https://github.com/tailwindlabs/tailwindcss-aspect-ratio/compare/v0.4.0...v0.4.1
[0.4.0]: https://github.com/tailwindlabs/tailwindcss-aspect-ratio/compare/v0.3.0...v0.4.0
[0.3.0]: https://github.com/tailwindlabs/tailwindcss-aspect-ratio/compare/v0.2.2...v0.3.0
[0.2.2]: https://github.com/tailwindlabs/tailwindcss-aspect-ratio/compare/v0.2.1...v0.2.2
[0.2.1]: https://github.com/tailwindlabs/tailwindcss-aspect-ratio/compare/v0.2.0...v0.2.1
[0.2.0]: https://github.com/tailwindlabs/tailwindcss-aspect-ratio/compare/v0.1.4...v0.2.0
[0.1.4]: https://github.com/tailwindlabs/tailwindcss-aspect-ratio/compare/v0.1.3...v0.1.4
[0.1.3]: https://github.com/tailwindlabs/tailwindcss-aspect-ratio/compare/v0.1.2...v0.1.3
[0.1.2]: https://github.com/tailwindlabs/tailwindcss-aspect-ratio/compare/v0.1.1...v0.1.2
[0.1.1]: https://github.com/tailwindlabs/tailwindcss-aspect-ratio/compare/v0.1.0...v0.1.1
[0.1.0]: https://github.com/tailwindlabs/tailwindcss-aspect-ratio/releases/tag/v0.1.0

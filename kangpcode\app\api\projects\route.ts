import { NextRequest, NextResponse } from 'next/server';
import db from '@/database/db';
import { project } from '@/database/schema';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const featured = searchParams.get('featured');
    const status = searchParams.get('status');
    const limit = parseInt(searchParams.get('limit') || '50');

    let query = db.select().from(project);

    // Apply filters
    if (featured === 'true') {
      query = query.where(project.featured.eq(true));
    }
    if (status) {
      query = query.where(project.status.eq(status));
    }

    const results = await query
      .orderBy(project.createdAt.desc())
      .limit(limit);

    return NextResponse.json(results);
  } catch (error) {
    console.error('Projects fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      description,
      stack,
      previewImage,
      demoUrl,
      githubUrl,
      status,
      featured = false,
    } = body;

    // Validate required fields
    if (!name || !description || !stack || !status) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    const now = new Date();

    const result = await db.insert(project).values({
      name,
      description,
      stack: JSON.stringify(stack),
      previewImage,
      demoUrl,
      githubUrl,
      status,
      featured,
      createdAt: now,
      updatedAt: now,
    });

    return NextResponse.json({ success: true, id: result.insertId });
  } catch (error) {
    console.error('Project creation error:', error);
    return NextResponse.json(
      { error: 'Failed to create project' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      id,
      name,
      description,
      stack,
      previewImage,
      demoUrl,
      githubUrl,
      status,
      featured,
    } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    const now = new Date();

    await db.update(project)
      .set({
        name,
        description,
        stack: stack ? JSON.stringify(stack) : undefined,
        previewImage,
        demoUrl,
        githubUrl,
        status,
        featured,
        updatedAt: now,
      })
      .where(project.id.eq(id));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Project update error:', error);
    return NextResponse.json(
      { error: 'Failed to update project' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Project ID is required' },
        { status: 400 }
      );
    }

    await db.delete(project).where(project.id.eq(parseInt(id)));

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Project deletion error:', error);
    return NextResponse.json(
      { error: 'Failed to delete project' },
      { status: 500 }
    );
  }
}

// API service layer for frontend-backend communication

interface ApiResponse<T> {
  data?: T;
  error?: string;
  success: boolean;
}

class ApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = process.env.NEXT_PUBLIC_API_URL || '';
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}/api${endpoint}`;
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        return {
          success: false,
          error: errorData.error || `HTTP ${response.status}: ${response.statusText}`,
        };
      }

      const data = await response.json();
      return {
        success: true,
        data,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  // Biodata API
  async getBiodata() {
    return this.request<any>('/biodata');
  }

  async updateBiodata(data: any) {
    return this.request<any>('/biodata', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Projects API
  async getProjects(params?: { featured?: boolean; status?: string; limit?: number }) {
    const searchParams = new URLSearchParams();
    if (params?.featured) searchParams.set('featured', 'true');
    if (params?.status) searchParams.set('status', params.status);
    if (params?.limit) searchParams.set('limit', params.limit.toString());

    const query = searchParams.toString();
    return this.request<any[]>(`/projects${query ? `?${query}` : ''}`);
  }

  async createProject(data: any) {
    return this.request<any>('/projects', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async updateProject(data: any) {
    return this.request<any>('/projects', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  async deleteProject(id: number) {
    return this.request<any>(`/projects?id=${id}`, {
      method: 'DELETE',
    });
  }

  // Analytics API
  async trackEvent(event: string, target: string, data?: any) {
    return this.request<any>('/analytics', {
      method: 'POST',
      body: JSON.stringify({ event, target, data }),
    });
  }

  async getAnalytics(params?: { limit?: number; event?: string; target?: string }) {
    const searchParams = new URLSearchParams();
    if (params?.limit) searchParams.set('limit', params.limit.toString());
    if (params?.event) searchParams.set('event', params.event);
    if (params?.target) searchParams.set('target', params.target);

    const query = searchParams.toString();
    return this.request<any>(`/analytics${query ? `?${query}` : ''}`);
  }

  // Game Scores API
  async getGameScores(game?: string, limit?: number) {
    const searchParams = new URLSearchParams();
    if (game) searchParams.set('game', game);
    if (limit) searchParams.set('limit', limit.toString());

    const query = searchParams.toString();
    return this.request<any[]>(`/game-scores${query ? `?${query}` : ''}`);
  }

  async saveGameScore(game: string, score: number, playerName?: string, data?: any) {
    return this.request<any>('/game-scores', {
      method: 'POST',
      body: JSON.stringify({ game, score, playerName, data }),
    });
  }

  // GitHub API (proxy through our backend)
  async getGitHubRepos(username: string = 'kangpcode') {
    return this.request<any[]>(`/github/repos?username=${username}`);
  }

  async getGitHubUser(username: string = 'kangpcode') {
    return this.request<any>(`/github/user?username=${username}`);
  }

  // Book API
  async getBookData() {
    return this.request<any>('/book');
  }

  // Scientists API
  async getScientists(category?: string) {
    const query = category ? `?category=${category}` : '';
    return this.request<any[]>(`/scientists${query}`);
  }

  // Figures API
  async getFigures() {
    return this.request<any[]>('/figures');
  }

  // Settings API
  async getSettings() {
    return this.request<any[]>('/settings');
  }

  async updateSetting(key: string, value: string) {
    return this.request<any>('/settings', {
      method: 'PUT',
      body: JSON.stringify({ key, value }),
    });
  }
}

// Export singleton instance
export const apiService = new ApiService();

// React hooks for API calls
export function useApi() {
  return apiService;
}

// Specific hooks for common operations
export function useBiodata() {
  const api = useApi();
  
  const getBiodata = async () => {
    const response = await api.getBiodata();
    if (!response.success) {
      console.error('Failed to fetch biodata:', response.error);
      return null;
    }
    return response.data;
  };

  const updateBiodata = async (data: any) => {
    const response = await api.updateBiodata(data);
    if (!response.success) {
      console.error('Failed to update biodata:', response.error);
      return false;
    }
    return true;
  };

  return { getBiodata, updateBiodata };
}

export function useProjects() {
  const api = useApi();
  
  const getProjects = async (params?: { featured?: boolean; status?: string; limit?: number }) => {
    const response = await api.getProjects(params);
    if (!response.success) {
      console.error('Failed to fetch projects:', response.error);
      return [];
    }
    return response.data || [];
  };

  const createProject = async (data: any) => {
    const response = await api.createProject(data);
    if (!response.success) {
      console.error('Failed to create project:', response.error);
      return false;
    }
    return true;
  };

  return { getProjects, createProject };
}

export function useAnalytics() {
  const api = useApi();
  
  const trackEvent = async (event: string, target: string, data?: any) => {
    // Don't block UI for analytics
    api.trackEvent(event, target, data).catch(error => {
      console.warn('Analytics tracking failed:', error);
    });
  };

  const getAnalytics = async (params?: { limit?: number; event?: string; target?: string }) => {
    const response = await api.getAnalytics(params);
    if (!response.success) {
      console.error('Failed to fetch analytics:', response.error);
      return null;
    }
    return response.data;
  };

  return { trackEvent, getAnalytics };
}

export function useGameScores() {
  const api = useApi();
  
  const getScores = async (game?: string, limit?: number) => {
    const response = await api.getGameScores(game, limit);
    if (!response.success) {
      console.error('Failed to fetch game scores:', response.error);
      return [];
    }
    return response.data || [];
  };

  const saveScore = async (game: string, score: number, playerName?: string, data?: any) => {
    const response = await api.saveGameScore(game, score, playerName, data);
    if (!response.success) {
      console.error('Failed to save game score:', response.error);
      return false;
    }
    return true;
  };

  return { getScores, saveScore };
}

export function useGitHub() {
  const api = useApi();
  
  const getRepos = async (username?: string) => {
    const response = await api.getGitHubRepos(username);
    if (!response.success) {
      console.error('Failed to fetch GitHub repos:', response.error);
      return [];
    }
    return response.data || [];
  };

  const getUser = async (username?: string) => {
    const response = await api.getGitHubUser(username);
    if (!response.success) {
      console.error('Failed to fetch GitHub user:', response.error);
      return null;
    }
    return response.data;
  };

  return { getRepos, getUser };
}

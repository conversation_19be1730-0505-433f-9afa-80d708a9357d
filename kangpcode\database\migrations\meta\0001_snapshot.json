{"version": "6", "dialect": "sqlite", "id": "db4ceed1-c3f2-484d-b800-e0f75410f251", "prevId": "09bd6ffa-751d-4924-8ee7-7b4142ff2b7b", "tables": {"analytics": {"name": "analytics", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "event": {"name": "event", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "target": {"name": "target", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ip": {"name": "ip", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "biodata": {"name": "biodata", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "education": {"name": "education", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "university": {"name": "university", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "github": {"name": "github", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "book": {"name": "book", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "github_url": {"name": "github_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "pdf_url": {"name": "pdf_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "cover_image": {"name": "cover_image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "chapters": {"name": "chapters", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "skills": {"name": "skills", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "error_log": {"name": "error_log", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "stack": {"name": "stack", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "context": {"name": "context", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "page": {"name": "page", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "figure": {"name": "figure", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "anime": {"name": "anime", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "quote": {"name": "quote", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "philosophy": {"name": "philosophy", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "position": {"name": "position", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "game_score": {"name": "game_score", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "game": {"name": "game", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "score": {"name": "score", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "player_name": {"name": "player_name", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "data": {"name": "data", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "id_card": {"name": "id_card", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "photo": {"name": "photo", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "qr_code": {"name": "qr_code", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "github_url": {"name": "github_url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "performance_metric": {"name": "performance_metric", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "metric": {"name": "metric", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "real", "primaryKey": false, "notNull": true, "autoincrement": false}, "unit": {"name": "unit", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "page": {"name": "page", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "device": {"name": "device", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "connection": {"name": "connection", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "timestamp": {"name": "timestamp", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "project": {"name": "project", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "stack": {"name": "stack", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "preview_image": {"name": "preview_image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "demo_url": {"name": "demo_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "github_url": {"name": "github_url", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "featured": {"name": "featured", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "scientist": {"name": "scientist", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "contribution": {"name": "contribution", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "birth_year": {"name": "birth_year", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "death_year": {"name": "death_year", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "nationality": {"name": "nationality", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_session": {"name": "user_session", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "session_id": {"name": "session_id", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "ip": {"name": "ip", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "country": {"name": "country", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "device": {"name": "device", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "browser": {"name": "browser", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "os": {"name": "os", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "referrer": {"name": "referrer", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "landing_page": {"name": "landing_page", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "theme": {"name": "theme", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "music_enabled": {"name": "music_enabled", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": false}, "start_time": {"name": "start_time", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch())"}, "end_time": {"name": "end_time", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "page_views": {"name": "page_views", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "interactions": {"name": "interactions", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": 0}, "created_at": {"name": "created_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch())"}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false, "default": "(unixepoch())"}}, "indexes": {"user_session_session_id_unique": {"name": "user_session_session_id_unique", "columns": ["session_id"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "user_settings": {"name": "user_settings", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": false}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "updated_at": {"name": "updated_at", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}}, "indexes": {"user_settings_key_unique": {"name": "user_settings_key_unique", "columns": ["key"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}
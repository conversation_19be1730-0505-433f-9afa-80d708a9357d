import Database from 'better-sqlite3';
import { drizzle } from 'drizzle-orm/better-sqlite3';
import * as schema from './schema';
import {
  biodata,
  book,
  idCard,
  project,
  scientist,
  figure,
  userSettings
} from './schema';

// Create database file
const sqlite = new Database('./database/db.sqlite');
const db = drizzle(sqlite, { schema });

// Create tables manually
sqlite.exec(`
CREATE TABLE IF NOT EXISTS analytics (
  id INTEGER PRIMARY KEY NOT NULL,
  event TEXT NOT NULL,
  target TEXT NOT NULL,
  data TEXT,
  user_agent TEXT,
  ip TEXT,
  timestamp INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS biodata (
  id INTEGER PRIMARY KEY NOT NULL,
  name TEXT NOT NULL,
  role TEXT NOT NULL,
  education TEXT NOT NULL,
  university TEXT NOT NULL,
  status TEXT NOT NULL,
  bio TEXT NOT NULL,
  github TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  location TEXT,
  avatar TEXT,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS book (
  id INTEGER PRIMARY KEY NOT NULL,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  github_url TEXT NOT NULL,
  pdf_url TEXT,
  cover_image TEXT,
  chapters TEXT,
  skills TEXT,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS figure (
  id INTEGER PRIMARY KEY NOT NULL,
  name TEXT NOT NULL,
  anime TEXT NOT NULL,
  quote TEXT NOT NULL,
  philosophy TEXT NOT NULL,
  image TEXT,
  position TEXT,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS game_score (
  id INTEGER PRIMARY KEY NOT NULL,
  game TEXT NOT NULL,
  score INTEGER NOT NULL,
  player_name TEXT,
  data TEXT,
  timestamp INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS id_card (
  id INTEGER PRIMARY KEY NOT NULL,
  name TEXT NOT NULL,
  role TEXT NOT NULL,
  status TEXT NOT NULL,
  photo TEXT NOT NULL,
  qr_code TEXT NOT NULL,
  github_url TEXT NOT NULL,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS project (
  id INTEGER PRIMARY KEY NOT NULL,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  stack TEXT NOT NULL,
  preview_image TEXT,
  demo_url TEXT,
  github_url TEXT,
  status TEXT NOT NULL,
  featured INTEGER DEFAULT 0,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS scientist (
  id INTEGER PRIMARY KEY NOT NULL,
  name TEXT NOT NULL,
  description TEXT NOT NULL,
  contribution TEXT NOT NULL,
  image TEXT,
  category TEXT NOT NULL,
  birth_year INTEGER,
  death_year INTEGER,
  nationality TEXT,
  created_at INTEGER NOT NULL,
  updated_at INTEGER NOT NULL
);

CREATE TABLE IF NOT EXISTS user_settings (
  id INTEGER PRIMARY KEY NOT NULL,
  key TEXT NOT NULL UNIQUE,
  value TEXT NOT NULL,
  updated_at INTEGER NOT NULL
);
`);

console.log('Database tables created successfully!');

// Insert sample data
const now = Date.now();

// Insert biodata
sqlite.prepare(`
  INSERT OR REPLACE INTO biodata (
    id, name, role, education, university, status, bio, github, 
    email, phone, location, avatar, created_at, updated_at
  ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
`).run(
  1,
  'Dhafa Nazula Permadi',
  'Fullstack Developer & Content Creator',
  'Informatika',
  'Universitas XYZ',
  'Open for Collaboration',
  'KangPCode adalah developer dan kreator teknologi Nusantara yang berfokus pada pengembangan aplikasi web modern dan edukasi teknologi.',
  'https://github.com/kangpcode',
  '<EMAIL>',
  '+62-xxx-xxxx-xxxx',
  'Indonesia',
  '/assets/images/kangpcode-avatar.jpg',
  now,
  now
);

// Seed comprehensive data using Drizzle ORM
async function seedDatabase() {
  console.log('🌱 Starting comprehensive database seeding...');

  try {
    // Seed biodata
    await db.insert(biodata).values({
      name: 'Dhafa Nazula Permadi',
      role: 'Fullstack Developer & Content Creator',
      education: 'Informatika',
      university: 'Universitas XYZ',
      status: 'Open for Collaboration',
      bio: 'Passionate fullstack developer with 3+ years of experience building modern web applications. Specialized in React/Next.js ecosystem with strong background in backend development. Active in tech community and committed to sharing knowledge through content creation and mentoring.',
      github: 'github.com/kangpcode',
      email: '<EMAIL>',
      phone: '+62-xxx-xxxx-xxxx',
      location: 'Indonesia',
      avatar: '/assets/images/kangpcode-avatar.jpg',
    }).onConflictDoNothing();

    // Seed book
    await db.insert(book).values({
      title: 'Langkah Kode Nusantara',
      subtitle: 'Perjalanan Belajar & Berkarya dalam Dunia Teknologi Indonesia',
      author: 'Dhafa Nazula Permadi (KangPCode)',
      description: 'Sebuah panduan komprehensif untuk developer Indonesia yang ingin membangun karir di dunia teknologi dengan tetap mengangkat nilai-nilai lokal dan berkontribusi untuk kemajuan bangsa.',
      githubUrl: 'https://github.com/kangpcode/langkah-kode-nusantara',
      pdfUrl: '/assets/book/langkah-kode-nusantara-sample.pdf',
      coverImage: '/assets/images/book/cover.jpg',
      chapters: JSON.stringify([
        { number: 1, title: 'Pengenalan Dunia Teknologi Indonesia' },
        { number: 2, title: 'Dasar-Dasar Programming' },
        { number: 3, title: 'Web Development Modern' },
        { number: 4, title: 'Mobile Development' },
        { number: 5, title: 'DevOps & Deployment' },
        { number: 6, title: 'Membangun Karir Tech di Indonesia' }
      ]),
      skills: JSON.stringify([
        'JavaScript & TypeScript', 'React & Next.js', 'Node.js & Express',
        'Python & Django', 'Database Design', 'Docker & Kubernetes',
        'AWS & Cloud Computing', 'Git & Version Control'
      ]),
      stats: JSON.stringify({ pages: 350, chapters: 6, codeExamples: 150, projects: 12 }),
      featured: true,
    }).onConflictDoNothing();

    console.log('✅ Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    throw error;
  } finally {
    sqlite.close();
  }
}

// Run seeding
seedDatabase()
  .then(() => {
    console.log('🎉 All done!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Seeding failed:', error);
    process.exit(1);
  });

// Analytics tracking utilities

interface AnalyticsEvent {
  event: string;
  target: string;
  data?: any;
}

class Analytics {
  private static instance: Analytics;
  private queue: AnalyticsEvent[] = [];
  private isOnline = true;

  private constructor() {
    // Listen for online/offline events
    if (typeof window !== 'undefined') {
      window.addEventListener('online', () => {
        this.isOnline = true;
        this.flushQueue();
      });
      
      window.addEventListener('offline', () => {
        this.isOnline = false;
      });
    }
  }

  static getInstance(): Analytics {
    if (!Analytics.instance) {
      Analytics.instance = new Analytics();
    }
    return Analytics.instance;
  }

  async track(event: string, target: string, data?: any) {
    const analyticsEvent: AnalyticsEvent = {
      event,
      target,
      data,
    };

    if (this.isOnline) {
      try {
        await this.sendEvent(analyticsEvent);
      } catch (error) {
        console.warn('Failed to send analytics event, queuing for later:', error);
        this.queue.push(analyticsEvent);
      }
    } else {
      this.queue.push(analyticsEvent);
    }
  }

  private async sendEvent(analyticsEvent: AnalyticsEvent) {
    const response = await fetch('/api/analytics', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(analyticsEvent),
    });

    if (!response.ok) {
      throw new Error(`Analytics request failed: ${response.status}`);
    }
  }

  private async flushQueue() {
    while (this.queue.length > 0 && this.isOnline) {
      const event = this.queue.shift();
      if (event) {
        try {
          await this.sendEvent(event);
        } catch (error) {
          console.warn('Failed to send queued analytics event:', error);
          // Put it back at the front of the queue
          this.queue.unshift(event);
          break;
        }
      }
    }
  }

  // Predefined event types
  trackPageView(page: string) {
    this.track('page_view', page);
  }

  trackClick(target: string, data?: any) {
    this.track('click', target, data);
  }

  trackHover(target: string, duration?: number) {
    this.track('hover', target, { duration });
  }

  trackInteraction(target: string, action: string, data?: any) {
    this.track('interaction', target, { action, ...data });
  }

  trackGameScore(game: string, score: number, playerName?: string) {
    this.track('game_score', game, { score, playerName });
  }

  trackDownload(file: string, type: string) {
    this.track('download', file, { type });
  }

  trackError(error: string, context?: string) {
    this.track('error', error, { context });
  }

  trackPerformance(metric: string, value: number, unit: string) {
    this.track('performance', metric, { value, unit });
  }
}

// Export singleton instance
export const analytics = Analytics.getInstance();

// React hook for analytics
export function useAnalytics() {
  const trackClick = (target: string, data?: any) => {
    analytics.trackClick(target, data);
  };

  const trackHover = (target: string, duration?: number) => {
    analytics.trackHover(target, duration);
  };

  const trackInteraction = (target: string, action: string, data?: any) => {
    analytics.trackInteraction(target, action, data);
  };

  const trackPageView = (page: string) => {
    analytics.trackPageView(page);
  };

  const trackGameScore = (game: string, score: number, playerName?: string) => {
    analytics.trackGameScore(game, score, playerName);
  };

  const trackDownload = (file: string, type: string) => {
    analytics.trackDownload(file, type);
  };

  const trackError = (error: string, context?: string) => {
    analytics.trackError(error, context);
  };

  return {
    trackClick,
    trackHover,
    trackInteraction,
    trackPageView,
    trackGameScore,
    trackDownload,
    trackError,
  };
}

// Higher-order component for automatic click tracking
export function withAnalytics<T extends React.ComponentProps<any>>(
  Component: React.ComponentType<T>,
  eventName: string
) {
  return function AnalyticsWrapper(props: T) {
    const { trackClick } = useAnalytics();

    const handleClick = (e: React.MouseEvent) => {
      trackClick(eventName, {
        timestamp: Date.now(),
        position: { x: e.clientX, y: e.clientY },
      });

      // Call original onClick if it exists
      if (props.onClick) {
        props.onClick(e);
      }
    };

    return <Component {...props} onClick={handleClick} />;
  };
}

// Utility for tracking time spent on elements
export function useTimeTracking(target: string) {
  const { trackHover } = useAnalytics();
  let startTime: number | null = null;

  const startTracking = () => {
    startTime = Date.now();
  };

  const stopTracking = () => {
    if (startTime) {
      const duration = Date.now() - startTime;
      trackHover(target, duration);
      startTime = null;
    }
  };

  return { startTracking, stopTracking };
}
